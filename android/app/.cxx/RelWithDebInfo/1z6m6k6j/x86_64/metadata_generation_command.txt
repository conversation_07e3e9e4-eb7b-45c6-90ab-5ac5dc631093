                        -H/Users/<USER>/FlutterDev/flutter/packages/flutter_tools/gradle/src/main/groovy
-DCMAKE_SYSTEM_NAME=Android
-DCMAKE_EXPORT_COMPILE_COMMANDS=ON
-DCMAKE_SYSTEM_VERSION=23
-<PERSON><PERSON><PERSON><PERSON><PERSON>_PLATFORM=android-23
-<PERSON><PERSON>DROID_ABI=x86_64
-DCMAKE_ANDROID_ARCH_ABI=x86_64
-DANDROID_NDK=/Users/<USER>/Library/Android/sdk/ndk/28.0.13004108
-DCMAKE_ANDROID_NDK=/Users/<USER>/Library/Android/sdk/ndk/28.0.13004108
-DCMAKE_TOOLCHAIN_FILE=/Users/<USER>/Library/Android/sdk/ndk/28.0.13004108/build/cmake/android.toolchain.cmake
-DCMAKE_MAKE_PROGRAM=/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja
-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=/Users/<USER>/Flutter-Projects/Ajory/Dubai-Apps/aqar_dxb_user/build/app/intermediates/cxx/RelWithDebInfo/1z6m6k6j/obj/x86_64
-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=/Users/<USER>/Flutter-Projects/Ajory/Dubai-Apps/aqar_dxb_user/build/app/intermediates/cxx/RelWithDebInfo/1z6m6k6j/obj/x86_64
-DCMAKE_BUILD_TYPE=RelWithDebInfo
-B/Users/<USER>/Flutter-Projects/Ajory/Dubai-Apps/aqar_dxb_user/android/app/.cxx/RelWithDebInfo/1z6m6k6j/x86_64
-GNinja
-Wno-dev
--no-warn-unused-cli
                        Build command args: []
                        Version: 2