import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:get/get.dart';
import 'package:google_translator/google_translator.dart';
import 'package:provider/provider.dart';

import 'core/localization/app_language.dart';
import 'core/localization/app_localizations.dart';
import 'features/views/splash_screen/view/first_splash.dart';

class MyApp extends StatelessWidget {
  final String apiKey = "AIzaSyCXOO147BdbuceLIl8Z8D5Jxru2Vjhqd4Q";

  final AppLanguage appLanguage;
  final String? languageCode;

  const MyApp({
    super.key,
    required this.appLanguage,
    required this.languageCode,
  });

  @override
  Widget build(BuildContext context) {
    return Consumer<AppLanguage>(builder: (context, model, child) {
      return GoogleTranslatorInit(apiKey,
          translateFrom: const Locale('en'),
          translateTo: const Locale('ar'),
          builder: () => GetMaterialApp(
                debugShowCheckedModeBanner: false,
                locale: model.appLocal,
                supportedLocales: const [
                  Locale('en', 'US'),
                  Locale('ar', ''),
                ],
                localizationsDelegates: const [
                  AppLocalizations.delegate,
                  GlobalCupertinoLocalizations.delegate,
                  GlobalMaterialLocalizations.delegate,
                  GlobalWidgetsLocalizations.delegate,
                ],
                title: 'Aqar Dubai',
                theme: ThemeData(
                  scaffoldBackgroundColor: Colors.white,
                  appBarTheme: const AppBarTheme(
                    titleTextStyle: TextStyle(
                      color: Colors.white,
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                    ),
                    toolbarTextStyle: TextStyle(color: Colors.white),
                    iconTheme: IconThemeData(color: Colors.white),
                  ),
                  useMaterial3: true,
                  fontFamily: model.appLocal.languageCode == 'en'
                      ? 'Roboto'
                      : 'Tajawal',
                  primarySwatch: Colors.blue,
                ),
                home:
                    // SuccessRequestScreen(),
                    const FirstSplash(),
                // const MainFirstPage(),
              ));
    });
  }
}
