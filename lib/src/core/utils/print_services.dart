import 'dart:developer';

import 'package:collection/collection.dart';
import 'package:flutter/material.dart' as material;
import 'package:flutter/services.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart';
import 'package:provider/provider.dart';

import '../../features/models/plan_details_model.dart';
import '../localization/app_language.dart';
import '../services/api.dart';

bool isEng(context) =>
    Provider.of<AppLanguage>(context, listen: false).appLocal.languageCode ==
    'en';

class PrintingService {
  static TextStyle style = const TextStyle(
    fontSize: 14,
  );

  static Widget mainText(
    String text, {
    required arabicFont,
    required context,
  }) {
    // var lang =
    //     Provider.of<AppLanguage>(context, listen: false).appLocal.languageCode;
    final isEnglish = isEng(context);

    return Text(
      text,
      style: TextStyle(
        fontSize: 16,
        font: arabicFont,
      ),
      textAlign: isEnglish ? TextAlign.left : TextAlign.right,
      textDirection: isEnglish ? TextDirection.ltr : TextDirection.rtl,
    );
  }

  static double imageHeight = 100;
  static double imageWidth = 100;

  static Future<Document> generateHelloWorldExamplePdf() async {
    final doc = Document();

    doc.addPage(
      Page(
        build: (Context context) {
          return Center(
            child: Text("Test PDF"),
          ); // Center
        }, // Build
      ), // Page
    ); // A

    return doc;
  }

  static Future<Document> generatePDF(
      int id, material.BuildContext context) async {
    log('Dataa $id');

    final PlanDetailsModel response = await Api.getplanDetails(id);

    final groupedItems =
        response.data!.items?.groupListsBy((element) => element.date);
    final List<Widget> widgetsToShow = [];
    int day = 0;
    final isEnglish = isEng(context);

    final arabicFont = Font.ttf(
      await rootBundle.load("assets/fonts/cairo/Cairo-Bold.ttf"),
    );

    groupedItems?.forEach(
      (key, value) async {
        day++;
        widgetsToShow.add(
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              ...value.map(
                (e) {
                  final dayWidget = Expanded(
                    child: mainText("${isEnglish ? 'Day' : 'اليوم'} $day",
                        context: context, arabicFont: arabicFont),
                  );

                  final dateWidget = Expanded(
                    child: mainText(e.date ?? '',
                        context: context, arabicFont: arabicFont),
                  );

                  final timeWidget = Expanded(
                    child: mainText(e.time ?? '',
                        context: context, arabicFont: arabicFont),
                  );

                  final nameWidget = Expanded(
                    flex: 2,
                    child: mainText(e.videoDetails?.name ?? '',
                        context: context, arabicFont: arabicFont),
                  );

                  return Padding(
                    padding: const EdgeInsets.symmetric(vertical: 4),
                    child: Row(
                      children: [
                        if (isEnglish) ...[
                          dayWidget,
                          dateWidget,
                          timeWidget,
                          nameWidget,
                        ] else ...[
                          nameWidget,
                          timeWidget,
                          dateWidget,
                          dayWidget,
                        ]
                      ],
                    ),
                  );
                },
              ).toList(),
              Divider(color: const PdfColor(0.9, 0.9, 0.9)),
            ],
          ),
        );
      },
    );
    widgetsToShow.add(
      Column(
        children: [
          SizedBox(height: 50),
          Text(
            isEnglish ? 'Download DubaiPage App' : 'حمل تطبيق صفحة دبي الآن',
            style: TextStyle(
                fontSize: 22,
                fontWeight: FontWeight.bold,
                font: arabicFont,
                fontNormal: arabicFont,
                fontBold: arabicFont),
            textAlign: isEnglish ? TextAlign.left : TextAlign.right,
            textDirection: isEnglish ? TextDirection.ltr : TextDirection.rtl,
            overflow: TextOverflow.visible,
          ),
          SizedBox(height: 20),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              UrlLink(
                child: Text(
                  isEnglish ? 'App Store' : 'متجر ابل',
                  style: TextStyle(
                    fontSize: 25,
                    color: PdfColors.blue,
                    font: arabicFont,
                    fontNormal: arabicFont,
                    fontBold: arabicFont,
                    fontWeight: FontWeight.bold,
                  ),
                  textAlign: TextAlign.center,
                  textDirection:
                      isEnglish ? TextDirection.ltr : TextDirection.rtl,
                  overflow: TextOverflow.visible,
                ),
                // Icon(
                //   IconData(material.Icons.apple.codePoint),
                //   color: PdfColors.blueGrey,
                //   size: 18,
                // ),
                // Image(
                //   MemoryImage(
                //     (await rootBundle.load('assets/app_store.png'))
                //         .buffer
                //         .asUint8List(),
                //   ),
                //   height: 75,
                //   width: 150,
                //   fit: BoxFit.fill,
                // ),
                destination:
                    'https://apps.apple.com/us/app/dubai-page/id1662039794',
              ),
              SizedBox(width: 50),
              UrlLink(
                child: Text(
                  isEnglish ? 'Google Play' : 'متجر جوجل',
                  style: TextStyle(
                    fontSize: 25,
                    color: PdfColors.red,
                    font: arabicFont,
                    fontNormal: arabicFont,
                    fontBold: arabicFont,
                    fontWeight: FontWeight.bold,
                  ),
                  textAlign: TextAlign.center,
                  textDirection:
                      isEnglish ? TextDirection.ltr : TextDirection.rtl,
                  overflow: TextOverflow.visible,
                ),
                destination:
                    'https://play.google.com/store/apps/details?id=ae.dubaipage.app_user&pli=1',
              ),
              // Image(
              //   MemoryImage(
              //     (await rootBundle.load('assets/google_play.png'))
              //         .buffer
              //         .asUint8List(),
              //   ),
              //   height: 60,
              //   width: 150,
              //   fit: BoxFit.fill,
              // ),
            ],
          ),
        ],
      ),
    );
    return globalGenerateFunction(context, widgetsToShow, response.data);
  }

  static Future<Font?> loadFont() async {
    return Font.ttf(
      await rootBundle.load("assets/fonts/cairo/Cairo-Bold.ttf"),
    );
  }

  static Future<Document> globalGenerateFunction(
    material.BuildContext context,
    List<Widget> children,
    PlanDetailsDataModel? planDetailsDataModel,
  ) async {
    final isEnglish = isEng(context);

    final arabicFont = await loadFont();

    final doc = Document();

    // final arabicFont = Font.ttf(
    //   await rootBundle.load("assets/fonts/cairo/Cairo-Bold.ttf"),
    // );

    final startDate = planDetailsDataModel?.startdate ?? '';
    final endDate = planDetailsDataModel?.enddate ?? '';
    final planName = planDetailsDataModel?.planname ?? '';

    final title = Column(
      crossAxisAlignment:
          isEnglish ? CrossAxisAlignment.start : CrossAxisAlignment.end,
      children: [
        Text(isEnglish ? 'Entertainment plan' : 'الخطة الترفيهية',
            style: TextStyle(
              fontSize: 26,
              color: PdfColors.white,
              font: arabicFont,
              // Font.ttf(
              //   await rootBundle.load("assets/fonts/cairo/Cairo-Bold.ttf"),
              // ),
            ),
            textAlign: isEnglish ? TextAlign.left : TextAlign.right,
            textDirection: isEnglish ? TextDirection.ltr : TextDirection.rtl),
        Text(
            isEnglish
                ? 'From $startDate to $endDate'
                : 'من $startDate إلى $endDate',
            style: TextStyle(
              fontSize: 14,
              color: PdfColors.white,
              font: arabicFont,
              fontWeight: FontWeight.bold,
            ),
            textAlign: isEnglish ? TextAlign.left : TextAlign.right,
            textDirection: isEnglish ? TextDirection.ltr : TextDirection.rtl,
            maxLines: 1),
      ],
    );

    final logo = Image(
      MemoryImage(
          (await rootBundle.load('assets/logo2.png')).buffer.asUint8List()),
      height: imageHeight,
      width: imageWidth,
    );

    children.insert(
        0,
        Container(
          margin: const EdgeInsets.symmetric(vertical: 10),
          padding: const EdgeInsets.all(12),
          color: PdfColor.fromHex("1B2D39"),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              if (isEnglish) ...[
                title,
                Spacer(),
                logo,
              ] else ...[
                logo,
                Spacer(),
                title,
              ],
            ],
          ),
        ));

    final day = Expanded(
      child: mainText(isEnglish ? 'Day' : 'اليوم',
          context: context, arabicFont: arabicFont),
    );

    final date = Expanded(
      child: mainText(isEnglish ? 'Date' : 'التاريخ',
          context: context, arabicFont: arabicFont),
    );

    final time = Expanded(
      child: mainText(isEnglish ? 'Time' : 'الوقت',
          context: context, arabicFont: arabicFont),
    );

    final place = Expanded(
      flex: 2,
      child: mainText(isEnglish ? 'Place' : 'المكان',
          context: context, arabicFont: arabicFont),
    );

    children.insert(
        1,
        Padding(
          padding: const EdgeInsets.symmetric(vertical: 10),
          child: Text(
            planName,
            style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                font: arabicFont,
                fontBold: arabicFont),
            textAlign: TextAlign.center,
            textDirection: isEnglish ? TextDirection.ltr : TextDirection.rtl,
          ),
        ));
    children.insert(
      2,
      Padding(
        padding: const EdgeInsets.symmetric(vertical: 4),
        child: Row(
          children: [
            if (isEnglish) ...[
              day,
              date,
              time,
              place,
            ] else ...[
              place,
              time,
              date,
              day,
            ]
          ],
        ),
      ),
    );

    final widgets = children.slices(50);

    log('adssadsadasd ${widgets.length}');

    log('asdassasgasa ${children.length}');
    for (var i = 0; i < widgets.length; i++) {
      doc.addPage(
        MultiPage(
          maxPages: 100,
          pageTheme: PageTheme(
              pageFormat: PdfPageFormat.a4,
              textDirection: isEnglish ? TextDirection.ltr : TextDirection.rtl,
              theme: ThemeData(
                defaultTextStyle: TextStyle(
                  font: arabicFont,
                  fontBold: arabicFont,
                  fontSize: 20,
                ),
                bulletStyle: TextStyle(font: arabicFont),
                textAlign: isEnglish ? TextAlign.left : TextAlign.right,
              )),
          build: (Context context) {
            return [
              Column(
                crossAxisAlignment: isEnglish
                    ? CrossAxisAlignment.start
                    : CrossAxisAlignment.end,
                children: widgets.toList()[i],
              )
            ];
          },
        ),
      );
    }

    return doc;
  }
}
