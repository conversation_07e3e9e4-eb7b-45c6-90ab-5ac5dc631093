import 'dart:developer';

import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:page/src/core/extensions/extensions.dart';
import 'package:page/src/core/response/offers_response.dart';
import 'package:page/src/core/services/rms_service.dart';
import 'package:page/src/features/models/booked_model.dart';
import 'package:page/src/features/models/requests.dart';
import 'package:page/src/features/views/holiday/widgets/holiday_filter.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:universal_platform/universal_platform.dart';

import '../../features/controllers/language_controller.dart';
import '../../features/models/plan_details_model.dart';
import '../../features/views/account/account.dart';
import '../config/constants.dart';
import '../response/album_response.dart';
import '../response/auth_response.dart';
import '../response/category_response.dart';
import '../response/content_response.dart';
import '../response/discussion_response.dart';
import '../response/generalResponse.dart';
import '../response/home_response.dart';
import '../response/notification_response.dart';
import '../response/plan_response.dart';
import '../response/profile_response.dart';
import '../response/property_status_response.dart';
import '../response/reel_response.dart';
import '../response/request_response.dart';
import '../response/reviews_response.dart';
import '../response/search_map_response.dart';

Map<int, List<String>> closedVideoDates = {};
// Map<int, num> holidayVideosPrices = {};

class Api {
  LanguageController languageController = LanguageController();

  static final Dio _dio = Dio();

  static String handleError(dynamic error) {
    String errorDescription = "";
    if (error == null) {
      errorDescription = "Unexpected error occurred";
      return errorDescription;
    }
    switch (error) {
      case DioErrorType.cancel:
        errorDescription = "Request to API server was cancelled";
        break;
      case DioErrorType.connectTimeout:
        errorDescription = "Connection timeout with API server";
        break;
      case DioErrorType.sendTimeout:
        errorDescription =
            "Connection to API server failed due to internet connection";
        break;
      case DioErrorType.receiveTimeout:
        errorDescription = "Receive timeout in connection with API server";
        break;
      case DioErrorType.response:
        _handleError(error.response!.statusCode!, error.response!.policy);
        break;
      case DioErrorType.other:
        errorDescription = "لا يوجد اتصال بالانترنيت";
        break;
      default:
        errorDescription = "Unexpected error occurred";
    }
    return errorDescription;
  }

  static String _handleError(int statusCode, dynamic error) {
    switch (statusCode) {
      case 400:
        return 'Bad request';
      case 404:
        return error["message"];
      case 500:
        return 'Internal server error';
      default:
        return 'Oops something went wrong';
    }
  }

  static Future<AuthResponse> register(String phone, String password,
      String fullname, String email, String token, String phonecode) async {
    SharedPreferences _prefs = await SharedPreferences.getInstance();
    var currentlanguagecode = _prefs.getString('language_code');

    _dio.options.headers['lang'] = currentlanguagecode;
    try {
      Map<String, String> data = {
        "password": password,
        "email": email,
        "name": fullname,
        "fcmtoken": token,
        "phone_code": phonecode,
        "phone": phone,
        'device_type': UniversalPlatform.isIOS ? 'ios' : 'android',
        'user_type': 'USER'
      };

      print('Dataa: $data');

      Response response = await _dio.post(
        '${pathUrl}register',
        data: data,
        options: Options(
            headers: {"Content-Type": "application/x-www-form-urlencoded"},
            followRedirects: false,
            validateStatus: (status) {
              return status! < 600;
            }),
      );

      log('responseDataa: ${response.data}');

      return AuthResponse.fromJson(response.data);
    } catch (error, stacktrace) {
      log("Exception occured: $error stackTrace: $stacktrace");
      return AuthResponse.withError(handleError(error));
    }
  }

  static Future<AuthResponse> login(
      String email, String password, String? token) async {
    try {
      Map<String, String> data = {
        "email": email,
        "password": password,
        "fcmtoken": token ?? 'web',
        'device_type': UniversalPlatform.isIOS ? 'ios' : 'android',
      };
      SharedPreferences _prefs = await SharedPreferences.getInstance();
      var currentlanguagecode = _prefs.getString('language_code');
      _dio.options.headers['lang'] = currentlanguagecode;
      Response response = await _dio.post(
        '${pathUrl}login',
        data: data,
        options: Options(
            headers: {"Content-Type": "application/x-www-form-urlencoded"},
            followRedirects: false,
            validateStatus: (status) {
              return status! < 500;
            }),
      );
      log('Dataaaadwewe ${data} adasf ${response.data}');

      if (response.data['data'] != null) {
        if (response.data['data']['user_type'] != 'USER' && !kDebugMode) {
          return AuthResponse.withError("You are not user");
        }
      }

      return AuthResponse.fromJson(response.data);
    } catch (error, stacktrace) {
      log("Exception occured: $error stackTrace: $stacktrace");
      return AuthResponse.withError(handleError(error));
    }
  }

  static Future<dynamic> updateToken(String? token) async {
    try {
      Map<String, String?> data = {"fcmtoken": token};
      SharedPreferences _prefs = await SharedPreferences.getInstance();
      var currentlanguagecode = _prefs.getString('language_code');
      _dio.options.headers['lang'] = currentlanguagecode;
      Response response = await _dio.post(
        '${pathUrl}updatetoken',
        data: data,
        options: Options(
            headers: {"Content-Type": "application/x-www-form-urlencoded"},
            followRedirects: false,
            validateStatus: (status) {
              return status! < 500;
            }),
      );

      return response.data;
    } catch (error, stacktrace) {
      log("Exception occured: $error stackTrace: $stacktrace");
      return handleError(error);
    }
  }

  static Future<AuthResponse> sendrequestpassword(String email) async {
    try {
      Map<String, String> data = {"email": email, 'user_type': 'USER'};

      Response response = await _dio.post(
        '${pathUrl}sendrequestpassword',
        data: data,
        options: Options(
            headers: {"Content-Type": "application/x-www-form-urlencoded"},
            followRedirects: false,
            validateStatus: (status) {
              return status! < 500;
            }),
      );

      log('asdjasdhuh ${response.data}');

      return AuthResponse.fromForgotPass(response.data);
    } catch (error, stacktrace) {
      log("Exception occured: $error stackTrace: $stacktrace");
      return AuthResponse.withError(handleError(error));
    }
  }

  static Future<AuthResponse> verfiycode(String email, String code) async {
    try {
      Map<String, String> data = {"phone": email, "code": code};

      Response response = await _dio.post(
        '${pathUrl}verfiycode',
        data: data,
        options: Options(
            headers: {"Content-Type": "application/x-www-form-urlencoded"},
            followRedirects: false,
            validateStatus: (status) {
              return status! < 500;
            }),
      );

      return AuthResponse.fromForgotPass(response.data);
    } catch (error, stacktrace) {
      log("Exception occured: $error stackTrace: $stacktrace");
      return AuthResponse.withError(handleError(error));
    }
  }

  static Future<AuthResponse> verfiycoderegister(
      String phone, String code) async {
    try {
      Map<String, dynamic> data = {"status": true, "phone": phone};
      // Map<String, String> data = {"phone": phone, "code": code};

      Response response = await _dio.post(
        '${pathUrl}verifyphonecode',
        data: data,
        options: Options(
            headers: {"Content-Type": "application/x-www-form-urlencoded"},
            followRedirects: false,
            validateStatus: (status) {
              return status! < 500;
            }),
      );

      log('Data: $data responseData: ${response.data}');

      return
          // fromRegister
          //   ?
          AuthResponse.fromJson(response.data);
      // : AuthResponse.fromForgotPass(response.data);
    } catch (error, stacktrace) {
      log("Exception occured: $error stackTrace: $stacktrace");
      return AuthResponse.withError(handleError(error));
    }
  }

  static Future<AuthResponse> resendcode(String phone) async {
    try {
      Map<String, String> data = {"phone": phone};
      print(data);
      Response response = await _dio.post(
        '${pathUrl}resendcode',
        data: data,
        options: Options(
            headers: {"Content-Type": "application/x-www-form-urlencoded"},
            followRedirects: false,
            validateStatus: (status) {
              return status! < 500;
            }),
      );

      return AuthResponse.fromForgotPass(response.data);
    } catch (error, stacktrace) {
      log("Exception occured: $error stackTrace: $stacktrace");
      return AuthResponse.withError(handleError(error));
    }
  }

  static Future<AuthResponse> resetpassword(
      String email, String password) async {
    try {
      Map<String, String> data = {"email": email, 'password': password};

      Response response = await _dio.post(
        '${pathUrl}resetpassword',
        data: data,
        options: Options(
            headers: {"Content-Type": "application/x-www-form-urlencoded"},
            followRedirects: false,
            validateStatus: (status) {
              return status! < 500;
            }),
      );

      return AuthResponse.fromForgotPass(response.data);
    } catch (error, stacktrace) {
      log("Exception occured: $error stackTrace: $stacktrace");
      return AuthResponse.withError(handleError(error));
    }
  }

  static Future<CategoryResponse> getmainCategory(
      int page, int size, String key, String category,
      {int? agentId, String? typeId}) async {
    try {
      SharedPreferences _prefs = await SharedPreferences.getInstance();
      var currentlanguagecode = _prefs.getString('language_code');
      var currency = _prefs.getString('currency');
      _dio.options.headers['lang'] = currentlanguagecode;
      String url =
          '${pathUrl}getmaincategory?category=$category&page=$page&size=$size&key=$key&currency=$currency${agentId != null ? '&agent_id=$agentId' : ''}${typeId != null ? '&type=$typeId' : ''}';

      log("The URLDD $url");
      log("The Headers ${_dio.options.headers}");
      Response response = await _dio.get(
        url,
        options: Options(
            headers: {"Content-Type": "application/x-www-form-urlencoded"},
            followRedirects: false,
            validateStatus: (status) {
              return status! < 600;
            }),
      );

      log('URRR $url RESSS ${response.data}');
      return CategoryResponse.fromJson(response.data);
    } catch (error, stacktrace) {
      log("Exception occured: $error stackTrace: $stacktrace");
      return CategoryResponse.withError(handleError(error));
    }
  }

  // static Future<CarRentResponse?> getcarrent(
  //     int page, int size, String key) async {
  //   try {
  //     SharedPreferences _prefs = await SharedPreferences.getInstance();
  //     var currentlanguagecode = _prefs.getString('language_code');
  //     var currency = _prefs.getString('currency');
  //     _dio.options.headers['lang'] = currentlanguagecode;
  //     String url =
  //         '${pathUrl}getmaincategory?category=${AppConstants.carRentalsId}&page=$page&size=$size&key=$key&currency=$currency';
  //
  //     log("The URL $url");
  //     log("The Headers ${_dio.options.headers}");
  //     Response response = await _dio.get(
  //       url,
  //       options: Options(
  //           headers: {"Content-Type": "application/x-www-form-urlencoded"},
  //           followRedirects: false,
  //           validateStatus: (status) {
  //             return status! < 500;
  //           }),
  //     );
  //
  //
  //     return CarRentResponse.fromJson(response.data);
  //   } catch (error, stacktrace) {
  //     log("Exception occured: $error stackTrace: $stacktrace");
  //     return CarRentResponse.withError(handleError(error));
  //   }
  // }

  static Future<CategoryResponse> getfeaturedvideo(
      int page, int size, String key, String category) async {
    try {
      SharedPreferences _prefs = await SharedPreferences.getInstance();
      var currentlanguagecode = _prefs.getString('language_code');
      var currency = _prefs.getString('currency');
      _dio.options.headers['lang'] = currentlanguagecode;

      String url =
          '${pathUrl}getfeaturedvideo?category=$category&page=$page&size=$size&key=$key&currency=$currency';

      log("The URL $url");
      log("The Headers ${_dio.options.headers}");
      Response response = await _dio.get(
        url,
        options: Options(
            headers: {"Content-Type": "application/x-www-form-urlencoded"},
            followRedirects: false,
            validateStatus: (status) {
              return status! < 600;
            }),
      );

      return CategoryResponse.fromJson(response.data);
    } catch (error, stacktrace) {
      log("Exception occured: $error stackTrace: $stacktrace");
      return CategoryResponse.withError(handleError(error));
    }
  }

  static Future<CategoryResponse?> getfeaturedvideocarrent() async {
    try {
      SharedPreferences _prefs = await SharedPreferences.getInstance();
      var currentlanguagecode = _prefs.getString('language_code');
      var currency = _prefs.getString('currency');
      _dio.options.headers['lang'] = currentlanguagecode;
      String url =
          '${pathUrl}getfeaturedvideo?category=${AppConstants.carRentalsId}&currency=$currency';

      log("The URL $url");
      log("The Headers ${_dio.options.headers}");
      Response response = await _dio.get(
        url,
        options: Options(
            headers: {"Content-Type": "application/x-www-form-urlencoded"},
            followRedirects: false,
            validateStatus: (status) {
              return status! < 600;
            }),
      );

      return CategoryResponse.fromJson(response.data);
    } catch (error, stacktrace) {
      log("Exception occured: $error stackTrace: $stacktrace");
      return CategoryResponse.withError(handleError(error));
    }
  }

  static Future<CategoryetailsResponse?> getmainCategorydetails(int id) async {
    try {
      SharedPreferences _prefs = await SharedPreferences.getInstance();
      var currentlanguagecode = _prefs.getString('language_code');
      var currency = _prefs.getString('currency');
      _dio.options.headers['lang'] = currentlanguagecode;

      var token = _prefs.getString('token');
      if (token != null && token != "") {
        _dio.options.headers['Authorization'] = 'Bearer $token';
      }
      String url;
      url = '${pathUrl}getmaincategory?id=$id&currency=$currency';
      log("The URL $url");
      log("The Headers ${_dio.options.headers}");
      Response response = await _dio.get(
        url,
        options: Options(
            headers: {"Content-Type": "application/x-www-form-urlencoded"},
            followRedirects: false,
            validateStatus: (status) {
              return status! < 600;
            }),
      );
      log("The URL $url");
      log("The Headers ${_dio.options.headers}");

      print(
          "========================== response =============================== ");
      print('MainCategoryDetailsResponse=> ${response.data}');
      return CategoryetailsResponse.fromJson(response.data);
    } catch (error, stacktrace) {
      log("Exception occured: $error stackTrace: $stacktrace");
      return CategoryetailsResponse.withError(handleError(error));
    }
  }

  // static Future<CategoryetailsResponse?> getFeaturedVideoDetails(int id) async {
  //   try {
  //     SharedPreferences _prefs = await SharedPreferences.getInstance();
  //     var currentlanguagecode = _prefs.getString('language_code');
  //     _dio.options.headers['lang'] = currentlanguagecode;
  //
  //     // var currency = _prefs.getString('currency');
  //
  //     var token = _prefs.getString('token');
  //     if (token != null && token != "") {
  //       _dio.options.headers['Authorization'] = 'Bearer $token';
  //     }
  //     String url;
  //     log('iid $id');
  //
  //     url = '${pathUrl}getfeaturedvideosingle/$id';
  //     log("The URL $url");
  //     log("The Headers ${_dio.options.headers}");
  //     Response response = await _dio.get(
  //       url,
  //       options: Options(
  //           headers: {"Content-Type": "application/x-www-form-urlencoded"},
  //           followRedirects: false,
  //           validateStatus: (status) {
  //             return status! < 600;
  //           }),
  //     );
  //     log("The URL $url");
  //     log("The Headers ${_dio.options.headers}");
  //
  //     print(
  //         "========================== response =============================== ");
  //     print('MainCategoryDetailsResponse=> ${response.data}');
  //     return CategoryetailsResponse.fromJson(response.data);
  //   } catch (error, stacktrace) {
  //     log("Exception occured: $error stackTrace: $stacktrace");
  //     return CategoryetailsResponse.withError(handleError(error));
  //   }
  // }

  static Future<CategoryetailsResponse?> getholidayhomedetails(int id) async {
    try {
      SharedPreferences _prefs = await SharedPreferences.getInstance();
      var currentlanguagecode = _prefs.getString('language_code');

      _dio.options.headers['lang'] = currentlanguagecode;
      var currency = _prefs.getString('currency');
      var token = _prefs.getString('token');
      if (token != null && token != "") {
        _dio.options.headers['Authorization'] = 'Bearer $token';
      }
      Response response = await _dio.get(
        '${pathUrl}getmaincategory?id=$id&currency=$currency',
        options: Options(
            headers: {"Content-Type": "application/x-www-form-urlencoded"},
            followRedirects: false,
            validateStatus: (status) {
              return status! < 600;
            }),
      );
      print("qqqqqqqUUU ${pathUrl}getmaincategory?id=$id&currency=$currency");

      return CategoryetailsResponse.fromJson(response.data);
    } catch (error, stacktrace) {
      log("Exception occured: $error stackTrace: $stacktrace");
      return CategoryetailsResponse.withError(handleError(error));
    }
  }

  // static Future<CategoryetailsResponse?> getcarrentdetails(int id) async {
  //   try {
  //     SharedPreferences _prefs = await SharedPreferences.getInstance();
  //     var currentlanguagecode = _prefs.getString('language_code');
  //     var currency = _prefs.getString('currency');
  //     _dio.options.headers['lang'] = currentlanguagecode;
  //     var token = _prefs.getString('token');
  //
  //
  //
  //
  //
  //     if (token != null && token != "") {
  //       _dio.options.headers['Authorization'] = 'Bearer $token';
  //     }
  //     Response response = await _dio.get(
  //       '${pathUrl}getcarrentdetails?id=$id&currency=$currency',
  //       options: Options(
  //           headers: {"Content-Type": "application/x-www-form-urlencoded"},
  //           followRedirects: false,
  //           validateStatus: (status) {
  //             return status! < 500;
  //           }),
  //     );
  //     print("Urrll ${pathUrl}getcarrentdetails?id=$id&currency=$currency");
  //
  //     return CategoryetailsResponse.fromJson(response.data);
  //   } catch (error, stacktrace) {
  //     log("Exception occured: $error stackTrace: $stacktrace");
  //     return CategoryetailsResponse.withError(handleError(error));
  //   }
  // }

  static Future<CategoryetailsResponse> getluxurydetails(int id) async {
    try {
      SharedPreferences _prefs = await SharedPreferences.getInstance();
      var currentlanguagecode = _prefs.getString('language_code');
      var currency = _prefs.getString('currency');
      _dio.options.headers['lang'] = currentlanguagecode;

      var token = _prefs.getString('token');

      if (token != null && token != "") {
        _dio.options.headers['Authorization'] = 'Bearer $token';
      }
      Response response = await _dio.get(
        '${pathUrl}getmaincategory?id=$id&currency=$currency',
        options: Options(
            headers: {"Content-Type": "application/x-www-form-urlencoded"},
            followRedirects: false,
            validateStatus: (status) {
              return status! < 500;
            }),
      );
      print("Urllll ${pathUrl}getmaincategory?id=$id&currency=$currency");

      print('propertyRRees: $response');
      return CategoryetailsResponse.fromJson(response.data);
    } catch (error, stacktrace) {
      log("Exception occured: $error stackTrace: $stacktrace");
      return CategoryetailsResponse.withError(handleError(error));
    }
  }

  static Future<CategoryImagesResponse> getmainCategoryimages(int id) async {
    try {
      SharedPreferences _prefs = await SharedPreferences.getInstance();
      var currentlanguagecode = _prefs.getString('language_code');

      _dio.options.headers['lang'] = currentlanguagecode;

      Response response = await _dio.get(
        '${pathUrl}getmaincategoryimages?id=$id',
        options: Options(
            headers: {"Content-Type": "application/x-www-form-urlencoded"},
            followRedirects: false,
            validateStatus: (status) {
              return status! < 500;
            }),
      );

      return CategoryImagesResponse.fromJson(response.data);
    } catch (error, stacktrace) {
      log("Exception occured: $error stackTrace: $stacktrace");
      return CategoryImagesResponse.withError(handleError(error));
    }
  }

  static Future<AlbumsResponse> getAlbums(categoryId) async {
    try {
      print("sadasdadadasdddddddddddddddddd");

      SharedPreferences _prefs = await SharedPreferences.getInstance();
      var token = _prefs.getString('token');
      if (token != null && token != "") {
        _dio.options.headers['Authorization'] = 'Bearer $token';
      }
      print("sadasdadadasdddddddddddddddddd");
      var currentlanguagecode = _prefs.getString('language_code');

      _dio.options.headers['lang'] = currentlanguagecode;

      String url;
      url = pathUrl + 'getalbums' + "?video_id=$categoryId";
      log("The URL $url");
      log("The Headers ${_dio.options.headers}");
      Response response = await _dio.get(url,
          options: Options(
              headers: {"Content-Type": "application/json"},
              // headers: {"Content-Type": "application/x-www-form-urlencoded"},
              followRedirects: false,
              validateStatus: (status) {
                return status! < 600;
              }));
      if (response.data["code"] == 1) {
        return AlbumsResponse.fromJson(response.data);
      } else {
        return AlbumsResponse.withError(response.data["msg"]);
      }
    } catch (error, _) {
      print("ERRRRRRRRRRRRRRRRRRORRRRRRRRRRRRR");
      print(error);
      return AlbumsResponse.withError(handleError(error));
    }
  }

  static Future<CategoryImagesResponse> gethoildayhomeimages(int id) async {
    try {
      SharedPreferences _prefs = await SharedPreferences.getInstance();
      var currentlanguagecode = _prefs.getString('language_code');

      _dio.options.headers['lang'] = currentlanguagecode;

      Response response = await _dio.get(
        '${pathUrl}geholidayhomeimages?id=$id',
        options: Options(
            headers: {"Content-Type": "application/x-www-form-urlencoded"},
            followRedirects: false,
            validateStatus: (status) {
              return status! < 500;
            }),
      );

      return CategoryImagesResponse.fromJson(response.data);
    } catch (error, stacktrace) {
      log("Exception occured: $error stackTrace: $stacktrace");
      return CategoryImagesResponse.withError(handleError(error));
    }
  }

  static Future<CategoryImagesResponse> getcarrentimages(int id) async {
    try {
      SharedPreferences _prefs = await SharedPreferences.getInstance();
      var currentlanguagecode = _prefs.getString('language_code');

      _dio.options.headers['lang'] = currentlanguagecode;

      Response response = await _dio.get(
        '${pathUrl}getcarrentimages?id=$id',
        options: Options(
            headers: {"Content-Type": "application/x-www-form-urlencoded"},
            followRedirects: false,
            validateStatus: (status) {
              return status! < 500;
            }),
      );

      return CategoryImagesResponse.fromJson(response.data);
    } catch (error, stacktrace) {
      log("Exception occured: $error stackTrace: $stacktrace");
      return CategoryImagesResponse.withError(handleError(error));
    }
  }

  static Future<CategoryImagesResponse?> getluxuryimages(int id) async {
    try {
      SharedPreferences _prefs = await SharedPreferences.getInstance();
      var currentlanguagecode = _prefs.getString('language_code');

      _dio.options.headers['lang'] = currentlanguagecode;

      Response response = await _dio.get(
        '${pathUrl}getluxuryimages?id=$id',
        options: Options(
            headers: {"Content-Type": "application/x-www-form-urlencoded"},
            followRedirects: false,
            validateStatus: (status) {
              return status! < 500;
            }),
      );

      return CategoryImagesResponse.fromJson(response.data);
    } catch (error, stacktrace) {
      log("Exception occured: $error stackTrace: $stacktrace");
      return CategoryImagesResponse.withError(handleError(error));
    }
  }

  static Future<CategoryReelsResponse?> getmainCategoryreels(int id) async {
    try {
      SharedPreferences _prefs = await SharedPreferences.getInstance();
      var currentlanguagecode = _prefs.getString('language_code');

      _dio.options.headers['lang'] = currentlanguagecode;

      Response response = await _dio.get(
        '${pathUrl}getmaincategory?id=$id',
        options: Options(
            headers: {"Content-Type": "application/x-www-form-urlencoded"},
            followRedirects: false,
            validateStatus: (status) {
              return status! < 500;
            }),
      );

      return CategoryReelsResponse.fromJson(response.data);
    } catch (error, stacktrace) {
      log("Exception occured: $error stackTrace: $stacktrace");
      return CategoryReelsResponse.withError(handleError(error));
    }
  }

  // static Future<CategoryReelsResponse> getholidayhomereels(int id) async {
  //   try {
  //     SharedPreferences _prefs = await SharedPreferences.getInstance();
  //     var currentlanguagecode = _prefs.getString('language_code');
  //
  //     _dio.options.headers['lang'] = currentlanguagecode;
  //
  //     Response response = await _dio.get(
  //       '${pathUrl}getholidayhomereels?id=$id',
  //       options: Options(
  //           headers: {"Content-Type": "application/x-www-form-urlencoded"},
  //           followRedirects: false,
  //           validateStatus: (status) {
  //             return status! < 500;
  //           }),
  //     );
  //
  //
  //     return CategoryReelsResponse.fromJson(response.data);
  //   } catch (error, stacktrace) {
  //     log("Exception occured: $error stackTrace: $stacktrace");
  //     return CategoryReelsResponse.withError(handleError(error));
  //   }
  // }

  static Future<CategoryReelsResponse?> getcarrentreels(int id) async {
    try {
      SharedPreferences _prefs = await SharedPreferences.getInstance();
      var currentlanguagecode = _prefs.getString('language_code');

      _dio.options.headers['lang'] = currentlanguagecode;

      Response response = await _dio.get(
        '${pathUrl}getcarrentreels?id=$id',
        options: Options(
            headers: {"Content-Type": "application/x-www-form-urlencoded"},
            followRedirects: false,
            validateStatus: (status) {
              return status! < 500;
            }),
      );

      return CategoryReelsResponse.fromJson(response.data);
    } catch (error, stacktrace) {
      log("Exception occured: $error stackTrace: $stacktrace");
      return CategoryReelsResponse.withError(handleError(error));
    }
  }

  static Future<CategoryReelsResponse?> getluxuryreels(int id) async {
    try {
      SharedPreferences _prefs = await SharedPreferences.getInstance();
      var currentlanguagecode = _prefs.getString('language_code');

      _dio.options.headers['lang'] = currentlanguagecode;

      Response response = await _dio.get(
        '${pathUrl}getmaincategory?id=$id',
        options: Options(
            headers: {"Content-Type": "application/x-www-form-urlencoded"},
            followRedirects: false,
            validateStatus: (status) {
              return status! < 500;
            }),
      );

      return CategoryReelsResponse.fromJson(response.data);
    } catch (error, stacktrace) {
      log("Exception occured: $error stackTrace: $stacktrace");
      return CategoryReelsResponse.withError(handleError(error));
    }
  }

  static Future<CategoryResponse?> filtermaincategory(
      int? location,
      List? starts,
      double? startprice,
      double? endprice,
      List<int?>? feature,
      String? category,
      int? type) async {
    try {
      SharedPreferences _prefs = await SharedPreferences.getInstance();
      var currentlanguagecode = _prefs.getString('language_code');
      var currency = _prefs.getString('currency');
      _dio.options.headers['lang'] = currentlanguagecode;
      // String url =
      //     '${pathUrl}filtermaincategory?category=$category&location=$location&&startprice=$startprice&endprice=$endprice&type=$type&currency=$currency';
      String url = '${pathUrl}getmaincategory?category=$category';
      if (location != null && location != 0) {
        url += "&location=$location";
      }

      if (startprice != null && startprice != 0) {
        url += "&startprice=$startprice";
      }

      if (endprice != null && endprice != 0) {
        url += "&endprice=$endprice";
      }

      if (type != null && type != 0) {
        url += "&type=$type";
      }

      if (currency != null && currency != "") {
        url += "&currency=$currency";
      }

      if (feature != null &&
          feature.isNotEmpty &&
          feature.first != 0 &&
          feature.first != null) {
        for (var i = 0; i < feature.length; i++) {
          url += "&feature[]=${feature[i]}";
        }
      }
      if (starts != null &&
          starts.isNotEmpty &&
          starts.first != 0 &&
          starts.first != null) {
        for (var i = 0; i < starts.length; i++) {
          url += "&stars[]=${starts[i]}.0";
        }
      }
      log("The URL $url");
      log("The Headers ${_dio.options.headers}");
      Response response = await _dio.get(
        url,
        options: Options(
            headers: {"Content-Type": "application/x-www-form-urlencoded"},
            followRedirects: false,
            validateStatus: (status) {
              return status! < 500;
            }),
      );

      return CategoryResponse.fromJson(response.data);
    } catch (error, stacktrace) {
      log("Exception occured: $error stackTrace: $stacktrace");
      return CategoryResponse.withError(handleError(error));
    }
  }

  static Future<HolidayHomeResponse?> filterholidayhome(
    int? location,
    List<double?>? numberofbedroom,
    double? startprice,
    double? endprice,
    List<int?>? feature,
    int? type,
    num? startsize,
    num? endsize,
  ) async {
    try {
      SharedPreferences _prefs = await SharedPreferences.getInstance();
      var currentlanguagecode = _prefs.getString('language_code');
      var currency = _prefs.getString('currency');
      _dio.options.headers['lang'] = currentlanguagecode;

      String url =
          '${pathUrl}getmaincategory?category=${AppConstants.propertiesId}&currency=$currency';

      final startDate = startDateNotifier.value;
      final endDate = endDateNotifier.value;

      if (location != null && location != 0) {
        url += "&location=$location";
      }

      if (startprice != null && startprice != 0) {
        url += "&startprice=$startprice";
      }

      if (endprice != null && endprice != 0) {
        url += "&endprice=$endprice";
      }

      if (type != null && type != 0) {
        url += "&type=$type";
      }

      // if (agent != null && agent != 0) {
      //   url += "&agent=$agent";
      // }

      if (feature != null &&
          feature.isNotEmpty &&
          feature.first != 0 &&
          feature.first != null) {
        for (var i = 0; i < feature.length; i++) {
          url += "&feature[]=${feature[i]}";
        }
      }

      // if (size != null &&
      //     size.isNotEmpty &&
      //     size.first != 0 &&
      //     size.first != null) {
      //   for (var i = 0; i < size.length; i++) {
      //     url += "&hsize[]=${size[i]}";
      //   }
      // }

      if (startsize != null && startsize != 0) {
        url += "&start_size=$startsize";
      }

      if (endsize != null && endsize != 0) {
        url += "&end_size=$endsize";
      }

      if (numberofbedroom != null &&
          numberofbedroom.isNotEmpty &&
          numberofbedroom.first != 0 &&
          numberofbedroom.first != null) {
        for (var i = 0; i < numberofbedroom.length; i++) {
          url += "&numberofbedroom[]=${numberofbedroom[i]?.toInt()}";
        }
      }

      log("The_URL $url");

      // if (startDate != null && endDate != null) {
      //   url +=
      //       "&start_date=${startDate.toIso8601String()}&end_date=${endDate.toIso8601String()}";
      // }

      Response response = await _dio.get(
        url,
        options: Options(
            headers: {"Content-Type": "application/x-www-form-urlencoded"},
            followRedirects: false,
            validateStatus: (status) {
              return status! < 600;
            }),
      );

      final data = HolidayHomeResponse.fromJson(response.data);

      if (startDate != null && endDate != null) {
        closedVideoDates =
            await RmsService.getClosedCalendarDaysByMonthForMultipleCategories(
                categoryIds: data.category
                    .where(
                      (element) => element.startprice != 0,
                    )
                    .map((e) => e.rmsCategoryId ?? 0)
                    .toList(),
                startDate: startDate.formatDateTimeToApi,
                endDate: endDate.formatDateTimeToApi);
      }

      return data;
    } catch (error, stacktrace) {
      log("Exception occured: $error stackTrace: $stacktrace");
      return HolidayHomeResponse.withError(handleError(error));
    }
  }

  // static Future<HolidayHomeResponse?> getholidayhome(
  //     int page, int size, String key,
  //     [int? location,
  //     List? starts,
  //     double? startprice,
  //     double? endprice,
  //     List<int?>? feature,
  //     String? category,
  //     int? type]) async {
  //   try {
  //     SharedPreferences _prefs = await SharedPreferences.getInstance();
  //     var currentlanguagecode = _prefs.getString('language_code');
  //     var currency = _prefs.getString('currency');
  //     _dio.options.headers['lang'] = currentlanguagecode;
  //     String url =
  //         '${pathUrl}getmaincategory?category=${AppConstants.holidayHomesId}&page=$page&size=$size&key=$key&currency=$currency';
  //     if (location != null && location != 0) {
  //       url += "&location=$location";
  //     }
  //
  //     if (startprice != null && startprice != 0) {
  //       url += "&startprice=$startprice";
  //     }
  //
  //     if (endprice != null && endprice != 0) {
  //       url += "&endprice=$endprice";
  //     }
  //
  //     if (type != null && type != 0) {
  //       url += "&type=$type";
  //     }
  //
  //     if (currency != null && currency != "") {
  //       url += "&currency=$currency";
  //     }
  //
  //     if (feature != null &&
  //         feature.isNotEmpty &&
  //         feature.first != 0 &&
  //         feature.first != null) {
  //       for (var i = 0; i < feature.length; i++) {
  //         url += "&feature[]=${feature[i]}";
  //       }
  //     }
  //     if (starts != null &&
  //         starts.isNotEmpty &&
  //         starts.first != 0 &&
  //         starts.first != null) {
  //       url += "&stars=$starts";
  //     }
  //
  //     log("The URL $url");
  //     log("The Headers ${_dio.options.headers}");
  //     Response response = await _dio.get(
  //       url,
  //       options: Options(
  //           headers: {"Content-Type": "application/x-www-form-urlencoded"},
  //           followRedirects: false,
  //           validateStatus: (status) {
  //             return status! < 500;
  //           }),
  //     );
  //
  //
  //     return HolidayHomeResponse.fromJson(response.data);
  //   } catch (error, stacktrace) {
  //     log("Exception occured: $error stackTrace: $stacktrace");
  //     return HolidayHomeResponse.withError(handleError(error));
  //   }
  // }

  static Future<CategoryResponse?> getfeaturedvideoholidayhome(
      int page, int size, String key, String category) async {
    try {
      SharedPreferences _prefs = await SharedPreferences.getInstance();
      var currentlanguagecode = _prefs.getString('language_code');
      var currency = _prefs.getString('currency');
      _dio.options.headers['lang'] = currentlanguagecode;
      String url =
          '${pathUrl}getfeaturedvideo?category=$category&page=$page&size=$size&key=$key&currency=$currency';

      log("The URL $url");
      log("The Headers ${_dio.options.headers}");
      Response response = await _dio.get(
        url,
        options: Options(
            headers: {"Content-Type": "application/x-www-form-urlencoded"},
            followRedirects: false,
            validateStatus: (status) {
              return status! < 600;
            }),
      );

      return CategoryResponse.fromJson(response.data);
    } catch (error, stacktrace) {
      log("Exception occured: $error stackTrace: $stacktrace");
      return CategoryResponse.withError(handleError(error));
    }
  }

  static Future<CarRentResponse> filtercarrent(int? brand, var year,
      double price, int? feature, int? type, int? agent) async {
    try {
      SharedPreferences _prefs = await SharedPreferences.getInstance();
      var currentlanguagecode = _prefs.getString('language_code');
      var currency = _prefs.getString('currency');
      _dio.options.headers['lang'] = currentlanguagecode;
      String url =
          '${pathUrl}getmaincategory?category=${AppConstants.carRentalsId}&currency=$currency';

      //?brand=$brand&year=$year&price=$price&feature=$feature&type=$type&agent=$agent&currency=$currency

      if (brand != null && brand != 0) {
        url += "brand=$brand";
      }

      if (year != null && year != 0) {
        url += "&year=$year";
      }

      if (price != 0) {
        url += "&price=$price";
      }

      if (feature != null && feature != 0) {
        url += "&feature=$feature";
      }

      if (type != null && type != 0) {
        url += "&type=$type";
      }

      if (agent != null && agent != 0) {
        url += "&agent=$agent";
      }

      if (currency != null && currency != 0) {
        url += "&currency=$currency";
      }

      log("The URL $url");
      log("The Headers ${_dio.options.headers}");
      Response response = await _dio.get(
        url,
        options: Options(
            headers: {"Content-Type": "application/x-www-form-urlencoded"},
            followRedirects: false,
            validateStatus: (status) {
              return status! < 600;
            }),
      );

      return CarRentResponse.fromJson(response.data);
    } catch (error, stacktrace) {
      log("Exception occured: $error stackTrace: $stacktrace");
      return CarRentResponse.withError(handleError(error));
    }
  }

  static Future<GeneralResponse> addmaincategoryfavourite(int id) async {
    try {
      Map<String, dynamic> data = {
        "video_id":
            // 435,
            id,
      };

      log('Link ${pathUrl}fav \n Data: $data');

      SharedPreferences _prefs = await SharedPreferences.getInstance();
      var token = _prefs.getString('token');

      _dio.options.headers['Authorization'] = 'Bearer ${token!}';
      Response response = await _dio.post(
        '${pathUrl}video/favourite?type=2',
        data: data,
        options: Options(
            headers: {"Content-Type": "application/x-www-form-urlencoded"},
            followRedirects: false,
            validateStatus: (status) {
              return status! < 600;
            }),
      );

      return GeneralResponse.fromJson(response.data);
    } catch (error, stacktrace) {
      log("Exception occured: $error stackTrace: $stacktrace");
      return GeneralResponse.withError(handleError(error));
    }
  }

  static Future<GeneralResponse> removemaincategoryfromfavourite(int id) async {
    try {
      Map<String, dynamic> data = {"video_id": id};
      SharedPreferences _prefs = await SharedPreferences.getInstance();
      var token = _prefs.getString('token');

      _dio.options.headers['Authorization'] = 'Bearer ${token!}';
      Response response = await _dio.post(
        '${pathUrl}video/favourite?type=2',
        data: data,
        options: Options(
            headers: {"Content-Type": "application/x-www-form-urlencoded"},
            followRedirects: false,
            validateStatus: (status) {
              return status! < 600;
            }),
      );

      return GeneralResponse.fromJson(response.data);
    } catch (error, stacktrace) {
      log("Exception occured: $error stackTrace: $stacktrace");
      return GeneralResponse.withError(handleError(error));
    }
  }

  static Future<GeneralResponse> addholidayhomefavourite(int id) async {
    try {
      Map<String, dynamic> data = {
        "video_id": id,
      };
      SharedPreferences _prefs = await SharedPreferences.getInstance();
      var token = _prefs.getString('token');

      _dio.options.headers['Authorization'] = 'Bearer ${token!}';
      Response response = await _dio.post(
        '${pathUrl}video/favourite?type=2',
        data: data,
        options: Options(
            headers: {"Content-Type": "application/x-www-form-urlencoded"},
            followRedirects: false,
            validateStatus: (status) {
              return status! < 600;
            }),
      );

      return GeneralResponse.fromJson(response.data);
    } catch (error, stacktrace) {
      log("Exception occured: $error stackTrace: $stacktrace");
      return GeneralResponse.withError(handleError(error));
    }
  }

  static Future<GeneralResponse> removehoildayhomefromfavourite(int id) async {
    try {
      Map<String, dynamic> data = {
        "video_id": id,
      };
      SharedPreferences _prefs = await SharedPreferences.getInstance();
      var token = _prefs.getString('token');

      _dio.options.headers['Authorization'] = 'Bearer ${token!}';
      Response response = await _dio.post(
        '${pathUrl}video/favourite?type=2',
        data: data,
        options: Options(
            headers: {"Content-Type": "application/x-www-form-urlencoded"},
            followRedirects: false,
            validateStatus: (status) {
              return status! < 600;
            }),
      );

      return GeneralResponse.fromJson(response.data);
    } catch (error, stacktrace) {
      log("Exception occured: $error stackTrace: $stacktrace");
      return GeneralResponse.withError(handleError(error));
    }
  }

  static Future<GeneralResponse> addcarrentfavourite(int id) async {
    try {
      Map<String, dynamic> data = {
        "video_id": id,
      };
      SharedPreferences _prefs = await SharedPreferences.getInstance();
      var token = _prefs.getString('token');

      _dio.options.headers['Authorization'] = 'Bearer ${token!}';
      Response response = await _dio.post(
        '${pathUrl}video/favourite?type=2',
        data: data,
        options: Options(
            headers: {"Content-Type": "application/x-www-form-urlencoded"},
            followRedirects: false,
            validateStatus: (status) {
              return status! < 600;
            }),
      );

      return GeneralResponse.fromJson(response.data);
    } catch (error, stacktrace) {
      log("Exception occured: $error stackTrace: $stacktrace");
      return GeneralResponse.withError(handleError(error));
    }
  }

  static Future<GeneralResponse> removecarrentfromfavourite(int id) async {
    try {
      Map<String, dynamic> data = {
        "video_id": id,
      };
      SharedPreferences _prefs = await SharedPreferences.getInstance();
      var token = _prefs.getString('token');

      _dio.options.headers['Authorization'] = 'Bearer ${token!}';
      Response response = await _dio.post(
        '${pathUrl}video/favourite?type=2',
        data: data,
        options: Options(
            headers: {"Content-Type": "application/x-www-form-urlencoded"},
            followRedirects: false,
            validateStatus: (status) {
              return status! < 600;
            }),
      );

      return GeneralResponse.fromJson(response.data);
    } catch (error, stacktrace) {
      log("Exception occured: $error stackTrace: $stacktrace");
      return GeneralResponse.withError(handleError(error));
    }
  }

  static Future<GeneralResponse> addluxuryfavourite(int id) async {
    try {
      Map<String, dynamic> data = {
        "video_id": id,
      };
      SharedPreferences _prefs = await SharedPreferences.getInstance();
      var token = _prefs.getString('token');

      _dio.options.headers['Authorization'] = 'Bearer ${token!}';
      Response response = await _dio.post(
        '${pathUrl}video/favourite?type=2',
        data: data,
        options: Options(
            headers: {"Content-Type": "application/x-www-form-urlencoded"},
            followRedirects: false,
            validateStatus: (status) {
              return status! < 600;
            }),
      );

      return GeneralResponse.fromJson(response.data);
    } catch (error, stacktrace) {
      log("Exception occured: $error stackTrace: $stacktrace");
      return GeneralResponse.withError(handleError(error));
    }
  }

  static Future<GeneralResponse> removeluxuryfromfavourite(int id) async {
    try {
      Map<String, dynamic> data = {
        "video_id": id,
      };
      SharedPreferences _prefs = await SharedPreferences.getInstance();
      var token = _prefs.getString('token');

      _dio.options.headers['Authorization'] = 'Bearer ${token!}';
      Response response = await _dio.post(
        '${pathUrl}video/favourite?type=2',
        data: data,
        options: Options(
            headers: {"Content-Type": "application/x-www-form-urlencoded"},
            followRedirects: false,
            validateStatus: (status) {
              return status! < 600;
            }),
      );

      return GeneralResponse.fromJson(response.data);
    } catch (error, stacktrace) {
      log("Exception occured: $error stackTrace: $stacktrace");
      return GeneralResponse.withError(handleError(error));
    }
  }

  static Future<GeneralResponse> sendluxuryinterests(
      int id, String note) async {
    try {
      log('[API] ${pathUrl}sendluxuryinterests [id:$id, note:$note]');

      Map<String, dynamic> data = {"luxury_id": id, "note": note};
      SharedPreferences _prefs = await SharedPreferences.getInstance();
      var token = _prefs.getString('token');

      _dio.options.headers['Authorization'] = 'Bearer ${token!}';
      Response response = await _dio.post(
        '${pathUrl}sendluxuryinterests',
        data: data,
        options: Options(
            headers: {"Content-Type": "application/x-www-form-urlencoded"},
            followRedirects: false,
            validateStatus: (status) {
              return status! < 600;
            }),
      );

      return GeneralResponse.fromJson(response.data);
    } catch (error, stacktrace) {
      log("Exception occured: $error stackTrace: $stacktrace");
      return GeneralResponse.withError(handleError(error));
    }
  }

  static Future<SearchMapResponse?> getmainCategorySearch(
    double lat,
    double lng,
    String category,
    String? key,
    List<double>? numberofbedroom,
    int? location,
  ) async {
    try {
      SharedPreferences _prefs = await SharedPreferences.getInstance();
      var currentlanguagecode = _prefs.getString('language_code');

      _dio.options.headers['lang'] = currentlanguagecode;
      String url;
      if (key != null) {
        url =
            '${pathUrl}searchmap?category_id=$category&latitude=$lat&longitude=$lng&key=$key';
      } else {
        url =
            '${pathUrl}searchmap?category_id=$category&latitude=$lat&longitude=$lng';
      }

      if (numberofbedroom != null && numberofbedroom.isNotEmpty) {
        for (var i = 0; i < numberofbedroom.length; i++) {
          url += "&numberofbedroom[]=${numberofbedroom[i]?.toInt()}";
        }
      }

      if (location != null && location != 0) {
        url += "&location=$location";
      }

      log("The URllL $url");
      log("The Headers ${_dio.options.headers}");
      Response response = await _dio.get(
        url,
        options: Options(
            headers: {"Content-Type": "application/x-www-form-urlencoded"},
            followRedirects: false,
            validateStatus: (status) {
              return status! < 500;
            }),
      );
      print("qqqqqqqafasfasfafsasasgg ${response.data}");

      return SearchMapResponse.fromJson(response.data);
    } catch (error, stacktrace) {
      print("SearchMapResponse occured: $error stackTrace: $stacktrace");
      return SearchMapResponse.withError(handleError(error));
    }
  }

  static Future<HomeResponse> gethome() async {
    try {
      SharedPreferences _prefs = await SharedPreferences.getInstance();
      var currentlanguagecode = _prefs.getString('language_code');
      _dio.options.headers['lang'] = currentlanguagecode;

      var currency = _prefs.getString('currency');

      Response response = await _dio.get(
        // '${pathUrl}home?light=1&currency=$currency',
        '${pathUrl}home?currency=$currency',
        options: Options(
            headers: {"Content-Type": "application/x-www-form-urlencoded"},
            followRedirects: false,
            validateStatus: (status) {
              return status! < 600;
            }),
      );
      log("qqqqqqqHeader ${_dio.options.headers}");
      log("HomeDataa ${response.data['data']['new_projects']}");
      return HomeResponse.fromJson(response.data);
    } catch (error, stacktrace) {
      log("Exception occured: $error stackTrace: $stacktrace");
      return HomeResponse.withError(handleError(error));
    }
  }

  static Future<LocationsResponse> getlocations() async {
    try {
      SharedPreferences _prefs = await SharedPreferences.getInstance();
      var currentlanguagecode = _prefs.getString('language_code');
      _dio.options.headers['lang'] = currentlanguagecode;

      String url = '${pathUrl}getlocations';

      log("The URL $url");
      log("The Headers ${_dio.options.headers}");
      Response response = await _dio.get(
        url,
        options: Options(
            headers: {"Content-Type": "application/x-www-form-urlencoded"},
            followRedirects: false,
            validateStatus: (status) {
              return status! < 500;
            }),
      );

      return LocationsResponse.fromJson(response.data);
    } catch (error, stacktrace) {
      log("Exception occured: $error stackTrace: $stacktrace");
      return LocationsResponse.withError(handleError(error));
    }
  }

  static Future<FeaturesResponse> getfeatures(String category) async {
    try {
      SharedPreferences _prefs = await SharedPreferences.getInstance();
      var currentlanguagecode = _prefs.getString('language_code');
      _dio.options.headers['lang'] = currentlanguagecode;

      String url = '${pathUrl}getfeatures?category=$category&page=1&size=25';
      // String url = '${pathUrl}getfeaturesAdmin?category=$category';
      print("asdsadwdwewew $url");
      log("The URL $url");
      log("The Headers ${_dio.options.headers}");
      Response response = await _dio.get(
        url,
        options: Options(
            headers: {"Content-Type": "application/x-www-form-urlencoded"},
            followRedirects: false,
            validateStatus: (status) {
              return status! < 500;
            }),
      );

      print('asdkaksdlsnadk $response');
      return FeaturesResponse.fromJson(response.data);
    } catch (error, stacktrace) {
      log("Exception occured: $error stackTrace: $stacktrace");
      return FeaturesResponse.withError(handleError(error));
    }
  }

  static Future<MaxPriceResponse> getminpricemaincategory(
      String category) async {
    try {
      String url = '${pathUrl}getminprice?category_id=$category';

      log("The URL $url");
      log("The Headers ${_dio.options.headers}");
      Response response = await _dio.get(
        url,
        options: Options(
            headers: {"Content-Type": "application/x-www-form-urlencoded"},
            followRedirects: false,
            validateStatus: (status) {
              return status! < 500;
            }),
      );

      return MaxPriceResponse.fromJson(response.data);
    } catch (error, stacktrace) {
      log("Exception occured: $error stackTrace: $stacktrace");
      return MaxPriceResponse.withError(handleError(error));
    }
  }

  static Future<MaxPriceResponse> getmaxpricemaincategory(
      String category) async {
    try {
      String url = '${pathUrl}getmaxprice?category=$category';

      log("The URL $url");
      log("The Headers ${_dio.options.headers}");
      Response response = await _dio.get(
        url,
        options: Options(
            headers: {"Content-Type": "application/x-www-form-urlencoded"},
            followRedirects: false,
            validateStatus: (status) {
              return status! < 500;
            }),
      );

      return MaxPriceResponse.fromJson(response.data);
    } catch (error, stacktrace) {
      log("Exception occured: $error stackTrace: $stacktrace");
      return MaxPriceResponse.withError(handleError(error));
    }
  }

  static Future<TypesResponse> gettypes(String category) async {
    try {
      SharedPreferences _prefs = await SharedPreferences.getInstance();
      var currentlanguagecode = _prefs.getString('language_code');

      _dio.options.headers['lang'] = currentlanguagecode;

      String url = '${pathUrl}gettypes?category=$category&page=1&size=25';
      // String url = '${pathUrl}gettypesusers?category=$category';

      log("The URL $url");
      log("The Headers ${_dio.options.headers}");
      Response response = await _dio.get(
        url,
        options: Options(
            headers: {"Content-Type": "application/x-www-form-urlencoded"},
            followRedirects: false,
            validateStatus: (status) {
              return status! < 500;
            }),
      );

      log('Datttaa ${response.data}');

      return TypesResponse.fromJson(response.data);
    } catch (error, stacktrace) {
      log("Exception occured: $error stackTrace: $stacktrace");
      return TypesResponse.withError(handleError(error));
    }
  }

  static Future<AgentsResponse> getagents() async {
    try {
      SharedPreferences _prefs = await SharedPreferences.getInstance();
      var currentlanguagecode = _prefs.getString('language_code');

      _dio.options.headers['lang'] = currentlanguagecode;
      String url = '${pathUrl}getagents';

      log("The URL $url");
      log("The Headers ${_dio.options.headers}");
      Response response = await _dio.get(
        url,
        options: Options(
            headers: {"Content-Type": "application/x-www-form-urlencoded"},
            followRedirects: false,
            validateStatus: (status) {
              return status! < 500;
            }),
      );

      return AgentsResponse.fromJson(response.data);
    } catch (error, stacktrace) {
      log("Exception occured: $error stackTrace: $stacktrace");
      return AgentsResponse.withError(handleError(error));
    }
  }

  static Future<AgentsResponse> getagentsbycategory(String category) async {
    try {
      SharedPreferences _prefs = await SharedPreferences.getInstance();
      var currentlanguagecode = _prefs.getString('language_code');

      _dio.options.headers['lang'] = currentlanguagecode;
      String url = '${pathUrl}getagents?category=$category';

      log("The URL $url");
      log("The Headers ${_dio.options.headers}");
      Response response = await _dio.get(
        url,
        options: Options(
            headers: {"Content-Type": "application/x-www-form-urlencoded"},
            followRedirects: false,
            validateStatus: (status) {
              return status! < 500;
            }),
      );
      print("rrrrrrrr");

      return AgentsResponse.fromJson(response.data);
    } catch (error, stacktrace) {
      log("Exception occured: $error stackTrace: $stacktrace");
      return AgentsResponse.withError(handleError(error));
    }
  }

  static Future<SizesResponse> getsizes() async {
    try {
      SharedPreferences _prefs = await SharedPreferences.getInstance();
      var currentlanguagecode = _prefs.getString('language_code');

      _dio.options.headers['lang'] = currentlanguagecode;
      String url = '${pathUrl}getholdayhomesize';

      log("The URL $url");
      log("The Headers ${_dio.options.headers}");
      Response response = await _dio.get(
        url,
        options: Options(
            headers: {"Content-Type": "application/x-www-form-urlencoded"},
            followRedirects: false,
            validateStatus: (status) {
              return status! < 500;
            }),
      );

      return SizesResponse.fromJson(response.data);
    } catch (error, stacktrace) {
      log("Exception occured: $error stackTrace: $stacktrace");
      return SizesResponse.withError(handleError(error));
    }
  }

  static Future<SizesAndRoomsResponse> getsizesAndRooms() async {
    try {
      SharedPreferences _prefs = await SharedPreferences.getInstance();
      var currentlanguagecode = _prefs.getString('language_code');

      _dio.options.headers['lang'] = currentlanguagecode;
      String url = '${pathUrl}getholdayhomerooms';

      log("The URL $url");
      log("The Headers ${_dio.options.headers}");
      Response response = await _dio.get(
        url,
        options: Options(
            headers: {"Content-Type": "application/x-www-form-urlencoded"},
            followRedirects: false,
            validateStatus: (status) {
              return status! < 500;
            }),
      );

      log('askdjasidhiofffff ${response.data}');
      return SizesAndRoomsResponse.fromJson(response.data);
    } catch (error, stacktrace) {
      log("Exception occured: $error stackTrace: $stacktrace");
      return SizesAndRoomsResponse.withError(handleError(error));
    }
  }

  static Future<MaxPriceResponse> getminpriceholidayhome() async {
    try {
      String url =
          '${pathUrl}getminprice?category=${AppConstants.holidayHomesId}';

      log("The URL $url");
      log("The Headers ${_dio.options.headers}");
      Response response = await _dio.get(
        url,
        options: Options(
            headers: {"Content-Type": "application/x-www-form-urlencoded"},
            followRedirects: false,
            validateStatus: (status) {
              return status! < 500;
            }),
      );

      return MaxPriceResponse.fromJson(response.data);
    } catch (error, stacktrace) {
      log("Exception occured: $error stackTrace: $stacktrace");
      return MaxPriceResponse.withError(handleError(error));
    }
  }

  static Future<MaxPriceResponse> getmaxpriceholidayhome() async {
    try {
      String url =
          '${pathUrl}getmaxprice?category_id=${AppConstants.holidayHomesId}';

      log("The URL $url");
      log("The Headers ${_dio.options.headers}");
      Response response = await _dio.get(
        url,
        options: Options(
            headers: {"Content-Type": "application/x-www-form-urlencoded"},
            followRedirects: false,
            validateStatus: (status) {
              return status! < 500;
            }),
      );

      print('askdjasdkjasn ${response.data}');
      return MaxPriceResponse.fromJson(response.data);
    } catch (error, stacktrace) {
      log("Exception occured: $error stackTrace: $stacktrace");
      return MaxPriceResponse.withError(handleError(error));
    }
  }

  static Future<BrandResponse> getbrands() async {
    try {
      SharedPreferences _prefs = await SharedPreferences.getInstance();
      var currentlanguagecode = _prefs.getString('language_code');

      _dio.options.headers['lang'] = currentlanguagecode;
      String url = '${pathUrl}getbrands';

      log("The URL $url");
      log("The Headers ${_dio.options.headers}");
      Response response = await _dio.get(
        url,
        options: Options(
            headers: {"Content-Type": "application/x-www-form-urlencoded"},
            followRedirects: false,
            validateStatus: (status) {
              return status! < 500;
            }),
      );

      return BrandResponse.fromJson(response.data);
    } catch (error, stacktrace) {
      log("Exception occured: $error stackTrace: $stacktrace");
      return BrandResponse.withError(handleError(error));
    }
  }

  static Future<YearsResponse> getyears() async {
    try {
      SharedPreferences _prefs = await SharedPreferences.getInstance();
      var currentlanguagecode = _prefs.getString('language_code');

      _dio.options.headers['lang'] = currentlanguagecode;
      String url = '${pathUrl}getyears';

      log("The URL $url");
      log("The Headers ${_dio.options.headers}");
      Response response = await _dio.get(
        url,
        options: Options(
            headers: {"Content-Type": "application/x-www-form-urlencoded"},
            followRedirects: false,
            validateStatus: (status) {
              return status! < 500;
            }),
      );

      return YearsResponse.fromJson(response.data);
    } catch (error, stacktrace) {
      log("Exception occured: $error stackTrace: $stacktrace");
      return YearsResponse.withError(handleError(error));
    }
  }

  static Future<MaxPriceResponse> getmaxpricecarrent() async {
    try {
      String url =
          '${pathUrl}getmaxprice?category=${AppConstants.carRentalsId}';

      log("The URL $url");
      log("The Headers ${_dio.options.headers}");
      Response response = await _dio.get(
        url,
        options: Options(
            headers: {"Content-Type": "application/x-www-form-urlencoded"},
            followRedirects: false,
            validateStatus: (status) {
              return status! < 500;
            }),
      );

      return MaxPriceResponse.fromJson(response.data);
    } catch (error, stacktrace) {
      log("Exception occured: $error stackTrace: $stacktrace");
      return MaxPriceResponse.withError(handleError(error));
    }
  }

  static Future<PloicyResponse> getpolicy() async {
    try {
      SharedPreferences _prefs = await SharedPreferences.getInstance();
      var currentlanguagecode = _prefs.getString('language_code');

      _dio.options.headers['lang'] = currentlanguagecode;
      String url = '${pathUrl}terms-condtions';

      log("The URL $url");
      log("The Headers ${_dio.options.headers}");
      Response response = await _dio.get(
        url,
        options: Options(
            headers: {"Content-Type": "application/x-www-form-urlencoded"},
            followRedirects: false,
            validateStatus: (status) {
              return status! < 500;
            }),
      );

      return PloicyResponse.fromJson(response.data);
    } catch (error, stacktrace) {
      log("Exception occured: $error stackTrace: $stacktrace");
      return PloicyResponse.withError(handleError(error));
    }
  }

  static Future<ConfigurationResponse?> getconfiguration() async {
    try {
      SharedPreferences _prefs = await SharedPreferences.getInstance();
      var currentlanguagecode = _prefs.getString('language_code');

      _dio.options.headers['lang'] = currentlanguagecode;
      Response response = await _dio.get(
        '${pathUrl}configuration',
        options: Options(
            headers: {"Content-Type": "application/x-www-form-urlencoded"},
            followRedirects: false,
            validateStatus: (status) {
              return status! < 500;
            }),
      );

      final dataList = response.data['data'] as List;

      final Map<String, dynamic> convertToMap = {};

      for (var element in dataList) {
        final key = element['key'];
        final value = element['value'];

        convertToMap[key] = value;
      }

      appConfiguration = ConfigurationResponse.fromJson(convertToMap);

      return ConfigurationResponse.fromJson(convertToMap);
    } catch (error, stacktrace) {
      log("Exception occured: $error stackTrace: $stacktrace");
      return ConfigurationResponse.withError(handleError(error));
    }
  }

  static Future<DiscussionsResponse?> getmainCategorydiscussions(
      int page, int size, int id) async {
    try {
      SharedPreferences _prefs = await SharedPreferences.getInstance();
      var currentlanguagecode = _prefs.getString('language_code');

      _dio.options.headers['lang'] = currentlanguagecode;
      String url =
          '${pathUrl}getmaincategorydiscussions?id=$id&page=$page&size=$size';

      log("The URL $url");
      log("The Headers ${_dio.options.headers}");
      Response response = await _dio.get(
        url,
        options: Options(
            headers: {"Content-Type": "application/x-www-form-urlencoded"},
            // "Content-Type": "application/x-www-form-urlencoded",
            followRedirects: false,
            validateStatus: (status) {
              return status! < 500;
            }),
      );

      return DiscussionsResponse.fromJson(response.data);
    } catch (error, stacktrace) {
      log("Exception occured: $error stackTrace: $stacktrace");
      return DiscussionsResponse.withError(handleError(error));
    }
  }

  static Future<GeneralResponse> sendmaincategorydiscussion(
      int id, String comment) async {
    try {
      Map<String, dynamic> data = {"video_id": id, "comment": comment};
      SharedPreferences _prefs = await SharedPreferences.getInstance();
      var token = _prefs.getString('token');

      _dio.options.headers['Authorization'] = 'Bearer ${token!}';
      Response response = await _dio.post(
        '${pathUrl}discussions/add',
        data: data,
        options: Options(
            headers: {"Content-Type": "application/x-www-form-urlencoded"},
            // "Content-Type": "application/x-www-form-urlencoded",
            followRedirects: false,
            validateStatus: (status) {
              return status! < 600;
            }),
      );

      return GeneralResponse.fromJson(response.data);
    } catch (error, stacktrace) {
      log("Exception occured: $error stackTrace: $stacktrace");
      return GeneralResponse.withError(handleError(error));
    }
  }

  static Future<DiscussionsResponse> gethoildayhomediscussions(
      int page, int size, int id) async {
    try {
      SharedPreferences _prefs = await SharedPreferences.getInstance();
      var currentlanguagecode = _prefs.getString('language_code');

      _dio.options.headers['lang'] = currentlanguagecode;
      String url =
          '${pathUrl}getholidayhomediscussions?id=$id&page=$page&size=$size';

      log("The URL $url");
      log("The Headers ${_dio.options.headers}");
      Response response = await _dio.get(
        url,
        options: Options(
            headers: {"Content-Type": "application/x-www-form-urlencoded"},
            // "Content-Type": "application/x-www-form-urlencoded",
            followRedirects: false,
            validateStatus: (status) {
              return status! < 500;
            }),
      );

      return DiscussionsResponse.fromJson(response.data);
    } catch (error, stacktrace) {
      log("Exception occured: $error stackTrace: $stacktrace");
      return DiscussionsResponse.withError(handleError(error));
    }
  }

  static Future<GeneralResponse> sendholidayhomediscussion(
      int id, String comment) async {
    try {
      Map<String, dynamic> data = {"holiday_home_id": id, "comment": comment};
      SharedPreferences _prefs = await SharedPreferences.getInstance();
      var token = _prefs.getString('token');

      _dio.options.headers['Authorization'] = 'Bearer ${token!}';
      Response response = await _dio.post(
        '${pathUrl}sendholidayhomediscussion',
        data: data,
        options: Options(
            headers: {"Content-Type": "application/x-www-form-urlencoded"},
            // "Content-Type": "application/x-www-form-urlencoded",
            followRedirects: false,
            validateStatus: (status) {
              return status! < 600;
            }),
      );
      print("Url=> ${pathUrl}sendholidayhomediscussion \n Data: $data");

      return GeneralResponse.fromJson(response.data);
    } catch (error, stacktrace) {
      log("Exception occured: $error stackTrace: $stacktrace");
      return GeneralResponse.withError(handleError(error));
    }
  }

  static Future<DiscussionsResponse?> getcarrentdiscussions(
      int page, int size, int id) async {
    try {
      SharedPreferences _prefs = await SharedPreferences.getInstance();
      var currentlanguagecode = _prefs.getString('language_code');

      _dio.options.headers['lang'] = currentlanguagecode;
      String url =
          '${pathUrl}getcarrentdiscussions?id=$id&page=$page&size=$size';

      log("The URL $url");
      log("The Headers ${_dio.options.headers}");
      Response response = await _dio.get(
        url,
        options: Options(
            headers: {"Content-Type": "application/x-www-form-urlencoded"},
            // "Content-Type": "application/x-www-form-urlencoded",
            followRedirects: false,
            validateStatus: (status) {
              return status! < 500;
            }),
      );

      return DiscussionsResponse.fromJson(response.data);
    } catch (error, stacktrace) {
      log("Exception occured: $error stackTrace: $stacktrace");
      return DiscussionsResponse.withError(handleError(error));
    }
  }

  static Future<GeneralResponse> sendcarrentdiscussion(
      int id, String comment) async {
    try {
      Map<String, dynamic> data = {"car_rent_id": id, "comment": comment};
      SharedPreferences _prefs = await SharedPreferences.getInstance();
      var token = _prefs.getString('token');

      _dio.options.headers['Authorization'] = 'Bearer ${token!}';
      Response response = await _dio.post(
        '${pathUrl}discussions/add',
        data: data,
        options: Options(
            headers: {"Content-Type": "application/x-www-form-urlencoded"},
            // "Content-Type": "application/x-www-form-urlencoded",
            followRedirects: false,
            validateStatus: (status) {
              return status! < 600;
            }),
      );

      return GeneralResponse.fromJson(response.data);
    } catch (error, stacktrace) {
      log("Exception occured: $error stackTrace: $stacktrace");
      return GeneralResponse.withError(handleError(error));
    }
  }

  static Future<DiscussionsResponse?> getluxurydiscussions(
      int page, int size, int id) async {
    try {
      SharedPreferences _prefs = await SharedPreferences.getInstance();
      var currentlanguagecode = _prefs.getString('language_code');

      _dio.options.headers['lang'] = currentlanguagecode;
      String url =
          '${pathUrl}getluxurydiscussions?id=$id&page=$page&size=$size';

      log("The URL $url");
      log("The Headers ${_dio.options.headers}");
      Response response = await _dio.get(
        url,
        options: Options(
            headers: {"Content-Type": "application/x-www-form-urlencoded"},
            // "Content-Type": "application/x-www-form-urlencoded",
            followRedirects: false,
            validateStatus: (status) {
              return status! < 500;
            }),
      );

      return DiscussionsResponse.fromJson(response.data);
    } catch (error, stacktrace) {
      log("Exception occured: $error stackTrace: $stacktrace");
      return DiscussionsResponse.withError(handleError(error));
    }
  }

  //sendMessage, post, params is (property_name, property_price) with token
  static Future<void> sendMessage(
      {required int propertyId,
      required String propertyName,
      required String propertyPrice,
      required String message,
      String? userName,
      String? userPhone,
      String? userEmail,
      String? userId}) async {
    SharedPreferences _prefs = await SharedPreferences.getInstance();
    var token = _prefs.getString('token');
    Map<String, dynamic> data = {
      "property_id": propertyId,
      "property_name": propertyName,
      "property_price": propertyPrice,
      "message": message
    };

    // if (pricePlanName != null && pricePlanName.isNotEmpty) {
    //   data["price_plan_name"] = pricePlanName;
    // }

    if (userName != null && userName.isNotEmpty) {
      data["user_name"] = userName;
    }

    if (userPhone != null && userPhone.isNotEmpty) {
      data["user_phone"] = userPhone;
    }

    if (userEmail != null && userEmail.isNotEmpty) {
      data["user_email"] = userEmail;
    }

    if (userId != null && userId.isNotEmpty) {
      data["user_id"] = userId;
    }

    log('dataaaa: ${data}');
    _dio.options.headers['Authorization'] = 'Bearer ${token!}';

    Response response = await _dio.post(
      '${pathUrl}sendmessage',
      data: data,
      options: Options(
          headers: {"Content-Type": "application/x-www-form-urlencoded"},
          // "Content-Type": "application/x-www-form-urlencoded",
          followRedirects: false,
          validateStatus: (status) {
            return status! < 600;
          }),
    );
    log('asfsafsaf ${response.data}');
    // return GeneralResponse.fromJson(response.data);
  }

  static Future<GeneralResponse> sendluxurydiscussion(
      int id, String comment) async {
    try {
      Map<String, dynamic> data = {"luxury_id": id, "comment": comment};
      SharedPreferences _prefs = await SharedPreferences.getInstance();
      var token = _prefs.getString('token');

      _dio.options.headers['Authorization'] = 'Bearer ${token!}';
      Response response = await _dio.post(
        '${pathUrl}sendluxurydiscussion',
        data: data,
        options: Options(
            headers: {"Content-Type": "application/x-www-form-urlencoded"},
            // "Content-Type": "application/x-www-form-urlencoded",
            followRedirects: false,
            validateStatus: (status) {
              return status! < 600;
            }),
      );

      return GeneralResponse.fromJson(response.data);
    } catch (error, stacktrace) {
      log("Exception occured: $error stackTrace: $stacktrace");
      return GeneralResponse.withError(handleError(error));
    }
  }

  static Future<NotificationsResponse> getnotifications(
      int page, int size) async {
    try {
      SharedPreferences _prefs = await SharedPreferences.getInstance();
      var token = _prefs.getString('token');

      _dio.options.headers['Authorization'] = 'Bearer ${token!}';

      var currentlanguagecode = _prefs.getString('language_code');

      _dio.options.headers['lang'] = currentlanguagecode;
      String url;

      url = '${pathUrl}getnotifications?size=$size&page=$page';

      log("The URL $url");
      log("The Headers ${_dio.options.headers}");
      Response response = await _dio.get(
        url,
        options: Options(
            headers: {"Content-Type": "application/x-www-form-urlencoded"},
            // "Content-Type": "application/x-www-form-urlencoded",
            followRedirects: false,
            validateStatus: (status) {
              return status! < 500;
            }),
      );

      return NotificationsResponse.fromJson(response.data);
    } catch (error, stacktrace) {
      print("SearchMapResponse occured: $error stackTrace: $stacktrace");
      return NotificationsResponse.withError(handleError(error));
    }
  }

  static Future<OffersResponse> getoffers(int page, int size) async {
    try {
      SharedPreferences _prefs = await SharedPreferences.getInstance();
      var token = _prefs.getString('token');

      var currentlanguagecode = _prefs.getString('language_code');

      _dio.options.headers['lang'] = currentlanguagecode;
      String url;

      url = '${pathUrl}getpromocodes?is_published=1&size=$size&page=$page';

      log("The URL $url");
      log("The Headers ${_dio.options.headers}");
      Response response = await _dio.get(
        url,
        options: Options(
            headers: {"Content-Type": "application/x-www-form-urlencoded"},
            // "Content-Type": "application/x-www-form-urlencoded",
            followRedirects: false,
            validateStatus: (status) {
              return status! < 500;
            }),
      );

      return OffersResponse.fromJson(response.data);
    } catch (error, stacktrace) {
      print("SearchMapResponse occured: $error stackTrace: $stacktrace");
      return OffersResponse.withError(handleError(error));
    }
  }

  static Future<ReviewsResponse?> getreviews(int id, String type) async {
    try {
      SharedPreferences _prefs = await SharedPreferences.getInstance();
      var currentlanguagecode = _prefs.getString('language_code');

      _dio.options.headers['lang'] = currentlanguagecode;
      String url = '${pathUrl}getreviews?id=$id&type=$type';

      log("The URL $url");
      log("The Headers ${_dio.options.headers}");
      Response response = await _dio.get(
        url,
        options: Options(
            headers: {"Content-Type": "application/x-www-form-urlencoded"},
            // "Content-Type": "application/x-www-form-urlencoded",
            followRedirects: false,
            validateStatus: (status) {
              return status! < 500;
            }),
      );

      return ReviewsResponse.fromJson(response.data);
    } catch (error, stacktrace) {
      log("Exception occured: $error stackTrace: $stacktrace");
      return ReviewsResponse.withError(handleError(error));
    }
  }

  static Future<(GeneralResponse, Requests?)> sendrequestholidayhome(
      Map<String, dynamic> data) async {
    try {
      SharedPreferences _prefs = await SharedPreferences.getInstance();
      var token = _prefs.getString('token');

      _dio.options.headers['Authorization'] = 'Bearer ${token!}';

      var currentlanguagecode = _prefs.getString('language_code');

      _dio.options.headers['lang'] = currentlanguagecode;
      Response response = await _dio.post(
        '${pathUrl}send_request_holiday',
        data: data,
        options: Options(
            headers: {"Content-Type": "application/x-www-form-urlencoded"},
            // "Content-Type": "application/x-www-form-urlencoded",
            followRedirects: false,
            validateStatus: (status) {
              return status! < 600;
            }),
      );
      print("UURL ${pathUrl}send_request_holiday");

      log('Dataaa ${data}');
      log('RESSSSS ${response.data}');

      return (
        GeneralResponse.fromJson(response.data),
        Requests.fromJson(response.data['data']),
      );
    } catch (error, stacktrace) {
      log("Exception occured: $error stackTrace: $stacktrace");
      return (GeneralResponse.withError(handleError(error)), null);
    }
  }

  static Future<RequestResponse?> getholidayhomerequestprice(
      int id,
      var startdate,
      var enddate,
      int? code,
      numberOfRooms,
      String numOfAdults,
      String numOfChildren) async {
    try {
      SharedPreferences _prefs = await SharedPreferences.getInstance();
      var currentlanguagecode = _prefs.getString('language_code');

      _dio.options.headers['lang'] = currentlanguagecode;

      var token = _prefs.getString('token');
      var currency = _prefs.getString('currency');

      _dio.options.headers['Authorization'] = 'Bearer ${token!}';
      String url =
          '${pathUrl}get_price_holiday?start_date=$startdate&end_date=$enddate&number_room=$numberOfRooms&video_id=$id&promo_code=$code&currency=$currency&adults=$numOfAdults&children=$numOfChildren';
      log("Get_Price_Holiday_URL: $url");
      log("The Headers ${_dio.options.headers}");
      Response response = await _dio.get(
        url,
        // options: Options(
        //     headers: {"Content-Type": "application/x-www-form-urlencoded"},
        //     // "Content-Type": "application/x-www-form-urlencoded",
        //     followRedirects: false,
        //     validateStatus: (status) {
        //       return status! < 500;
        //     }
        //     ),
      );
      print("qqqqqqqrr ${response.data}");
      return RequestResponse.fromJson(response.data);
    } catch (error, stacktrace) {
      log("Exception occured: $error stackTrace: $stacktrace");
      return RequestResponse.withError(handleError(error));
    }
  }

  static Future<GeneralResponse> sendrequestcarrent(
      Map<String, dynamic> data) async {
    try {
      print("profilasdsff ${data}");

      SharedPreferences _prefs = await SharedPreferences.getInstance();
      var token = _prefs.getString('token');

      var currentlanguagecode = _prefs.getString('language_code');

      _dio.options.headers['lang'] = currentlanguagecode;

      _dio.options.headers['Authorization'] = 'Bearer ${token!}';
      Response response = await _dio.post(
        '${pathUrl}send_request_car_rent',
        data: data,
        options: Options(
            headers: {"Content-Type": "application/x-www-form-urlencoded"},
            // "Content-Type": "application/x-www-form-urlencoded",
            followRedirects: false,
            validateStatus: (status) {
              return status! < 600;
            }),
      );

      return GeneralResponse.fromJson(response.data);
    } catch (error, stacktrace) {
      log("Exception occured: $error stackTrace: $stacktrace");
      return GeneralResponse.withError(handleError(error));
    }
  }

  static Future<RequestResponse> getcarrentprice(
      int id, var startdate, var enddate, int? code, int isPrivate) async {
    try {
      SharedPreferences _prefs = await SharedPreferences.getInstance();
      var currentlanguagecode = _prefs.getString('language_code');

      _dio.options.headers['lang'] = currentlanguagecode;

      var token = _prefs.getString('token');
      var currency = _prefs.getString('currency');

      _dio.options.headers['Authorization'] = 'Bearer ${token!}';
      String url =
          '${pathUrl}get_price_car_rent?start_date=$startdate&end_date=$enddate&video_id=$id&promo_code=$code&currency=$currency&isprivate=$isPrivate';

      log("The URL $url");
      log("The Headers ${_dio.options.headers}");
      Response response = await _dio.get(
        url,
        options: Options(
            headers: {"Content-Type": "application/x-www-form-urlencoded"},
            // "Content-Type": "application/x-www-form-urlencoded",
            followRedirects: false,
            validateStatus: (status) {
              return status! < 600;
            }),
      );

      return RequestResponse.fromJson(response.data);
    } catch (error, stacktrace) {
      log("Exception occured: $error stackTrace: $stacktrace");
      return RequestResponse.withError(handleError(error));
    }
  }

  static Future<List<BookedModel>> getBookedDays(int videoId) async {
    try {
      SharedPreferences _prefs = await SharedPreferences.getInstance();
      var currentlanguagecode = _prefs.getString('language_code');

      _dio.options.headers['lang'] = currentlanguagecode;

      var token = _prefs.getString('token');
      var currency = _prefs.getString('currency');

      _dio.options.headers['Authorization'] = 'Bearer ${token!}';
      String url = '${pathUrl}getbooked?video_id=$videoId';
      log("The URL $url");
      // data
      Response response = await _dio.get(
        url,
        options: Options(
            headers: {"Content-Type": "application/x-www-form-urlencoded"},
            // "Content-Type": "application/x-www-form-urlencoded",
            followRedirects: false,
            validateStatus: (status) {
              return status! < 600;
            }),
      );

      return (response.data['data'] as List)
          .map((e) => BookedModel.fromJson(e))
          .toList();
    } catch (error, stacktrace) {
      log("Exception occured: $error stackTrace: $stacktrace");
      return [];
    }
  }

  static Future<RequestsResponse?> getallrequestsusers(
      int page, int size, String? category) async {
    try {
      SharedPreferences _prefs = await SharedPreferences.getInstance();
      var currentlanguagecode = _prefs.getString('language_code');
      var currency = _prefs.getString('currency');
      _dio.options.headers['lang'] = currentlanguagecode;

      var token = _prefs.getString('token');

      _dio.options.headers['Authorization'] = 'Bearer ${token!}';
      String url =
          '${pathUrl}get_my_requests?page=$page&size=$size&category_id=${category ?? ''}&currency=$currency';
      // '${pathUrl}getallrequestsusers?page=$page&size=$size&category=$category&currency=$currency';

      log("The URL $url");
      log("The Headers ${_dio.options.headers}");
      Response response = await _dio.get(
        url,
        options: Options(
            headers: {"Content-Type": "application/x-www-form-urlencoded"},
            // "Content-Type": "application/x-www-form-urlencoded",
            followRedirects: false,
            validateStatus: (status) {
              return status! < 500;
            }),
      );

      return RequestsResponse.fromJson(response.data);
    } catch (error, stacktrace) {
      log("Exception occured: $error stackTrace: $stacktrace");
      return RequestsResponse.withError(handleError(error));
    }
  }

  static Future<RequestResponse> getholidayhomerequest(int id) async {
    try {
      SharedPreferences _prefs = await SharedPreferences.getInstance();
      var currentlanguagecode = _prefs.getString('language_code');

      _dio.options.headers['lang'] = currentlanguagecode;

      var token = _prefs.getString('token');
      var currency = _prefs.getString('currency');

      _dio.options.headers['Authorization'] = 'Bearer ${token!}';
      String url =
          '${pathUrl}getrequestdetailsholidayhome?id=$id&currency=$currency';

      log("The URL $url");
      log("The Headers ${_dio.options.headers}");
      Response response = await _dio.get(
        url,
        options: Options(
            headers: {"Content-Type": "application/x-www-form-urlencoded"},
            // "Content-Type": "application/x-www-form-urlencoded",
            followRedirects: false,
            validateStatus: (status) {
              return status! < 500;
            }),
      );

      return RequestResponse.fromJson(response.data);
    } catch (error, stacktrace) {
      log("Exception occured: $error stackTrace: $stacktrace");
      return RequestResponse.withError(handleError(error));
    }
  }

  static Future<RequestResponse> getcarrentrequest(int id) async {
    try {
      SharedPreferences _prefs = await SharedPreferences.getInstance();
      var currentlanguagecode = _prefs.getString('language_code');

      _dio.options.headers['lang'] = currentlanguagecode;

      var token = _prefs.getString('token');
      var currency = _prefs.getString('currency');

      _dio.options.headers['Authorization'] = 'Bearer ${token!}';
      String url =
          '${pathUrl}getrequestdetailscarent?id=$id&currency=$currency';

      log("The URL $url");
      log("The Headers ${_dio.options.headers}");
      Response response = await _dio.get(
        url,
        options: Options(
            headers: {"Content-Type": "application/x-www-form-urlencoded"},
            // "Content-Type": "application/x-www-form-urlencoded",
            followRedirects: false,
            validateStatus: (status) {
              return status! < 500;
            }),
      );

      return RequestResponse.fromJson(response.data);
    } catch (error, stacktrace) {
      log("Exception occured: $error stackTrace: $stacktrace");
      return RequestResponse.withError(handleError(error));
    }
  }

  static Future<GeneralResponseWithData> applypromocode(
      String code, int agentId) async {
    try {
      SharedPreferences _prefs = await SharedPreferences.getInstance();
      var token = _prefs.getString('token');

      var currentlanguagecode = _prefs.getString('language_code');

      _dio.options.headers['lang'] = currentlanguagecode;

      Map<String, dynamic> data = {"code": code, 'agent_id': agentId};

      _dio.options.headers['Authorization'] = 'Bearer ${token!}';
      Response response = await _dio.post(
        '${pathUrl}applypromocode',
        data: data,
        options: Options(
            headers: {"Content-Type": "application/x-www-form-urlencoded"},
            // "Content-Type": "application/x-www-form-urlencoded",
            followRedirects: false,
            validateStatus: (status) {
              return status! < 600;
            }),
      );

      return GeneralResponseWithData.fromJson(response.data);
    } catch (error, stacktrace) {
      log("Exception occured: $error stackTrace: $stacktrace");
      return GeneralResponseWithData.withError(handleError(error));
    }
  }

  static Future<ReelsResponse?> getreels(int size) async {
    try {
      SharedPreferences _prefs = await SharedPreferences.getInstance();
      var currentlanguagecode = _prefs.getString('language_code');
      var token = _prefs.getString('token');
      if (token != null && token != "") {
        print(("fsffsfssfsggssg"));
        _dio.options.headers['Authorization'] = 'Bearer $token';
      }
      _dio.options.headers['lang'] = currentlanguagecode;
      String url = '${pathUrl}reels?page=1&size=200';
      log("The URL $url");
      log("The Headers ${_dio.options.headers}");

      log("The URL $url");
      log("The Headers ${_dio.options.headers}");
      Response response = await _dio.get(
        url,
        options: Options(
            headers: {"Content-Type": "application/x-www-form-urlencoded"},
            // "Content-Type": "application/x-www-form-urlencoded",
            followRedirects: false,
            validateStatus: (status) {
              return status! < 600;
            }),
      );
      print("===================== response ========================= ");

      return ReelsResponse.fromJson(response.data);
    } catch (error, stacktrace) {
      log("Exception occured: $error stackTrace: $stacktrace");
      return ReelsResponse.withError(handleError(error));
    }
  }

  static Future<OneReelResponse?> getOneReel(int id) async {
    try {
      SharedPreferences _prefs = await SharedPreferences.getInstance();
      var currentlanguagecode = _prefs.getString('language_code');
      var token = _prefs.getString('token');
      if (token != null && token != "") {
        _dio.options.headers['Authorization'] = 'Bearer $token';
      }
      _dio.options.headers['lang'] = currentlanguagecode;
      String url = '${pathUrl}reels?id=$id';
      log("The URL $url");
      log("The Headers ${_dio.options.headers}");

      log("The URL $url");
      log("The Headers ${_dio.options.headers}");
      Response response = await _dio.get(
        url,
        options: Options(
            headers: {"Content-Type": "application/x-www-form-urlencoded"},
            // "Content-Type": "application/x-www-form-urlencoded",
            followRedirects: false,
            validateStatus: (status) {
              return status! < 600;
            }),
      );
      print("===================== response ========================= ");

      return OneReelResponse.fromJson(response.data);
    } catch (error, stacktrace) {
      log("Exception occured: $error stackTrace: $stacktrace");
      return OneReelResponse.withError(handleError(error));
    }
  }

  static Future<GeneralResponse> addreelfavourite(int id) async {
    try {
      Map<String, dynamic> data = {
        "video_id": id,
      };
      SharedPreferences _prefs = await SharedPreferences.getInstance();
      var token = _prefs.getString('token');

      print(data);
      _dio.options.headers['Authorization'] = 'Bearer ${token!}';
      Response response = await _dio.post(
        '${pathUrl}video/favourite?type=1',
        data: data,
        options: Options(
            headers: {"Content-Type": "application/x-www-form-urlencoded"},
            // "Content-Type": "application/x-www-form-urlencoded",
            followRedirects: false,
            validateStatus: (status) {
              return status! < 600;
            }),
      );

      return GeneralResponse.fromJson(response.data);
    } catch (error, stacktrace) {
      log("Exception occured: $error stackTrace: $stacktrace");
      return GeneralResponse.withError(handleError(error));
    }
  }

  static Future<GeneralResponse> removemainreelfavourite(int id) async {
    try {
      Map<String, dynamic> data = {"video_id": id};
      SharedPreferences _prefs = await SharedPreferences.getInstance();
      var token = _prefs.getString('token');

      _dio.options.headers['Authorization'] = 'Bearer ${token!}';
      Response response = await _dio.post(
        '${pathUrl}video/favourite?type=1',
        data: data,
        options: Options(
            headers: {"Content-Type": "application/x-www-form-urlencoded"},
            // "Content-Type": "application/x-www-form-urlencoded",
            followRedirects: false,
            validateStatus: (status) {
              return status! < 600;
            }),
      );

      return GeneralResponse.fromJson(response.data);
    } catch (error, stacktrace) {
      log("Exception occured: $error stackTrace: $stacktrace");
      return GeneralResponse.withError(handleError(error));
    }
  }

  static Future<ProfileResponse> getProfile() async {
    try {
      SharedPreferences _prefs = await SharedPreferences.getInstance();
      var token = _prefs.getString('token');
      var userid = _prefs.getInt('user_id');
      log("token");
      print(userid);
      _dio.options.headers['Authorization'] = 'Bearer ${token!}';

      Response response = await _dio.get(
        '${pathUrl}profile',
        options: Options(
            headers: {"Content-Type": "application/x-www-form-urlencoded"},
            // "Content-Type": "application/x-www-form-urlencoded",
            followRedirects: false,
            validateStatus: (status) {
              return status! < 600;
            }),
      );

      return ProfileResponse.fromJson(response.data);
    } catch (error, stacktrace) {
      log("Exception occured: $error stackTrace: $stacktrace");
      return ProfileResponse.withError(handleError(error));
    }
  }

  static Future<ProfileResponse> editProfile(
    String name,
    String email,
    String phone,
    String phoneCode,
  ) async {
    try {
      SharedPreferences _prefs = await SharedPreferences.getInstance();
      var token = _prefs.getString('token');
      var userid = _prefs.getInt('user_id');

      _dio.options.headers['Authorization'] = 'Bearer ${token!}';
      Map<String, dynamic> data;

      data = {
        "name": name,
        "email": email,
        "phone": phone,
        "phone_code": phoneCode,
        "id": userid,
        'user_type': 'USER'
      };

      print(data);
      Response response = await _dio.post('${pathUrl}update_profile',
          data: data,
          options: Options(
              headers: {"Content-Type": "application/json"},
              // "Content-Type": "application/x-www-form-urlencoded",
              followRedirects: false,
              validateStatus: (status) {
                return status! < 500;
              }));

      return ProfileResponse.fromJson(response.data);
    } catch (error, stacktrace) {
      log("Exception occured: $error stackTrace: $stacktrace");
      return ProfileResponse.withError(handleError(error));
    }
  }

  static Future<CategoryFavoriteResponse> getfavouritecategory(
      int page, int size) async {
    try {
      SharedPreferences _prefs = await SharedPreferences.getInstance();
      var token = _prefs.getString('token');

      _dio.options.headers['Authorization'] = 'Bearer ${token!}';

      var currentlanguagecode = _prefs.getString('language_code');
      var currency = _prefs.getString('currency');
      _dio.options.headers['lang'] = currentlanguagecode;
      String url =
          '${pathUrl}video/favourite/show?page=$page&size=$size&currency=$currency';
      // '${pathUrl}getfavouriteuser?page=$page&size=$size&currency=$currency';

      log("The Headers ${_dio.options.headers}");

      Response response = await _dio.get(
        url,
        options: Options(
            headers: {"Content-Type": "application/x-www-form-urlencoded"},
            // "Content-Type": "application/x-www-form-urlencoded",
            followRedirects: false,
            validateStatus: (status) {
              return status! < 600;
            }),
      );
      log('UUUseFav $url Resss ${response}');

      // log("The URL $url Resss $response");

      // print("qqqqqqqFFFFF");
      //
      return CategoryFavoriteResponse.fromJson(response.data);
    } catch (error, stacktrace) {
      log("Exception occured: $error stackTrace: $stacktrace");
      return CategoryFavoriteResponse.withError(handleError(error));
    }
  }

  // static Future<CategoryFavoriteResponse> getfavouriteuserreels(
  //     int page, int size) async {
  //   try {
  //     SharedPreferences _prefs = await SharedPreferences.getInstance();
  //     var token = _prefs.getString('token');
  //
  //
  //
  //
  //     _dio.options.headers['Authorization'] = 'Bearer ${token!}';
  //
  //     var currentlanguagecode = _prefs.getString('language_code');
  //
  //     _dio.options.headers['lang'] = currentlanguagecode;
  //     String url = '${pathUrl}video/favourite/show?page=$page&size=$size';
  //     // String url = '${pathUrl}getfavouriteuserreels?page=$page&size=$size';
  //
  //     log("The URL $url");
  //     log("The Headers ${_dio.options.headers}");
  //     Response response = await _dio.get(
  //       url,
  //       options: Options(
  //           headers: {"Content-Type": "application/x-www-form-urlencoded"},
  //           // "Content-Type": "application/x-www-form-urlencoded",
  //           followRedirects: false,
  //           validateStatus: (status) {
  //             return status! < 500;
  //           }),
  //     );
  //
  //
  //     return CategoryFavoriteResponse.fromVibesJson(response.data);
  //   } catch (error, stacktrace) {
  //     log("Exception occured: $error stackTrace: $stacktrace");
  //     return CategoryFavoriteResponse.withError(handleError(error));
  //   }
  // }

  static Future<GeneralResponse> contactus(
      var fullname, String email, String message) async {
    try {
      Map<String, dynamic> data = {
        "fullname": fullname,
        "email": email,
        "message": message,
      };

      Response response = await _dio.post(
        '${pathUrl}contactus',
        data: data,
        options: Options(
            headers: {"Content-Type": "application/x-www-form-urlencoded"},
            // "Content-Type": "application/x-www-form-urlencoded",
            followRedirects: false,
            validateStatus: (status) {
              return status! < 600;
            }),
      );

      return GeneralResponse.fromJson(response.data);
    } catch (error, stacktrace) {
      log("Exception occured: $error stackTrace: $stacktrace");
      return GeneralResponse.withError(handleError(error));
    }
  }

  static Future<GeneralResponse> changepassword(
      var email, String newpassword) async {
    try {
      SharedPreferences _prefs = await SharedPreferences.getInstance();
      var token = _prefs.getString('token');

      _dio.options.headers['Authorization'] = 'Bearer ${token!}';
      Map<String, dynamic> data = {
        // "old_password": currentpassword,
        // "new_password": newpassword,
        "email": email,
        "password": newpassword,
      };

      Response response = await _dio.post(
        '${pathUrl}resetpassword',
        data: data,
        options: Options(
            headers: {"Content-Type": "application/x-www-form-urlencoded"},
            // "Content-Type": "application/x-www-form-urlencoded",
            followRedirects: false,
            validateStatus: (status) {
              return status! < 600;
            }),
      );

      return GeneralResponse.fromJson(response.data);
    } catch (error, stacktrace) {
      log("Exception occured: $error stackTrace: $stacktrace");
      return GeneralResponse.withError(handleError(error));
    }
  }

  static Future<ProfileResponse> deleteAccount() async {
    try {
      Response response = await _dio.post(
        '${pathUrl}account/delete',
        options: Options(
            headers: {"Content-Type": "application/x-www-form-urlencoded"},
            // "Content-Type": "application/x-www-form-urlencoded",
            followRedirects: false,
            validateStatus: (status) {
              return status! < 600;
            }),
      );

      return ProfileResponse.fromJson(response.data);
    } catch (error, stacktrace) {
      log("Exception occured: $error stackTrace: $stacktrace");
      return ProfileResponse.withError(handleError(error));
    }
  }

  static Future<int?> createplan(
      String name, var startdate, var enddate) async {
    try {
      Map<String, dynamic> data = {
        "name": name,
        "start_date": startdate,
        "end_date": enddate
      };
      SharedPreferences _prefs = await SharedPreferences.getInstance();
      var token = _prefs.getString('token');

      _dio.options.headers['Authorization'] = 'Bearer ${token!}';
      Response response = await _dio.post(
        '${pathUrl}createplan',
        data: data,
        options: Options(
            headers: {"Content-Type": "application/x-www-form-urlencoded"},
            // "Content-Type": "application/x-www-form-urlencoded",
            followRedirects: false,
            validateStatus: (status) {
              return status! < 600;
            }),
      );

      print("NewewwewpPPPP ${response}");

      return response.data != null && response.data['data'] != null
          ? response.data['data']['id']
          : null;
    } catch (error, stacktrace) {
      log("Exception occured: $error stackTrace: $stacktrace");
      return null;
    }
  }

  static Future<PlanResponse?> getplanuser(int page, int size) async {
    try {
      SharedPreferences _prefs = await SharedPreferences.getInstance();
      var token = _prefs.getString('token');

      _dio.options.headers['Authorization'] = 'Bearer ${token!}';

      var currentlanguagecode = _prefs.getString('language_code');

      _dio.options.headers['lang'] = currentlanguagecode;
      String url = '${pathUrl}getplanuser?page=$page&size=$size';

      log("The URL $url");
      log("The Headers ${_dio.options.headers}");
      Response response = await _dio.get(
        url,
        options: Options(
            headers: {"Content-Type": "application/x-www-form-urlencoded"},
            // "Content-Type": "application/x-www-form-urlencoded",
            followRedirects: false,
            validateStatus: (status) {
              return status! < 500;
            }),
      );

      return PlanResponse.fromJson(response.data);
    } catch (error, stacktrace) {
      log("Exception occured: $error stackTrace: $stacktrace");
      return PlanResponse.withError(handleError(error));
    }
  }

  static Future<PlanDetailsResponse> getplanuserdetails(int id) async {
    try {
      SharedPreferences _prefs = await SharedPreferences.getInstance();
      var token = _prefs.getString('token');

      _dio.options.headers['Authorization'] = 'Bearer ${token!}';

      var currentlanguagecode = _prefs.getString('language_code');

      _dio.options.headers['lang'] = currentlanguagecode;
      String url = '${pathUrl}getplanuser?id=$id';

      log("The URL $url");
      log("The Headers ${_dio.options.headers}");
      Response response = await _dio.get(
        url,
        options: Options(
            headers: {"Content-Type": "application/x-www-form-urlencoded"},
            // "Content-Type": "application/x-www-form-urlencoded",
            followRedirects: false,
            validateStatus: (status) {
              return status! < 500;
            }),
      );

      return PlanDetailsResponse.fromJson(response.data);
    } catch (error, stacktrace) {
      log("Exception occured: $error stackTrace: $stacktrace");
      return PlanDetailsResponse.withError(handleError(error));
    }
  }

  static Future<GeneralResponse> additemtoplan(
      var date, String type, int itemid, int planid) async {
    try {
      Map<String, dynamic> data = {
        "date": date,
        "type": type,
        "video_id": itemid,
        "plan_id": planid,
      };

      SharedPreferences _prefs = await SharedPreferences.getInstance();
      var token = _prefs.getString('token');

      print('Dataaa $data');
      _dio.options.headers['Authorization'] = 'Bearer ${token!}';
      Response response = await _dio.post(
        '${pathUrl}additemtoplan',
        data: data,
        options: Options(
            headers: {"Content-Type": "application/x-www-form-urlencoded"},
            // "Content-Type": "application/x-www-form-urlencoded",
            followRedirects: false,
            validateStatus: (status) {
              return status! < 600;
            }),
      );

      print("Addded ItemDataa $itemid PlanIdd $planid\nResponse $response");

      return GeneralResponse.fromJson(response.data);
    } catch (error, stacktrace) {
      log("Exception occured: $error stackTrace: $stacktrace");
      return GeneralResponse.withError(handleError(error));
    }
  }

  static Future<PlanResponse> getplanuseritems(int page, int size) async {
    try {
      SharedPreferences _prefs = await SharedPreferences.getInstance();
      var token = _prefs.getString('token');

      _dio.options.headers['Authorization'] = 'Bearer ${token!}';

      var currentlanguagecode = _prefs.getString('language_code');

      _dio.options.headers['lang'] = currentlanguagecode;
      String url =
          '${pathUrl}getplanuser?page=$page&size=$size'; //getplanuseritems

      log("The URL $url");
      log("The Headers ${_dio.options.headers}");
      Response response = await _dio.get(
        url,
        options: Options(
            headers: {"Content-Type": "application/x-www-form-urlencoded"},
            // "Content-Type": "application/x-www-form-urlencoded",
            followRedirects: false,
            validateStatus: (status) {
              return status! < 500;
            }),
      );

      return PlanResponse.fromJson(response.data);
    } catch (error, stacktrace) {
      log("Exception occured: $error stackTrace: $stacktrace");
      return PlanResponse.withError(handleError(error));
    }
  }

  static Future<PlanItemsResponse> getplanudetailsseritems(
      int id, var date) async {
    try {
      SharedPreferences _prefs = await SharedPreferences.getInstance();
      var token = _prefs.getString('token');

      _dio.options.headers['Authorization'] = 'Bearer ${token!}';

      var currentlanguagecode = _prefs.getString('language_code');
      var currency = _prefs.getString('currency');
      _dio.options.headers['lang'] = currentlanguagecode;
      String url =
          '${pathUrl}getplanbydate?plan_id=$id&date=$date&currency=$currency';
      // '${pathUrl}getplanudetailsseritems?plan_id=$id&date=$date&currency=$currency';

      log("The URL $url");
      log("The Headers ${_dio.options.headers}");
      Response response = await _dio.get(
        url,
        options: Options(
          headers: {"Content-Type": "application/x-www-form-urlencoded"},
          // "Content-Type": "application/x-www-form-urlencoded",
          followRedirects: false,
          validateStatus: (status) {
            return status! < 500;
          },
        ),
      );
      print("qqqqqqadasdkadjksadasdq");

      return PlanItemsResponse.fromJson(response.data);
    } catch (error, stacktrace) {
      log("Exception occured: $error stackTrace: $stacktrace");
      return PlanItemsResponse.withError(handleError(error));
    }
  }

  static Future<PlanItemsResponse> getplanuserdates(int id) async {
    try {
      SharedPreferences _prefs = await SharedPreferences.getInstance();
      var token = _prefs.getString('token');

      _dio.options.headers['Authorization'] = 'Bearer ${token!}';

      var currentlanguagecode = _prefs.getString('language_code');

      _dio.options.headers['lang'] = currentlanguagecode;
      String url = '${pathUrl}getplannamebyid?id=$id';
      // String url = '${pathUrl}getplanuserdates?id=$id';

      log("The URL $url");
      log("The Headers ${_dio.options.headers}");

      Response response = await _dio.get(
        url,
        options: Options(
            headers: {"Content-Type": "application/x-www-form-urlencoded"},
            // "Content-Type": "application/x-www-form-urlencoded",
            followRedirects: false,
            validateStatus: (status) {
              return status! < 500;
            }),
      );
      print('LLLLLLL $response');
      return PlanItemsResponse.fromJson(response.data);
    } catch (error, stacktrace) {
      log("Exception occured: $error stackTrace: $stacktrace");
      return PlanItemsResponse.withError(handleError(error));
    }
  }

  static Future<GeneralResponse> addtimetoitem(
    var time,
    int itemid,
    planId,
    date,
  ) async {
    try {
      Map<String, dynamic> data = {
        "video_id": itemid,
        "time": time,
        "plan_id": planId,
        "date": date
      };
      SharedPreferences _prefs = await SharedPreferences.getInstance();
      var token = _prefs.getString('token');

      print("proafsasfasfsaffile $data");

      _dio.options.headers['Authorization'] = 'Bearer ${token!}';
      Response response = await _dio.post(
        '${pathUrl}addtimetoitem',
        data: data,
        options: Options(
            headers: {"Content-Type": "application/x-www-form-urlencoded"},
            // "Content-Type": "application/x-www-form-urlencoded",
            followRedirects: false,
            validateStatus: (status) {
              return status! < 600;
            }),
      );

      return GeneralResponse.fromJson(response.data);
    } catch (error, stacktrace) {
      log("Exception occured: $error stackTrace: $stacktrace");
      return GeneralResponse.withError(handleError(error));
    }
  }

  // static Future<PlanResponse> getplandetailsschedules(int id, var date) async {
  //   try {
  //     SharedPreferences _prefs = await SharedPreferences.getInstance();
  //     var token = _prefs.getString('token');
  //     var currency = _prefs.getString('currency');
  //
  //
  //
  //     _dio.options.headers['Authorization'] = 'Bearer ${token!}';
  //
  //     var currentlanguagecode = _prefs.getString('language_code');
  //
  //     _dio.options.headers['lang'] = currentlanguagecode;
  //     String url =
  //         '${pathUrl}getplanbydate?plan_id=$id&date=$date&currency=$currency';
  //
  //     log("The URL $url");
  //     log("The Headers ${_dio.options.headers}");
  //     Response response = await _dio.get(
  //       url,
  //       options: Options(
  //         headers: {"Content-Type": "application/x-www-form-urlencoded"},
  //         // "Content-Type": "application/x-www-form-urlencoded",
  //         followRedirects: false,
  //         validateStatus: (status) {
  //           return status! < 500;
  //         },
  //       ),
  //     );
  //     print("Sccccccjhhhee");
  //
  //     return PlanResponse.fromJson(response.data);
  //   } catch (error, stacktrace) {
  //     log("Exception occured: $error stackTrace: $stacktrace");
  //     return PlanResponse.withError(handleError(error));
  //   }
  // }

  static Future<GeneralResponse> deleteitem(int itemid,
      {bool isSchedule = false}) async {
    try {
      // Map<String, dynamic> data = {"item_id": itemid, "_method": "DELETE"};
      SharedPreferences _prefs = await SharedPreferences.getInstance();
      var token = _prefs.getString('token');

      final url = isSchedule
          ? '${pathUrl}deleteitemschedule?id=$itemid'
          : '${pathUrl}deleteitem?id=$itemid';

      _dio.options.headers['Authorization'] = 'Bearer ${token!}';
      Response response = await _dio.delete(
        url,
        options: Options(
            headers: {"Content-Type": "application/x-www-form-urlencoded"},
            // "Content-Type": "application/x-www-form-urlencoded",
            followRedirects: false,
            validateStatus: (status) {
              return status! < 600;
            }),
      );

      print('kmasdasd $url');

      return GeneralResponse.fromJson(response.data);
    } catch (error, stacktrace) {
      log("Exception occured: $error stackTrace: $stacktrace");
      return GeneralResponse.withError(handleError(error));
    }
  }

  static Future<GeneralResponse> deleteday(var date, int id) async {
    try {
      Map<String, dynamic> data = {
        "date": date,
        "plan_id": id,
        "_method": "DELETE"
      };

      SharedPreferences _prefs = await SharedPreferences.getInstance();
      var token = _prefs.getString('token');

      _dio.options.headers['Authorization'] = 'Bearer ${token!}';
      Response response = await _dio.post(
        '${pathUrl}deleteday',
        data: data,
        options: Options(
            headers: {"Content-Type": "application/x-www-form-urlencoded"},
            // "Content-Type": "application/x-www-form-urlencoded",
            followRedirects: false,
            validateStatus: (status) {
              return status! < 600;
            }),
      );

      return GeneralResponse.fromJson(response.data);
    } catch (error, stacktrace) {
      log("Exception occured: $error stackTrace: $stacktrace");
      return GeneralResponse.withError(handleError(error));
    }
  }

  static Future<GeneralResponse> deleteplan(int id) async {
    try {
      SharedPreferences _prefs = await SharedPreferences.getInstance();
      var token = _prefs.getString('token');

      _dio.options.headers['Authorization'] = 'Bearer ${token!}';
      Response response = await _dio.delete(
        '${pathUrl}deleteplan?id=$id',
        options: Options(
            headers: {"Content-Type": "application/x-www-form-urlencoded"},
            // "Content-Type": "application/x-www-form-urlencoded",
            followRedirects: false,
            validateStatus: (status) {
              return status! < 600;
            }),
      );

      return GeneralResponse.fromJson(response.data);
    } catch (error, stacktrace) {
      log("Exception occured: $error stackTrace: $stacktrace");
      return GeneralResponse.withError(handleError(error));
    }
  }

  static Future<PlannameResponse> getplannamebyid(int id) async {
    try {
      SharedPreferences _prefs = await SharedPreferences.getInstance();
      var token = _prefs.getString('token');

      _dio.options.headers['Authorization'] = 'Bearer ${token!}';

      var currentlanguagecode = _prefs.getString('language_code');

      _dio.options.headers['lang'] = currentlanguagecode;
      String url = '${pathUrl}getplannamebyid?plan_id=$id';

      log("The URL $url");
      log("The Headers ${_dio.options.headers}");
      Response response = await _dio.get(
        url,
        options: Options(
            headers: {"Content-Type": "application/x-www-form-urlencoded"},
            // "Content-Type": "application/x-www-form-urlencoded",
            followRedirects: false,
            validateStatus: (status) {
              return status! < 500;
            }),
      );

      return PlannameResponse.fromJson(response.data);
    } catch (error, stacktrace) {
      log("Exception occured: $error stackTrace: $stacktrace");
      return PlannameResponse.withError(handleError(error));
    }
  }

  static Future<PlanDetailsModel> getplanDetails(int id) async {
    try {
      SharedPreferences _prefs = await SharedPreferences.getInstance();
      var token = _prefs.getString('token');
      _dio.options.headers['Authorization'] = 'Bearer ${token!}';

      var currentlanguagecode = _prefs.getString('language_code');

      _dio.options.headers['lang'] = currentlanguagecode;
      String url = '${pathUrl}getplannamebyid?id=$id';

      Response response = await _dio.get(
        url,
        options: Options(
            headers: {"Content-Type": "application/x-www-form-urlencoded"},
            // "Content-Type": "application/x-www-form-urlencoded",
            followRedirects: false,
            validateStatus: (status) {
              return status! < 500;
            }),
      );

      log('asdknasdsjdnlkan ${pathUrl}getplannamebyid?id=$id');

      log('asdknasdafasfsajkfaksfafsjdnlkan ${response.data}');

      return PlanDetailsModel.fromJson(response.data);
    } catch (error, stacktrace) {
      log("Exception occured: $error stackTrace: $stacktrace");
      return PlanDetailsModel.withError(handleError(error));
    }
  }

  // Project-specific API methods
  static Future<PropertyStatusResponse> getPropertyStatuses() async {
    try {
      SharedPreferences _prefs = await SharedPreferences.getInstance();
      var currentlanguagecode = _prefs.getString('language_code');
      _dio.options.headers['lang'] = currentlanguagecode;

      String url = '${pathUrl}property-status';

      log("The URL $url");
      log("The Headers ${_dio.options.headers}");
      Response response = await _dio.get(
        url,
        options: Options(
            headers: {"Content-Type": "application/x-www-form-urlencoded"},
            followRedirects: false,
            validateStatus: (status) {
              return status! < 500;
            }),
      );

      return PropertyStatusResponse.fromJson(response.data);
    } catch (error, stacktrace) {
      log("Exception occured: $error stackTrace: $stacktrace");
      return PropertyStatusResponse.withError(handleError(error));
    }
  }

  static Future<CategoryResponse?> filterProjectsCategory(int? location,
      int? propertyStatus, String? paymentMethod, int? type) async {
    try {
      SharedPreferences _prefs = await SharedPreferences.getInstance();
      var currentlanguagecode = _prefs.getString('language_code');
      var currency = _prefs.getString('currency');
      _dio.options.headers['lang'] = currentlanguagecode;

      String url =
          '${pathUrl}getmaincategory?category=${AppConstants.projectsId}';

      if (location != null && location != 0) {
        url += "&location=$location";
      }

      if (propertyStatus != null && propertyStatus != 0) {
        url += "&property_status=$propertyStatus";
      }

      if (paymentMethod != null && paymentMethod.isNotEmpty) {
        url += "&payment_method=$paymentMethod";
      }

      if (type != null && type != 0) {
        url += "&type=$type";
      }

      if (currency != null && currency != "") {
        url += "&currency=$currency";
      }

      log("The URL $url");
      log("The Headers ${_dio.options.headers}");
      Response response = await _dio.get(
        url,
        options: Options(
            headers: {"Content-Type": "application/x-www-form-urlencoded"},
            followRedirects: false,
            validateStatus: (status) {
              return status! < 500;
            }),
      );

      return CategoryResponse.fromJson(response.data);
    } catch (error, stacktrace) {
      log("Exception occured: $error stackTrace: $stacktrace");
      return CategoryResponse.withError(handleError(error));
    }
  }
} //end of api
