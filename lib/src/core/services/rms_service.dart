import 'dart:developer';

import 'package:dio/dio.dart';
import 'package:intl/intl.dart';
import 'package:page/src/core/utils/logger.dart';

class RmsService {
  static final Dio _dio = Dio();
  static String token = '';
  static List<(int id, String name)> properties = [];

  // * Get Auth Token
  static Future<String> getAuthToken() async {
    try {
      final response = await _dio.post(
        _RmsConstants.authToken,
        data: _RmsConstants.demoCredentials,
      );

      log('RMS_Auth_Token_Response: $response');

      if (response.statusCode == 200 && response.data['token'] != null) {
        token = response.data['token'];
        return token;
      }
    } catch (error, stacktrace) {
      log('RMS_Auth_Token_Error: $error stackTrace: $stacktrace');
    }
    return '';
  }

  // * Get Properties
  static Future<List<(int id, String name)>> getProperties() async {
    try {
      if (token.isEmpty) {
        await getAuthToken();
      }
      final response = await _dio.get(
        _RmsConstants.properties,
        options: Options(
          headers: {
            'authtoken': token,
          },
        ),
      );

      if (response.statusCode == 200) {
        final propertiesList = response.data as List;
        log('RMS_Properties_Response: $response');

        properties = List.generate(
          propertiesList.length,
          (index) =>
              (propertiesList[index]['id'], propertiesList[index]['name']),
        );

        return properties;
      }
    } catch (error, stacktrace) {
      log('RMS_Properties_Error: $error stackTrace: $stacktrace');
    }

    return [];
  }

  // * Get Categories By Property
  static Future<List<(int id, String name)>> getCategoriesByProperty(
      int? propertyId) async {
    try {
      if (token.isEmpty) {
        await getAuthToken();
      }
      final response = await _dio.get(
        '${_RmsConstants.categories}?propertyId=$propertyId',
        options: Options(
          headers: {
            'authtoken': token,
          },
        ),
      );

      if (response.statusCode == 200) {
        final categories = response.data as List;
        log('RMS_Categories_Response: $response');

        return List.generate(
          categories.length,
          (index) => (categories[index]['id'], categories[index]['name']),
        );
      }
    } catch (error, stacktrace) {
      log('RMS_Categories_Error: $error stackTrace: $stacktrace');
    }

    return [];
  }

  // * Get Category Price
  static Future<num> getUnitPrice({
    required int? propertyId,
    required int? unitId,
  }) async {
    try {
      if (token.isEmpty) {
        await getAuthToken();
      }
      final response = await _dio.post(
        _RmsConstants.categoryPrice,
        data: {
          "propertyId": propertyId,
          "categoryId": unitId,
          "agentId": _RmsConstants.agentId,
          "rateTypeId": _RmsConstants.rateTypeId,
          "arrivalDate": _RmsConstants.arrivalDate,
          "departureDate": _RmsConstants.departureDate,
        },
        options: Options(
          headers: {
            'authtoken': token,
          },
        ),
      );

      if (response.statusCode == 200) {
        final category = response.data;
        log('RMS_Category_Price_Response: $response');

        return category['baseRate'];
      }
    } catch (error, stacktrace) {
      log('RMS_Category_Price_Error: $error stackTrace: $stacktrace');
    }

    return 0.0;
  }

  static Future<(Map<String, bool>, Map<String, num>)>
      getClosedCalendarDaysByMonthForOneCategory({
    required int? categoryId,
    required String startDate,
    required String endDate,
  }) async {
    try {
      if (token.isEmpty) {
        await getAuthToken();
      }

      final data = {
        "categoryIds": [categoryId],
        "arrival": startDate,
        "departure": endDate,
        "RateIds": [_RmsConstants.rateTypeId],
      };

      Log.w('URL: ${_RmsConstants.closedCalendarDaysByMonth}\nRMS_Data: $data');

      final response = await _dio.post(
        _RmsConstants.closedCalendarDaysByMonth,
        data: data,
        options: Options(
          headers: {
            'authtoken': token,
          },
        ),
      );

      if (response.statusCode == 200) {
        final data = response.data;
        log('RMS_Category_Closed_Calendar_Days_Response: $response');

        Map<String, bool> closedDays = {};
        Map<String, num> priceMap = {};

        for (var category in data['categories']) {
          for (var rate in category['rates']) {
            for (var day in rate['dayBreakdown']) {
              // String date = day['theDate']; //2024-12-16 00:00:00 make it 2024-12-16
              String date = DateFormat('yyyy-MM-dd').format(
                DateTime.parse(day['theDate']),
              );
              bool isAvailable = day['availableAreas'] > 0;
              closedDays[date] = !isAvailable;
              priceMap[date] = day['dailyRate'];
            }
          }
        }

        log('RMS_Availability_Map: $closedDays');

        return (closedDays, priceMap);
      }
    } catch (error, stacktrace) {
      log('RMS_Category_Closed_Calendar_Days_Error: $error stackTrace: $stacktrace');
    }

    return (<String, bool>{}, <String, num>{});
  }

  static Future<Map<int, List<String>>>
      getClosedCalendarDaysByMonthForMultipleCategories({
    required List<int> categoryIds,
    required String startDate,
    required String endDate,
  }) async {
    try {
      if (token.isEmpty) {
        await getAuthToken();
      }

      final data = {
        "categoryIds": categoryIds.where((element) => element != 0).toList(),
        "arrival": startDate,
        "departure": endDate,
        "RateIds": [_RmsConstants.rateTypeId],
      };

      Log.w('URL: ${_RmsConstants.closedCalendarDaysByMonth}\nRMS_Data: $data');

      final response = await _dio.post(
        _RmsConstants.closedCalendarDaysByMonth,
        data: data,
        options: Options(
          headers: {
            'authtoken': token,
          },
        ),
      );

      if (response.statusCode == 200) {
        final data = response.data;
        log('RMS_Categories_Closed_Calendar_Days_Response: $response');

        Map<int, List<String>> categoriesClosedDatesMap = {};

        for (var category in data['categories']) {
          int categoryId = category['categoryId'];
          List<String> closedDates = [];

          for (var rate in category['rates']) {
            for (var day in rate['dayBreakdown']) {
              String date = DateFormat('yyyy-MM-dd').format(
                DateTime.parse(day['theDate']),
              );
              bool isAvailable = day['availableAreas'] > 0;
              if (!isAvailable) {
                closedDates.add(date);
              }
            }
          }

          categoriesClosedDatesMap[categoryId] = closedDates;
        }

        log('RMS_Categories_Closed_Dates_Map: $categoriesClosedDatesMap');

        return categoriesClosedDatesMap;
      }
    } catch (error, stacktrace) {
      log('RMS_Categories_Closed_Calendar_Days_Error: $error stackTrace: $stacktrace');
    }

    return {};
  }
}

class _RmsConstants {
  static const String baseUrl = 'https://restapi12.rmscloud.com';

  static const String authToken = '$baseUrl/authToken';
  static const String properties = '$baseUrl/properties';
  static const String categories = '$baseUrl/categories';
  static const String categoryPrice = '$baseUrl/rates/rateQuote';
  static const String closedCalendarDaysByMonth = '$baseUrl/rates/grid/quick';

  // * Production Constants
  static const int agentId = 1078;
  static const int rateTypeId = 9;
  static final String arrivalDate = DateFormat('yyyy-MM-dd HH:mm:ss').format(
    DateTime.now(),
  );

  static final String departureDate = DateFormat('yyyy-MM-dd HH:mm:ss').format(
    DateTime.now().add(
      const Duration(hours: 1),
    ),
  );

  // * Demo Constants
  // static const int agentId = 15;
  // static const int rateTypeId = 9;
  // static const String arrivalDate = "2019-08-23 10:00:00";
  // static const String departureDate = "2019-08-25 13:25:00";

  // * Production Credentials
  static const demoCredentials = {
    "agentId": 1078,
    "agentPassword": "a@6syWmkuaa",
    "clientId": 20642,
    "clientPassword": r"3Kw$!j7B",
    "useTrainingDatabase": false,
    "moduleType": ["guestServices"]
  };

// * Demo Credentials
// static const demoCredentials = {
//   "agentId": 15,
//   "agentPassword": r"1h&29$vk449f8",
//   "clientId": 11281,
//   "clientPassword": r"6k!Dp$N4",
//   "useTrainingDatabase": false,
//   "moduleType": ["datawarehouse"]
// };
}

// get closed calendar days by month every time but send the start date and end date every time
// e.g example of response
//{
//   "categories": [
//     {
//       "billingCategoryId": 0,
//       "categoryId": 4,
//       "name": "Deluxe 004",
//       "rates": [
//         {
//           "dayBreakdown": [
//             {
//               "availableAreas": 5,
//               "closedOnArrival": false,
//               "closedOnDeparture": false,
//               "dailyRate": 5418,
//               "maxStay": "0,",
//               "minStay": 0,
//               "minStayOnArrival": "0,",
//               "theDate": "2023-08-15 00:00:00",
//               "stopSell": false
//             }
//           ],
//           "name": "Testing Rate",
//           "personBase": 0,
//           "rateId": 1416
//         }
//       ]
//     }
//   ]
// }

// * Demo closed days
//   static Future<Map<String, bool>> getClosedCalendarDaysByMonth({
//     required int? categoryId,
//     required String startDate,
//     required String endDate,
//   }) async {
//     try {
//       if (token.isEmpty) {
//         await getAuthToken();
//       }
//
//       // Test response data
//       final response = {
//         "categories": [
//           {
//             "billingCategoryId": 0,
//             "categoryId": 4,
//             "name": "Deluxe 004",
//             "rates": [
//               {
//                 "dayBreakdown": [
//                   {
//                     "availableAreas": 5,
//                     "closedOnArrival": false,
//                     "closedOnDeparture": false,
//                     "dailyRate": 5418,
//                     "maxStay": "0,",
//                     "minStay": 0,
//                     "minStayOnArrival": "0,",
//                     "theDate": "2024-11-20 00:00:00",
//                     "stopSell": false
//                   },
//                   {
//                     "availableAreas": 5,
//                     "closedOnArrival": false,
//                     "closedOnDeparture": false,
//                     "dailyRate": 5418,
//                     "maxStay": "0,",
//                     "minStay": 0,
//                     "minStayOnArrival": "0,",
//                     "theDate": "2024-11-21 00:00:00",
//                     "stopSell": false
//                   },
//                   {
//                     "availableAreas": 5,
//                     "closedOnArrival": false,
//                     "closedOnDeparture": false,
//                     "dailyRate": 5418,
//                     "maxStay": "0,",
//                     "minStay": 0,
//                     "minStayOnArrival": "0,",
//                     "theDate": "2024-11-23 00:00:00",
//                     "stopSell": false
//                   },
//                   {
//                     "availableAreas": 0,
//                     "closedOnArrival": true,
//                     "closedOnDeparture": true,
//                     "dailyRate": 5418,
//                     "maxStay": "0,",
//                     "minStay": 0,
//                     "minStayOnArrival": "0,",
//                     "theDate": "2024-11-24 00:00:00",
//                     "stopSell": true
//                   },
//                   {
//                     "availableAreas": 0,
//                     "closedOnArrival": true,
//                     "closedOnDeparture": true,
//                     "dailyRate": 5418,
//                     "maxStay": "0,",
//                     "minStay": 0,
//                     "minStayOnArrival": "0,",
//                     "theDate": "2024-11-25 00:00:00",
//                     "stopSell": true
//                   },
//                   {
//                     "availableAreas": 5,
//                     "closedOnArrival": false,
//                     "closedOnDeparture": false,
//                     "dailyRate": 5418,
//                     "maxStay": "0,",
//                     "minStay": 0,
//                     "minStayOnArrival": "0,",
//                     "theDate": "2024-11-30 00:00:00",
//                     "stopSell": false
//                   },
//                   {
//                     "availableAreas": 0,
//                     "closedOnArrival": false,
//                     "closedOnDeparture": false,
//                     "dailyRate": 5418,
//                     "maxStay": "0,",
//                     "minStay": 0,
//                     "minStayOnArrival": "0,",
//                     "theDate": "2024-12-01 00:00:00",
//                     "stopSell": false
//                   },
//                   {
//                     "availableAreas": 5,
//                     "closedOnArrival": false,
//                     "closedOnDeparture": false,
//                     "dailyRate": 5418,
//                     "maxStay": "0,",
//                     "minStay": 0,
//                     "minStayOnArrival": "0,",
//                     "theDate": "2024-12-02 00:00:00",
//                     "stopSell": false
//                   },
//                   {
//                     "availableAreas": 0,
//                     "closedOnArrival": false,
//                     "closedOnDeparture": false,
//                     "dailyRate": 5418,
//                     "maxStay": "0,",
//                     "minStay": 0,
//                     "minStayOnArrival": "0,",
//                     "theDate": "2024-12-03 00:00:00",
//                     "stopSell": false
//                   },
//                   {
//                     "availableAreas": 5,
//                     "closedOnArrival": false,
//                     "closedOnDeparture": false,
//                     "dailyRate": 5418,
//                     "maxStay": "0,",
//                     "minStay": 0,
//                     "minStayOnArrival": "0,",
//                     "theDate": "2024-12-04 00:00:00",
//                     "stopSell": false
//                   },
//                 ],
//                 "name": "Testing Rate",
//                 "personBase": 0,
//                 "rateId": 1416
//               }
//             ]
//           }
//         ]
//       };
//
//       log('RMS_Category_Closed_Calendar_Days_Response: $response');
//
//       await Future.delayed(const Duration(seconds: 1));
//
//       Map<String, bool> availabilityMap = {};
//
//       for (var category in response['categories']!) {
//         for (var rate in (category['rates'] as List?) ?? []) {
//           for (var day in rate['dayBreakdown']) {
//             String date =
//                 DateFormat('yyyy-MM-dd').format(DateTime.parse(day['theDate']));
//             bool isAvailable = day['availableAreas'] > 0;
//             availabilityMap[date] = isAvailable;
//           }
//         }
//       }
//
//       return availabilityMap;
//     } catch (error, stacktrace) {
//       log('RMS_Category_Closed_Calendar_Days_Error: $error stackTrace: $stacktrace');
//     }
//
//     return {};
//   }
