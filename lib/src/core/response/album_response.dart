import 'dart:developer';

class AlbumsResponse {
  int? code;
  String? msg;
  List<Data>? data;

  AlbumsResponse({this.code, this.msg, this.data});

  AlbumsResponse.fromJson(Map<String?, dynamic> json) {
    code = json['code'];
    msg = json['msg'];
    if (json['data'] != null) {
      data = [];
      json['data'].forEach((v) {
        data!.add(new Data.fromJson(v));
      });
    }
  }
  AlbumsResponse.withError(String? errorValue)
      : data = [],
        code = 0,
        msg = "fail";
}

class Data {
  int? id;
  String? name;
  String? type;
  int? category;
  String? label;
  int? location;
  int? numberofstarts;
  double? latitude;
  double? longitude;
  int? media;
  int? startprice;
  int? endprice;
  String? startpricecurrency;
  String? endpricecurrency;
  String? greviewlink;
  String? phone;
  String? facebook;
  String? instagram;
  String? createdAt;
  String? updatedAt;
  int? rating;
  List<Photos>? photos;

  Data(
      {this.id,
      this.name,
      this.type,
      this.category,
      this.label,
      this.location,
      this.numberofstarts,
      this.latitude,
      this.longitude,
      this.media,
      this.startprice,
      this.endprice,
      this.startpricecurrency,
      this.endpricecurrency,
      this.greviewlink,
      this.phone,
      this.facebook,
      this.instagram,
      this.createdAt,
      this.updatedAt,
      this.rating,
      this.photos});

  Data.fromJson(Map<String?, dynamic> json) {
    log('asdasdsad $json');
    id = json['album_id'];
    name = json['name'];
    type = json['type'].toString();
    category = json['category'];
    label = json['label'];
    location = json['location'];
    numberofstarts = json['numberofstarts'];
    latitude = json['latitude'];
    longitude = json['longitude'];
    media = json['media'];
    startprice = json['startprice'];
    endprice = json['endprice'];
    startpricecurrency = json['startpricecurrency'];
    endpricecurrency = json['endpricecurrency'];
    greviewlink = json['greviewlink'];
    phone = json['phone'];
    facebook = json['website'];
    instagram = json['instagram'];
    createdAt = json['created_at'];
    updatedAt = json['updated_at'];
    rating = json['rating'];
    if (json['images'] != null) {
      photos = [];
      json['images'].forEach((v) {
        photos?.add(new Photos.fromJson(v));
      });
    }
  }
}

class Photos {
  int? id;
  String? image;
  String? createdat;

  Photos({this.id, this.image, this.createdat});

  Photos.fromJson(Map<String?, dynamic> json) {
    id = json['id'];
    image = json['url'];
    createdat = json['createdat'];
  }
}
