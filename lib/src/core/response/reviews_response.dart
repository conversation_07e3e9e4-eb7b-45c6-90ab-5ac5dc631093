import '../../features/models/reviews.dart';

class ReviewsResponse {
  final List<Reviews> reviews;

  final String error;
  final int code;
  final String msg;
  ReviewsResponse.fromJson(Map parsedJson)
      : reviews = parsedJson['data'] != null
            ? (parsedJson['data'] as List)
                .map((p) => Reviews.fromJson(p))
                .toList()
            : [],
        error = "",
        code = 1,
        msg = "success";
  ReviewsResponse.withError(String errorValue)
      : reviews = [],
        error = errorValue,
        code = 0,
        msg = "fail";
}
