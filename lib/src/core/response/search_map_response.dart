import '../../features/models/search_map.dart';

class SearchMapResponse {
  final List<SearchMapModel> results;
  final String error;
  final int code;
  final String msg;
  SearchMapResponse(this.results, this.error, this.code, this.msg);

  SearchMapResponse.fromJson(Map<String, dynamic> parsedJson)
      : results = parsedJson['data'] != null
            ? (parsedJson['data'] as List)
                .map((p) => SearchMapModel.fromJson(p))
                .toList()
            : [],
        error = "",
        code = parsedJson['code'],
        msg = parsedJson['message'];

  SearchMapResponse.withError(String errorValue)
      : results = [],
        error = errorValue,
        code = 0,
        msg = "fail";
}
