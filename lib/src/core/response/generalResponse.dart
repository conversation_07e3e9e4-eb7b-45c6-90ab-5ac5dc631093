class GeneralResponse {
  final String? error;
  final String? code;
  final String? msg;
  final int? data;
  final int? id;
  GeneralResponse({this.error, this.code, this.msg, this.data, this.id});

  GeneralResponse.fromJson(Map<String?, dynamic> parsedJson)
      : data = parsedJson['code'],
        // parsedJson['data'],
        error = "",
        code = parsedJson['code'].toString(),
        id = parsedJson['id'],
        msg = parsedJson['msg'];

  GeneralResponse.withError(String? errorValue)
      : data = 0,
        id = 0,
        error = errorValue,
        code = "0",
        msg = errorValue;
}

class MaxPriceResponse {
  final String? error;
  final String? code;
  final String? msg;
  final int? data;
  MaxPriceResponse({this.error, this.code, this.msg, this.data});

  MaxPriceResponse.fromJson(Map<String?, dynamic> parsedJson)
      : data = parsedJson['data'],
        error = "",
        code = parsedJson['code'].toString(),
        msg = parsedJson['msg'];

  MaxPriceResponse.withError(String? errorValue)
      : data = 1,
        error = errorValue,
        code = "0",
        msg = errorValue;
}

class GeneralResponseWithData {
  final String? error;
  final String? code;
  final String? msg;
  final int? data;
  GeneralResponseWithData({this.error, this.code, this.msg, this.data});

  GeneralResponseWithData.fromJson(Map<String?, dynamic> parsedJson)
      : data = parsedJson['data'],
        error = "",
        code = parsedJson['code'].toString(),
        msg = parsedJson['msg'] ?? parsedJson['message'] ?? '';

  GeneralResponseWithData.withError(String? errorValue)
      : data = 1,
        error = errorValue,
        code = "0",
        msg = errorValue;
}
