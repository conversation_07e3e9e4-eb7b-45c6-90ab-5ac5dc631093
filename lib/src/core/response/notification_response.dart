import '../../features/models/notification.dart';

class NotificationsResponse {
  final List<NotificationModel> notifications;

  final String error;
  final int code;
  final String msg;
  NotificationsResponse.fromJson(Map parsedJson)
      : notifications = parsedJson['data'] != null
            ? (parsedJson['data'] as List)
                .map((p) => NotificationModel.fromJson(p))
                .toList()
            : [],
        error = "",
        code = 1,
        msg = "success";
  NotificationsResponse.withError(String errorValue)
      : notifications = [],
        error = errorValue,
        code = 0,
        msg = "fail";
}
