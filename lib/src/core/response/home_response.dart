import 'package:page/src/features/models/content.dart';

import '../../features/models/video_model.dart';

class HomeResponse {
  final List<VideoModel> featuredvideo;
  final List<VideoModel> newProjects;
  final List<VideoModel> category;
  final List<VideoModel> luxury;
  final List<VideoModel> places;
  final List<VideoModel> holidayHomes;
  final List<VideoModel> carRentals;
  final List<Types> types;

  // New project type arrays
  final List<VideoModel> type1; // Apartments
  final List<VideoModel> type2; // Villa
  final List<VideoModel> type3; // Luxury

  final String error;
  final int code;
  final String msg;

  HomeResponse.fromJson(Map parsedJson)
      : featuredvideo = parsedJson['data']['featured_videos'] != null
            ? (parsedJson['data']['featured_videos'] as List)
                .map((p) => VideoModel.fromJson(p))
                .toList()
            : [],
        newProjects = parsedJson['data']['new_projects'] != null
            ? (parsedJson['data']['new_projects'] as List)
                .map((p) => VideoModel.fromJson(p))
                .toList()
            : [],
        category = parsedJson['data']['recent_videos'] != null
            ? (parsedJson['data']['recent_videos'] as List)
                .map((p) => VideoModel.fromJson(p))
                .toList()
            : [],
        luxury = parsedJson['data']['properties'] != null
            ? (parsedJson['data']['properties'] as List)
                .map((p) => VideoModel.fromJson(p))
                .toList()
            : [],
        places = parsedJson['data']['places'] != null
            ? (parsedJson['data']['places'] as List)
                .map((p) => VideoModel.fromJson(p))
                .toList()
            : [],
        holidayHomes = parsedJson['data']['holiday_homes'] != null
            ? (parsedJson['data']['holiday_homes'] as List)
                .map((p) => VideoModel.fromJson(p))
                .toList()
            : [],
        carRentals = parsedJson['data']['car_rentals'] != null
            ? (parsedJson['data']['car_rentals'] as List)
                .map((p) => VideoModel.fromJson(p))
                .toList()
            : [],
        types = parsedJson['data']['types'] != null
            ? (parsedJson['data']['types'] as List)
                .map((p) => Types.fromJson(p))
                .toList()
            : [],
        type1 = parsedJson['data']['type1'] != null
            ? (parsedJson['data']['type1'] as List)
                .map((p) => VideoModel.fromJson(p))
                .toList()
            : [],
        type2 = parsedJson['data']['type2'] != null
            ? (parsedJson['data']['type2'] as List)
                .map((p) => VideoModel.fromJson(p))
                .toList()
            : [],
        type3 = parsedJson['data']['type3'] != null
            ? (parsedJson['data']['type3'] as List)
                .map((p) => VideoModel.fromJson(p))
                .toList()
            : [],
        error = "",
        code = 1,
        msg = "success";

  HomeResponse.withError(String errorValue)
      : featuredvideo = [],
        newProjects = [],
        category = [],
        luxury = [],
        types = [],
        places = [],
        holidayHomes = [],
        carRentals = [],
        type1 = [],
        type2 = [],
        type3 = [],
        error = errorValue,
        code = 0,
        msg = "fail";
}
