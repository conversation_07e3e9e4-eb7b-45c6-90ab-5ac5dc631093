import 'package:page/src/features/models/offer_model.dart';

class OffersResponse {
  final List<OfferModel> offers;

  final String error;
  final int code;
  final String msg;
  OffersResponse.fromJson(Map parsedJson)
      : offers = parsedJson['data'] != null
            ? (parsedJson['data'] as List)
                .map((p) => OfferModel.fromJson(p))
                .toList()
            : [],
        error = "",
        code = 1,
        msg = "success";
  OffersResponse.withError(String errorValue)
      : offers = [],
        error = errorValue,
        code = 0,
        msg = "fail";
}
