class ProfileResponse {
  final dynamic results;
  final String error;
  final int code;
  final String msg;
  ProfileResponse(this.results, this.error, this.code, this.msg);

  ProfileResponse.fromJson(Map<String, dynamic> parsedJson)
      : results = parsedJson['data'] ?? [],

//       :results=json['data'][0],
        error = "",
        code = parsedJson['code'],
        msg = parsedJson['msg'] ?? "";

  ProfileResponse.withError(String errorValue)
      : results = [],
        error = errorValue,
        code = 0,
        msg = "fail";
}

class AboutResponse {
  final dynamic results;
  final String error;
  final String code;
  final String msg;
  AboutResponse(this.results, this.error, this.code, this.msg);

  AboutResponse.fromJson(Map<String, dynamic> parsedJson)
      : results = parsedJson['data'],

//       :results=json['data'][0],
        error = "",
        code = parsedJson['code'],
        msg = parsedJson['msg'];

  AboutResponse.withError(String errorValue)
      : results = [],
        error = errorValue,
        code = "0",
        msg = "fail";
}
