import '../../features/models/project_models.dart';

class PropertyStatusResponse {
  final List<PropertyStatusModel> propertyStatuses;
  final String error;
  final int code;
  final String msg;

  PropertyStatusResponse.fromJson(Map parsedJson)
      : propertyStatuses = parsedJson['data'] != null
            ? (parsedJson['data'] as List)
                .map((p) => PropertyStatusModel.fromJson(p))
                .toList()
            : [],
        error = "",
        code = 1,
        msg = "success";

  PropertyStatusResponse.withError(String errorValue)
      : propertyStatuses = [],
        error = errorValue,
        code = 0,
        msg = "fail";
}
