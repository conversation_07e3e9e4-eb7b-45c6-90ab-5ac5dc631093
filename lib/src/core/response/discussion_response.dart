import '../../features/models/discussions.dart';

class DiscussionsResponse {
  final List<Discussions> dscussions;

  final String error;
  final int code;
  final String msg;
  DiscussionsResponse.fromJson(Map parsedJson)
      : dscussions = parsedJson['data'] != null
            ? (parsedJson['data'] as List)
                .map((p) => Discussions.fromJson(p))
                .toList()
            : [],
        error = "",
        code = 1,
        msg = "success";
  DiscussionsResponse.withError(String errorValue)
      : dscussions = [],
        error = errorValue,
        code = 0,
        msg = "fail";
}
