import '../../features/models/video_model.dart';

class CategoryResponse {
  final List<VideoModel> category;
  final List<VideoModel> featuredvideo;
  final String error;
  final int code;
  final String msg;

  CategoryResponse.fromJson(Map parsedJson)
      : category = parsedJson['data'].runtimeType == List
            ? (parsedJson['data'] as List)
                .map((p) => VideoModel.fromJson(p))
                .toList()
            : parsedJson['data']['data'] != null
                ? (parsedJson['data']['data']['data'] as List)
                    .map((p) => VideoModel.fromJson(p))
                    .toList()
                : [],
        featuredvideo = parsedJson['data'].runtimeType == List
            ? []
            : parsedJson['data']['featured'] != null
                ? (parsedJson['data']['featured']['data'] as List)
                    .map((p) => VideoModel.fromJson(p))
                    .toList()
                : [],
        // : category = parsedJson['data']['data'] != null
        //       ? (parsedJson['data']['data'] as List)
        //           .map((p) => VideoDetails.fromJson(p))
        //           .toList()
        //       : [],
        //   featuredvideo = parsedJson['data']['featured_video'] != null
        //       ? (parsedJson['data']['featured_video'] as List)
        //           .map((p) => FeaturedVideo.fromJson(p))
        //           .toList()
        //       : [],
        error = "",
        code = 1,
        msg = "success";

  CategoryResponse.withError(String errorValue)
      : category = [],
        featuredvideo = [],
        error = errorValue,
        code = 0,
        msg = "fail";
}

class CategoryetailsResponse {
  final Map<String, dynamic> results;
  final String error;
  final int code;
  final String msg;

  CategoryetailsResponse(this.results, this.error, this.code, this.msg);

  CategoryetailsResponse.fromJson(Map<String, dynamic> parsedJson)
      : results = parsedJson['data']['data'][0],

//       :results=json['data'][0],
        error = "",
        code = parsedJson['code'],
        msg = parsedJson['msg'] ?? '';

  CategoryetailsResponse.withError(String errorValue)
      : results = {},
        error = errorValue,
        code = 0,
        msg = "fail";
}

class ConfigurationResponse {
  final Map<String, dynamic> results;
  final String error;
  final int code;
  final String msg;

  ConfigurationResponse(this.results, this.error, this.code, this.msg);

  ConfigurationResponse.fromJson(Map<String, dynamic> parsedJson)
      : results = parsedJson,
        error = "",
        code = 1,
        msg = "success";

  ConfigurationResponse.withError(String errorValue)
      : results = {},
        error = errorValue,
        code = 0,
        msg = "fail";
}

class AboutResponse {
  final dynamic results;
  final String error;
  final String code;
  final String msg;

  AboutResponse(this.results, this.error, this.code, this.msg);

  AboutResponse.fromJson(Map<String, dynamic> parsedJson)
      : results = parsedJson['data'],

//       :results=json['data'][0],
        error = "",
        code = parsedJson['code'],
        msg = parsedJson['msg'];

  AboutResponse.withError(String errorValue)
      : results = [],
        error = errorValue,
        code = "0",
        msg = "fail";
}

class CategoryImagesResponse {
  final List<Categoryimages> results;
  final String error;
  final int code;
  final String msg;

  CategoryImagesResponse(this.results, this.error, this.code, this.msg);

  CategoryImagesResponse.fromJson(Map<String, dynamic> parsedJson)
      : results = parsedJson['data'] != null
            ? (parsedJson['data'] as List)
                .map((p) => Categoryimages.fromJson(p))
                .toList()
            : [],

//       :results=json['data'][0],
        error = "",
        code = parsedJson['code'],
        msg = parsedJson['msg'];

  CategoryImagesResponse.withError(String errorValue)
      : results = [],
        error = errorValue,
        code = 0,
        msg = "fail";
}

class CategoryReelsResponse {
  final List<Categoryreels> results;
  final String error;
  final int code;
  final String msg;

  CategoryReelsResponse(this.results, this.error, this.code, this.msg);

  CategoryReelsResponse.fromJson(Map<String, dynamic> parsedJson)
      : results = parsedJson['data'] != null
            ? (parsedJson['data'].runtimeType == List)
                ? (parsedJson['data'] as List)
                    .map((p) => Categoryreels.fromJson(p))
                    .toList()
                : (parsedJson['data']['data'] as List)
                    .map((p) => Categoryreels.fromJson(p))
                    .toList()
            : [],

//       :results=json['data'][0],
        error = "",
        code = parsedJson['code'] ?? 0,
        msg = parsedJson['msg'] ?? '';

  CategoryReelsResponse.withError(String errorValue)
      : results = [],
        error = errorValue,
        code = 0,
        msg = "fail";
}

class HolidayHomeResponse {
  final List<VideoModel> category;
  final List<VideoModel> featuredvideo;
  final String error;
  final int code;
  final String msg;

  HolidayHomeResponse.fromJson(Map parsedJson)
      : category = parsedJson['data'] != null
            ? (parsedJson['data']['data']['data'] as List)
                .map((p) => VideoModel.fromJson(p))
                .toList()
            : [],
        featuredvideo = parsedJson['data']['featured'] != null
            ? (parsedJson['data']['featured']['data'] as List)
                .map((p) => VideoModel.fromJson(p))
                .toList()
            : [],
        error = "",
        code = 1,
        msg = "success";

  HolidayHomeResponse.withError(String errorValue)
      : category = [],
        featuredvideo = [],
        error = errorValue,
        code = 0,
        msg = "fail";
}

class CarRentResponse {
  final List<VideoModel> category;
  final List<VideoModel> featuredvideo;
  final String error;
  final int code;
  final String msg;

  CarRentResponse.fromJson(Map parsedJson)
      : category = parsedJson['data'] != null
            ? (parsedJson['data']['data']['data'] as List)
                .map((p) => VideoModel.fromJson(p))
                .toList()
            : [],
        featuredvideo = parsedJson['data']['featured'] != null
            ? (parsedJson['data']['featured']['data'] as List)
                .map((p) => VideoModel.fromJson(p))
                .toList()
            : [],
        error = "",
        code = 1,
        msg = "success";

  CarRentResponse.withError(String errorValue)
      : category = [],
        featuredvideo = [],
        error = errorValue,
        code = 0,
        msg = "fail";
}

class CategoryFavoriteResponse {
  final List<VideoModel> category;
  final List<VideoModel> vibes;

  final String error;
  final int code;
  final String msg;

  CategoryFavoriteResponse.fromJson(Map<String, dynamic> parsedJson)
      : category = parsedJson['data'] != null
            ? (parsedJson['data']['category'] as List)
                .map((p) => VideoModel.fromJson(p['video_id']))
                .toList()
            : [],
        vibes = parsedJson['data'] != null
            ? (parsedJson['data']['vibes'] as List)
                .map((p) => VideoModel.fromJson(p['video_id']))
                .toList()
            : [],
        error = "",
        code = 1,
        msg = "success";

  CategoryFavoriteResponse.withError(String errorValue)
      : category = [],
        vibes = [],
        error = errorValue,
        code = 0,
        msg = "fail";
}
