import '../../features/models/requests.dart';

class RequestResponse {
  final dynamic results;
  final String error;
  final int code;
  final String msg;
  RequestResponse(this.results, this.error, this.code, this.msg);

  RequestResponse.fromJson(Map<String, dynamic> parsedJson)
      : results = parsedJson['data'],

//       :results=json['data'][0],
        error = "",
        code = parsedJson['code'],
        msg = parsedJson['message'];

  RequestResponse.withError(String errorValue)
      : results = null,
        error = errorValue,
        code = 0,
        msg = "fail";
}

class RequestsResponse {
  final List<Requests> plans;

  final String error;
  final int code;
  final String msg;
  RequestsResponse.fromJson(Map<String, dynamic> parsedJson)
      : plans = parsedJson['data'] != null
            ? (parsedJson['data'] as List)
                .map((p) => Requests.fromJson(p))
                .toList()
            : [],
        error = "",
        code = 1,
        msg = "success";
  RequestsResponse.withError(String errorValue)
      : plans = [],
        error = errorValue,
        code = 0,
        msg = "fail";
}
