import '../../features/models/plans.dart';

class PlanResponse {
  final List<Plans> plans;

  final String error;
  final int code;
  final String msg;
  PlanResponse.fromJson(Map<String, dynamic> parsedJson)
      : plans = parsedJson['data'] != null
            ? (parsedJson['data'] as List)
                .map((p) => Plans.fromJson(p))
                .toList()
            : [],
        error = "",
        code = 1,
        msg = "success";
  PlanResponse.withError(String errorValue)
      : plans = [],
        error = errorValue,
        code = 0,
        msg = "fail";
}

class PlanDetailsResponse {
  final List<PlanDetails> plans;
  final String planname;
  final String error;
  final int code;
  final String msg;
  PlanDetailsResponse.fromJson(Map<String, dynamic> parsedJson)
      : plans = parsedJson['data'] != null
            ? (parsedJson['data'] as List)
                .map((p) => PlanDetails.fromJson(p))
                .toList()
            : [],
        planname = parsedJson['name'],
        error = "",
        code = 1,
        msg = "success";
  PlanDetailsResponse.withError(String errorValue)
      : plans = [],
        planname = "",
        error = errorValue,
        code = 0,
        msg = "fail";
}

class PlanItemsResponse {
  final List<Plans> plans;
  final List<Plans> schedules;
  final List<PlanDetails> dates;
  final String? planName;
  final String error;
  final int code;
  final String msg;
  PlanItemsResponse.fromJson(Map parsedJson)
      : plans = parsedJson['data']['data'] != null
            ? (parsedJson['data']['data'] as List)
                .map((p) => Plans.fromJson(p))
                .toList()
            : [],
        schedules = parsedJson['data']['schedule'] != null
            ? (parsedJson['data']['schedule'] as List)
                .map((p) => Plans.fromJson(p))
                .toList()
            : [],
        dates = parsedJson['data']['days'] != null
            ? (parsedJson['data']['days'] as List)
                .map((p) => PlanDetails.fromJson(p))
                .toList()
            : [],
        planName = parsedJson['data']['name'] ?? "",
        error = "",
        code = 1,
        msg = "success";
  PlanItemsResponse.withError(String errorValue)
      : plans = [],
        schedules = [],
        dates = [],
        planName = "",
        error = errorValue,
        code = 0,
        msg = "fail";
}

class PlannameResponse {
  final Map<String, dynamic> data;
  final String error;
  final int code;
  final String msg;
  PlannameResponse.fromJson(Map<String, dynamic> parsedJson)
      : data = parsedJson['data'] ?? {},
        error = "",
        code = 1,
        msg = "success";
  PlannameResponse.withError(String errorValue)
      : data = {},
        error = errorValue,
        code = 0,
        msg = "fail";
}
