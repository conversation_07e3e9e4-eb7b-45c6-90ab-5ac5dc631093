import '../../features/models/content.dart';
import '../../features/models/features.dart';
import '../../features/models/locations.dart';

class LocationsResponse {
  final List<Locations> city;

  final String error;
  final int code;
  final String msg;
  LocationsResponse.fromJson(Map parsedJson)
      : city = parsedJson['data'] != null
            ? (parsedJson['data'] as List)
                .map((p) => Locations.fromJson(p))
                .toList()
            : [],
        error = "",
        code = 1,
        msg = "success";
  LocationsResponse.withError(String errorValue)
      : city = [],
        error = errorValue,
        code = 0,
        msg = "fail";
}

class FeaturesResponse {
  final List<Features> features;

  final String error;
  final int code;
  final String msg;
  FeaturesResponse.fromJson(Map parsedJson)
      : features = parsedJson['data'] != null
            ? (parsedJson['data'] as List)
                .map((p) => Features.fromJson(p))
                .toList()
            : [],
        error = "",
        code = 1,
        msg = "success";
  FeaturesResponse.withError(String errorValue)
      : features = [],
        error = errorValue,
        code = 0,
        msg = "fail";
}

class TypesResponse {
  final List<Types> types;

  final String error;
  final int code;
  final String msg;
  TypesResponse.fromJson(Map parsedJson)
      : types = parsedJson['data'] != null
            ? (parsedJson['data'] as List)
                .map((p) => Types.fromJson(p))
                .toList()
            : [],
        error = "",
        code = 1,
        msg = "success";
  TypesResponse.withError(String errorValue)
      : types = [],
        error = errorValue,
        code = 0,
        msg = "fail";
}

class AgentsResponse {
  final List<Agents> agents;

  final String error;
  final int code;
  final String msg;
  AgentsResponse.fromJson(Map parsedJson)
      : agents = parsedJson['data'] != null
            ? (parsedJson['data'] as List)
                .map((p) => Agents.fromJson(p))
                .toList()
            : [],
        error = "",
        code = 1,
        msg = "success";
  AgentsResponse.withError(String errorValue)
      : agents = [],
        error = errorValue,
        code = 0,
        msg = "fail";
}

class SizesAndRoomsResponse {
  final List<Sizes> holidayRoomSizes;
  final List<Sizes> propertiesRoomSizes;

  final String error;
  final int code;
  final String msg;
  SizesAndRoomsResponse.fromJson(Map parsedJson)
      : holidayRoomSizes = (parsedJson['holiday_home_rooms'] as List)
            .map((p) => Sizes.fromJson(p))
            .toList(),
        propertiesRoomSizes = (parsedJson['luxury_rooms'] as List)
            .map((p) => Sizes.fromJson(p))
            .toList(),
        error = "",
        code = 1,
        msg = "success";

  SizesAndRoomsResponse.withError(String errorValue)
      : holidayRoomSizes = [],
        propertiesRoomSizes = [],
        error = errorValue,
        code = 0,
        msg = "fail";
}

class SizesResponse {
  final List<Sizes> sizes;

  final String error;
  final int code;
  final String msg;
  SizesResponse.fromJson(Map parsedJson)
      : sizes = parsedJson['data'] != null
            ? (parsedJson['data'] as List)
                .map((p) => Sizes.fromJson(p))
                .toList()
            : [],
        error = "",
        code = 1,
        msg = "success";
  SizesResponse.withError(String errorValue)
      : sizes = [],
        error = errorValue,
        code = 0,
        msg = "fail";
}

class BrandResponse {
  final List<Brands> brands;

  final String error;
  final int code;
  final String msg;
  BrandResponse.fromJson(Map parsedJson)
      : brands = parsedJson['data'] != null
            ? (parsedJson['data'] as List)
                .map((p) => Brands.fromJson(p))
                .toList()
            : [],
        error = "",
        code = 1,
        msg = "success";
  BrandResponse.withError(String errorValue)
      : brands = [],
        error = errorValue,
        code = 0,
        msg = "fail";
}

class YearsResponse {
  final List<Years> years;

  final String error;
  final int code;
  final String msg;
  YearsResponse.fromJson(Map parsedJson)
      : years = parsedJson['data'] != null
            ? (parsedJson['data'] as List)
                .map((p) => Years.fromJson(p))
                .toList()
            : [],
        error = "",
        code = 1,
        msg = "success";
  YearsResponse.withError(String errorValue)
      : years = [],
        error = errorValue,
        code = 0,
        msg = "fail";
}

class PloicyResponse {
  final Map<String, dynamic> data;

  final String error;
  final int code;
  final String msg;
  PloicyResponse.fromJson(Map<String, dynamic> parsedJson)
      : data = parsedJson['data'],
        error = "",
        code = 1,
        msg = "success";
  PloicyResponse.withError(String errorValue)
      : data = {},
        error = errorValue,
        code = 0,
        msg = "fail";
}
