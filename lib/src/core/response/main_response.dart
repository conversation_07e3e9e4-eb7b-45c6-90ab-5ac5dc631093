import 'package:equatable/equatable.dart';

class MainModel extends Equatable {
  final int? id;
  final String? name;

  const MainModel({
    this.id,
    this.name,
  });

  factory MainModel.fromJson(Map<String?, dynamic> json) => MainModel(
        id: json['id'] as int?,
        name: json['name'].runtimeType == Map
            ? json['name']['en']
            : json['name'].toString(),
      );

  @override
  List<Object?> get props => [id, name];
}
