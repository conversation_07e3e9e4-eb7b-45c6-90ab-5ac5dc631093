class AuthResponse {
  final Map<String, dynamic> results;
  final String error;
  final int code;
  final String msg;
  final String token;
  final List<dynamic>? emailerror;
  final List<dynamic>? phoneError;
  AuthResponse(this.results, this.error, this.code, this.msg, this.token,
      this.emailerror, this.phoneError);

  AuthResponse.fromJson(Map<String, dynamic> json)
      : results = json['data'] ?? {},
        emailerror = json['errors'] != null ? json['errors']['email'] : null,
        error = "",
        code = json['code'] != null ? int.parse(json["code"].toString()) : 0,
        token = json['access_token'] ?? "",
        phoneError = json['errors'] != null ? json['errors']['phone'] : null,
        msg = json["message"] ?? "";

  AuthResponse.withError(String errorValue)
      : results = {},
        error = errorValue,
        code = 0,
        emailerror = [],
        phoneError = [],
        token = "",
        msg = errorValue;
  AuthResponse.fromJsonadd(Map<String, dynamic> json)
      : results = json['data'],
        error = "",
        code = json['code'] != null ? int.parse(json["code"].toString()) : 0,
        token = "",
        emailerror = [],
        phoneError = [],
        msg = json["msg"];
  AuthResponse.fromForgotPass(Map<String, dynamic> json)
      : results = json,
        error = "",
        code = json['code'] != null ? int.parse(json["code"].toString()) : 0,
        token = "",
        emailerror = [],
        phoneError = [],
        msg = json["msg"] ?? "";
}
