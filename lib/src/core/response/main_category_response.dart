import 'package:equatable/equatable.dart';

class MainCategoryModel extends Equatable {
  final int? id;
  final String? name;
  final String? nameEn;
  final String? icon;

  const MainCategoryModel({
    this.id,
    this.name,
    this.nameEn,
    this.icon,
  });

  factory MainCategoryModel.fromJson(Map<String?, dynamic>? json) {
    if(json == null) return const MainCategoryModel();
    return MainCategoryModel(
        id: json['id'] as int?,
        name: json['name'].runtimeType == Map
            ? json['name']['en']
            : json['name'].toString(),
        nameEn: json['name'].runtimeType == Map
            ? json['name']['en']
            : json['name_en'],
        icon: json['icon'] as String?);
  }

  Map<String?, dynamic> toJson() {
    final Map<String?, dynamic> data = <String?, dynamic>{};
    data['id'] = id;
    data['name'] = name;
    data['icon'] = icon;
    return data;
  }

  @override
  List<Object?> get props => [id, name, icon];
}
