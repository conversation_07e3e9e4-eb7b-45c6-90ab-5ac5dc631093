import '../../features/models/reels.dart';

class ReelsResponse {
  final List<Reels> category;

  final String error;
  final int code;
  final String msg;
  ReelsResponse.fromJson(Map parsedJson)
      : category = parsedJson['data'] != null
            ? (parsedJson['data'] as List)
                .map((p) => Reels.fromJson(p))
                .toList()
            : [],
        error = "",
        code = 1,
        msg = "success";
  ReelsResponse.withError(String errorValue)
      : category = [],
        error = errorValue,
        code = 0,
        msg = "fail";
}

class OneReelResponse {
  final Reels? category;

  final String error;
  final int code;
  final String msg;
  OneReelResponse.fromJson(Map parsedJson)
      : category = parsedJson['data'] != null
            ? Reels.fromJson(parsedJson['data'][0])
            : null,
        error = "",
        code = 1,
        msg = "success";
  OneReelResponse.withError(String errorValue)
      : category = null,
        error = errorValue,
        code = 0,
        msg = "fail";
}
