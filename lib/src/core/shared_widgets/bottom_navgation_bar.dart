import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:page/src/core/config/constants.dart';
import 'package:page/src/features/views/holiday/holiday_search.dart';
import 'package:page/src/features/views/holiday/widgets/holiday_filter.dart';
import 'package:page/src/features/views/map/show_on_map/properties_map_page.dart';
import 'package:page/src/features/views/projects/projects_screen.dart';
import 'package:video_player/video_player.dart';

import '../../features/controllers/auth_controller.dart';
import '../../features/views/account/account.dart';
import '../../features/views/home/<USER>';
import '../localization/app_localizations.dart';

// ignore: must_be_immutable
class CustomBottomNavgationBar extends StatefulWidget {
  int cuurent;
  VideoPlayerController? videoPlayerController;

  CustomBottomNavgationBar(this.cuurent, {this.videoPlayerController});

  @override
  _BottomNavgationBar createState() => _BottomNavgationBar();
}

class _BottomNavgationBar extends State<CustomBottomNavgationBar> {
  bool islogin = false;
  AuthController authController = AuthController();

  void isloggedin() async {
    setState(() {
      islogin = authController.isLogged;
    });
  }

  @override
  void initState() {
    isloggedin();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final Widget svg1 = SizedBox(
        child: SvgPicture.asset(
      'assets/noun_Maps_-1.svg',
      semanticsLabel: 'Acme Logo',
      fit: BoxFit.cover,
    ));
    final Widget svg2 = SizedBox(
        child: SvgPicture.asset(
      'assets/noun_vacation plan_-1.svg',
      semanticsLabel: 'Acme Logo',
      fit: BoxFit.cover,
      color: primaryColor,
    ));

    final Widget svg4 = SizedBox(
        child: SvgPicture.asset(
      'assets/noun_vacation plan_3073953.svg',
      semanticsLabel: 'Acme Logo',
      fit: BoxFit.cover,
    ));
    final Widget svg3 = SizedBox(
        height: 19,
        width: 19,
        child: SvgPicture.asset(
          'assets/search.svg',
          semanticsLabel: 'Acme Logo',
          fit: BoxFit.cover,
        ));
    final Widget svg5 = SizedBox(
        height: 19,
        width: 19,
        child: SvgPicture.asset(
          'assets/search.svg',
          color: primaryColor,
          colorFilter:
              const ColorFilter.mode(Color(0xFF27b4a8), BlendMode.srcIn),
          // 'assets/noun_Video_91748.svg',
          semanticsLabel: 'Acme Logo',
          fit: BoxFit.cover,
        ));
    final Widget svg6 = SizedBox(
        child: SvgPicture.asset(
      'assets/noun_Maps_4170806.svg',
      semanticsLabel: 'Acme Logo',
      color: primaryColor,
      fit: BoxFit.cover,
    ));
    SizedBox(
        child: SvgPicture.asset(
      'assets/noun_account_1404638.svg',
      semanticsLabel: 'Acme Logo',
      fit: BoxFit.cover,
    ));
    final Widget svg8 = SizedBox(
        child: SvgPicture.asset(
      'assets/01.svg',
      semanticsLabel: 'Acme Logo',
      fit: BoxFit.cover,
    ));
    final Widget svg9 = SizedBox(
        child: SvgPicture.asset(
      'assets/noun_Home_4083569.svg',
      semanticsLabel: 'Acme Logo',
      color: primaryColor,
      fit: BoxFit.cover,
    ));
    return BottomNavigationBar(
      currentIndex: widget.cuurent,
      type: BottomNavigationBarType.fixed,
      selectedFontSize: 12,
      unselectedFontSize: 12,
      selectedLabelStyle: const TextStyle(color: Color(0xff51565B)),
      unselectedLabelStyle: const TextStyle(color: Color(0xff51565B)),
      showUnselectedLabels: true,
      showSelectedLabels: true,
      unselectedItemColor: const Color(0xff51565B),
      selectedItemColor: primaryColor,
      // selectedItemColor: const Color(0xFF27b4a8),
      onTap: (index) async {
        resetHolidayFilter();
        if (widget.videoPlayerController != null) {
          setState(() {
            widget.videoPlayerController!.pause();
            widget.videoPlayerController!.dispose();
          });
        }
        if (widget.cuurent != index) {
          widget.cuurent = index;
          print(widget.cuurent);
          if (widget.cuurent == 0) {
            Navigator.of(context).pushReplacement(
                MaterialPageRoute(builder: (context) => const Home()));
          }
          if (widget.cuurent == 1) {
            Navigator.of(context).pushReplacement(
              MaterialPageRoute(
                  builder: (BuildContext context) => const HolidaySearchPage()),
              // builder: (BuildContext context) => const ReelsScreen()),
            );
          }
          if (widget.cuurent == 2) {
            // await Permission.location.request().then((value) {
            Navigator.of(context).pushReplacement(MaterialPageRoute(
                builder: (BuildContext context) => const PropertiesMapPage()));
            // builder: (BuildContext context) => SearchMap()));
            // });
          }

          if (widget.cuurent == 3) {
            // if (authController.islogin == true) {
            Navigator.of(context).pushReplacement(
              MaterialPageRoute(
                  builder: (BuildContext context) => const NewProjectScreen(
                        fromBottomNav: true,
                      )),
              // builder: (BuildContext context) => const MyPlans()),
            );
            // } else {
            //   snackbar(
            //       AppLocalizations.of(context).translate('Please login first'));
            // }
          }

          if (widget.cuurent == 4) {
            Navigator.of(context).pushReplacement(
              MaterialPageRoute(
                  builder: (BuildContext context) => const Account()),
            );
          }
        }
      },
      backgroundColor: Colors.white,

      // type: BottomNavigationBarType.fixed,

      // backgroundColor: Colors.white,
      items: <BottomNavigationBarItem>[
        BottomNavigationBarItem(
          icon: widget.cuurent == 0 ? svg9 : svg8,
          label: AppLocalizations.of(context).translate('Home'),
        ),
        BottomNavigationBarItem(
          icon: widget.cuurent == 1 ? svg5 : svg3,
          label: AppLocalizations.of(context).translate('Search2'),
          // label: AppLocalizations.of(context).translate('Reels'),
        ),
        BottomNavigationBarItem(
          icon: widget.cuurent == 2 ? svg6 : svg1,
          label: AppLocalizations.of(context).translate('Maps'),
        ),
        BottomNavigationBarItem(
          icon: widget.cuurent == 3 ? svg2 : svg4,
          label: AppLocalizations.of(context).translate('Latest Projects'),
        ),
        BottomNavigationBarItem(
          icon: widget.cuurent == 4
              ? Image.asset(
                  'assets/noun_account_1404638.png',
                  width: 20,
                  height: 20,
                  color: const Color(0xFF27b4a8),
                )
              : Image.asset(
                  'assets/noun_account_1404638.png',
                  width: 20,
                  height: 20,
                ),
          label: AppLocalizations.of(context).translate('Account'),
        ),
      ],
    );
  }
}
