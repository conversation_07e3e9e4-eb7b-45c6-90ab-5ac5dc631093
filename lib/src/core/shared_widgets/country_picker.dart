import 'package:country_pickers/country.dart';
import 'package:country_pickers/country_pickers.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';

import '../localization/app_language.dart';

void showCountryPickerDialog(
    BuildContext context, Function(Country) onCountrySelected) {
  var lang =
      Provider.of<AppLanguage>(context, listen: false).appLocal.languageCode;
  final isEnglish = lang == 'en';

  showDialog(
    context: context,
    builder: (context) => Theme(
        data: Theme.of(context).copyWith(primaryColor: Colors.pink),
        child: CountryPickerDialog(
            priorityList: [
              CountryPickerUtils.getCountryByIsoCode('AE'),
              CountryPickerUtils.getCountryByIsoCode('SA'),
              CountryPickerUtils.getCountryByIsoCode('EG'),
            ],
            titlePadding: const EdgeInsets.all(8.0),
            searchCursorColor: Colors.pinkAccent,
            searchInputDecoration:
                InputDecoration(hintText: isEnglish ? 'Search' : 'بحث...'),
            isSearchable: true,
            title: Text(isEnglish ? 'Choose Country' : 'اختر الدولة'),
            onValuePicked: (Country country) => onCountrySelected(country),
            itemBuilder: buildDialogItem)),
  );
}

Widget countryCodeField(BuildContext context, Country country) => SizedBox(
      child: Container(
        width: 85.w,
        padding: const EdgeInsets.symmetric(horizontal: 12),
        child: FittedBox(
          fit: BoxFit.scaleDown,
          child: Row(
            children: <Widget>[
              CountryPickerUtils.getDefaultFlagImage(country),
              10.horizontalSpace,
              Text(
                "+${country.phoneCode}",
              ),
              10.horizontalSpace,
              Container(
                width: 1,
                height: 20,
                color: Colors.grey,
              ),
            ],
          ),
        ),
      ),
    );

Widget buildDialogItem(Country country) => Row(
      children: <Widget>[
        CountryPickerUtils.getDefaultFlagImage(country),
        const SizedBox(width: 8.0),
        Text("+${country.phoneCode}"),
        const SizedBox(width: 8.0),
        Flexible(child: Text(country.name))
      ],
    );
