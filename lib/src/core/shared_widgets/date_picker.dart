import 'package:datetime_picker_formfield/datetime_picker_formfield.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';

class BuildDateFieldWidget extends StatelessWidget {
  final TextEditingController controller;
  final format;
  const BuildDateFieldWidget(this.controller, {Key? key, required this.format})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    final Widget svg1 = SizedBox(
        width: 20,
        height: 20,
        child: SvgPicture.asset(
          'assets/icons8-calendar.svg',
          semanticsLabel: 'Acme Logo',
          fit: BoxFit.cover,
        ));
    return Container(
        height: 50,
        decoration: BoxDecoration(
            color: Colors.white, borderRadius: BorderRadius.circular(3)),
        child: Container(
            decoration: BoxDecoration(
                borderRadius: const BorderRadius.all(Radius.circular(5)),
                border: Border.all(color: Colors.black12, width: 1.0)),
            child: DateTimeField(
                controller: controller,
                format: format,
                initialValue: DateTime.now(),
                decoration: InputDecoration(
                    hintText: 'DD/MM/YY',
                    hintStyle:
                        const TextStyle(color: Colors.grey, fontSize: 12),
                    prefixIcon: Padding(
                      padding: const EdgeInsets.all(8.0),
                      child: svg1,
                    ),
                    prefixIconConstraints: const BoxConstraints(
                      minWidth: 15,
                      minHeight: 15,
                    ),
                    border: InputBorder.none),
                onShowPicker: (context, currentValue) {
                  return showDatePicker(
                      context: context,
                      firstDate: DateTime(1900),
                      initialDate: currentValue ?? DateTime.now(),
                      lastDate: DateTime(2100));
                })));
  }
}
