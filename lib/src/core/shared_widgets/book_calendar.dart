import 'package:calendar_date_picker2/calendar_date_picker2.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:page/src/core/localization/app_localizations.dart';
import 'package:page/src/core/services/rms_service.dart';
import 'package:page/src/core/shared_widgets/shared_widgets.dart';
import 'package:page/src/core/utils/print_services.dart';

class BookCalendar extends StatefulWidget {
  final ValueNotifier<List<DateTime?>> selectedDates;
  final ValueNotifier<String> arrivalTime;
  final ValueNotifier<String> departureTime;
  final Function(List<DateTime?>)? onDateChanged;
  final int? categoryId;

  const BookCalendar({
    super.key,
    required this.selectedDates,
    this.onDateChanged,
    required this.arrivalTime,
    required this.departureTime,
    required this.categoryId,
  });

  @override
  State<BookCalendar> createState() => _BookCalendarState();
}

class _BookCalendarState extends State<BookCalendar> {
  bool isLoading = false;
  DateTime selectedMonth = DateTime.now();
  Map<String, bool> closedDays = {};
  Map<String, num> prices = {};

  Future<void> getData(DateTime month) async {
    setState(() {
      isLoading = true;
    });

    final startDate =
        DateFormat('yyyy-MM-dd').format(DateTime(month.year, month.month, 1));
    final endDate = DateFormat('yyyy-MM-dd')
        .format(DateTime(month.year, month.month + 1, 1));

    final data = await RmsService.getClosedCalendarDaysByMonthForOneCategory(
      categoryId: widget.categoryId,
      startDate: startDate,
      endDate: endDate,
    );

    closedDays = data.$1;
    prices = data.$2;

    setState(() {
      isLoading = false;
      selectedMonth = month;
    });
  }

  @override
  void initState() {
    super.initState();
    getData(DateTime.now());
  }

  Future<void> _selectTime(
      BuildContext context, ValueNotifier<String> timeNotifier) async {
    final TimeOfDay? picked = await showTimePicker(
      context: context,
      initialTime: TimeOfDay.now(),
    );
    if (picked != null) {
      final now = DateTime.now();
      final formattedTime = DateFormat('HH:mm').format(
          DateTime(now.year, now.month, now.day, picked.hour, picked.minute));
      timeNotifier.value = formattedTime;
    }
  }

  bool _selectableDayPredicate(DateTime date) {
    final now = DateTime.now();
    final dateString = DateFormat('yyyy-MM-dd').format(date);

    // Only future dates that are not closed
    return date.isAfter(now) && (closedDays[dateString] ?? false) == false;
  }

  bool _isRangeSelectable(List<DateTime?> dates) {
    if (dates.isEmpty || dates.first == null || dates.last == null) {
      return false;
    }
    final startDate = dates.first!;
    final endDate = dates.last!;
    for (DateTime date = startDate;
        date.isBefore(endDate) || date.isAtSameMomentAs(endDate);
        date = date.add(const Duration(days: 1))) {
      if (!_selectableDayPredicate(date)) {
        return false;
      }
    }
    return true;
  }

  @override
  Widget build(BuildContext context) {
    if (isLoading) return Center(child: buildLoadingWidget());

    String formatPrice(num price) {
      if (price >= 1000) {
        return '${(price / 1000).toStringAsFixed(1)}k';
      }
      return price.round().toString();
    }

    final isDecember2024 =
        selectedMonth.year == 2024 && selectedMonth.month == 12;
    final isMarch2025 = selectedMonth.year == 2025 && selectedMonth.month == 3;
    final isMarch2026 = selectedMonth.year == 2026 && selectedMonth.month == 3;
    final isNovember2026 =
        selectedMonth.year == 2026 && selectedMonth.month == 11;
    final isMay2027 = selectedMonth.year == 2027 && selectedMonth.month == 5;
    final isAug2027 = selectedMonth.year == 2027 && selectedMonth.month == 8;
    final isJan2028 = selectedMonth.year == 2028 && selectedMonth.month == 1;
    final isJuly2028 = selectedMonth.year == 2028 && selectedMonth.month == 7;
    final oct2028 = selectedMonth.year == 2028 && selectedMonth.month == 10;
    final isApr2029 = selectedMonth.year == 2029 && selectedMonth.month == 4;
    final isJuly2029 = selectedMonth.year == 2029 && selectedMonth.month == 7;
    final isDec2029 = selectedMonth.year == 2029 && selectedMonth.month == 12;
    final isSep2030 = selectedMonth.year == 2030 && selectedMonth.month == 9;
    final isDec2030 = selectedMonth.year == 2030 && selectedMonth.month == 12;

    final haveNormalSpace = isDecember2024 ||
        isMarch2026 ||
        isNovember2026 ||
        isMay2027 ||
        isAug2027 ||
        isJan2028 ||
        isJuly2028 ||
        oct2028 ||
        isApr2029 ||
        isJuly2029 ||
        isDec2029 ||
        isSep2030 ||
        isDec2030;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Stack(
          alignment: Alignment.bottomCenter,
          children: [
            Padding(
              padding: EdgeInsets.only(
                  bottom: haveNormalSpace
                      ? 30
                      : isMarch2025
                          ? 40
                          : 0),
              child: CalendarDatePicker2(
                displayedMonthDate: selectedMonth,
                config: CalendarDatePicker2WithActionButtonsConfig(
                  calendarViewMode: CalendarDatePicker2Mode.day,
                  selectableDayPredicate: _selectableDayPredicate,
                  firstDayOfWeek: 1,
                  calendarType: CalendarDatePicker2Type.range,
                  selectedDayTextStyle: const TextStyle(
                      color: Colors.white, fontWeight: FontWeight.w700),
                  selectedDayHighlightColor: Theme.of(context).primaryColor,
                  centerAlignModePicker: true,
                  customModePickerIcon: const Padding(
                    padding: EdgeInsets.all(8.0),
                    child: Icon(Icons.arrow_drop_down_circle_outlined),
                  ),
                ),
                value: widget.selectedDates.value,
                onValueChanged: (dates) {
                  final isSelectEndDateAfterOneDayOnlyFromStartDate =
                      dates.first!.add(const Duration(days: 1)) == dates.last;

                  if (dates.length == 2 && !_isRangeSelectable(dates)) {
                    setState(() {
                      widget.selectedDates.value = [dates.last];
                    });
                    snackbar(AppLocalizations.of(context).translate(
                        'You have chosen a range that includes closed or unavailable days'));
                  } else if (isSelectEndDateAfterOneDayOnlyFromStartDate) {
                    setState(() {
                      widget.selectedDates.value = [dates.last];
                    });

                    snackbar(AppLocalizations.of(context)
                        .translate('Minimum stay 2 nights'));
                  } else {
                    widget.selectedDates.value = dates;
                    if (widget.onDateChanged != null) {
                      widget.onDateChanged!(dates);
                    }
                  }
                },
                onDisplayedMonthChanged: getData,
              ),
            ),
            // Row(
            //   mainAxisAlignment: MainAxisAlignment.spaceBetween,
            //   children: [
            //     Column(
            //       crossAxisAlignment: CrossAxisAlignment.start,
            //       children: [
            //         Text(
            //           '* ${AppLocalizations.of(context).translate('The nights you will stay')}',
            //           style: const TextStyle(
            //             fontSize: 12,
            //             color: Colors.black,
            //             fontWeight: FontWeight.bold,
            //           ),
            //         ),
            //         const SizedBox(height: 5),
            //         Text(
            //           '* ${AppLocalizations.of(context).translate('Minimum stay 2 nights')}',
            //           style: const TextStyle(
            //             fontSize: 12,
            //             color: Colors.black,
            //             fontWeight: FontWeight.bold,
            //           ),
            //         ),
            //       ],
            //     ),
            //     const SizedBox(
            //       width: 10,
            //     ),
            //     if (widget.selectedDates.value.length == 2 &&
            //         !isStartDateSameOfEndDate)
            //       Text(
            //         '${AppLocalizations.of(context).translate('End Date')}: ${DateFormat('dd/MM/yyyy').format(widget.selectedDates.value.last!.add(Duration(days: 1)))}',
            //         style: const TextStyle(
            //           fontSize: 10,
            //           color: Colors.black,
            //         ),
            //       ),
            //   ],
            // ),
            Align(
              alignment:
                  isEng(context) ? Alignment.bottomLeft : Alignment.bottomRight,
              child: Text(
                '* ${AppLocalizations.of(context).translate('Minimum stay 2 nights')}',
                style: const TextStyle(
                  fontSize: 12,
                  color: Colors.black,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: 20),
        Row(
          children: [
            Expanded(
              child: GestureDetector(
                onTap: () => _selectTime(context, widget.arrivalTime),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      AppLocalizations.of(context).translate('Arrival Time'),
                      style: const TextStyle(fontSize: 13),
                    ),
                    const SizedBox(
                      height: 5,
                    ),
                    Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 10, vertical: 15),
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(5),
                        border: Border.all(color: Colors.black12, width: 1.0),
                      ),
                      child: ValueListenableBuilder<String>(
                        valueListenable: widget.arrivalTime,
                        builder: (context, value, child) {
                          return Row(
                            children: [
                              const Icon(Icons.access_time),
                              const SizedBox(width: 5),
                              Text(
                                value.isEmpty
                                    ? AppLocalizations.of(context)
                                        .translate('Arrival Time')
                                    : value,
                                style: const TextStyle(
                                    fontSize: 16, color: Colors.black),
                              ),
                            ],
                          );
                        },
                      ),
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(width: 10),
            Expanded(
              child: GestureDetector(
                onTap: () => _selectTime(context, widget.departureTime),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      AppLocalizations.of(context).translate('Departure Time'),
                      style: const TextStyle(fontSize: 13),
                    ),
                    const SizedBox(
                      height: 5,
                    ),
                    Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 10, vertical: 15),
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(5),
                        border: Border.all(color: Colors.black12, width: 1.0),
                      ),
                      child: ValueListenableBuilder<String>(
                        valueListenable: widget.departureTime,
                        builder: (context, value, child) {
                          return Row(
                            children: [
                              const Icon(Icons.access_time),
                              const SizedBox(width: 5),
                              Text(
                                value.isEmpty
                                    ? AppLocalizations.of(context)
                                        .translate('Departure Time')
                                    : value,
                                style: const TextStyle(
                                    fontSize: 16, color: Colors.black),
                              ),
                            ],
                          );
                        },
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: 20),
      ],
    );
  }
}

// class BookCalendar extends StatefulWidget {
//   final ValueNotifier<List<DateTime?>> selectedDates;
//   final ValueNotifier<String> arrivalTime;
//   final ValueNotifier<String> departureTime;
//   final Function(List<DateTime?>)? onDateChanged;
//
//   const BookCalendar({
//     super.key,
//     required this.selectedDates,
//     this.onDateChanged,
//     required this.arrivalTime,
//     required this.departureTime,
//   });
//
//   @override
//   State<BookCalendar> createState() => _BookCalendarState();
// }
//
// class _BookCalendarState extends State<BookCalendar> {
//   bool isLoading = false;
//   Map<String, bool> closedDays = {};
//   DateTime selectedMonth = DateTime.now();
//
//   Future<void> getData(DateTime month) async {
//     setState(() {
//       isLoading = true;
//     });
//
//     final startDate =
//         DateFormat('yyyy-MM-dd').format(DateTime(month.year, month.month, 1));
//     final endDate = DateFormat('yyyy-MM-dd')
//         .format(DateTime(month.year, month.month + 1, 0));
//
//     closedDays = await RmsService.getClosedCalendarDaysByMonth(
//       categoryId: 1, //fixme
//       startDate: startDate,
//       endDate: endDate,
//     );
//
//     setState(() {
//       isLoading = false;
//       selectedMonth = month;
//     });
//   }
//
//   @override
//   void initState() {
//     super.initState();
//     getData(DateTime.now());
//   }
//
//   Future<void> _selectTime(
//       BuildContext context, ValueNotifier<String> timeNotifier) async {
//     final TimeOfDay? picked = await showTimePicker(
//       context: context,
//       initialTime: TimeOfDay.now(),
//     );
//     if (picked != null) {
//       final now = DateTime.now();
//       final formattedTime = DateFormat('HH:mm').format(
//           DateTime(now.year, now.month, now.day, picked.hour, picked.minute));
//       timeNotifier.value = formattedTime;
//     }
//   }
//
//   @override
//   Widget build(BuildContext context) {
//     if (isLoading) return Center(child: buildLoadingWidget());
//
//     return Column(
//       children: [
//         CalendarDatePicker2(
//           displayedMonthDate: selectedMonth,
//           config: CalendarDatePicker2WithActionButtonsConfig(
//             selectableDayPredicate: (DateTime val) {
//               final dateString = DateFormat('yyyy-MM-dd').format(val);
//               return true;
//             },
//             firstDayOfWeek: 1,
//             calendarType: CalendarDatePicker2Type.range,
//             selectedDayTextStyle: const TextStyle(
//                 color: Colors.white, fontWeight: FontWeight.w700),
//             selectedDayHighlightColor: Theme.of(context).primaryColor,
//             centerAlignModePicker: true,
//             customModePickerIcon: const Padding(
//               padding: EdgeInsets.all(8.0),
//               child: Icon(Icons.arrow_drop_down_circle_outlined),
//             ),
//           ),
//           value: widget.selectedDates.value,
//           onValueChanged: (dates) {
//             widget.selectedDates.value = dates;
//             if (widget.onDateChanged != null) widget.onDateChanged!(dates);
//           },
//           onDisplayedMonthChanged: getData,
//         ),
//         Row(
//           children: [
//             Expanded(
//               child: GestureDetector(
//                 onTap: () => _selectTime(context, widget.arrivalTime),
//                 child: Column(
//                   crossAxisAlignment: CrossAxisAlignment.start,
//                   children: [
//                     Text(
//                       AppLocalizations.of(context).translate('Arrival Time'),
//                       style: const TextStyle(fontSize: 13),
//                     ),
//                     const SizedBox(
//                       height: 5,
//                     ),
//                     Container(
//                       padding: const EdgeInsets.symmetric(
//                           horizontal: 10, vertical: 15),
//                       decoration: BoxDecoration(
//                         borderRadius: BorderRadius.circular(5),
//                         border: Border.all(color: Colors.black12, width: 1.0),
//                       ),
//                       child: ValueListenableBuilder<String>(
//                         valueListenable: widget.arrivalTime,
//                         builder: (context, value, child) {
//                           return Row(
//                             children: [
//                               const Icon(Icons.access_time),
//                               const SizedBox(width: 5),
//                               Text(
//                                 value.isEmpty
//                                     ? AppLocalizations.of(context)
//                                         .translate('Arrival Time')
//                                     : value,
//                                 style: const TextStyle(
//                                     fontSize: 16, color: Colors.black),
//                               ),
//                             ],
//                           );
//                         },
//                       ),
//                     ),
//                   ],
//                 ),
//               ),
//             ),
//             const SizedBox(width: 10),
//             Expanded(
//               child: GestureDetector(
//                 onTap: () => _selectTime(context, widget.departureTime),
//                 child: Column(
//                   crossAxisAlignment: CrossAxisAlignment.start,
//                   children: [
//                     Text(
//                       AppLocalizations.of(context).translate('Departure Time'),
//                       style: const TextStyle(fontSize: 13),
//                     ),
//                     const SizedBox(
//                       height: 5,
//                     ),
//                     Container(
//                       padding: const EdgeInsets.symmetric(
//                           horizontal: 10, vertical: 15),
//                       decoration: BoxDecoration(
//                         borderRadius: BorderRadius.circular(5),
//                         border: Border.all(color: Colors.black12, width: 1.0),
//                       ),
//                       child: ValueListenableBuilder<String>(
//                         valueListenable: widget.departureTime,
//                         builder: (context, value, child) {
//                           return Row(
//                             children: [
//                               const Icon(Icons.access_time),
//                               const SizedBox(width: 5),
//                               Text(
//                                 value.isEmpty
//                                     ? AppLocalizations.of(context)
//                                         .translate('Departure Time')
//                                     : value,
//                                 style: const TextStyle(
//                                     fontSize: 16, color: Colors.black),
//                               ),
//                             ],
//                           );
//                         },
//                       ),
//                     ),
//                   ],
//                 ),
//               ),
//             ),
//           ],
//         ),
//         const SizedBox(height: 20),
//       ],
//     );
//   }
// }
