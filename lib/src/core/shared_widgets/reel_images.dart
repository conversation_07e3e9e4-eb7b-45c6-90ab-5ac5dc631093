import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:video_player/video_player.dart';

import '../../features/models/video_model.dart';
import '../localization/app_localizations.dart';
import '../response/album_response.dart';
import '../services/api.dart';
import 'shared_widgets.dart';

AlbumsResponse? albums;
int imageindex = 0;
int? imageindex2;
// List<Categoryreels> videos = [];

moreimagesrelated(BuildContext context, int id,
    VideoPlayerController videoPlayerController) async {
  progrsss(context);
  pr!.show();
  await Api.getAlbums(id).then((value) {
    albums = null;

    albums = value;
  });

  buildAlbomGalary(context, videoPlayerController,
      albums!.data != null && albums!.data!.isNotEmpty ? 0 : null);
}

buildAlbomGalary(BuildContext context,
    VideoPlayerController videoPlayerController, int? index) {
  pr!.hide();
  log('asdlkajdlkdn ${index}');
  String selectedImage =
      index == null ? '' : albums!.data![index].photos![0].image ?? '';
  Future<void> future = showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => albums!.data!.isNotEmpty
          ? StatefulBuilder(builder: (context, StateSetter stateSetter) {
              stateSetter(() {
                imageindex2 = imageindex + 1;
              });
              return Padding(
                  padding: EdgeInsets.only(
                      bottom: MediaQuery.of(context).viewInsets.bottom),
                  child: Container(
                      height: MediaQuery.of(context).size.height * 0.85,
                      decoration: BoxDecoration(
                          color: const Color(0xffF5F6F7),
                          borderRadius: const BorderRadius.only(
                              topLeft: Radius.circular(25.0),
                              topRight: Radius.circular(25.0)),
                          border: Border.all(color: Colors.black, width: 1.0)),
                      padding: const EdgeInsets.only(top: 18),
                      child: StatefulBuilder(
                        builder: (context, StateSetter setStateImage) {
                          return SingleChildScrollView(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Align(
                                    alignment: Alignment.center,
                                    child: Container(
                                        height: 5,
                                        width: 30,
                                        color: const Color(0xffD2D4D6))),
                                Container(
                                  height: 450,
                                  padding: const EdgeInsets.all(10),
                                  alignment: Alignment.center,
                                  margin: const EdgeInsets.only(
                                    top: 35,
                                    left: 20,
                                    right: 20,
                                  ),
                                  decoration: BoxDecoration(
                                    color: Colors.white,
                                    borderRadius: BorderRadius.circular(7),
                                  ),
                                  child: Container(
                                    decoration: BoxDecoration(
                                      borderRadius: BorderRadius.circular(7),
                                      image: DecorationImage(
                                        fit: BoxFit.cover,
                                        image: NetworkImage(selectedImage),
                                      ),
                                    ),
                                  ),
                                ),
                                const SizedBox(height: 10),
                                Container(
                                  height: 140,
                                  padding: const EdgeInsets.only(
                                      top: 10, left: 10, bottom: 10, right: 10),
                                  margin: const EdgeInsets.only(
                                    left: 20,
                                  ),
                                  decoration: BoxDecoration(
                                    color: Colors.white,
                                    borderRadius: BorderRadius.circular(7),
                                  ),
                                  child: ListView.separated(
                                      shrinkWrap: true,
                                      scrollDirection: Axis.horizontal,
                                      itemCount:
                                          albums!.data![index!].photos!.length,
                                      itemBuilder: (context, ind) {
                                        return InkWell(
                                          onTap: () {
                                            setStateImage(() {
                                              selectedImage = albums!
                                                  .data![index]
                                                  .photos![ind]
                                                  .image!;
                                            });
                                          },
                                          child: Container(
                                              width: 85,
                                              height: 130,
                                              decoration: BoxDecoration(
                                                borderRadius:
                                                    BorderRadius.circular(7),
                                                image: DecorationImage(
                                                  fit: BoxFit.cover,
                                                  image: NetworkImage(albums!
                                                      .data![index]
                                                      .photos![ind]
                                                      .image!),
                                                ),
                                              )),
                                        );
                                      },
                                      separatorBuilder: (context, ind) {
                                        return const SizedBox(
                                          width: 8,
                                        );
                                      }),
                                ),
                              ],
                            ),
                          );
                        },
                      )));
            })
          : Container(
              height: MediaQuery.of(context).size.height * 0.80,
              decoration: BoxDecoration(
                  color: const Color(0xffffffff),
                  borderRadius: const BorderRadius.only(
                      topLeft: Radius.circular(25.0),
                      topRight: Radius.circular(25.0)),
                  border: Border.all(color: Colors.black, width: 1.0)),
              child: nodatafound(
                  AppLocalizations.of(context).translate('No images to show')),
            ));
  future.then((void value) => _closeModal(videoPlayerController));
}

buildLuxuryAlbomGalary(BuildContext context,
    VideoPlayerController videoPlayerController, albumsData) {
  pr!.hide();

  final List<Categoryimages> albums = albumsData;

  log('asdksajldkdsan ${albums.length}');

  String selectedImage = albums[0].name ?? '';

  Future<void> future = showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => albums.isNotEmpty
          ? StatefulBuilder(builder: (context, StateSetter stateSetter) {
              stateSetter(() {
                imageindex2 = imageindex + 1;
              });
              return Padding(
                  padding: EdgeInsets.only(
                      bottom: MediaQuery.of(context).viewInsets.bottom),
                  child: Container(
                      height: MediaQuery.of(context).size.height * 0.85,
                      decoration: BoxDecoration(
                          color: const Color(0xffF5F6F7),
                          borderRadius: const BorderRadius.only(
                              topLeft: Radius.circular(25.0),
                              topRight: Radius.circular(25.0)),
                          border: Border.all(color: Colors.black, width: 1.0)),
                      padding: const EdgeInsets.only(top: 18),
                      child: StatefulBuilder(
                        builder: (context, StateSetter setStateImage) {
                          return SingleChildScrollView(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Align(
                                    alignment: Alignment.center,
                                    child: Container(
                                        height: 5,
                                        width: 30,
                                        color: const Color(0xffD2D4D6))),
                                Container(
                                  height: 450,
                                  padding: const EdgeInsets.all(10),
                                  alignment: Alignment.center,
                                  margin: const EdgeInsets.only(
                                    top: 35,
                                    left: 20,
                                    right: 20,
                                  ),
                                  decoration: BoxDecoration(
                                    color: Colors.white,
                                    borderRadius: BorderRadius.circular(7),
                                  ),
                                  child: Container(
                                    decoration: BoxDecoration(
                                      borderRadius: BorderRadius.circular(7),
                                      image: DecorationImage(
                                        fit: BoxFit.cover,
                                        image: NetworkImage(selectedImage),
                                      ),
                                    ),
                                  ),
                                ),
                                const SizedBox(height: 10),
                                Container(
                                  height: 140,
                                  padding: const EdgeInsets.only(
                                      top: 10, left: 10, bottom: 10, right: 10),
                                  margin: const EdgeInsets.only(
                                    left: 20,
                                  ),
                                  decoration: BoxDecoration(
                                    color: Colors.white,
                                    borderRadius: BorderRadius.circular(7),
                                  ),
                                  child: ListView.separated(
                                      shrinkWrap: true,
                                      scrollDirection: Axis.horizontal,
                                      itemCount: albums.length,
                                      itemBuilder: (context, index) {
                                        return InkWell(
                                          onTap: () {
                                            setStateImage(() {
                                              selectedImage =
                                                  albums[index].name!;
                                            });
                                          },
                                          child: Container(
                                              width: 85,
                                              height: 130,
                                              decoration: BoxDecoration(
                                                borderRadius:
                                                    BorderRadius.circular(7),
                                                image: DecorationImage(
                                                  fit: BoxFit.cover,
                                                  image: NetworkImage(
                                                      albums[index].name!),
                                                ),
                                              )),
                                        );
                                      },
                                      separatorBuilder: (context, ind) {
                                        return const SizedBox(
                                          width: 8,
                                        );
                                      }),
                                ),
                              ],
                            ),
                          );
                        },
                      )));
            })
          : Container(
              height: MediaQuery.of(context).size.height * 0.80,
              decoration: BoxDecoration(
                  color: const Color(0xffffffff),
                  borderRadius: const BorderRadius.only(
                      topLeft: Radius.circular(25.0),
                      topRight: Radius.circular(25.0)),
                  border: Border.all(color: Colors.black, width: 1.0)),
              child: nodatafound(
                  AppLocalizations.of(context).translate('No images to show')),
            ));
  future.then((void value) => _closeModal(videoPlayerController));
}

void _closeModal(VideoPlayerController videoPlayerController) {
  print('modal closed');
  try {
    videoPlayerController.play();
  } catch (e) {}
}

// buildmoreimage(
//     BuildContext context, VideoPlayerController videoPlayerController) {
//   pr!.hide();
//   Future<void> future = showModalBottomSheet(
//       context: context,
//       isScrollControlled: true,
//       backgroundColor: Colors.transparent,
//       builder: (context) => albums!.data!.isNotEmpty
//           ? StatefulBuilder(builder: (context, StateSetter stateSetter) {
//               stateSetter(() {
//                 imageindex2 = imageindex + 1;
//               });
//               return Padding(
//                   padding: EdgeInsets.only(
//                       bottom: MediaQuery.of(context).viewInsets.bottom),
//                   child: Container(
//                     height: MediaQuery.of(context).size.height * 0.85,
//                     decoration: BoxDecoration(
//                         color: const Color(0xffF5F6F7),
//                         borderRadius: const BorderRadius.only(
//                             topLeft: Radius.circular(25.0),
//                             topRight: Radius.circular(25.0)),
//                         border: Border.all(color: Colors.black, width: 1.0)),
//                     child: SingleChildScrollView(
//                         child: Column(
//                       children: [
//                         for (var i = 0; i < albums!.data!.length; i++)
//                           Padding(
//                             padding: const EdgeInsets.symmetric(
//                                 horizontal: 15, vertical: 25),
//                             child: ExpandablePanel(
//                                 controller: i == 0
//                                     ? ExpandableController(
//                                         initialExpanded: true)
//                                     : null,
//                                 header: Padding(
//                                   padding: const EdgeInsets.all(8.0),
//                                   child: Text(
//                                     albums!.data![i].name!,
//                                     style: const TextStyle(fontSize: 18),
//                                   ),
//                                 ),
//                                 expanded: Builder(
//                                   builder: (context) {
//                                     int currentIndex = 0;
//                                     return StatefulBuilder(
//                                       builder:
//                                           (BuildContext context, setStateDots) {
//                                         print(albums!.data![i].photos);
//                                         return InkWell(
//                                           onTap: () {
//                                             buildAlbomGalary(context,
//                                                 videoPlayerController, i);
//                                           },
//                                           child: Column(
//                                             children: [
//                                               CarouselSlider(
//                                                 options: CarouselOptions(
//                                                   enlargeCenterPage: true,
//                                                   enableInfiniteScroll: false,
//                                                   aspectRatio: 1,
//                                                   viewportFraction: 1,
//                                                   height: 400,
//                                                   onPageChanged:
//                                                       (index, reason) {
//                                                     setStateDots(() {
//                                                       currentIndex = index;
//                                                     });
//                                                   },
//                                                 ),
//                                                 items: albums!.data![i].photos!
//                                                     .map((image) {
//                                                   return Builder(
//                                                     builder:
//                                                         (BuildContext context) {
//                                                       return ClipRRect(
//                                                         borderRadius:
//                                                             BorderRadius
//                                                                 .circular(7),
//                                                         child: MainCachedImage(
//
//                                                               image.image!,
//                                                           height: 300,
//                                                           width: MediaQuery.of(
//                                                                       context)
//                                                                   .size
//                                                                   .width *
//                                                               0.9,
//                                                           fit: BoxFit.cover,
//                                                         ),
//                                                       );
//                                                     },
//                                                   );
//                                                 }).toList(),
//                                               ),
//                                               const SizedBox(height: 10),
//                                               DotsIndicator(
//                                                 dotsCount: albums!
//                                                     .data![i].photos!.length,
//                                                 position: currentIndex,
//                                                 decorator: const DotsDecorator(
//                                                   color: Colors
//                                                       .black87, // Inactive color
//                                                   activeColor: Colors.redAccent,
//                                                 ),
//                                               ),
//                                             ],
//                                           ),
//                                         );
//                                       },
//                                     );
//                                   },
//                                 ),
//                                 collapsed: SizedBox()),
//                           ),
//                       ],
//                     )),
//                   ));
//             })
//           : Container(
//               height: MediaQuery.of(context).size.height * 0.80,
//               decoration: BoxDecoration(
//                   color: const Color(0xffffffff),
//                   borderRadius: const BorderRadius.only(
//                       topLeft: Radius.circular(25.0),
//                       topRight: Radius.circular(25.0)),
//                   border: Border.all(color: Colors.black, width: 1.0)),
//               child: nodatafound(
//                   AppLocalizations.of(context).translate('No images to show')),
//             ));
//   future.then((void value) => _closeModal(videoPlayerController));
// }
