import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:lottie/lottie.dart';
import 'package:page/src/features/controllers/language_controller.dart';
import 'package:progress_dialog_null_safe/progress_dialog_null_safe.dart';

import '../localization/app_localizations.dart';

void snackbar(String msg) {
  Get.showSnackbar(GetSnackBar(
    duration: 3.seconds,
    margin: const EdgeInsets.all(8.0),
    backgroundColor: const Color(0xffE04E4D),
    borderColor: const Color(0xffE04E4D),
    snackStyle: SnackStyle.FLOATING,
    messageText: Text(
      msg,
      style: const TextStyle(color: Colors.white),
    ),
    borderRadius: 5.0,
    isDismissible: true,
  ));
}

void snackbar2(String msg) {
  Get.showSnackbar(GetSnackBar(
    duration: 3.seconds,
    margin: const EdgeInsets.all(8.0),
    backgroundColor: const Color(0xff2AC294),
    borderColor: const Color(0xff2AC294),
    snackStyle: SnackStyle.FLOATING,
    messageText: Text(
      msg,
      style: const TextStyle(color: Colors.white),
    ),
    borderRadius: 5.0,
    isDismissible: true,
  ));
}

ProgressDialog? pr;

void progrsss(BuildContext context) {
  final isEng = isEnglish(context);

  pr = ProgressDialog(
    context,
    textDirection: TextDirection.ltr,
    textAlign: isEng ? TextAlign.left : TextAlign.right,
    progressWidgetAlignment:
        isEng ? Alignment.centerLeft : Alignment.centerRight,
  );
  pr!.update(
    progress: 50.0,
    message: AppLocalizations.of(context).translate('Please Wait'),
    progressWidget: Container(
        padding: const EdgeInsets.all(8.0),
        child: const CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(Color(0xff233549)))),
    maxProgress: 100.0,
    progressTextStyle: const TextStyle(
        color: Colors.black, fontSize: 13.0, fontWeight: FontWeight.w400),
    messageTextStyle: const TextStyle(
        color: Colors.black, fontSize: 19.0, fontWeight: FontWeight.w600),
  );
}

Widget buildErrorWidget(String error) {
  return Center(
      child: Column(
    mainAxisAlignment: MainAxisAlignment.center,
    children: [
      Text("$error"),
    ],
  ));
}

Widget buildLoadingWidget() {
  return Center(
    child: Lottie.asset(
      'assets/lf30_editor_llvb8wei.json',
      height: 80,
      width: 200,
    ),
  );
}

Widget nodatafound(String msg) {
  final Widget svg2 = SizedBox(
      width: 88,
      height: 88.0,
      child: SvgPicture.asset(
        'assets/Group 6222.svg',
        semanticsLabel: 'Acme Logo',
        fit: BoxFit.cover,
      ));
  return Center(
    child: Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        const SizedBox(
          height: 20,
        ),
        svg2,
        const SizedBox(
          height: 20,
        ),
        Text(
          msg,
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
      ],
    ),
  );
}

Widget buildnodatafavourite(String msg1, String msg2) {
  final Widget svg2 = SizedBox(
      width: 88,
      height: 88.0,
      child: SvgPicture.asset(
        'assets/Group 7271.svg',
        semanticsLabel: 'Acme Logo',
        fit: BoxFit.cover,
      ));
  return Column(
    // crossAxisAlignment: CrossAxisAlignment.start,
    children: [
      const SizedBox(
        height: 20,
      ),
      svg2,
      const SizedBox(
        height: 20,
      ),
      Text(
        msg1,
        style: const TextStyle(fontWeight: FontWeight.bold),
      ),
      const SizedBox(
        height: 10,
      ),
      Text(
        msg2,
        style: const TextStyle(color: Color(0xff51565B), fontSize: 12),
      ),
      const SizedBox(
        height: 40,
      )
    ],
  );
}
