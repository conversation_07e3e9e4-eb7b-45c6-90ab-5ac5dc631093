import 'package:flutter/material.dart';

class BackButtonWidget extends StatelessWidget {
  final Function? onTap;
  const BackButtonWidget({Key? key, this.onTap}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(5),
      decoration: BoxDecoration(
          color: Colors.black.withOpacity(0.3),
          borderRadius: BorderRadius.circular(10)),
      child: GestureDetector(
          onTap: () {
            Navigator.pop(context);

            if (onTap != null) {
              onTap!();
            }
          },
          child: const Icon(
            Icons.arrow_back,
            color: Colors.white,
          )),
    );
  }
}
