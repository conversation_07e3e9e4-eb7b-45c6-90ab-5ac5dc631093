import 'package:flutter/material.dart';
import 'package:page/src/core/config/constants.dart';
import 'package:page/src/core/localization/app_localizations.dart';

class MainFeaturedContainer extends StatelessWidget {
  final Widget child;
  final String label;

  const MainFeaturedContainer(
      {super.key, required this.child, this.label = 'FeturedVideos'});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 20),
      decoration: BoxDecoration(
        // color: const Color(0xffE0C28A).withOpacity(0.35),
        border: Border(
          bottom: BorderSide(color: primaryColor, width: 1),
          // bottom: BorderSide(color: Color(0xffE0C28A), width: 1),
        ),
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            primaryColor.withOpacity(0.25),
            primaryColor.withOpacity(0.15),
            primaryColor.withOpacity(0.1),
          ],
          // colors: [
          //   const Color(0xffD8B77F).withOpacity(0.2),
          //   const Color(0xffE0C28A).withOpacity(0.4),
          //   const Color(0xffE0C28A).withOpacity(0.5),
          // ],
        ),
      ),
      child: Column(
        children: [
          Container(
              padding: const EdgeInsets.symmetric(horizontal: 20),
              child: Row(
                children: [
                  Image.asset(
                    'assets/icons/featured.png',
                    height: 20,
                    fit: BoxFit.cover,
                  ),
                  const SizedBox(
                    width: 10,
                  ),
                  Text(
                    AppLocalizations.of(context).translate(label),
                    style: const TextStyle(
                        color: Color(0xff51565B),
                        fontSize: 17,
                        fontWeight: FontWeight.bold),
                  ),
                ],
              )),
          const SizedBox(
            height: 20,
          ),
          child,
        ],
      ),
    );
  }
}
