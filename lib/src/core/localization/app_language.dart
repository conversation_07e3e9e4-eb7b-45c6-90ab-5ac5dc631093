import 'dart:developer';

import 'dart:developer';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:shared_preferences/shared_preferences.dart';

class AppLanguage extends ChangeNotifier {
  Locale _appLocale = const Locale('en');

  Locale get appLocal => _appLocale;
  Future<String> fetchLocale() async {
    var prefs = await SharedPreferences.getInstance();
    var languageCode = prefs.getString('language_code');
    var isFirstTime = prefs.getBool('is_first_time') ?? true;

    if (languageCode == null || isFirstTime) {
      // Get device language on first app open
      String deviceLanguage = 'en'; // default fallback
      try {
        final locale = WidgetsBinding.instance.platformDispatcher.locale;
        deviceLanguage = locale.languageCode;

        // Only support 'ar' and 'en', default to 'en' for other languages
        if (deviceLanguage != 'ar' && deviceLanguage != 'en') {
          deviceLanguage = 'en';
        }
      } catch (e) {
        deviceLanguage = 'en'; // fallback if detection fails
      }

      await prefs.setString("language_code", deviceLanguage);
      await prefs.setBool('is_first_time', false);
      _appLocale = Locale(deviceLanguage);
      return deviceLanguage;
    }

    _appLocale = Locale(languageCode);
    return languageCode;
  }

  void changeLanguage(Locale type) async {
    print(type);
    var prefs = await SharedPreferences.getInstance();
    if (type == const Locale("ar")) {
      _appLocale = const Locale("ar");
      await prefs.setString('language_code', 'ar');
      await prefs.setString('countryCode', '');
    } else {
      _appLocale = const Locale("en");
      await prefs.setString('language_code', 'en');
      await prefs.setString('countryCode', 'US');
    }
    Get.updateLocale(_appLocale);
    prefs.getString('language_code');
    notifyListeners();
  }

  static void getDefaultLanguage() async {
    // This method is now handled by fetchLocale() in main.dart
    // Keeping for backward compatibility but logic moved to fetchLocale()
    SharedPreferences _prefs = await SharedPreferences.getInstance();
    var languageCode = _prefs.getString('language_code');
    if (languageCode == null) {
      _prefs.setString("language_code", "en");
    }
  }

  // cancel dispose
  // @override
  // void dispose() {
  //   super.dispose();
  // }
}
