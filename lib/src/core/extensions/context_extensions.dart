part of 'extensions.dart';

extension ThemeExtensions on BuildContext {
  //? Get color shortcuts
  // Color get primaryColor => ConstColors.primaryColor;
  //
  // Color get accentColor => ConstColors.accentColor;
}

extension Localization on BuildContext {
  //? Get the current language
  AppLocalizations get local => AppLocalizations.of(this);

  //? Get the current language code
  String get languageCode => Localizations.localeOf(this).languageCode;

  //? Get the current language is Arabic
  bool get isEnglish => languageCode == 'en';
}

extension NavigationExtensions on BuildContext {
  void to(Widget widget) => Navigator.of(this).push(
        MaterialPageRoute(builder: (_) => widget),
      );

  void toNamed(String routeName) => Navigator.of(this).pushNamed(routeName);

  void back() => Navigator.of(this).pop();
}

extension SizeExensions on BuildContext {
  //? Get size shortcuts
  double get height => MediaQuery.of(this).size.height;

  double get width => MediaQuery.of(this).size.width;

  //? Get space shortcuts -- Horizontal & Vertical
  SizedBox get fieldSpace => 15.verticalSpace;

  //? Horizontal Spaces
  Widget get hSmallSpace => 5.horizontalSpace;

  Widget get hMediumSpace => 10.horizontalSpace;

  Widget get hLargeSpace => 20.horizontalSpace;

  //? Vertical Spaces
  Widget get vSmallSpace => 5.verticalSpace;

  Widget get vMediumSpace => 10.verticalSpace;

  Widget get vLargeSpace => 20.verticalSpace;
}
