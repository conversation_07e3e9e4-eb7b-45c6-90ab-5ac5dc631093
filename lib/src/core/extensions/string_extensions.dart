part of 'extensions.dart';

extension StringExtenstions on String {
  String get formatDate {
    if (this == null || isEmpty) return '';
    return DateFormat('yyyy-MM-dd').format(DateTime.parse(this));
  }

  String get formatDateWithTime {
    if (this == null || isEmpty) return '';
    return DateFormat('yyyy-MM-dd hh:mm a').format(DateTime.parse(this));
  }

  String get formatDateTimeToApi {
    if (this == null || isEmpty) return '';
    return DateFormat('yyyy-MM-dd').format(DateTime.parse(this));
  }

  DateTime? get formatDateTimeToApiToDate {
    if (this == null || isEmpty) return null;
    return DateFormat('yyyy-MM-dd').parse(this);
  }

  int toInt() {
    return int.parse(this);
  }

  double? toDouble() {
    return double.tryParse(this);
  }

  num? toNum() {
    return num.tryParse(this);
  }

  String? get filterMultiDropDownList {
    if (this == null || isEmpty) return null;
    return this.replaceAll('[', '').replaceAll(']', '');
  }
}
