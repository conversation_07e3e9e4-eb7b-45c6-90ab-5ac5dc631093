import 'package:flutter/cupertino.dart';

// const fileurl = "http://api.dubaipage.ae/images/";
// const videourl = "http://api.dubaipage.ae/videos/";
// const pathUrl = "http://*************/api/"; //? Live
// const pathUrl = "https://api.dubaipage.ae/api/"; //? Live
// const pathUrl = "https://api2.dubaipage.ae/api/"; //? New Live
const pathUrl = "https://www.aqardxbapp.com/api/"; //? New Demo
// const pathUrl = "https://www.demo.dubaipage.ae/api/"; //? New Demo
// const pathUrl = "https://api.dubaipage.ae/new/public/api/"; //? New Live
// const pathUrl = "http://newapi.dubaipage.ae/api/"; //? Old Demo

double baseWebWidth = 180;

double baseWebWidth2 = baseWebWidth * 1.2;

Color primaryColor = const Color(0xFF27b4a8);
Color lightPrimaryColor = const Color(0xFF27b4a8).withOpacity(.1);
Color secondaryColor = const Color(0xFFeeeeee);

class AppConstants {
  //? Main Categories IDs
  static const int hotelsId = 1;
  static const int restaurantsId = 2;
  static const int activitiesId = 3;
  static const int coffeeShopsId = 4;
  static const int holidayHomesId = 8;
  static const int carRentalsId = 6;
  static const int destinationsId = 7;
  static const int propertiesId = 8;
  static const int chaletsId = 9;
  static const int projectsId = 10;

  //? Configuration
  static const String youtube = "youtube";
  static const String instagram = "instagram";
  static const String policy = "policy";
}

String sortPrice(num number, [bool decimals = true]) {
  String fullNumber = '';

  String numberString = number.toString();
  List<String> parts = numberString.split('.');
  String intNumber = parts[0];
  String decimal = (parts.length > 1) ? parts[1] : '00';

  switch (intNumber.length) {
    case 2:
      fullNumber = subString(intNumber, 0, 1) + subString(intNumber, 1);
      break;
    case 3:
      fullNumber = subString(intNumber, 0, 2) + subString(intNumber, 2);
      break;
    case 4:
      fullNumber = '${subString(intNumber, 0, 1)},${subString(intNumber, 1)}';
      break;
    case 5:
      fullNumber = '${subString(intNumber, 0, 2)},${subString(intNumber, 2)}';
      break;
    case 6:
      fullNumber = '${subString(intNumber, 0, 3)},${subString(intNumber, 3)}';
      break;
    case 7:
      fullNumber =
          '${subString(intNumber, 0, 1)},${subString(intNumber, 1, 4)},${subString(intNumber, 4)}';
      break;
    case 8:
      fullNumber =
          '${subString(intNumber, 0, 2)},${subString(intNumber, 2, 5)},${subString(intNumber, 5)}';
      break;
    case 9:
      fullNumber =
          '${subString(intNumber, 0, 3)},${subString(intNumber, 3, 6)},${subString(intNumber, 6)}';
      break;
    case 10:
      fullNumber =
          '${subString(intNumber, 0, 1)},${subString(intNumber, 1, 4)},${subString(intNumber, 4, 7)},${subString(intNumber, 7)}';
      break;
    case 11:
      fullNumber =
          '${subString(intNumber, 0, 2)},${subString(intNumber, 2, 5)},${subString(intNumber, 5, 8)},${subString(intNumber, 8)}';
      break;
    case 12:
      fullNumber =
          '${subString(intNumber, 0, 3)},${subString(intNumber, 3, 6)},${subString(intNumber, 6, 9)},${subString(intNumber, 9)}';
      break;
    default:
      fullNumber = intNumber;
  }

  if (decimals && decimal != '0' && decimal != '00') {
    fullNumber = '$fullNumber.$decimal';
  }

  return fullNumber;
}

// String sortPrice(num number, [bool decimals = true]) {
//   if (number % 1 == 0) number = number.toDouble();
//
//   String fullNumber = '';
//
//   String numberString = number.toString();
//
//   List<String> parts = numberString.split('.');
//   String intNumber = parts[0];
//   String decimal = (parts.length > 1) ? parts[1] : '00';
//
//   switch (intNumber.length) {
//     case 2:
//       fullNumber = subString(intNumber, 0, 1) + subString(intNumber, 1);
//       break;
//     case 3:
//       fullNumber = subString(intNumber, 0, 2) + subString(intNumber, 2);
//       break;
//     case 4:
//       fullNumber = '${subString(intNumber, 0, 1)},${subString(intNumber, 1)}';
//       break;
//     case 5:
//       fullNumber = '${subString(intNumber, 0, 2)},${subString(intNumber, 2)}';
//       break;
//     case 6:
//       fullNumber = '${subString(intNumber, 0, 3)},${subString(intNumber, 3)}';
//       break;
//     case 7:
//       fullNumber =
//           '${subString(intNumber, 0, 1)},${subString(intNumber, 1, 4)},${subString(intNumber, 4)}';
//       break;
//     case 8:
//       fullNumber =
//           '${subString(intNumber, 0, 2)},${subString(intNumber, 2, 5)},${subString(intNumber, 5)}';
//       break;
//     case 9:
//       fullNumber =
//           '${subString(intNumber, 0, 3)},${subString(intNumber, 3, 6)},${subString(intNumber, 6)}';
//       break;
//     case 10:
//       fullNumber =
//           '${subString(intNumber, 0, 1)},${subString(intNumber, 1, 4)},${subString(intNumber, 4, 7)},${subString(intNumber, 7)}';
//       break;
//     case 11:
//       fullNumber =
//           '${subString(intNumber, 0, 2)},${subString(intNumber, 2, 5)},${subString(intNumber, 5, 8)},${subString(intNumber, 8)}';
//       break;
//     case 12:
//       fullNumber =
//           '${subString(intNumber, 0, 3)},${subString(intNumber, 3, 6)},${subString(intNumber, 6, 9)},${subString(intNumber, 9)}';
//       break;
//     default:
//       fullNumber = intNumber;
//   }
//
//   if (decimals) fullNumber = '$fullNumber.$decimal';
//
//   return fullNumber;
// }

String subString(String number, [int start = 0, int? end]) =>
    number.substring(start, end);
