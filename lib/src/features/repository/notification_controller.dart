import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
// import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:get/get.dart';

// import 'package:get_storage/get_storage.dart';

import '../models/notification.dart';

Future<void> firebaseMessagingBackgroundHandler(RemoteMessage message) async {
  await Firebase.initializeApp();

  // notificationController.getNotification(message);
}

class NotificationController extends GetxController {
  // GetStorage box = GetStorage('notificstions');
  RxInt? _notificationsNumber;

  get notificationsNumber => this._notificationsNumber?.value;

  set notificationsNumber(value) => this._notificationsNumber?.value = value;

  List<NotificationModel> notifications = [];

  // FlutterLocalNotificationsPlugin? flutterLocalNotificationsPlugin;

  final FirebaseMessaging firebaseMessaging = FirebaseMessaging.instance;

  // static Future<void> firebaseMessagingBackgroundHandler(
  //     RemoteMessage message) async {
  //   log("Handling a background message: ${message.messageId}");
  //   await handlingNotification();
  // }

  // static Future<void> handlingNotification() async {
  //   FirebaseMessaging messaging = FirebaseMessaging.instance;
  //
  //   if (GetPlatform.isIOS) {
  //     await messaging.requestPermission(
  //       alert: true,
  //       announcement: false,
  //       badge: true,
  //       carPlay: false,
  //       criticalAlert: false,
  //       provisional: false,
  //       sound: true,
  //     );
  //   }
  //
  //   await FirebaseMessaging.instance
  //       .setForegroundNotificationPresentationOptions(
  //     alert: true,
  //     badge: true,
  //     sound: true,
  //   );
  // }

  static Future<void> _firebaseMessagingBackgroundHandler(
      RemoteMessage message) async {
    if (Firebase.apps.isEmpty) {
      await Firebase.initializeApp();
    }
  }

  //! Request Notification Permissions & Init
  static Future<void> handleNotifications() async {
    final fcm = FirebaseMessaging.instance;

    await fcm.requestPermission(
      alert: true,
      badge: true,
      provisional: false,
      sound: true,
    );

    await fcm.setForegroundNotificationPresentationOptions(
        badge: true, alert: true, sound: true);

    FirebaseMessaging.onBackgroundMessage(_firebaseMessagingBackgroundHandler);
  }

  Future<String> getToken() async {
    String? token;
    try {
      token = await firebaseMessaging.getToken(
        vapidKey: "BGpdLRs......",
      );
    } catch (e) {
      print(e);
    }
    return token ?? '';
  }
}
