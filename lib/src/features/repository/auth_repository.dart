import '../../core/response/auth_response.dart';
import '../../core/services/api.dart';

class AuthRepository {
  Future<AuthResponse> register(String phone, String password, String fullname,
          String email, String token, String phonecode) =>
      Api.register(phone, password, fullname, email, token, phonecode);

  Future<AuthResponse> login(String email, String password, String? token) =>
      Api.login(email, password, token);

  Future<dynamic> updateFcmToken(String? token) => Api.updateToken(token);

  Future<AuthResponse> sendrequestpassword(String email) =>
      Api.sendrequestpassword(email);

  Future<AuthResponse> verfiycode(String email, String code) =>
      Api.verfiycode(email, code);

  Future<AuthResponse> resetpassword(String email, String password) =>
      Api.resetpassword(email, password);
}
