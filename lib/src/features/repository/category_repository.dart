import 'package:page/src/core/config/constants.dart';

import '../../core/response/category_response.dart';
import '../../core/services/api.dart';

class CategoryRepository {
  //CategoryApiProvider _apiProvider = CategoryApiProvider();
  Future<CategoryResponse> getmaincategory(
          int page, int size, String key, String category) =>
      Api.getmainCategory(page, size, key, category);
  Future<CategoryResponse> getfeaturedvideo(
          int page, int size, String key, String category) =>
      Api.getfeaturedvideo(page, size, key, category);
  Future<CategoryetailsResponse?> getmaincategorydetails(int id) =>
      Api.getmainCategorydetails(id);
  Future<CategoryImagesResponse> getmainCategoryimages(int id) =>
      Api.getmainCategoryimages(id);
  Future<CategoryResponse> getholidaygome(int page, int size, String key) =>
      Api.getmainCategory(
          page, size, key, AppConstants.holidayHomesId.toString());
}
