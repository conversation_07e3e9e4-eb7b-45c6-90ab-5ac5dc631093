import '../../core/response/plan_response.dart';
import '../../core/services/api.dart';

class PlanRepository {
  Future<PlanResponse?> getplan(int page, int size) =>
      Api.getplanuser(page, size);
  Future<PlanDetailsResponse> getplandetails(int id) =>
      Api.getplanuserdetails(id);
  Future<PlanResponse> getplanuseritems(int page, int size) =>
      Api.getplanuseritems(page, size);
  Future<PlanItemsResponse> getplanudetailsseritems(int id, var date) =>
      Api.getplanudetailsseritems(id, date);
  Future<PlanItemsResponse> getplanuserdates(int id) =>
      Api.getplanuserdates(id);
  Future<PlanItemsResponse> getplandetailsschedules(int id, var date) =>
      Api.getplanudetailsseritems(id, date);
}
