import '../../core/response/category_response.dart';
import '../../core/response/profile_response.dart';
import '../../core/services/api.dart';

class ProfileRepository {
  Future<ProfileResponse> getProfile() => Api.getProfile();

  Future<ProfileResponse> deleteAccount() => Api.deleteAccount();

  Future<ProfileResponse> editProfile(
    String name,
    String email,
    String phone,
    String phoneCode,
  ) =>
      Api.editProfile(name, email, phone, phoneCode);
  Future<CategoryFavoriteResponse> getfavouritecategory(int page, int size) =>
      Api.getfavouritecategory(page, size);
  // Future<CategoryFavoriteResponse> getfavouriteuserreels(int page, int size) =>
  //     Api.getfavouriteuserreels(page, size);
}
