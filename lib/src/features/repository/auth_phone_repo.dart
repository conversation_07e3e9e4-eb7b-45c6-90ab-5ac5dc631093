import 'dart:developer';

import 'package:firebase_auth/firebase_auth.dart';

class AuthPhoneRepo {
  final FirebaseAuth auth = FirebaseAuth.instance;

  int? _resendToken;
  String? _verificationID;

  String? get verificationID => _verificationID;

  Future<void> sendOtp(String phone) async {
    try {
      await auth.verifyPhoneNumber(
          phoneNumber: phone,
          forceResendingToken: _resendToken,
          verificationCompleted: _verificationCompleted,
          verificationFailed: (FirebaseAuthException e) {
            throw e;
          },
          codeSent: _codeSent,
          codeAutoRetrievalTimeout: _codeAutoRetrievalTimeout,
          timeout: const Duration(seconds: 120));
    } catch (e) {
      rethrow;
    }
  }

  //? Verification Completed ------------------------
  Future<void> _verificationCompleted(
      PhoneAuthCredential phoneAuthCredential) async {
    await auth.signInWithCredential(phoneAuthCredential).then((value) {
      log("You are logged in successfully");
    });
  }

  //? Code Sent ------------------------
  void _codeSent(String verificationId, int? resendToken) {
    _verificationID = verificationId;
    log('verificationId XXX :$verificationId');
    _resendToken = resendToken;
  }

  //? Code Auto Retrieval Timeout ------------------------
  void _codeAutoRetrievalTimeout(String verificationId) {
    _verificationID = verificationId;
  }

  Future<UserCredential> verifyOTP(String otp) async {
    PhoneAuthCredential credential = PhoneAuthProvider.credential(
        verificationId: _verificationID!, smsCode: otp);

    return await auth.signInWithCredential(credential).catchError((error) {
      throw error;
    });
  }
}
