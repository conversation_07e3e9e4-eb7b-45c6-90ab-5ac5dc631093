import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:page/src/core/config/constants.dart';
import 'package:page/src/core/shared_widgets/main_featured_container.dart';
import 'package:page/src/features/views/places/widgets/places_filter.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

import '../../../core/localization/app_localizations.dart';
import '../../../core/services/api.dart';
import '../../../core/shared_widgets/shared_widgets.dart';
import '../../controllers/auth_controller.dart';
import '../../controllers/content_controller.dart';
import '../../controllers/currency_controller.dart';
import '../../models/video_model.dart';
import 'widgets/places_featured_videos.dart';
import 'widgets/places_list.dart';

int? placesCurrentvalue2;
List<int> placesFeatures = [];
int? selectedPlacesFeature;

// ignore: must_be_immutable
class Places extends StatefulWidget {
  final int? id;

  final time;
  final int? itemid;
  final date;

  Places({this.id, this.time, this.itemid, this.date});

  @override
  _Places createState() => _Places();
}

class _Places extends State<Places> {
  final TextEditingController searchController = TextEditingController();
  List<VideoModel> results = [];
  List<VideoModel> featuredvideo = [];

  int code2 = 0;
  String msg2 = 'loading';
  RefreshController _refreshControllerwait =
      RefreshController(initialRefresh: false);
  int pagenumber = 1;
  ContentController contentController = ContentController();
  CurrencyController currencyController = CurrencyController();
  GlobalKey<ScaffoldState> _drawerKey = GlobalKey();
  AuthController authController = AuthController();
  String? lang;

  filtermaincategory(int location, int starts, double startprice,
      double endprice, int? feature, String category, int type) async {
    await Api.filtermaincategory(
            location, [], startprice, endprice, [feature], category, type)
        .then((value) {
      value != null
          ? setState(() {
              // results.clear();
              // results.addAll(value.category);
              results = value.category;
            })
          // ignore: unnecessary_statements
          : null;
    });
  }

  void _onLoading() async {
    pagenumber = pagenumber + 1;
    getcategory(pagenumber, "");
    _refreshControllerwait.loadComplete();
  }

  @override
  void initState() {
    super.initState();
    contentController.getlocations();
    contentController.getfeatures(AppConstants.destinationsId.toString());

    authController.isloggedin();
    getcategory(pagenumber, "");
    print(widget.time);
    print(widget.date);
    currencyController.getcuurentcurrency(context);
  }

  void clearResultsAndFeaturedVideos() {
    setState(() {
      results.clear();
      if (searchController.text.isEmpty) {
        featuredvideo.clear();
      }
    });
  }

  void _onRefresh() async {
    await Future.delayed(const Duration(milliseconds: 1000));
    results.clear();
    featuredvideo.clear();
    pagenumber = 1;

    searchController.clear();

    await getcategory(pagenumber, "");

    _refreshControllerwait.refreshCompleted();

    setState(() {});
  }

  void onSearchSubmitted(String text) async {
    clearResultsAndFeaturedVideos();
    if (text.isEmpty) {
      await getcategory(
        1,
        "",
      );
    } else {
      pagenumber = 1;
      await getcategory(
        1,
        text,
      );
    }

    setState(() {});
  }

  getcategory(int page, String key) async {
    await Api.getmainCategory(
            page, 10, key, AppConstants.destinationsId.toString())
        .then((value) {
      setState(() {
        code2 = value.code;
        msg2 = value.error;
        results.addAll(value.category);
        if (searchController.text.isEmpty) {
          featuredvideo.addAll(value.featuredvideo);
        }
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    final Widget svg2 = SizedBox(
        width: 20,
        height: 20.0,
        child: SvgPicture.asset(
          'assets/filter.svg',
          semanticsLabel: 'Acme Logo',
          fit: BoxFit.cover,
        ));
    return SafeArea(
        child: Scaffold(
            key: _drawerKey,
            resizeToAvoidBottomInset: false,
            appBar: AppBar(
              backgroundColor: const Color(0xFF27b4a8),
              centerTitle: true,
              title:
                  Text(AppLocalizations.of(context).translate('Destinations')),
            ),
            body: SmartRefresher(
              enablePullDown: true,
              enablePullUp: true,
              header: ClassicHeader(
                refreshingIcon: buildLoadingWidget(),
              ),
              footer: CustomFooter(
                builder: (BuildContext context, LoadStatus? mode) {
                  Widget body;
                  if (mode == LoadStatus.loading) {
                    body = const CupertinoActivityIndicator();
                  } else if (mode == LoadStatus.failed) {
                    body = const Text("Load Failed!Click retry!",
                        style: TextStyle(color: Color(0xff233549)));
                  } else if (mode == LoadStatus.canLoading) {
                    body = const Text("release to load more",
                        style: TextStyle(color: Color(0xff233549)));
                  } else {
                    body = const Text("No more Data",
                        style: TextStyle(color: Color(0xff233549)));
                  }
                  return Center(child: body);
                },
              ),
              controller: _refreshControllerwait,
              onRefresh: _onRefresh,
              onLoading: _onLoading,
              child: SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    //! Featured Video
                    code2 == 0 && msg2 == 'loading'
                        ? buildLoadingWidget()
                        : code2 == 1
                            ? featuredvideo.isNotEmpty
                                ? MainFeaturedContainer(
                                    label: 'featuredDestinations',
                                    child: PlacesFeaturedVideo(
                                        featuredvideo: featuredvideo,
                                        results: results,
                                        id: widget.id,
                                        date: widget.date,
                                        time: widget.time),
                                  )
                                : nodatafound(AppLocalizations.of(context)
                                    .translate('No featured videos to show'))
                            : buildErrorWidget(msg2),
                    const SizedBox(
                      height: 20,
                    ),

                    Padding(
                        padding: const EdgeInsets.only(left: 20, right: 20),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              AppLocalizations.of(context)
                                  .translate('AllDestinations'),
                              style: const TextStyle(
                                  color: Color(0xff51565B),
                                  fontWeight: FontWeight.bold),
                            ),
                            GestureDetector(
                              onTap: () {
                                filterPlaces(
                                  context,
                                  onApply: () async {
                                    filtermaincategory(
                                        placesCurrentvalue2 != null
                                            ? placesCurrentvalue2!
                                            : 0,
                                        0,
                                        0,
                                        0,
                                        selectedPlacesFeature,
                                        AppConstants.destinationsId.toString(),
                                        0);
                                  },
                                  // onReset: () {
                                  //   setState(() {
                                  //     placesCurrentvalue2 = null;
                                  //   });
                                  //   filtermaincategory(
                                  //       0, 0, 0, 0, 0, 'places', 0);
                                  //   Navigator.pop(context);
                                  // },
                                  contentController: contentController,
                                  // currentvalue2: currentvalue2,
                                  // rangeValues: rangeValues
                                );
                              },
                              child: svg2,
                            )
                          ],
                        )),

                    10.verticalSpace,

                    Padding(
                        padding: const EdgeInsets.only(left: 20, right: 20),
                        child: Container(
                            height: 40,
                            decoration: BoxDecoration(
                                color: const Color(0xffF1F1F1),
                                borderRadius: BorderRadius.circular(5)),
                            child: TextField(
                              controller: searchController,
                              onSubmitted: onSearchSubmitted,
                              decoration: InputDecoration(
                                  prefixIcon: const Icon(
                                    Icons.search,
                                    color: Color(0xff8B959E),
                                  ),
                                  contentPadding: const EdgeInsets.only(
                                      left: 20, right: 20, top: 5),
                                  hintText: AppLocalizations.of(context)
                                      .translate('SearchDestinationslocations'),
                                  hintStyle: const TextStyle(
                                      color: Color(0xff8B959E), fontSize: 13),
                                  border: InputBorder.none),
                            ))),

                    //! Places List
                    code2 == 0 && msg2 == 'loading'
                        ? buildLoadingWidget()
                        : code2 == 1
                            ? results.isNotEmpty
                                ? PlacesList(
                                    results,
                                    currencyController,
                                    authController,
                                    widget.date,
                                    widget.time,
                                    widget.id)
                                : nodatafound("No places to show")
                            : buildErrorWidget(msg2)
                  ],
                ),
              ),
            )));
  }
}
