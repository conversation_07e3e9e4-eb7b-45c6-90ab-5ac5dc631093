import 'package:flutter/material.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:page/src/features/models/video_model.dart';
import 'package:provider/provider.dart';

import '../../../../core/localization/app_language.dart';
import '../../../../core/localization/app_localizations.dart';
import '../../ad_details/video_widget.dart';

class ResturantList extends StatelessWidget {
  final List<VideoModel> results;
  final currencyController;
  final authController;
  final date;
  final time;
  final id;

  const ResturantList(
    this.results,
    this.currencyController,
    this.authController,
    this.date,
    this.time,
    this.id, {
    Key? key,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    var lang =
        Provider.of<AppLanguage>(context, listen: false).appLocal.languageCode;

    final isEnglish = lang == 'en';

    return StaggeredGridView.countBuilder(
        staggeredTileBuilder: (index) {
          return const StaggeredTile.fit(1);
        },
        shrinkWrap: true,
        padding: const EdgeInsets.symmetric(horizontal: 10.0, vertical: 20.0),
        crossAxisCount: 2,
        physics: const NeverScrollableScrollPhysics(),
        mainAxisSpacing: 8,
        itemCount: results.length,
        primary: false,
        itemBuilder: (context, index) {
          Widget titleWithPriceWidget() {
            return Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                results[index].name != null
                    ? SizedBox(
                        width: MediaQuery.of(context).size.width * 0.3,
                        child: Text(
                          results[index].name!,
                          style: const TextStyle(
                              color: Colors.white,
                              fontWeight: FontWeight.w700,
                              fontSize: 12),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                      )
                    : Container(),
                const SizedBox(height: 5),
                Text(
                  '${AppLocalizations.of(context).translate('Average price')} ${results[index].startprice!.toStringAsFixed(0)} ${currencyController.currency}',
                  style: const TextStyle(color: Colors.white, fontSize: 12),
                )
              ],
            );
          }

          if (results.isEmpty) return Container();

          return Column(
            children: [
              Stack(children: [
                GestureDetector(
                    onTap: () {
                      Navigator.of(context).push(MaterialPageRoute(
                          builder: (BuildContext context) => VideoViewWidget(
                              video: results[index],
                              planId: id,
                              date: date,
                              time: time)));
                    },
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(5),
                      child: Image.network(
                        results[index].images ?? '',
                        height: 288,
                        width: MediaQuery.of(context).size.width * 0.45,
                        fit: BoxFit.fitHeight,
                      ),
                    )),
                GestureDetector(
                    onTap: () {
                      Navigator.of(context).push(MaterialPageRoute(
                          builder: (BuildContext context) => VideoViewWidget(
                              video: results[index],
                              planId: id,
                              date: date,
                              time: time)));
                    },
                    child: Container(
                      height: 288,
                      width: MediaQuery.of(context).size.width * 0.45,
                      decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(5),
                          gradient: LinearGradient(
                            end: Alignment.bottomCenter,
                            begin: Alignment.center,
                            colors: <Color>[
                              Colors.transparent,
                              Colors.black.withOpacity(0.7)
                            ],
                          )),
                    )),
                // Positioned(
                //     top: 10,
                //     right: 10,
                //     child: GestureDetector(
                //       onTap: () {
                //         authController.isLogged == true
                //             ? time != null && time != ""
                //                 ? planController.additemtoplan(
                //                     date,
                //                     'maincategory',
                //                     results[index].id!,
                //                     id!,
                //                     context,
                //                     time: time)
                //                 : id != null
                //                     ? listmyplandetails(
                //                         context,
                //                         id!,
                //                         "maincategory",
                //                         results[index].id!,
                //                         "yes")
                //                     : listmyplan(context, "maincategory",
                //                         results[index].id!)
                //             : snackbar(AppLocalizations.of(context)
                //                 .translate('Please login first'));
                //       },
                //       child: Container(
                //           height: 30,
                //           width: 30,
                //           decoration: BoxDecoration(
                //               color: const Color(0xff4d5e72).withOpacity(0.5),
                //               borderRadius:
                //                   const BorderRadius.all(Radius.circular(30))),
                //           child: const Center(
                //               child: Icon(
                //             Icons.add,
                //             color: Colors.white,
                //           ))),
                //     )),
                if (!isEnglish)
                  Positioned(
                      bottom: 12, right: 10, child: titleWithPriceWidget())
                else
                  Positioned(
                      bottom: 12, left: 10, child: titleWithPriceWidget()),
              ]),
            ],
          );
        });
  }
}
