import 'package:flutter/material.dart';
import 'package:page/src/features/controllers/content_controller.dart';

import '../../../../core/localization/app_localizations.dart';
import '../../../controllers/currency_controller.dart';
import '../../../models/locations.dart';
import '../resturants.dart';

void filterRestaurants(
  BuildContext context, {
  required VoidCallback onApply,
  // required VoidCallback onReset,
  required ContentController contentController,
  required CurrencyController currencyController,
}) {
  restaurantsF_id.clear();

  showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) =>
          StatefulBuilder(builder: (context, StateSetter stateSetter) {
            return Padding(
                padding: EdgeInsets.only(
                    bottom: MediaQuery.of(context).viewInsets.bottom),
                child: Container(
                  height: MediaQuery.of(context).size.height * 0.70,
                  decoration: BoxDecoration(
                      color: const Color(0xffF5F6F7),
                      borderRadius: const BorderRadius.only(
                          topLeft: Radius.circular(25.0),
                          topRight: Radius.circular(25.0)),
                      border: Border.all(color: Colors.black, width: 1.0)),
                  child: SingleChildScrollView(
                      child: Column(
                    children: [
                      const SizedBox(
                        height: 10,
                      ),
                      Container(
                          height: 5, width: 50, color: const Color(0xffD2D4D6)),
                      const SizedBox(
                        height: 20,
                      ),
                      Padding(
                          padding: const EdgeInsets.only(left: 20, right: 20),
                          child: Stack(
                            children: [
                              Align(
                                alignment:
                                    AppLocalizations.of(context).locale ==
                                            const Locale('ar')
                                        ? Alignment.centerLeft
                                        : Alignment.centerRight,
                                child: GestureDetector(
                                    onTap: () {
                                      stateSetter(() {
                                        restaurantsLocation = null;
                                        selectedRestaurantFeature = null;
                                        restaurantsCurrentvalue3 = null;
                                        restaurantsCurrentvalue4 = null;
                                        restaurantsF_id.clear();
                                        restaurantsCurrentRangeValues =
                                            RangeValues(restaurantsMinprice,
                                                restaurantsMaxprice);
                                      });
                                    },
                                    child: Text(
                                      AppLocalizations.of(context)
                                          .translate('reset'),
                                      style: const TextStyle(
                                          color: Color(0xff51565B)),
                                    )),
                              ),
                              Center(
                                child: Text(
                                  AppLocalizations.of(context)
                                      .translate('filter'),
                                  style: const TextStyle(
                                      fontWeight: FontWeight.bold),
                                  textAlign: TextAlign.center,
                                ),
                              )
                            ],
                          )),
                      Padding(
                        padding: const EdgeInsets.all(15),
                        child: Container(
                          decoration: BoxDecoration(
                              color: Colors.white,
                              borderRadius: BorderRadius.circular(10)),
                          child: Padding(
                            padding: const EdgeInsets.all(15),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  AppLocalizations.of(context)
                                      .translate('location'),
                                  style: const TextStyle(fontSize: 13),
                                ),
                                const SizedBox(
                                  height: 10,
                                ),
                                ContentController.locations.isNotEmpty
                                    ? Container(
                                        height: 50,
                                        decoration: BoxDecoration(
                                          borderRadius:
                                              BorderRadius.circular(5),
                                          border: Border.all(
                                              color: Colors.black12,
                                              width: 1.0),
                                        ),
                                        child: DropdownButton<int>(
                                            isExpanded: true,
                                            hint: Padding(
                                              padding: const EdgeInsets.only(
                                                  left: 20, right: 20),
                                              child: Text(
                                                AppLocalizations.of(context)
                                                    .translate(
                                                        'Choose Location'),
                                                style: const TextStyle(
                                                    color: Color(0xffB7B7B7)),
                                              ),
                                            ),
                                            value: restaurantsLocation,
                                            underline: const SizedBox(),
                                            iconEnabledColor: Colors.black,
                                            items: ContentController.locations
                                                .map((Locations value) {
                                              return DropdownMenuItem<int>(
                                                value: value.id,
                                                child: Container(
                                                    padding:
                                                        const EdgeInsets.only(
                                                            left: 10,
                                                            right: 10),
                                                    child: Text(
                                                      value.name!,
                                                      style: const TextStyle(
                                                          fontSize: 16),
                                                    )),
                                              );
                                            }).toList(),
                                            onChanged: (value) {
                                              stateSetter(() {
                                                restaurantsLocation = value;
                                              });
                                            }))
                                    : const SizedBox(),
                                const SizedBox(
                                  height: 10,
                                ),
                                Text(
                                  AppLocalizations.of(context)
                                      .translate('Feature'),
                                  style: const TextStyle(fontSize: 13),
                                ),
                                const SizedBox(
                                  height: 10,
                                ),
                                StatefulBuilder(
                                  builder: (BuildContext context, setStateF) {
                                    return SizedBox(
                                      height: 60,
                                      child: ListView.builder(
                                        shrinkWrap: true,
                                        scrollDirection: Axis.horizontal,
                                        itemCount:
                                            ContentController.features.length,
                                        itemBuilder:
                                            (BuildContext context, int i) {
                                          return Container(
                                              margin: const EdgeInsets.all(5),
                                              child: InkWell(
                                                onTap: () {
                                                  if (restaurantsF_id.contains(
                                                      ContentController
                                                          .features[i].id)) {
                                                    restaurantsF_id.remove(
                                                        ContentController
                                                            .features[i].id);
                                                  } else {
                                                    restaurantsF_id.add(
                                                        ContentController
                                                            .features[i].id!);
                                                  }
                                                  setStateF(() {});
                                                },
                                                child: Card(
                                                  color: restaurantsF_id
                                                          .contains(
                                                              ContentController
                                                                  .features[i]
                                                                  .id)
                                                      ? const Color(0xff233549)
                                                      : Colors.white,
                                                  child: Center(
                                                    child: Padding(
                                                      padding:
                                                          const EdgeInsets.all(
                                                              8.0),
                                                      child: Text(
                                                        ContentController
                                                            .features[i].name!,
                                                        style: TextStyle(
                                                            color: restaurantsF_id
                                                                    .contains(
                                                                        ContentController
                                                                            .features[i]
                                                                            .id)
                                                                ? Colors.white
                                                                : Colors.black),
                                                      ),
                                                    ),
                                                  ),
                                                ),
                                              ));
                                        },
                                      ),
                                    );
                                  },
                                ),
                                // const SizedBox(
                                //   height: 20,
                                // ),
                                // Row(
                                //   mainAxisAlignment:
                                //       MainAxisAlignment.spaceBetween,
                                //   children: [
                                //     Text(
                                //       AppLocalizations.of(context).translate(
                                //           'Average Price (Per Person)'),
                                //       style: const TextStyle(fontSize: 13),
                                //     ),
                                //     Row(
                                //       children: [
                                //         contentController.minprice != null
                                //             ? Text(
                                //                 '${contentController.minprice} ${currencyController.currency}-',
                                //                 style: const TextStyle(
                                //                     fontSize: 13,
                                //                     color: Color(0xff51565B)))
                                //             : Container(),
                                //         contentController.maxprice != null
                                //             ? Text(
                                //                 '${contentController.maxprice} ${currencyController.currency}',
                                //                 style: const TextStyle(
                                //                     fontSize: 13,
                                //                     color: Color(0xff51565B)))
                                //             : Container(),
                                //       ],
                                //     ),
                                //   ],
                                // ),
                                // StatefulBuilder(builder:
                                //     (context, StateSetter stateSetter) {
                                //   final labels = RangeLabels(
                                //     restaurantsCurrentRangeValues!.start
                                //         .round()
                                //         .toString(),
                                //     restaurantsCurrentRangeValues!.end
                                //         .round()
                                //         .toString(),
                                //   );
                                //
                                //   log('afasfsaf ${restaurantsCurrentRangeValues} Minn ${restaurantsMinprice} Maxx ${restaurantsMaxprice} aafasf ${labels}');
                                //
                                //   return RangeSlider(
                                //     activeColor: const Color(0xFFE2CBA2),
                                //     inactiveColor: const Color(0xFF485E77)
                                //         .withOpacity(0.9),
                                //     values: restaurantsCurrentRangeValues!,
                                //     min: restaurantsMinprice,
                                //     max: restaurantsMaxprice,
                                //     divisions: 50,
                                //     labels: labels,
                                //     onChanged: (RangeValues values) {
                                //       stateSetter(() {
                                //         restaurantsCurrentRangeValues = values;
                                //       });
                                //     },
                                //   );
                                // }),
                                const SizedBox(height: 20),
                                Center(
                                    child: GestureDetector(
                                        onTap: onApply,
                                        child: Container(
                                          height: 50,
                                          width:
                                              MediaQuery.of(context).size.width,
                                          decoration: BoxDecoration(
                                              color: const Color(0xFF27b4a8),
                                              borderRadius:
                                                  BorderRadius.circular(10)),
                                          child: Container(
                                              padding: const EdgeInsets.all(10),
                                              child: Center(
                                                  child: Text(
                                                AppLocalizations.of(context)
                                                    .translate('Apply Filter'),
                                                style: const TextStyle(
                                                    color: Colors.white),
                                              ))),
                                        ))),
                                const SizedBox(height: 20),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ],
                  )),
                ));
          }));
}
