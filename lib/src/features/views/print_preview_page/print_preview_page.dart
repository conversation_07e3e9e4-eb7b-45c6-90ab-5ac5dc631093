// ignore_for_file: prefer_typing_uninitialized_variables

import 'package:flutter/material.dart';
import 'package:printing/printing.dart';

class PrintPreviewPage extends StatelessWidget {
  final document;
  const PrintPreviewPage({this.document});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: const Color(0xFF27b4a8),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back_ios),
          onPressed: () => Navigator.of(context).pop(),
        ),
      ),
      body: Center(
        child: PdfPreview(
          useActions: true,
          padding: EdgeInsets.zero,
          maxPageWidth: double.infinity,
          build: (format) => document.save(),
          shouldRepaint: true,
        ),
      ),
    );
  }
}
