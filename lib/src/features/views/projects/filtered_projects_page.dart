import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:page/src/core/config/constants.dart';
import 'package:page/src/core/localization/app_localizations.dart';
import 'package:page/src/core/services/api.dart';
import 'package:page/src/core/shared_widgets/shared_widgets.dart';
import 'package:page/src/core/utils/print_services.dart';
import 'package:page/src/features/controllers/auth_controller.dart';
import 'package:page/src/features/controllers/content_controller.dart';
import 'package:page/src/features/controllers/currency_controller.dart';
import 'package:page/src/features/controllers/language_controller.dart';
import 'package:page/src/features/models/content.dart';
import 'package:page/src/features/models/video_model.dart';
import 'package:page/src/features/views/projects/widgets/project_card.dart';
import 'package:page/src/features/views/projects/widgets/project_filter.dart';

class FilteredProjectsPage extends StatefulWidget {
  final Types? type;
  final String? date;
  final String? time;
  final int? id;

  const FilteredProjectsPage({
    super.key,
    this.type,
    this.date,
    this.time,
    this.id,
  });

  @override
  _FilteredProjectsPageState createState() => _FilteredProjectsPageState();
}

class _FilteredProjectsPageState extends State<FilteredProjectsPage> {
  List<VideoModel> results = [];
  List<VideoModel> featuredvideo = [];
  int pagenumber = 1;
  int code2 = 0;

  // String msg2 = 'loading';
  bool isLoading = false;

  final ContentController contentController = ContentController();
  final CurrencyController currencyController = CurrencyController();
  final AuthController authController = AuthController();
  final TextEditingController searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    initializeData();
  }

  Future<void> initializeData() async {
    await Future.wait([
      if (ContentController.locations.isEmpty) contentController.getlocations(),
      if (ContentController.propertyStatuses.isEmpty)
        contentController.getPropertyStatuses(),
      contentController.gettypes(AppConstants.projectsId.toString()),
      authController.isloggedin(),
      currencyController.getcuurentcurrency(context),
    ]);

    await getProjects(pagenumber, "", typeId: widget.type?.id);
  }

  Future<void> getProjects(int page, String key, {int? typeId}) async {
    setState(() {
      isLoading = true;
    });

    await Api.getmainCategory(
      page,
      10,
      key,
      AppConstants.projectsId.toString(),
      typeId: typeId?.toString(),
    ).then((value) {
      setState(() {
        code2 = value.code;
        // msg2 = value.error;
        isLoading = false;
        results.addAll(value.category);
        if (searchController.text.isEmpty) {
          featuredvideo.addAll(value.featuredvideo);
        }
      });
    });
  }

  Future<void> filterProjects() async {
    results.clear();
    setState(() {
      isLoading = true;
    });

    await Api.filterProjectsCategory(
      projectLocationValue,
      projectPropertyStatusValue,
      projectPaymentMethodValue,
      widget.type?.id,
    ).then((value) {
      if (value != null) {
        setState(() {
          code2 = value.code;
          // msg2 = value.error;
          isLoading = false;
          results.addAll(value.category);
        });
      } else {
        setState(() {
          isLoading = false;
        });
      }
    });
  }

  void clearResultsAndFeaturedVideos() {
    setState(() {
      results.clear();
      if (searchController.text.isEmpty) {
        featuredvideo.clear();
      }
    });
  }

  void onSearchSubmitted(String text) async {
    clearResultsAndFeaturedVideos();
    if (text.isEmpty) {
      await getProjects(1, "", typeId: widget.type?.id);
    } else {
      pagenumber = 1;
      await getProjects(1, text, typeId: widget.type?.id);
    }
    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    final Widget filterIcon = SizedBox(
      width: 20,
      height: 20.0,
      child: SvgPicture.asset(
        'assets/filter.svg',
        semanticsLabel: 'Filter',
        fit: BoxFit.cover,
        color: Colors.white,
      ),
    );

    return SafeArea(
      child: Scaffold(
        resizeToAvoidBottomInset: false,
        appBar: AppBar(
          backgroundColor: const Color(0xFF27b4a8),
          centerTitle: true,
          title: Text(
            isEnglish(context)
                ? widget.type?.nameEn ??
                    widget.type?.name ??
                    AppLocalizations.of(context).translate('Projects')
                : widget.type?.nameAr ??
                    widget.type?.name ??
                    AppLocalizations.of(context).translate('Projects'),
            style: TextStyle(
              fontFamily: isEng(context) ? 'Roboto' : 'Tajawal',
            ),
          ),
          actions: [
            IconButton(
              onPressed: () {
                projectFilter(
                  context,
                  onApply: filterProjects,
                  contentController: contentController,
                );
              },
              icon: filterIcon,
            ),
          ],
        ),
        body: Column(
          children: [
            // Search bar
            Container(
              padding: const EdgeInsets.all(16),
              child: TextField(
                controller: searchController,
                decoration: InputDecoration(
                  hintText: AppLocalizations.of(context).translate('Search'),
                  prefixIcon: const Icon(Icons.search),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(10),
                    borderSide: BorderSide.none,
                  ),
                  filled: true,
                  fillColor: Colors.grey[200],
                ),
                onSubmitted: onSearchSubmitted,
              ),
            ),
            // Projects list
            Expanded(
              child: isLoading
                  ? buildLoadingWidget()
                  : code2 == 1
                      ? results.isNotEmpty
                          ? ListView.builder(
                              padding: const EdgeInsets.all(16),
                              itemCount: results.length,
                              itemBuilder: (context, index) {
                                return ProjectCard(
                                  project: results[index],
                                  date: widget.date,
                                  time: widget.time,
                                  planId: widget.id,
                                );
                              },
                            )
                          : nodatafound(AppLocalizations.of(context)
                              .translate('No Projects to show'))
                      : buildLoadingWidget(),
            ),
          ],
        ),
      ),
    );
  }
}
