import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:page/src/core/localization/app_localizations.dart';

import '../../../../core/utils/main_cached_image.dart';

class QuillDescriptionParser extends StatelessWidget {
  final String description;
  final bool isExpanded;
  final VoidCallback? onToggleExpanded;

  const QuillDescriptionParser({
    super.key,
    required this.description,
    this.isExpanded = true,
    this.onToggleExpanded,
  });

  @override
  Widget build(BuildContext context) {
    try {
      // Try to parse as JSON first
      final List<dynamic> quillData = json.decode(description);
      return _buildQuillContent(context, quillData);
    } catch (e) {
      // If parsing fails, treat as plain text
      return Text(
        description,
        style: const TextStyle(
          fontSize: 16,
          color: Colors.black87,
        ),
      );
    }
  }

  Widget _buildQuillContent(BuildContext context, List<dynamic> quillData) {
    List<Widget> widgets = [];
    int imageCount = 0;
    bool hasMultipleImages = _hasMultipleImages(quillData);
    bool shouldTruncate = !isExpanded && hasMultipleImages;
    bool foundSecondImage = false;
    bool addedOneMoreLine = false;

    for (int i = 0; i < quillData.length; i++) {
      var item = quillData[i];
      if (item is Map<String, dynamic>) {
        final insert = item['insert'];
        final attributes = item['attributes'] as Map<String, dynamic>?;

        if (insert is String) {
          // Handle text content
          if (insert.trim().isNotEmpty && insert != '\n') {
            widgets.add(_buildTextWidget(insert, attributes));

            // If we found the second image and this is a text line after it, mark as added
            if (shouldTruncate && foundSecondImage && !addedOneMoreLine) {
              addedOneMoreLine = true;
              // Add the toggle button after this line and stop
              widgets.add(_buildToggleButton(context));
              break;
            }
          }
        } else if (insert is Map<String, dynamic>) {
          // Handle embedded content (like images)
          if (insert.containsKey('image')) {
            imageCount++;
            widgets.add(_buildImageWidget(insert['image'], attributes));

            // Mark when we've found the second image
            if (shouldTruncate && imageCount == 2) {
              foundSecondImage = true;
            }
          }
        }
      }
    }

    // If we truncated but didn't find a text line after the second image, add button anyway
    if (shouldTruncate && foundSecondImage && !addedOneMoreLine) {
      widgets.add(_buildToggleButton(context));
    }

    // If expanded and has multiple images, add "Show Less" button at the end
    if (isExpanded && hasMultipleImages && onToggleExpanded != null) {
      widgets.add(_buildToggleButton(context));
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: widgets,
    );
  }

  Widget _buildTextWidget(String text, Map<String, dynamic>? attributes) {
    double fontSize = 16;
    FontWeight fontWeight = FontWeight.normal;
    Color color = Colors.black87;
    String fontFamily = 'Poppins';

    if (attributes != null) {
      // Parse font size
      if (attributes['size'] != null) {
        fontSize = double.tryParse(attributes['size'].toString()) ?? 16;
      }

      // Parse color
      if (attributes['color'] != null) {
        String colorStr = attributes['color'].toString();
        if (colorStr.startsWith('#FF')) {
          colorStr = colorStr.substring(3); // Remove #FF prefix
          color = Color(int.parse('FF$colorStr', radix: 16));
        }
      }

      // Parse font family
      if (attributes['font'] != null) {
        fontFamily = attributes['font'].toString();
      }

      // Parse font weight (if bold attribute exists)
      if (attributes['bold'] == true) {
        fontWeight = FontWeight.bold;
      }
    }

    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Text(
        text.trim(),
        style: TextStyle(
          fontSize: fontSize,
          fontWeight: fontWeight,
          color: color,
          fontFamily: fontFamily,
          height: 1.5,
        ),
      ),
    );
  }

  Widget _buildImageWidget(String imageUrl, Map<String, dynamic>? attributes) {
    double? width;
    double? height;

    if (attributes != null && attributes['style'] != null) {
      String style = attributes['style'].toString();

      // Parse width from style
      RegExp widthRegex = RegExp(r'width:(\d+)px');
      Match? widthMatch = widthRegex.firstMatch(style);
      if (widthMatch != null) {
        width = double.tryParse(widthMatch.group(1)!);
      }

      // Parse height from style
      RegExp heightRegex = RegExp(r'height:(\d+)px');
      Match? heightMatch = heightRegex.firstMatch(style);
      if (heightMatch != null) {
        height = double.tryParse(heightMatch.group(1)!);
      }
    }

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 12),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(8),
        child: MainCachedImage(
          imageUrl,
          width: width,
          height: height,
          fit: BoxFit.cover,
        ),
      ),
    );
  }

  bool _hasMultipleImages(List<dynamic> quillData) {
    int imageCount = 0;
    for (var item in quillData) {
      if (item is Map<String, dynamic>) {
        final insert = item['insert'];
        if (insert is Map<String, dynamic> && insert.containsKey('image')) {
          imageCount++;
          if (imageCount >= 2) {
            return true;
          }
        }
      }
    }
    return false;
  }

  Widget _buildToggleButton(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(top: 12),
      child: GestureDetector(
        onTap: onToggleExpanded,
        child: Container(
          padding: const EdgeInsets.symmetric(
            horizontal: 16,
            vertical: 8,
          ),
          decoration: BoxDecoration(
            color: const Color(0xFF27b4a8).withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(20),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                isExpanded
                    ? AppLocalizations.of(context).translate('Show Less')
                    : AppLocalizations.of(context).translate('Show More'),
                style: const TextStyle(
                  color: Color(0xFF27b4a8),
                  fontWeight: FontWeight.w600,
                  fontSize: 14,
                ),
              ),
              const SizedBox(width: 4),
              Icon(
                isExpanded
                    ? Icons.keyboard_arrow_up
                    : Icons.keyboard_arrow_down,
                color: const Color(0xFF27b4a8),
                size: 18,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
