// import 'package:flutter/material.dart';
// import 'package:page/src/core/utils/main_cached_image.dart';
//
// class ZoomablePageViewWidget extends StatefulWidget {
//   final List<String> imageUrls;
//   final int initialIndex;
//   final String title;
//
//   const ZoomablePageViewWidget({
//     super.key,
//     required this.imageUrls,
//     this.initialIndex = 0,
//     this.title = '',
//   });
//
//   @override
//   State<ZoomablePageViewWidget> createState() => _ZoomablePageViewWidgetState();
// }
//
// class _ZoomablePageViewWidgetState extends State<ZoomablePageViewWidget> {
//   late PageController _pageController;
//   late int _currentIndex;
//   bool _isZoomed = false;
//
//   @override
//   void initState() {
//     super.initState();
//     _currentIndex = widget.initialIndex;
//     _pageController = PageController(initialPage: widget.initialIndex);
//   }
//
//   @override
//   void dispose() {
//     _pageController.dispose();
//     super.dispose();
//   }
//
//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//       backgroundColor: Colors.black,
//       appBar: AppBar(
//         backgroundColor: Colors.black,
//         iconTheme: const IconThemeData(color: Colors.white),
//         title: Text(
//           widget.title.isNotEmpty
//               ? widget.title
//               : '${_currentIndex + 1} / ${widget.imageUrls.length}',
//           style: const TextStyle(color: Colors.white),
//         ),
//       ),
//       body: PageView.builder(
//         controller: _pageController,
//         itemCount: widget.imageUrls.length,
//         physics: _isZoomed
//             ? const NeverScrollableScrollPhysics()
//             : const ClampingScrollPhysics(),
//         onPageChanged: (index) {
//           setState(() {
//             _currentIndex = index;
//           });
//         },
//         itemBuilder: (context, index) {
//           return Center(
//             child: PinchZoom(
//               maxScale: 3.0,
//               onZoomStart: () {
//                 setState(() {
//                   _isZoomed = true;
//                 });
//               },
//               onZoomEnd: () {
//                 setState(() {
//                   _isZoomed = false;
//                 });
//               },
//               child: MainCachedImage(
//                 widget.imageUrls[index],
//                 fit: BoxFit.contain,
//               ),
//             ),
//           );
//         },
//       ),
//     );
//   }
// }
//
// class ZoomableImageCarousel extends StatefulWidget {
//   final List<String> imageUrls;
//   final double height;
//   final String title;
//
//   const ZoomableImageCarousel({
//     super.key,
//     required this.imageUrls,
//     this.height = 300,
//     this.title = '',
//   });
//
//   @override
//   State<ZoomableImageCarousel> createState() => _ZoomableImageCarouselState();
// }
//
// class _ZoomableImageCarouselState extends State<ZoomableImageCarousel> {
//   late PageController _pageController;
//   int _currentIndex = 0;
//   bool _isZoomed = false;
//
//   @override
//   void initState() {
//     super.initState();
//     _pageController = PageController();
//   }
//
//   @override
//   void dispose() {
//     _pageController.dispose();
//     super.dispose();
//   }
//
//   void _openFullScreen(int index) {
//     Navigator.of(context).push(
//       MaterialPageRoute(
//         builder: (context) => ZoomablePageViewWidget(
//           imageUrls: widget.imageUrls,
//           initialIndex: index,
//           title: widget.title,
//         ),
//       ),
//     );
//   }
//
//   @override
//   Widget build(BuildContext context) {
//     if (widget.imageUrls.isEmpty) {
//       return Container(
//         height: widget.height,
//         decoration: BoxDecoration(
//           color: Colors.grey[200],
//           borderRadius: BorderRadius.circular(12),
//         ),
//         child: const Center(
//           child: Icon(
//             Icons.image_not_supported,
//             size: 50,
//             color: Colors.grey,
//           ),
//         ),
//       );
//     }
//
//     return Column(
//       children: [
//         // Main image carousel
//         Container(
//           height: widget.height,
//           decoration: BoxDecoration(
//             borderRadius: BorderRadius.circular(12),
//             boxShadow: [
//               BoxShadow(
//                 color: Colors.black.withOpacity(0.1),
//                 blurRadius: 8,
//                 offset: const Offset(0, 4),
//               ),
//             ],
//           ),
//           child: ClipRRect(
//             borderRadius: BorderRadius.circular(12),
//             child: Stack(
//               children: [
//                 PageView.builder(
//                   controller: _pageController,
//                   itemCount: widget.imageUrls.length,
//                   physics: _isZoomed
//                       ? const NeverScrollableScrollPhysics()
//                       : const ClampingScrollPhysics(),
//                   onPageChanged: (index) {
//                     setState(() {
//                       _currentIndex = index;
//                     });
//                   },
//                   itemBuilder: (context, index) {
//                     return GestureDetector(
//                       onTap: () => _openFullScreen(index),
//                       child: PinchZoom(
//                         maxScale: 2.0,
//                         onZoomStart: () {
//                           setState(() {
//                             _isZoomed = true;
//                           });
//                         },
//                         onZoomEnd: () {
//                           setState(() {
//                             _isZoomed = false;
//                           });
//                         },
//                         child: MainCachedImage(
//                           widget.imageUrls[index],
//                           fit: BoxFit.cover,
//                         ),
//                       ),
//                     );
//                   },
//                 ),
//
//                 // Page indicator
//                 if (widget.imageUrls.length > 1)
//                   Positioned(
//                     bottom: 16,
//                     left: 0,
//                     right: 0,
//                     child: Row(
//                       mainAxisAlignment: MainAxisAlignment.center,
//                       children: widget.imageUrls.asMap().entries.map((entry) {
//                         return Container(
//                           width: 8,
//                           height: 8,
//                           margin: const EdgeInsets.symmetric(horizontal: 4),
//                           decoration: BoxDecoration(
//                             shape: BoxShape.circle,
//                             color: _currentIndex == entry.key
//                                 ? Colors.white
//                                 : Colors.white.withOpacity(0.4),
//                           ),
//                         );
//                       }).toList(),
//                     ),
//                   ),
//               ],
//             ),
//           ),
//         ),
//
//         // Thumbnail row (if more than one image)
//         if (widget.imageUrls.length > 1) ...[
//           const SizedBox(height: 16),
//           SizedBox(
//             height: 80,
//             child: ListView.builder(
//               scrollDirection: Axis.horizontal,
//               itemCount: widget.imageUrls.length,
//               itemBuilder: (context, index) {
//                 return GestureDetector(
//                   onTap: () {
//                     _pageController.animateToPage(
//                       index,
//                       duration: const Duration(milliseconds: 300),
//                       curve: Curves.easeInOut,
//                     );
//                   },
//                   child: Container(
//                     width: 80,
//                     height: 80,
//                     margin: const EdgeInsets.only(right: 12),
//                     decoration: BoxDecoration(
//                       borderRadius: BorderRadius.circular(8),
//                       border: Border.all(
//                         color: _currentIndex == index
//                             ? const Color(0xFF27b4a8)
//                             : Colors.grey[300]!,
//                         width: 2,
//                       ),
//                     ),
//                     child: ClipRRect(
//                       borderRadius: BorderRadius.circular(6),
//                       child: MainCachedImage(
//                         widget.imageUrls[index],
//                         fit: BoxFit.cover,
//                       ),
//                     ),
//                   ),
//                 );
//               },
//             ),
//           ),
//         ],
//       ],
//     );
//   }
// }
