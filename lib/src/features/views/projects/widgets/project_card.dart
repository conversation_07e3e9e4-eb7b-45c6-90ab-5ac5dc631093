import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:get/get.dart';
import 'package:page/src/core/config/constants.dart';
import 'package:page/src/core/localization/app_localizations.dart';
import 'package:page/src/core/utils/main_cached_image.dart';
import 'package:page/src/features/models/video_model.dart';
import 'package:page/src/features/views/story/widgets/reel_widgets/reel_details.dart';

import '../../../controllers/language_controller.dart';
import '../project_details_page.dart';

class ProjectCard extends StatelessWidget {
  final VideoModel project;
  final String? date;
  final String? time;
  final int? planId;

  const ProjectCard({
    super.key,
    required this.project,
    this.date,
    this.time,
    this.planId,
  });

  @override
  Widget build(BuildContext context) {
    final firstPlan = project.projectPlans?.isNotEmpty == true
        ? project.projectPlans!.first
        : null;

    return GestureDetector(
      onTap: () {
        Navigator.of(context).push(MaterialPageRoute(
          builder: (BuildContext context) => ProjectDetailsPage(
            project: project,
          ),
        ));
      },
      child: Container(
        margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 8),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.grey.withOpacity(0.2),
              spreadRadius: 1,
              blurRadius: 5,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Image section (half of card height)
            ClipRRect(
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(12),
                topRight: Radius.circular(12),
              ),
              child: SizedBox(
                height: 150,
                width: double.infinity,
                child: project.images?.isNotEmpty == true
                    ? MainCachedImage(
                        project.images!,
                        fit: BoxFit.cover,
                      )
                    : Container(
                        color: Colors.grey[300],
                        child: const Icon(
                          Icons.image,
                          size: 50,
                          color: Colors.grey,
                        ),
                      ),
              ),
            ),
            // Content section
            Padding(
              padding: const EdgeInsets.all(12),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    project.name ?? '',
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Colors.black87,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),

                  // const SizedBox(height: 8),

                  // Three icons with values
                  Row(
                    children: [
                      Icon(
                        Icons.location_on,
                        size: 16,
                        color: primaryColor,
                      ),
                      const SizedBox(width: 4),
                      Expanded(
                        child: Text(
                          project.locationName ?? '',
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.grey[600],
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),

                  Row(
                    children: [
                      Icon(
                        FontAwesomeIcons.moneyBillWave,
                        size: 14,
                        color: primaryColor,
                      ),
                      const SizedBox(width: 4),
                      Expanded(
                        child: Text(
                          firstPlan?.priceFrom != 0
                              ? '${AppLocalizations.of(context).translate('Price From')} ${sortPrice(firstPlan?.priceFrom ?? 0)} ${currencyController.currency} (${AppLocalizations.of(context).translate(project.paymentMethod?.capitalize ?? 'Cash')})'
                              : '-',
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.grey[600],
                          ),
                          maxLines: 3,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ],
                  ),

                  const SizedBox(height: 8),

                  // Three icons with values
                  Row(
                    children: [
                      Icon(
                        Icons.bed,
                        size: 16,
                        color: primaryColor,
                      ),
                      const SizedBox(width: 4),
                      Expanded(
                        child: Text(
                          project.projectPlans?.map((plan) {
                                return isEnglish(context)
                                    ? plan.bedroomsEn ?? plan.bedroomsAr ?? ''
                                    : plan.bedroomsAr ?? plan.bedroomsEn ?? '';
                              }).join(', ') ??
                              '-',
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.grey[600],
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
