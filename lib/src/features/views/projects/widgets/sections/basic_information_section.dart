import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:get/get.dart';
import 'package:page/src/core/localization/app_localizations.dart';
import 'package:page/src/core/utils/main_cached_image.dart';
import 'package:page/src/features/controllers/language_controller.dart';
import 'package:page/src/features/models/video_model.dart';

import '../../../../../core/config/constants.dart';
import '../../../story/widgets/reel_widgets/reel_details.dart';

class BasicInformationSection extends StatelessWidget {
  final VideoModel project;

  BasicInformationSection({
    super.key,
    required this.project,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Developer Information
        if (project.developer != null) ...[
          _buildDeveloperInfo(context),
          const SizedBox(height: 16),
        ],

        // Property Status
        if (project.propertyStatus != null) ...[
          _buildInfoRow(
            context,
            icon: Icons.info_outline,
            label: AppLocalizations.of(context).translate('Property Status'),
            value: isEnglish(context)
                ? project.propertyStatus!.nameEn ??
                    project.propertyStatus!.name ??
                    ''
                : project.propertyStatus!.nameAr ??
                    project.propertyStatus!.name ??
                    '',
          ),
          const SizedBox(height: 16),
        ],

        // Payment Method
        if (project.paymentMethod?.isNotEmpty == true) ...[
          _buildInfoRow(
            context,
            icon: Icons.payment,
            label: AppLocalizations.of(context).translate('Payment Method'),
            value: AppLocalizations.of(context)
                .translate(project.paymentMethod?.capitalize ?? 'Cash'),
          ),
          const SizedBox(height: 16),
        ],

        // Location
        if (project.locationName?.isNotEmpty == true) ...[
          _buildInfoRow(
            context,
            icon: Icons.location_on,
            label: AppLocalizations.of(context).translate('Location'),
            value: project.locationName!,
          ),
          const SizedBox(height: 16),
        ],

        // Price Range
        _buildPriceInfo(context),

        // RERA Permit
        // if (project.reraPermit?.reraNumber?.isNotEmpty == true) ...[
        //   _buildInfoRow(
        //     context,
        //     icon: Icons.verified,
        //     label: AppLocalizations.of(context).translate('RERA Number'),
        //     value: project.reraPermit!.reraNumber!,
        //   ),
        //   const SizedBox(height: 16),
        // ],
      ],
    );
  }

  Widget _buildInfoRow(
    BuildContext context, {
    required IconData icon,
    required String label,
    required String value,
    Color? iconColor,
  }) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: (iconColor ?? const Color(0xFF27b4a8)).withOpacity(0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            icon,
            size: 20,
            color: iconColor ?? const Color(0xFF27b4a8),
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey[600],
                  fontWeight: FontWeight.w500,
                ),
              ),
              Text(
                value,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: Colors.black87,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildDeveloperInfo(BuildContext context) {
    final developer = project.developer!;

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[200]!),
      ),
      child: Row(
        children: [
          // Developer Logo
          if (developer.developerLogo?.isNotEmpty == true)
            Container(
              width: 60,
              height: 60,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(8),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.1),
                    blurRadius: 4,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(8),
                child: MainCachedImage(
                  developer.developerLogo!,
                  fit: BoxFit.cover,
                  errorWidget: Container(
                    color: Colors.grey[200],
                    child: const Icon(
                      Icons.business,
                      color: Colors.grey,
                    ),
                  ),
                ),
              ),
            )
          else
            Container(
              width: 60,
              height: 60,
              decoration: BoxDecoration(
                color: const Color(0xFF27b4a8).withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: const Icon(
                Icons.business,
                color: Color(0xFF27b4a8),
                size: 30,
              ),
            ),

          const SizedBox(width: 16),

          // Developer Info
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  AppLocalizations.of(context).translate('Developer'),
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey[600],
                    fontWeight: FontWeight.w500,
                  ),
                ),
                Text(
                  isEnglish(context)
                      ? developer.nameEn ?? developer.name ?? ''
                      : developer.nameAr ?? developer.name ?? '',
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: Colors.black87,
                  ),
                ),
                if (developer.displayDescription.isNotEmpty) ...[
                  Text(
                    isEnglish(context)
                        ? developer.descriptionEn ?? developer.description ?? ''
                        : developer.descriptionAr ??
                            developer.description ??
                            '',
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey[600],
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPriceInfo(BuildContext context) {
    String priceText =
        AppLocalizations.of(context).translate('Price on Request');

    if (project.projectPlans?.isNotEmpty == true) {
      final firstPlan = project.projectPlans!.first;
      if (firstPlan.priceFrom != 0) {
        priceText =
            '${sortPrice(firstPlan.priceFrom ?? 0)} ${currencyController.currency}';
      }
    }

    return _buildInfoRow(
      context,
      icon:     FontAwesomeIcons.moneyBillWave,
      label: AppLocalizations.of(context).translate('From'),
      value: priceText,
    );
  }
}
