import 'package:carousel_slider/carousel_slider.dart';
import 'package:easy_image_viewer/easy_image_viewer.dart';
import 'package:flutter/material.dart' hide CarouselController;
import 'package:page/src/core/localization/app_localizations.dart';
import 'package:page/src/core/utils/main_cached_image.dart';
import 'package:page/src/features/models/video_model.dart';

class GalleryImagesSection extends StatefulWidget {
  final VideoModel project;

  const GalleryImagesSection({
    super.key,
    required this.project,
  });

  @override
  State<GalleryImagesSection> createState() => _GalleryImagesSectionState();
}

class _GalleryImagesSectionState extends State<GalleryImagesSection> {
  int _currentImageIndex = 0;

  final CarouselSliderController _carouselController =
      CarouselSliderController();

  @override
  Widget build(BuildContext context) {
    final imageUrls = widget.project.projectImages;

    if (imageUrls.isEmpty) {
      return _buildNoImagesWidget();
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Main image carousel
        Container(
          height: 250,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.1),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(12),
            child: CarouselSlider.builder(
              carouselController: _carouselController,
              itemCount: imageUrls.length,
              itemBuilder: (context, index, realIndex) {
                return GestureDetector(
                  onTap: () => _showFullScreenImage(context, imageUrls, index),
                  child: SizedBox(
                    width: double.infinity,
                    child: MainCachedImage(
                      imageUrls[index],
                      fit: BoxFit.cover,
                      errorWidget: Container(
                        color: Colors.grey[200],
                        child: const Icon(
                          Icons.image_not_supported,
                          size: 50,
                          color: Colors.grey,
                        ),
                      ),
                    ),
                  ),
                );
              },
              options: CarouselOptions(
                height: 250,
                viewportFraction: 1.0,
                enableInfiniteScroll: imageUrls.length > 1,
                autoPlay: imageUrls.length > 1,
                autoPlayInterval: const Duration(seconds: 4),
                onPageChanged: (index, reason) {
                  setState(() {
                    _currentImageIndex = index;
                  });
                },
              ),
            ),
          ),
        ),

        if (imageUrls.length > 1) ...[
          const SizedBox(height: 16),

          // Image indicators
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: imageUrls.asMap().entries.map((entry) {
              return GestureDetector(
                onTap: () {
                  _carouselController.animateToPage(entry.key);
                },
                child: Container(
                  width: 8,
                  height: 8,
                  margin: const EdgeInsets.symmetric(horizontal: 4),
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: _currentImageIndex == entry.key
                        ? const Color(0xFF27b4a8)
                        : Colors.grey[300],
                  ),
                ),
              );
            }).toList(),
          ),

          const SizedBox(height: 16),

          // Thumbnail row
          SizedBox(
            height: 80,
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              itemCount: imageUrls.length,
              itemBuilder: (context, index) {
                return GestureDetector(
                  onTap: () {
                    _carouselController.animateToPage(index);
                  },
                  child: Container(
                    width: 80,
                    height: 80,
                    margin: const EdgeInsets.only(right: 12),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(
                        color: _currentImageIndex == index
                            ? const Color(0xFF27b4a8)
                            : Colors.grey[300]!,
                        width: 2,
                      ),
                    ),
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(6),
                      child: MainCachedImage(
                        imageUrls[index],
                        fit: BoxFit.cover,
                      ),
                    ),
                  ),
                );
              },
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildNoImagesWidget() {
    return Container(
      height: 200,
      decoration: BoxDecoration(
        color: Colors.grey[100],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[300]!),
      ),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.image_not_supported,
              size: 50,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 12),
            Text(
              AppLocalizations.of(context).translate('No Images Available'),
              style: TextStyle(
                color: Colors.grey[600],
                fontSize: 16,
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showFullScreenImage(
      BuildContext context, List<String> imageUrls, int initialIndex) {
    final imageProviders = imageUrls.map((url) => NetworkImage(url)).toList();

    final multiImageProvider =
        MultiImageProvider(imageProviders, initialIndex: initialIndex);

    showImageViewerPager(
      context,
      multiImageProvider,
      onPageChanged: (page) {
        // Optional: handle page change
      },
      onViewerDismissed: (page) {
        // Optional: handle viewer dismissed
      },
      swipeDismissible: true,
      doubleTapZoomable: true,
    );
  }
}
