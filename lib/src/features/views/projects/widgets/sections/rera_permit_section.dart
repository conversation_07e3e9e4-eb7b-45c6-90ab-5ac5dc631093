import 'package:flutter/material.dart';
import 'package:page/src/core/localization/app_localizations.dart';
import 'package:page/src/features/models/video_model.dart';

import '../../../../../core/config/constants.dart';

class ReraPermitSection extends StatelessWidget {
  final VideoModel project;

  const ReraPermitSection({
    super.key,
    required this.project,
  });

  @override
  Widget build(BuildContext context) {
    if (project.reraPermit == null) {
      return _buildNoReraPermitWidget(context);
    }

    final reraPermit = project.reraPermit!;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        // RERA number
        if (reraPermit.reraNumber?.isNotEmpty == true) ...[
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                AppLocalizations.of(context).translate('RERA Permit'),
                style: TextStyle(
                  fontSize: 18,
                  color: Colors.grey[600],
                  fontWeight: FontWeight.w500,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                reraPermit.reraNumber!,
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: primaryColor,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
        ],

        // RERA permit documents
        if (reraPermit.media?.isNotEmpty == true) ...[
          _buildDocumentCard(context, reraPermit.media?.firstOrNull),
          // GridView.builder(
          //   shrinkWrap: true,
          //   physics: const NeverScrollableScrollPhysics(),
          //   gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
          //     crossAxisCount: 2,
          //     crossAxisSpacing: 12,
          //     mainAxisSpacing: 12,
          //     childAspectRatio: 1.2,
          //   ),
          //   itemCount: reraPermit.media!.length,
          //   itemBuilder: (context, index) {
          //     final media = reraPermit.media![index];
          //     return ;
          //   },
          // ),
        ],
      ],
    );
  }

  Widget _buildDocumentCard(BuildContext context, dynamic media) {
    return SizedBox(
      height: 200,
      child: GestureDetector(
        onTap: () => _showFullScreenDocument(context, media),
        child: ClipRRect(
          borderRadius: const BorderRadius.vertical(top: Radius.circular(12)),
          child: Image.network(
            media.url ?? '',
            fit: BoxFit.contain,
            height: 200,
            width: double.infinity,
          ),
        ),
      ),
    );
  }

  Widget _buildNoReraPermitWidget(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.red[50],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.red[200]!),
      ),
      child: Center(
        child: Column(
          children: [
            Icon(
              Icons.warning_outlined,
              size: 48,
              color: Colors.red[400],
            ),
            const SizedBox(height: 12),
            Text(
              AppLocalizations.of(context)
                  .translate('RERA Information Not Available'),
              style: TextStyle(
                color: Colors.red[700],
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              AppLocalizations.of(context)
                  .translate('RERA permit information will be updated soon'),
              style: TextStyle(
                color: Colors.red[600],
                fontSize: 14,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 12),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              decoration: BoxDecoration(
                color: Colors.red[100],
                borderRadius: BorderRadius.circular(20),
              ),
              child: Text(
                AppLocalizations.of(context)
                    .translate('Please verify RERA status before investing'),
                style: TextStyle(
                  color: Colors.red[800],
                  fontSize: 12,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showFullScreenDocument(BuildContext context, dynamic media) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => Scaffold(
          backgroundColor: Colors.black,
          appBar: AppBar(
            backgroundColor: Colors.black,
            iconTheme: const IconThemeData(color: Colors.white),
            title: Text(
              AppLocalizations.of(context).translate('RERA Document'),
              style: const TextStyle(color: Colors.white),
            ),
          ),
          body: Center(
            child: InteractiveViewer(
              child: Image.network(
                media.url ?? '',
                fit: BoxFit.contain,
              ),
            ),
          ),
        ),
      ),
    );
  }
}
