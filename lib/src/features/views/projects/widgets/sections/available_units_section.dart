import 'package:flutter/material.dart';
import 'package:page/src/core/config/constants.dart';
import 'package:page/src/core/localization/app_localizations.dart';
import 'package:page/src/features/controllers/currency_controller.dart';
import 'package:page/src/features/controllers/language_controller.dart';
import 'package:page/src/features/models/project_models.dart';
import 'package:page/src/features/models/video_model.dart';

import '../price_plan_table.dart';

class AvailableUnitsSection extends StatefulWidget {
  final VideoModel project;

  const AvailableUnitsSection({
    super.key,
    required this.project,
  });

  @override
  State<AvailableUnitsSection> createState() => _AvailableUnitsSectionState();
}

class _AvailableUnitsSectionState extends State<AvailableUnitsSection> {
  final CurrencyController _currencyController = CurrencyController();
  int? _selectedUnitIndex;

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    _currencyController.getcuurentcurrency(context).then((value) {
      setState(() {});
    });
  }

  @override
  Widget build(BuildContext context) {
    if (widget.project.projectPlans?.isEmpty ?? true) {
      return _buildNoPlansWidget(context);
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Units horizontal list
        SizedBox(
          height: 240,
          child: ListView.separated(
            shrinkWrap: true,
            scrollDirection: Axis.horizontal,
            itemCount: widget.project.projectPlans!.length,
            separatorBuilder: (context, index) => Container(
              color: Colors.grey[200],
              width: 1,
              margin: const EdgeInsets.symmetric(
                vertical: 50,
              ),
            ),
            itemBuilder: (context, index) {
              final plan = widget.project.projectPlans![index];
              return _buildPlanCard(context, plan, index);
            },
          ),
        ),

        // Price calculation tables
        if (_selectedUnitIndex != null &&
            widget.project.pricePlan != null &&
            widget.project.paymentMethod != 'cash' &&
            widget.project.pricePlan!.data?.isNotEmpty == true) ...[
          _buildPriceCalculationSection(),
        ],
      ],
    );
  }

  Widget _buildPlanCard(
      BuildContext context, ProjectPlanModel plan, int index) {
    return SizedBox(
      width: 270,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Bedrooms
            if (plan.bedrooms.isNotEmpty) ...[
              _buildPlanDetail(
                context,
                icon: Icons.bed,
                // label:
                //     AppLocalizations.of(context).translate('Bedrooms Project'),
                value: isEnglish(context)
                    ? plan.bedroomsEn ?? plan.bedroomsAr ?? ''
                    : plan.bedroomsAr ?? plan.bedroomsEn ?? '',
              ),
              const SizedBox(height: 12),
            ],

            // Space Size
            if (plan.spaceSize.isNotEmpty) ...[
              _buildPlanDetail(
                context,
                icon: Icons.square_foot,
                label: AppLocalizations.of(context).translate('Size Project'),
                value: isEnglish(context)
                    ? plan.spaceSizeEn ?? plan.spaceSizeAr ?? ''
                    : plan.spaceSizeAr ?? plan.spaceSizeEn ?? '',
              ),
              const SizedBox(height: 12),
            ],

            // Price information
            _buildPriceSection(context, plan),

            // Calculate Installments Button
            if (widget.project.pricePlan != null &&
                widget.project.paymentMethod != 'cash' &&
                widget.project.pricePlan!.data?.isNotEmpty == true) ...[
              const SizedBox(height: 12),
              TextButton(
                onPressed: () {
                  setState(() {
                    _selectedUnitIndex =
                        _selectedUnitIndex == index ? null : index;
                  });
                },
                style: TextButton.styleFrom(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  minimumSize: Size.zero,
                  tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                  backgroundColor: _selectedUnitIndex == index
                      ? const Color(0xFF27b4a8).withOpacity(0.1)
                      : null,
                ),
                child: Text(
                  AppLocalizations.of(context)
                      .translate('Calculate Installments'),
                  style: TextStyle(
                    fontSize: 14,
                    color: _selectedUnitIndex == index
                        ? const Color(0xFF27b4a8)
                        : const Color(0xFF27b4a8),
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildPlanDetail(
    BuildContext context, {
    required IconData icon,
    String? label,
    required String value,
  }) {
    return Row(
      children: [
        Icon(
          icon,
          size: 16,
          color: Colors.grey[600],
        ),
        const SizedBox(width: 8),
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (label != null)
              Text(
                label,
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey[600],
                ),
              ),
            Text(
              value,
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: Colors.black87,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildPriceSection(BuildContext context, ProjectPlanModel plan) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.green[50],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.green[200]!),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            AppLocalizations.of(context).translate('Price'),
            style: TextStyle(
              fontSize: 12,
              color: Colors.green[700],
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 4),
          if (plan.priceFrom != 0) ...[
            if (plan.priceTo != 0 && plan.priceTo != plan.priceFrom) ...[
              // Price range
              FittedBox(
                fit: BoxFit.scaleDown,
                child: Text(
                  '${sortPrice(plan.priceFrom ?? 0)} ${_currencyController.currency} - ${sortPrice(plan.priceTo ?? 0)} ${_currencyController.currency}',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: Colors.green[800],
                  ),
                ),
              ),
            ] else ...[
              // Single price
              FittedBox(
                fit: BoxFit.scaleDown,
                child: Row(
                  children: [
                    Text(
                      AppLocalizations.of(context).translate('From'),
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.green[600],
                      ),
                    ),
                    const SizedBox(width: 4),
                    Expanded(
                      child: Text(
                        '${sortPrice(plan.priceFrom ?? 0)} ${_currencyController.currency}',
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.bold,
                          color: Colors.green[800],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ] else ...[
            Text(
              AppLocalizations.of(context).translate('Price on Request'),
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: Colors.green[700],
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildPriceCalculationSection() {
    final selectedPlan = widget.project.projectPlans![_selectedUnitIndex!];

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 1,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Unit Info Header
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: const Color(0xFF27b4a8).withOpacity(0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Row(
              children: [
                const Icon(
                  Icons.calculate,
                  color: Color(0xFF27b4a8),
                  size: 20,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        isEnglish(context)
                            ? selectedPlan.bedroomsEn ??
                                selectedPlan.bedroomsAr ??
                                ''
                            : selectedPlan.bedroomsAr ??
                                selectedPlan.bedroomsEn ??
                                '',
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Color(0xFF27b4a8),
                        ),
                      ),
                      if (selectedPlan.spaceSize.isNotEmpty)
                        Text(
                          isEnglish(context)
                              ? selectedPlan.spaceSizeEn ??
                                  selectedPlan.spaceSizeAr ??
                                  ''
                              : selectedPlan.spaceSizeAr ??
                                  selectedPlan.spaceSizeEn ??
                                  '',
                          style: TextStyle(
                            fontSize: 14,
                            color: Colors.grey[600],
                          ),
                        ),
                    ],
                  ),
                ),
              ],
            ),
          ),

          const SizedBox(height: 12),

          // Price From Table
          if (selectedPlan.priceFrom != null &&
              selectedPlan.priceFrom! > 0) ...[
            PricePlanTable(
              data: widget.project.pricePlan!.data ?? [],
              title:
                  '${AppLocalizations.of(context).translate('Lowest Price')}: ${sortPrice(selectedPlan.priceFrom!)} ${_currencyController.currency}',
              multiplier: selectedPlan.priceFrom,
            ),
            const SizedBox(height: 12),
          ],

          // Price To Table
          if (selectedPlan.priceTo != null && selectedPlan.priceTo! > 0) ...[
            PricePlanTable(
              data: widget.project.pricePlan!.data ?? [],
              title:
                  '${AppLocalizations.of(context).translate('Highest Price')}: ${sortPrice(selectedPlan.priceTo!)} ${_currencyController.currency}',
              multiplier: selectedPlan.priceTo,
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildNoPlansWidget(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.grey[100],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[300]!),
      ),
      child: Center(
        child: Column(
          children: [
            Icon(
              Icons.apartment_outlined,
              size: 48,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 12),
            Text(
              AppLocalizations.of(context)
                  .translate('No Available Units Available'),
              style: TextStyle(
                color: Colors.grey[600],
                fontSize: 16,
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              AppLocalizations.of(context)
                  .translate('Available units will be added soon'),
              style: TextStyle(
                color: Colors.grey[500],
                fontSize: 14,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
