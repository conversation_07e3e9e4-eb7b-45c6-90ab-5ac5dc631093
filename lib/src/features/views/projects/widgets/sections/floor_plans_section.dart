import 'package:easy_image_viewer/easy_image_viewer.dart';
import 'package:flutter/material.dart';
import 'package:page/src/core/localization/app_localizations.dart';
import 'package:page/src/core/utils/main_cached_image.dart';
import 'package:page/src/features/controllers/language_controller.dart';
import 'package:page/src/features/models/project_models.dart';
import 'package:page/src/features/models/video_model.dart';

class FloorPlansSection extends StatefulWidget {
  final VideoModel project;

  const FloorPlansSection({
    super.key,
    required this.project,
  });

  @override
  State<FloorPlansSection> createState() => _FloorPlansSectionState();
}

class _FloorPlansSectionState extends State<FloorPlansSection> {
  late List<bool> _isExpanded;

  @override
  void initState() {
    super.initState();
    // Initialize expansion state - first one open, others closed
    _isExpanded = List.generate(
      widget.project.floorPlans?.length ?? 0,
      (index) => index == 0,
    );
  }

  @override
  Widget build(BuildContext context) {
    if (widget.project.floorPlans?.isEmpty ?? true) {
      return _buildNoFloorPlansWidget(context);
    }

    return ListView.builder(
      shrinkWrap: true,
      padding: EdgeInsets.zero,
      physics: const NeverScrollableScrollPhysics(),
      itemCount: widget.project.floorPlans!.length,
      itemBuilder: (context, index) {
        final floorPlan = widget.project.floorPlans![index];
        return _buildFloorPlanExpansionTile(context, floorPlan, index);
      },
    );
  }

  Widget _buildFloorPlanExpansionTile(
      BuildContext context, FloorPlanModel floorPlan, int index) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Theme(
        data: Theme.of(context).copyWith(
          dividerColor: Colors.transparent,
          expansionTileTheme: const ExpansionTileThemeData(
            tilePadding: EdgeInsets.zero,
            childrenPadding: EdgeInsets.zero,
          ),
        ),
        child: ExpansionTile(
          initiallyExpanded: _isExpanded[index],
          onExpansionChanged: (expanded) {
            setState(() {
              _isExpanded[index] = expanded;
            });
          },
          tilePadding: const EdgeInsets.all(16),
          childrenPadding: EdgeInsets.zero,
          title: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: const Color(0xFF27b4a8).withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Icon(
                  Icons.architecture,
                  size: 20,
                  color: Color(0xFF27b4a8),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  floorPlan.displayName.isNotEmpty
                      ? isEnglish(context)
                          ? floorPlan.nameEn ??
                              floorPlan.nameAr ??
                              floorPlan.name ??
                              ''
                          : floorPlan.nameAr ??
                              floorPlan.nameEn ??
                              floorPlan.name ??
                              ''
                      : AppLocalizations.of(context).translate('Floor Plan'),
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Colors.black87,
                  ),
                ),
              ),
            ],
          ),
          children: [
            _buildFloorPlanContent(context, floorPlan),
          ],
        ),
      ),
    );
  }

  Widget _buildFloorPlanContent(
      BuildContext context, FloorPlanModel floorPlan) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Floor plan images
        if (floorPlan.media?.isNotEmpty == true) ...[
          Builder(builder: (context) {
            return SizedBox(
              height: 200,
              child: PageView.builder(
                itemCount: floorPlan.media!.length,
                itemBuilder: (context, imageIndex) {
                  final media = floorPlan.media![imageIndex];
                  return GestureDetector(
                    onTap: () => _showFullScreenImage(
                      context,
                      floorPlan.media!,
                      imageIndex,
                      floorPlan.displayName,
                    ),
                    child: Container(
                      margin: const EdgeInsets.symmetric(horizontal: 16),
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(8),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withOpacity(0.1),
                            blurRadius: 4,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(8),
                        child: MainCachedImage(
                          media.url ?? '',
                          width: double.infinity,
                          fit: BoxFit.cover,
                          errorWidget: Container(
                            color: Colors.grey[200],
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Icon(
                                  Icons.image_not_supported,
                                  size: 40,
                                  color: Colors.grey[400],
                                ),
                                const SizedBox(height: 8),
                                Text(
                                  AppLocalizations.of(context)
                                      .translate('Image not available'),
                                  style: TextStyle(
                                    color: Colors.grey[600],
                                    fontSize: 12,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ),
                  );
                },
              ),
            );
          }),

          // Image indicators if multiple images
          if (floorPlan.media!.length > 1) ...[
            const SizedBox(height: 12),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: floorPlan.media!.asMap().entries.map((entry) {
                return Container(
                  width: 6,
                  height: 6,
                  margin: const EdgeInsets.symmetric(horizontal: 3),
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: Colors.grey[400],
                  ),
                );
              }).toList(),
            ),
          ],

          const SizedBox(height: 16),
        ] else ...[
          // No images placeholder
          Container(
            height: 150,
            margin: const EdgeInsets.symmetric(horizontal: 16),
            decoration: BoxDecoration(
              color: Colors.grey[100],
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.grey[300]!),
            ),
            child: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.image_not_supported,
                    size: 40,
                    color: Colors.grey[400],
                  ),
                  const SizedBox(height: 8),
                  Text(
                    AppLocalizations.of(context)
                        .translate('No Images Available'),
                    style: TextStyle(
                      color: Colors.grey[600],
                      fontSize: 14,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],

        // Floor plan actions
        Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: floorPlan.media?.isNotEmpty == true
                      ? () => _showFullScreenImage(
                            context,
                            floorPlan.media!,
                            0,
                            floorPlan.displayName,
                          )
                      : null,
                  icon: const Icon(Icons.zoom_in, size: 18),
                  label: Text(
                    AppLocalizations.of(context).translate('View Full Size'),
                    style: const TextStyle(fontSize: 14),
                  ),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xFF27b4a8),
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 12),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                ),
              ),
              if ((floorPlan.media?.length ?? 0) > 1) ...[
                const SizedBox(width: 12),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 8,
                  ),
                  decoration: BoxDecoration(
                    color: Colors.blue[50],
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.blue[200]!),
                  ),
                  child: Text(
                    '${floorPlan.media!.length} ${AppLocalizations.of(context).translate('Images')}',
                    style: TextStyle(
                      color: Colors.blue[700],
                      fontSize: 12,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ],
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildNoFloorPlansWidget(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.grey[100],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[300]!),
      ),
      child: Center(
        child: Column(
          children: [
            Icon(
              Icons.architecture_outlined,
              size: 48,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 12),
            Text(
              AppLocalizations.of(context)
                  .translate('No Floor Plans Available'),
              style: TextStyle(
                color: Colors.grey[600],
                fontSize: 16,
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              AppLocalizations.of(context)
                  .translate('Floor plans will be added soon'),
              style: TextStyle(
                color: Colors.grey[500],
                fontSize: 14,
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showFullScreenImage(
    BuildContext context,
    List<dynamic> mediaList,
    int initialIndex,
    String title,
  ) {
    final imageUrls =
        mediaList.map((media) => media.url ?? '').toList().cast<String>();
    final imageProviders = imageUrls.map((url) => NetworkImage(url)).toList();

    final multiImageProvider =
        MultiImageProvider(imageProviders, initialIndex: initialIndex);

    showImageViewerPager(
      context,
      multiImageProvider,
      // initialIndex: initialIndex,
      onPageChanged: (page) {
        // Optional: handle page change
      },
      onViewerDismissed: (page) {
        // Optional: handle viewer dismissed
      },
      swipeDismissible: true,
      doubleTapZoomable: true,
    );
  }
}
