import 'package:flutter/material.dart';
import 'package:page/src/core/localization/app_localizations.dart';
import 'package:page/src/features/controllers/content_controller.dart';
import 'package:page/src/features/models/locations.dart';
import 'package:page/src/features/models/project_models.dart';

void projectFilter(
  BuildContext context, {
  required VoidCallback onApply,
  required ContentController contentController,
}) {
  showModalBottomSheet(
    context: context,
    isScrollControlled: true,
    backgroundColor: Colors.transparent,
    builder: (context) => ProjectFilterWidget(
      onApply: onApply,
      contentController: contentController,
    ),
  );
}

void resetProjectFilter() {
  projectLocationValue = null;
  projectPropertyStatusValue = null;
  projectPaymentMethodValue = null;
}

int? projectLocationValue;
int? projectPropertyStatusValue;
String? projectPaymentMethodValue;

class ProjectFilterWidget extends StatefulWidget {
  final VoidCallback onApply;
  final ContentController contentController;

  const ProjectFilterWidget({
    super.key,
    required this.onApply,
    required this.contentController,
  });

  @override
  State<ProjectFilterWidget> createState() => _ProjectFilterWidgetState();
}

class _ProjectFilterWidgetState extends State<ProjectFilterWidget> {
  @override
  Widget build(BuildContext context) {
    return Padding(
      padding:
          EdgeInsets.only(bottom: MediaQuery.of(context).viewInsets.bottom),
      child: Container(
        height: MediaQuery.of(context).size.height * 0.70,
        decoration: BoxDecoration(
          color: const Color(0xffF5F6F7),
          borderRadius: const BorderRadius.only(
            topLeft: Radius.circular(25.0),
            topRight: Radius.circular(25.0),
          ),
          border: Border.all(color: Colors.black, width: 1.0),
        ),
        child: SingleChildScrollView(
          child: Padding(
            padding: const EdgeInsets.all(20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Header
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      AppLocalizations.of(context).translate('Filter'),
                      style: const TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    IconButton(
                      onPressed: () => Navigator.pop(context),
                      icon: const Icon(Icons.close),
                    ),
                  ],
                ),
                const SizedBox(height: 20),

                // Location Filter
                Container(
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(10),
                  ),
                  child: Container(
                    padding: const EdgeInsets.all(15),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          AppLocalizations.of(context).translate('location'),
                          style: const TextStyle(fontSize: 13),
                        ),
                        const SizedBox(height: 10),
                        ContentController.locations.isNotEmpty
                            ? Container(
                                height: 50,
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(5),
                                  border: Border.all(
                                      color: Colors.black12, width: 1.0),
                                ),
                                child: DropdownButton<int>(
                                  isExpanded: true,
                                  hint: Padding(
                                    padding: const EdgeInsets.only(
                                        left: 20, right: 20),
                                    child: Text(
                                      AppLocalizations.of(context)
                                          .translate('Choose Location'),
                                      style: const TextStyle(
                                          color: Color(0xffB7B7B7)),
                                    ),
                                  ),
                                  value: projectLocationValue,
                                  dropdownColor: Colors.white,
                                  borderRadius: BorderRadius.circular(12),
                                  underline: const SizedBox(),
                                  iconEnabledColor: Colors.black,
                                  items: ContentController.locations
                                      .map((Locations value) {
                                    return DropdownMenuItem<int>(
                                      value: value.id,
                                      child: Container(
                                        padding: const EdgeInsets.only(
                                            left: 10, right: 10),
                                        child: Text(
                                          value.name!,
                                          style: const TextStyle(fontSize: 16),
                                        ),
                                      ),
                                    );
                                  }).toList(),
                                  onChanged: (value) {
                                    setState(() {
                                      projectLocationValue = value;
                                    });
                                  },
                                ),
                              )
                            : Text(AppLocalizations.of(context)
                                .translate('No Loction to show')),
                      ],
                    ),
                  ),
                ),
                const SizedBox(height: 20),

                // Property Status Filter
                Container(
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(10),
                  ),
                  child: Container(
                    padding: const EdgeInsets.all(15),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          AppLocalizations.of(context)
                              .translate('Property Status'),
                          style: const TextStyle(fontSize: 13),
                        ),
                        const SizedBox(height: 10),
                        ContentController.propertyStatuses.isNotEmpty
                            ? Container(
                                height: 50,
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(5),
                                  border: Border.all(
                                      color: Colors.black12, width: 1.0),
                                ),
                                child: DropdownButton<int>(
                                  isExpanded: true,
                                  dropdownColor: Colors.white,
                                  borderRadius: BorderRadius.circular(10),
                                  hint: Padding(
                                    padding: const EdgeInsets.only(
                                        left: 20, right: 20),
                                    child: Text(
                                      AppLocalizations.of(context)
                                          .translate('Choose Property Status'),
                                      style: const TextStyle(
                                          color: Color(0xffB7B7B7)),
                                    ),
                                  ),
                                  value: projectPropertyStatusValue,
                                  underline: const SizedBox(),
                                  iconEnabledColor: Colors.black,
                                  items: ContentController.propertyStatuses
                                      .map((PropertyStatusModel value) {
                                    return DropdownMenuItem<int>(
                                      value: value.id,
                                      child: Container(
                                        padding: const EdgeInsets.only(
                                            left: 10, right: 10),
                                        child: Text(
                                          value.displayName,
                                          style: const TextStyle(fontSize: 16),
                                        ),
                                      ),
                                    );
                                  }).toList(),
                                  onChanged: (value) {
                                    setState(() {
                                      projectPropertyStatusValue = value;
                                    });
                                  },
                                ),
                              )
                            : Text(AppLocalizations.of(context)
                                .translate('No Property Status to show')),
                      ],
                    ),
                  ),
                ),
                const SizedBox(height: 20),

                // Payment Method Filter
                Container(
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(10),
                  ),
                  child: Container(
                    padding: const EdgeInsets.all(15),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          AppLocalizations.of(context)
                              .translate('Payment Method'),
                          style: const TextStyle(fontSize: 13),
                        ),
                        const SizedBox(height: 10),
                        Container(
                          height: 50,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(5),
                            border:
                                Border.all(color: Colors.black12, width: 1.0),
                          ),
                          child: DropdownButton<String>(
                            isExpanded: true,
                            dropdownColor: Colors.white,
                            borderRadius: BorderRadius.circular(10),
                            hint: Padding(
                              padding:
                                  const EdgeInsets.only(left: 20, right: 20),
                              child: Text(
                                AppLocalizations.of(context)
                                    .translate('Choose Payment Method'),
                                style:
                                    const TextStyle(color: Color(0xffB7B7B7)),
                              ),
                            ),
                            value: projectPaymentMethodValue,
                            underline: const SizedBox(),
                            iconEnabledColor: Colors.black,
                            items: [
                              DropdownMenuItem<String>(
                                value: 'cash',
                                child: Container(
                                  padding: const EdgeInsets.only(
                                      left: 10, right: 10),
                                  child: Text(
                                    AppLocalizations.of(context)
                                        .translate('Cash'),
                                    style: const TextStyle(fontSize: 16),
                                  ),
                                ),
                              ),
                              DropdownMenuItem<String>(
                                value: 'installment',
                                child: Container(
                                  padding: const EdgeInsets.only(
                                      left: 10, right: 10),
                                  child: Text(
                                    AppLocalizations.of(context)
                                        .translate('Installment'),
                                    style: const TextStyle(fontSize: 16),
                                  ),
                                ),
                              ),
                            ],
                            onChanged: (value) {
                              setState(() {
                                projectPaymentMethodValue = value;
                              });
                            },
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                const SizedBox(height: 30),

                // Apply and Reset buttons
                Row(
                  children: [
                    Expanded(
                      child: ElevatedButton(
                        onPressed: () {
                          resetProjectFilter();
                          setState(() {});
                          widget.onApply();
                        },
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.grey[300],
                          foregroundColor: Colors.black,
                          padding: const EdgeInsets.symmetric(vertical: 15),
                        ),
                        child: Text(
                          AppLocalizations.of(context).translate('Reset'),
                        ),
                      ),
                    ),
                    const SizedBox(width: 15),
                    Expanded(
                      child: ElevatedButton(
                        onPressed: () {
                          Navigator.pop(context);
                          widget.onApply();
                        },
                        style: ElevatedButton.styleFrom(
                          backgroundColor: const Color(0xFF27b4a8),
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(vertical: 15),
                        ),
                        child: Text(
                          AppLocalizations.of(context).translate('Apply'),
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
