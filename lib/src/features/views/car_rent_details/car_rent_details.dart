import 'dart:developer';

import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:google_cloud_translation/google_cloud_translation.dart';
import 'package:page/src/core/utils/logger.dart';
import 'package:page/src/features/views/ad_details/services/cached_video_services.dart';
import 'package:page/src/features/views/ad_details/video_widget.dart';
import 'package:page/src/features/views/car_rent_details/widgets/car_rental_details.dart';
import 'package:page/src/features/views/car_rent_details/widgets/discussion.dart';
import 'package:page/src/features/views/car_rent_details/widgets/join_discussion.dart';
import 'package:page/src/features/views/send_request_car/send_request_car_rental.dart';
import 'package:page/src/features/views/splash_screen/services/splash_services.dart';
import 'package:page/src/features/views/story/widgets/see_more.dart';
import 'package:share_plus/share_plus.dart';
import 'package:video_player/video_player.dart';

import '../../../core/localization/app_localizations.dart';
import '../../../core/response/generalResponse.dart';
import '../../../core/services/api.dart';
import '../../../core/shared_widgets/back_button.dart';
import '../../../core/shared_widgets/reel_images.dart';
import '../../../core/shared_widgets/shared_widgets.dart';
import '../../../core/utils/dynamic_links.dart';
import '../../controllers/auth_controller.dart';
import '../../controllers/currency_controller.dart';
import '../../controllers/language_controller.dart';
import '../../models/discussions.dart';
import '../../models/video_model.dart';
import '../holiday_home_details/from_shared_link_holiday_home_details.dart';

bool isactions = false;
TextEditingController note = TextEditingController();

class CarRentDetailsVideoWidget extends StatefulWidget {
  final VideoModel? video;

  const CarRentDetailsVideoWidget({
    super.key,
    required this.video,
  });

  @override
  State<StatefulWidget> createState() {
    return _CarRentDetails();
  }
}

class _CarRentDetails extends State<CarRentDetailsVideoWidget> {
  VideoPlayerController? _controller;

  // => videoPlayerController[id];

  bool isdiscussion = false;
  int imageindex = 0;
  int? imageindex2;
  Translation? _translation;
  TranslationModel? _translated;
  LanguageController language = LanguageController();
  String? text;
  int index2 = -1;
  AuthController authController = AuthController();
  CurrencyController currencyController = CurrencyController();
  bool isload = false;

  bool ismute = false;
  bool isfav = false;

  List<Discussions> dis = [];
  int pagenumber = 1;
  int? code;
  String msg = 'Loading';

  int get id => widget.video!.id!;

  String? get video => widget.video?.video;

  String? get name => widget.video?.name;

  String? get label => widget.video?.category?.name;

  String? get phone => widget.video?.phone;

  int? get rating => widget.video?.rating;

  String? get description => widget.video?.description;

  String? get location => widget.video?.locationName;

  String? get website => widget.video?.website;

  String? get instagram => widget.video?.instagram;

  String? get greviewlink => widget.video?.greviewlink;

  String? get greviewName => widget.video?.greviewName;

  String? get category => widget.video?.category?.name;

  num? get startprice => widget.video?.startprice;

  num? get endprice => widget.video?.endprice;

  num? get price => widget.video?.price;

  double? get lat => widget.video?.latitude;

  double? get lng => widget.video?.longitude;

  int? get agentId => widget.video?.agent?.id;

  String? get agentName => widget.video?.agent?.name;

  int? get size => widget.video?.size;

  String? get roomnumber => widget.video?.rooms;

  String? get type => widget.video?.type;

  int? get isfavouriate => widget.video?.isFav;

  set isfavouriate(int? value) => widget.video?.isFav = value;

  String? get brand => widget.video?.brand;

  String? get year => widget.video?.year;

  String? get feature => widget.video?.feature;

  num? get privateDriverPrice => widget.video?.privateDriverPrice;

  getdiscussions() async {
    progrsss(context);
    pr!.show();
    await Api.getcarrentdiscussions(pagenumber, 20, id).then((value) {
      value != null
          ? setState(() {
              dis.addAll(value.dscussions);
              code = value.code;
              msg = value.msg;

              // isload = true;
            })
          // ignore: unnecessary_statements
          : null;
      discussionCarRent(context,
          translated: _translated,
          translation: _translation,
          id: id, function: () {
        setState(() {
          dis = [];
          discussioncontroller.text = "";
          getdiscussions();
          discussioncontroller.text = "";
          // isfav = true;
        });
      });
    });
  }

  getmorerelatedreels() async {
    await Api.getmainCategorydetails(id).then((value) {
      value != null
          ? setState(() {
              isload = true;
            })
          : null;
    }).catchError((e) {
      log(e);
    });
  }

  int? carRentalId;

  @override
  void initState() {
    authController.isloggedin();
    initPlayer();

    print("dffdffd");
    currencyController.getcuurentcurrency(context);
    _translation = Translation(
      apiKey: 'AIzaSyCXOO147BdbuceLIl8Z8D5Jxru2Vjhqd4Q',
    );
    language.getcuurentlanguage();
    super.initState();
  }

  // void initPlayer() async {
  //   _controller = VideoPlayerController.networkUrl(
  //     Uri.parse(video!),
  //   );
  //
  //   if (_controller?.value.isInitialized ?? false) {
  //     _controller?.play();
  //   } else {
  //     _controller = VideoPlayerController.networkUrl(
  //       Uri.parse(video!),
  //     );
  //
  //     try {
  //       log('Errofsdhdfhfhsgwd');
  //
  //       await _controller?.initialize();
  //
  //       _controller?.play();
  //     } catch (e) {
  //       log('ErrofssfrRR: $e');
  //     }
  //   }
  //
  //   setState(() {
  //     isload = true;
  //   });
  // }
  final cachedVideoServices = CachedVideoServices();

  void initPlayer() async {
    Future.delayed(const Duration(seconds: cacheDisposeDuration), () async {
      if (!(_controller?.value.isInitialized ?? false)) {
        await Future.microtask(() => cachedVideoServices.disposeCache());
      }
    });

    final fileInfo = await Future.microtask(
        () => cachedVideoServices.checkVideoIfCached(video!));

    if (fileInfo == null) {
      _controller = VideoPlayerController.networkUrl(
        Uri.parse(video!),
      );

      Future.microtask(() => cachedVideoServices.cacheVideo(video!));

      Log.i('CachedVideoServices: $video');
    } else {
      _controller = VideoPlayerController.file(fileInfo.file);

      Log.w('AlreadyCached: $video');
    }

    if (_controller?.value.isInitialized ?? false) {
      _controller?.play();
    } else {
      try {
        await Future.microtask(() => _controller?.initialize());

        _controller?.play();
      } catch (e) {
        Log.e('ErrorOccurred: $video');
      }
    }

    setState(() {
      isload = true;
    });
  }

  @override
  void dispose() {
    _controller?.pause();
    _controller?.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final isEng = isEnglish(context);

    return WillPopScope(
        onWillPop: () {
          SplashServices().initControllersForHomeData();

          if (_controller!.value.isPlaying) {
            _controller!.pause();
            // _controller = null;
            // _controller?.dispose();
          }
          setState(() {
            isactions = false;
          });

          return Future.value(true);
        },
        child: SafeArea(
            child: Scaffold(
          backgroundColor: Colors.black,
          body: Stack(
            alignment: Alignment.bottomCenter,
            children: <Widget>[
              _controller != null
                  ? SizedBox(
                      height: MediaQuery.sizeOf(context).height,
                      width: MediaQuery.sizeOf(context).width,
                      child: _controller!.value.isInitialized
                          ? VideoPlayer(_controller!)
                          : const SizedBox())
                  : const SizedBox(),

              // if (!(_controller?.value.isInitialized ?? false)) ...[
              //   Positioned.fill(
              //     child: Shimmer.fromColors(
              //         baseColor: Colors.black.withOpacity(0.5),
              //         highlightColor: Colors.grey.withOpacity(0.5),
              //         child: Container(
              //           color: Colors.black.withOpacity(0.5),
              //         )),
              //   ),
              // ],

              // _controller != null
              //     ? SizedBox(
              //         height: MediaQuery.of(context).size.height,
              //         width: MediaQuery.of(context).size.width,
              //         child: _controller!.value.isInitialized
              //             ? FittedBox(
              //                 fit: BoxFit.fill,
              //                 child: SizedBox(
              //                     height: _controller!.value.size.height,
              //                     width: _controller!.value.size.width,
              //                     child: VideoPlayer(_controller!)))
              //             : Container())
              //     : Container(),
              Positioned(
                  left: 10,
                  right: 10,
                  top: 20,
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      BackButtonWidget(
                        onTap: () {
                          SplashServices().initControllersForHomeData();
                          if (_controller!.value.isPlaying) {
                            _controller!.pause();
                          }
                          setState(() {
                            isactions = false;
                          });
                        },
                      ),
                      InkWell(
                          onTap: () {
                            setState(() {
                              ismute = !ismute;
                              ismute
                                  ? _controller!.setVolume(0.0)
                                  : _controller!.setVolume(30.0);
                            });
                            // _controller!.setVolume(0.0);
                          },
                          child: !ismute
                              ? const Icon(
                                  Icons.volume_down,
                                  color: Colors.white,
                                )
                              : const Icon(
                                  Icons.volume_off,
                                  color: Colors.white,
                                ))
                    ],
                  )),
              if (!isEng)
                Positioned(bottom: 65, right: 30, child: _nameDetails())
              else
                Positioned(bottom: 65, left: 25, child: _nameDetails()),
              _controller != null
                  ? Padding(
                      padding: const EdgeInsetsDirectional.only(
                        bottom: 25,
                        top: 9,
                        start: 10,
                        end: 10,
                      ),
                      child: Row(
                        children: [
                          Expanded(
                            child: IconButton(
                                onPressed: () {
                                  _controller!.value.isPlaying
                                      ? _controller!.pause()
                                      : _controller!.play();
                                  setState(() {});
                                },
                                icon: Icon(
                                  _controller!.value.isPlaying
                                      ? Icons.pause
                                      : Icons.play_arrow,
                                  color: Colors.white,
                                )),
                          ),
                          const SizedBox(width: 10),
                          Expanded(
                            flex: 8,
                            child: VideoProgressIndicator(
                              _controller!,
                              allowScrubbing: true,
                              colors: VideoProgressColors(
                                  playedColor: _controller!.value.isInitialized
                                      ? Colors.white
                                      : const Color(0xFF27b4a8),
                                  backgroundColor:
                                      Colors.white.withOpacity(0.44)),
                            ),
                          ),
                          const SizedBox(width: 10),
                          Expanded(
                            child: FittedBox(
                              fit: BoxFit.scaleDown,
                              child: Text(
                                _controller!.value.duration
                                    .toString()
                                    .substring(
                                        _controller!.value.duration
                                                .toString()
                                                .indexOf(":") +
                                            1,
                                        _controller!.value.duration
                                            .toString()
                                            .indexOf(".")),
                                maxLines: 1,
                                style: const TextStyle(color: Colors.white),
                              ),
                            ),
                          ),
                        ],
                      ),
                    )
                  : Container(),
              if (!isEng)
                Positioned(bottom: 75, left: 20, child: _columnDetailsWidget())
              else
                Positioned(
                    bottom: 75, right: 20, child: _columnDetailsWidget()),
              isactions
                  ? JoinDiscussionCarRent(
                      onShare: () async {
                        try {
                          final String linkPathData =
                              '?id=$id&type=${'car-rental'}';
                          final dynamicLink =
                              await DynamicLinkHandler.createDynamicLink(
                            linkPathData,
                          );

                          log('sdfdfdsfsf ${dynamicLink.toString()}');

                          Share.share(dynamicLink.toString()).then((value) {
                            setState(() {
                              isactions = false;
                            });
                          });
                        } catch (e) {
                          log('Eerrrror ${e.toString()}');
                        }
                      },
                      onJoinDiscussion: () {
                        getdiscussions();
                      },
                      onCancel: () {
                        setState(() {
                          isactions = !isactions;
                        });
                      },
                      name: name)
                  : Container(),

              //? linear progress indicator
              if (!(_controller?.value.isInitialized ?? false))
                Positioned(
                  bottom: 40,
                  right: 0,
                  left: 0,
                  child: LinearProgressIndicator(
                    backgroundColor: Colors.black.withOpacity(0.8),
                    valueColor: AlwaysStoppedAnimation<Color>(
                      Colors.white.withOpacity(0.3),
                    ),
                  ),
                ),
            ],
          ),
        )));
  }

  Widget _columnDetailsWidget() {
    final Widget svg5 = SizedBox(
        width: 22,
        height: 25,
        child: SvgPicture.asset(
          'assets/Group 7408.svg',
          semanticsLabel: 'Acme Logo',
          fit: BoxFit.cover,
        ));
    final Widget svg6 = SizedBox(
        width: 30,
        height: 25,
        child: SvgPicture.asset(
          'assets/media_icon.svg',
          semanticsLabel: 'Acme Logo',
          width: 13,
          height: 13,
          fit: BoxFit.fill,
          color: Colors.white,
        ));
    return Column(
      children: [
        InkWell(
          onTap: () {
            _controller!.pause();
            authController.isLogged == true
                ? Navigator.of(context).pushReplacement(MaterialPageRoute(
                    builder: (BuildContext context) => SendRequestCarRental(
                          id,
                          agentId: agentId!,
                          startPrice: startprice,
                          privateDriverPrice: privateDriverPrice,
                        )))
                : snackbar(AppLocalizations.of(context)
                    .translate('Please login first'));
          },
          child: svg5,
        ),
        const SizedBox(height: 20),
        InkWell(
            onTap: () {
              _controller!.pause();

              moreimagesrelated(context, id, _controller!);
            },
            child: svg6),
        const SizedBox(
          height: 20,
        ),
        authController.isLogged == true
            ? isfavouriate == 1
                ? InkWell(
                    onTap: () async {
                      GeneralResponse sucessinformation =
                          await Api.removecarrentfromfavourite(id);
                      print(sucessinformation.code);
                      if (sucessinformation.code == "1") {
                        snackbar2(AppLocalizations.of(context)
                            .translate('remove from favourite successfuly'));
                        setState(() {
                          isfavouriate = 0;
                        });
                      } else {
                        snackbar(AppLocalizations.of(context).translate(
                            'Something went wrong, please try again later'));
                      }
                    },
                    child: const Icon(
                      Icons.favorite,
                      color: Colors.white,
                    ))
                : InkWell(
                    onTap: () async {
                      GeneralResponse sucessinformation =
                          await Api.addmaincategoryfavourite(id);
                      print(sucessinformation.code);
                      if (sucessinformation.code == "1") {
                        snackbar2(AppLocalizations.of(context)
                            .translate('add to favourite successfuly'));
                        setState(() {
                          isfavouriate = 1;
                        });
                      } else {
                        snackbar(AppLocalizations.of(context).translate(
                            'Something went wrong, please try again later'));
                      }
                    },
                    child: const Icon(
                      Icons.favorite_outline,
                      color: Colors.white,
                    ))
            : InkWell(
                onTap: () async {
                  snackbar(AppLocalizations.of(context)
                      .translate('Please login first'));
                },
                child: const Icon(
                  Icons.favorite_outline,
                  color: Colors.white,
                )),
        const SizedBox(
          height: 10,
        ),
        GestureDetector(
            onTap: () {
              _controller!.pause();
              setState(() {
                isactions = !isactions;
              });
            },
            child: Container(
                color: Colors.transparent,
                child: const Icon(Icons.more_horiz,
                    color: Colors.white, size: 30)))
      ],
    );
  }

  Widget _nameDetails() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        name != null
            ? Text(
                name!,
                style: const TextStyle(
                    color: Colors.white, fontWeight: FontWeight.w700),
              )
            : const SizedBox(),
        const SizedBox(
          height: 5,
        ),
        SizedBox(
            width: MediaQuery.of(context).size.width * 0.75,
            child: Text.rich(
              TextSpan(
                style: const TextStyle(
                  color: Colors.white,
                ),
                children: [
                  TextSpan(
                    text: description != null
                        ? description!.length > maxChars
                            ? description?.substring(0, maxChars)
                            : description
                        : '',
                    style: const TextStyle(
                      fontWeight: FontWeight.normal,
                    ),
                  ),
                  TextSpan(
                    text: AppLocalizations.of(context).translate('seemore'),
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                    ),
                    recognizer: TapGestureRecognizer()
                      ..onTap = () {
                        _controller!.pause();
                        _controller!.pause();
                        carrentaldetaialsWidget(context,
                            id: id,
                            agentId: agentId!,
                            description: description,
                            name: name,
                            startprice: startprice,
                            privateDriverPrice: privateDriverPrice,
                            phone: phone,
                            currencyController: currencyController,
                            type: type,
                            brand: brand,
                            feature: feature,
                            featureList: widget.video!.featureList,
                            year: year);
                      },
                  ),
                ],
              ),
              overflow: TextOverflow.ellipsis,
              maxLines: 1,
            ))
      ],
    );
  }
}
