import 'package:flutter/material.dart';

import '../../../../core/localization/app_localizations.dart';

class JoinDiscussionCarRent extends StatelessWidget {
  final onJoinDiscussion;
  final onCancel;
  final name;
  final onShare;
  const JoinDiscussionCarRent(
      {Key? key,
      required this.onJoinDiscussion,
      required this.onCancel,
      required this.onShare,
      required this.name})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      height: MediaQuery.of(context).size.height,
      width: MediaQuery.of(context).size.width,
      color: Colors.grey.withOpacity(0.4),
      child: Container(
          padding: const EdgeInsets.only(
            left: 10,
            right: 10,
            bottom: 20,
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.end,
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(20),
                  color: Colors.grey[100],
                ),
                width: MediaQuery.of(context).size.width,
                child: Column(
                  children: [
                    const SizedBox(
                      height: 20,
                    ),
                    InkWell(
                      onTap: onJoinDiscussion,
                      child: SizedBox(
                          width: MediaQuery.of(context).size.width,
                          child: Center(
                              child: Text(
                            AppLocalizations.of(context)
                                .translate('Joindiscussion'),
                            style: const TextStyle(
                                color: Color(0xff007AFF), fontSize: 18),
                          ))),
                    ),
                    const SizedBox(
                      height: 10,
                    ),
                    Divider(
                      color: Colors.green[100],
                    ),
                    const SizedBox(
                      height: 10,
                    ),
                    InkWell(
                        onTap: onShare,
                        //     () {
                        //   Share.share(
                        //       name! + '    ' + 'https://play.google.com/');
                        // },
                        child: SizedBox(
                            width: MediaQuery.of(context).size.width,
                            child: Center(
                                child: Text(
                              AppLocalizations.of(context).translate('Share'),
                              style: const TextStyle(
                                  color: Color(0xff007AFF), fontSize: 18),
                            )))),
                    const SizedBox(
                      height: 20,
                    ),
                  ],
                ),
              ),
              const SizedBox(
                height: 10,
              ),
              InkWell(
                  onTap: onCancel,
                  child: Container(
                    height: 50,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(20),
                      color: Colors.white,
                    ),
                    width: MediaQuery.of(context).size.width,
                    child: Center(
                      child: Text(
                        AppLocalizations.of(context).translate('Cancel'),
                        style: const TextStyle(
                            color: Color(0xff007AFF), fontSize: 18),
                      ),
                    ),
                  ))
            ],
          )),
    );
  }
}
