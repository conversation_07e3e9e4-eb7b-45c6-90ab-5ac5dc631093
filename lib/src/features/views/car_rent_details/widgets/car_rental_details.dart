import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:page/src/core/shared_widgets/shared_widgets.dart';
import 'package:page/src/features/controllers/auth_controller.dart';
import 'package:page/src/features/models/video_model.dart';
import 'package:page/src/features/views/car_rent_details/car_rent_details.dart';
import 'package:page/src/features/views/property_details/widgets/property_details.dart';
import 'package:page/src/features/views/send_request_car/send_request_car_rental.dart';

import '../../../../core/localization/app_localizations.dart';

Future<void> carrentaldetaialsWidget(
  BuildContext context, {
  required int id,
  required int agentId,
  required description,
  required phone,
  required startprice,
  required privateDriverPrice,
  required currencyController,
  required type,
  required brand,
  required year,
  required feature,
  required featureList,
  required name,
  bool fromReels = false,
  VideoModel? video,
}) {
  log('afsfa ${featureList}');
  return showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Padding(
          padding:
              EdgeInsets.only(bottom: MediaQuery.of(context).viewInsets.bottom),
          child: Container(
            height: MediaQuery.of(context).size.height * 0.60,
            decoration: BoxDecoration(
                color: const Color(0xffF5F6F7),
                borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(25.0),
                    topRight: Radius.circular(25.0)),
                border: Border.all(color: Colors.black, width: 1.0)),
            child: SingleChildScrollView(
                child: Column(
              children: [
                const SizedBox(
                  height: 10,
                ),
                Container(height: 5, width: 50, color: const Color(0xffD2D4D6)),
                const SizedBox(
                  height: 20,
                ),
                Center(
                    child: Text(
                  name,
                  style: const TextStyle(fontWeight: FontWeight.bold),
                )),
                Container(
                  padding: const EdgeInsets.all(15),
                  child: Container(
                    decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(10)),
                    child: Container(
                      padding: const EdgeInsets.all(15),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Container(
                                  height: 30,
                                  width: 70,
                                  color: const Color(0xffF1F1F1),
                                  child: Center(
                                      child: FittedBox(
                                    fit: BoxFit.scaleDown,
                                    child: Text(
                                      AppLocalizations.of(context)
                                          .translate('carrental'),
                                      style: const TextStyle(
                                          fontWeight: FontWeight.bold),
                                      maxLines: 1,
                                    ),
                                  ))),
                            ],
                          ),
                          const SizedBox(
                            height: 10,
                          ),
                          Row(
                            children: [
                              Text(
                                  '${AppLocalizations.of(context).translate('Day Price')}: '),
                              const SizedBox(
                                width: 5,
                              ),
                              startprice != null
                                  ? Text(
                                      '${parsedPrice(startprice)} ' +
                                          currencyController.currency,
                                      style: const TextStyle(
                                          fontWeight: FontWeight.bold),
                                    )
                                  : Container(),
                            ],
                          ),
                          const SizedBox(
                            height: 10,
                          ),
                          Row(
                            children: [
                              Text(
                                  '${AppLocalizations.of(context).translate('type')}:'),
                              const SizedBox(
                                width: 10,
                              ),
                              type != null
                                  ? Text(
                                      type.toString(),
                                      style: const TextStyle(
                                          fontWeight: FontWeight.bold),
                                    )
                                  : Container(),
                            ],
                          ),
                          const SizedBox(
                            height: 10,
                          ),
                          Row(
                            children: [
                              Text(
                                  '${AppLocalizations.of(context).translate('brand')}:'),
                              const SizedBox(
                                width: 10,
                              ),
                              brand != null
                                  ? Text(
                                      brand!,
                                      style: const TextStyle(
                                          fontWeight: FontWeight.bold),
                                    )
                                  : Container(),
                            ],
                          ),
                          const SizedBox(
                            height: 10,
                          ),
                          Row(
                            children: [
                              Text(
                                  '${AppLocalizations.of(context).translate('Year')}:'),
                              const SizedBox(
                                width: 10,
                              ),
                              year != null
                                  ? Text(
                                      year.toString(),
                                      style: const TextStyle(
                                          fontWeight: FontWeight.bold),
                                    )
                                  : Container(),
                            ],
                          ),
                          const SizedBox(
                            height: 10,
                          ),
                          Row(
                            children: [
                              Text(
                                  '${AppLocalizations.of(context).translate('Feature')}:'),
                              const SizedBox(
                                width: 10,
                              ),
                              featureList != null && featureList.isNotEmpty
                                  ? Text(
                                      featureList
                                          .toString()
                                          .replaceAll('[', '')
                                          .replaceAll(']', ''),
                                      style: const TextStyle(
                                          fontWeight: FontWeight.bold),
                                    )
                                  : Container(),
                            ],
                          ),
                          const SizedBox(
                            height: 10,
                          ),
                          Divider(
                            color: Colors.grey[100],
                          ),
                          const SizedBox(
                            height: 10,
                          ),
                          description != null
                              ? Text(
                                  description.toString(),
                                  softWrap: true,
                                )
                              : Container(),
                          const SizedBox(
                            height: 10,
                          ),
                          Center(
                              child: GestureDetector(
                                  onTap: () async {
                                    try {
                                      final String? phoneNumber = phone
                                          ?.replaceAll(RegExp(r'[^0-9]'), '');

                                      log('Call Phone $phoneNumber');

                                      await callNumber(phoneNumber!);
                                      // await launchUrlString("tel:$phoneNumber");
                                    } catch (e) {
                                      log('Cannot open $e');
                                    }
                                  },
                                  child: Container(
                                    height: 40,
                                    width: MediaQuery.of(context).size.width,
                                    decoration: BoxDecoration(
                                        color: const Color(0xffF3F7FB),
                                        borderRadius: BorderRadius.circular(5)),
                                    child: Container(
                                        padding: const EdgeInsets.all(5),
                                        child: Center(
                                          child: Text(
                                            AppLocalizations.of(context)
                                                .translate('Call Car Rental'),
                                            style: const TextStyle(
                                                color: Color(0xff0852AB)),
                                          ),
                                        )),
                                  ))),
                          const SizedBox(
                            height: 20,
                          ),
                          _SendRequest(
                            id: id,
                            agentId: agentId,
                            price: startprice,
                            privateDriverPrice: num.parse(
                                privateDriverPrice?.toString() ?? '0'),
                          ),
                          const SizedBox(
                            height: 20,
                          ),
                          if (fromReels)
                            _OpenVideoDetails(id: id, video: video),
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            )),
          )));
}

class _OpenVideoDetails extends StatelessWidget {
  final id;
  final VideoModel? video;

  const _OpenVideoDetails({
    Key? key,
    this.id,
    required this.video,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Center(
        child: GestureDetector(
            onTap: () async {
              Navigator.of(context).push(MaterialPageRoute(
                  builder: (BuildContext context) => CarRentDetailsVideoWidget(
                        video: video,
                      )));
            },
            child: Container(
              height: 40,
              width: MediaQuery.of(context).size.width,
              decoration: BoxDecoration(
                  color: const Color(0xFF27b4a8),
                  borderRadius: BorderRadius.circular(5)),
              child: Container(
                  padding: const EdgeInsets.all(10),
                  child: Center(
                      child: Text(
                    AppLocalizations.of(context).translate('Watch Video'),
                    style: const TextStyle(color: Colors.white),
                  ))),
            )));
  }
}

class _SendRequest extends StatelessWidget {
  final int? id;
  final int agentId;
  final num? price;
  final num? privateDriverPrice;

  const _SendRequest(
      {Key? key,
      required this.id,
      required this.agentId,
      required this.price,
      required this.privateDriverPrice})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Center(
        child: GestureDetector(
            onTap: () async {
              final authController = AuthController();

              await authController.isloggedin();

              authController.isLogged == true
                  ? Navigator.of(context).pushReplacement(MaterialPageRoute(
                      builder: (BuildContext context) => SendRequestCarRental(
                            id!,
                            agentId: agentId,
                            startPrice: price,
                            privateDriverPrice: privateDriverPrice,
                          )))
                  : snackbar(AppLocalizations.of(context)
                      .translate('Please login first'));
            },
            child: Container(
              height: 40,
              width: MediaQuery.of(context).size.width,
              decoration: BoxDecoration(
                  color: const Color(0xFF27b4a8),
                  borderRadius: BorderRadius.circular(5)),
              child: Container(
                  padding: const EdgeInsets.all(10),
                  child: Center(
                      child: Text(
                    AppLocalizations.of(context).translate('Make Booking'),
                    style: const TextStyle(color: Colors.white),
                  ))),
            )));
  }
}
