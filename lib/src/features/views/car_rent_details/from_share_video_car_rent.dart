import 'dart:developer';

import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:google_cloud_translation/google_cloud_translation.dart';
import 'package:page/src/core/extensions/extensions.dart';
import 'package:page/src/features/views/car_rent_details/widgets/car_rental_details.dart';
import 'package:page/src/features/views/car_rent_details/widgets/discussion.dart';
import 'package:page/src/features/views/car_rent_details/widgets/join_discussion.dart';
import 'package:page/src/features/views/story/widgets/see_more.dart';
import 'package:share_plus/share_plus.dart';

import '../../../core/localization/app_localizations.dart';
import '../../../core/response/generalResponse.dart';
import '../../../core/services/api.dart';
import '../../../core/shared_widgets/back_button.dart';
import '../../../core/shared_widgets/reel_images.dart';
import '../../../core/shared_widgets/shared_widgets.dart';
import '../../../core/utils/dynamic_links.dart';
import '../../controllers/auth_controller.dart';
import '../../controllers/currency_controller.dart';
import '../../controllers/language_controller.dart';
import '../../models/discussions.dart';
import '../../models/reviews.dart';
import '../../models/video_model.dart';
import '../send_request_car/send_request_car_rental.dart';

bool isactions = false;
TextEditingController note = TextEditingController();

TextEditingController discussioncontroller = TextEditingController();

// ignore: must_be_immutable
class FromShareLinkCarRentDetailsVideo extends StatefulWidget {
  int id;

  FromShareLinkCarRentDetailsVideo(
    this.id, {
    super.key,
  });

  @override
  State<StatefulWidget> createState() {
    return _CarRentDetails();
  }
}

class _CarRentDetails extends State<FromShareLinkCarRentDetailsVideo> {
  var _controller;
  bool isdiscussion = false;
  int imageindex = 0;
  int? imageindex2;
  Translation? _translation;
  TranslationModel? _translated;
  LanguageController language = LanguageController();
  String? text;
  int index2 = -1;
  AuthController authController = AuthController();
  CurrencyController currencyController = CurrencyController();
  bool isload = false;
  String? video;
  String? name;
  dynamic description, location, brand, type, phone, feature, featureList;
  var year;
  var startprice, privateDriverPrice, agentId;
  double? lat, lng;
  int isfavouriate = 0;
  List<Reviews> reviews = [];
  bool ismute = false;
  bool isfav = false;
  List<Categoryimages> images = [];
  List<Discussions> dis = [];
  int pagenumber = 1;
  int? code;
  String msg = 'Loading';

  getdiscussions() async {
    progrsss(context);
    pr!.show();
    await Api.getcarrentdiscussions(pagenumber, 20, widget.id).then((value) {
      value != null
          ? setState(() {
              dis.addAll(value.dscussions);
              code = value.code;
              msg = value.msg;

              // isload = true;
            })
          // ignore: unnecessary_statements
          : null;
      discussionCarRent(context,
          translated: _translated,
          translation: _translation,
          id: widget.id, function: () {
        setState(() {
          dis = [];
          discussioncontroller.text = "";
          getdiscussions();
          discussioncontroller.text = "";
          // isfav = true;
        });
      });
    });
  }

  getmorerelatedreels() async {
    await Api.getmainCategorydetails(widget.id).then((value) {
      value != null
          ? setState(() {
              isload = true;
            })
          : null;
    }).catchError((e) {
      log(e);
    });
  }

  int? carRentalId;

  @override
  void initState() {
    getdetails();
    // moreimagesrelated();
    // getmorerelatedreels();
    authController.isloggedin();
    print("dffdffd");
    print(widget.id);
    currencyController.getcuurentcurrency(context);
    _translation = Translation(
      apiKey: 'AIzaSyCXOO147BdbuceLIl8Z8D5Jxru2Vjhqd4Q',
    );
    language.getcuurentlanguage();
    super.initState();
  }

  void getdetails() async {
    print("valuasdsasdults ${widget.id}");

    await Api.getmainCategorydetails(widget.id).then((value) {
      value != null
          ? setState(() {
              log('Dataa ${value.results}');
              video = value.results['video'];
              name = value.results['name'];
              description = value.results['description'];
              location = value.results['location'] != null
                  ? value.results['location']['name']
                  : '';
              startprice = value.results['price'];
              privateDriverPrice = value.results['private_driver_price'];
              agentId = value.results['agent']['id'];
              lat = value.results['latitude'].toString().toDouble();
              lng = value.results['longitude'].toString().toDouble();
              brand = value.results['brand_car'] != null
                  ? language.languagecode == 'en'
                      ? value.results['brand_car']['name']['en']
                      : value.results['brand_car']['name']['ar']
                  : '';
              type = value.results['type'] != null
                  ? language.languagecode == 'en'
                      ? value.results['type']['name']['en']
                      : value.results['type']['name']['ar']
                  : '';
              year = value.results['year_car'] != null
                  ? value.results['year_car']['year']
                  : '';
              isfavouriate = value.results['is_favorite'] == true ? 1 : 0;
              phone = value.results['phone'];
              feature = value.results['features'] != null &&
                      value.results['features'].isNotEmpty
                  ? language.languagecode == 'en'
                      ? value.results['features'][0]['name']['en']
                      : value.results['features'][0]['name']['ar']
                  : '';

              featureList = value.results['features'] != null &&
                      value.results['features'].length > 0
                  ? List<String>.from(value.results['features'].map((e) =>
                      language.languagecode == 'en'
                          ? e['name']['en']
                          : e['name']['ar']))
                  // List<String>.from(value.results['features'].map((e) =>
                  //         language.languagecode == 'en'
                  //             ? e['name']['en']
                  //             : e['name']['ar']))
                  : [];

              print(name);
              print(video);
              try {
                // _controller =
                //     VideoPlayerController.network(Uri.parse(video!).toString())
                //       ..initialize().then((_) {
                //         // Ensure the first frame is shown after the video is initialized, even before the play button has been pressed.
                //         setState(() {});
                //       });
                // isload = true;
              } catch (e) {
                Navigator.pop(context);
                snackbar(
                    AppLocalizations.of(context).translate('error occured'));
                print(e);
              }
            })
          // ignore: unnecessary_statements
          : null;
      _controller!.play();
    });
  }

  @override
  void dispose() {
    _controller?.disposeHomeVideoControllers();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
        onWillPop: () {
          if (_controller!.value.isPlaying) {
            _controller!.pause();
            _controller = null;
            _controller?.disposeHomeVideoControllers();
          }
          setState(() {
            isactions = false;
          });

          return Future.value(true);
        },
        child: SafeArea(
            child: Scaffold(
          body: Stack(
            alignment: Alignment.bottomCenter,
            children: <Widget>[
              _controller != null
                  ? SizedBox(
                      height: MediaQuery.of(context).size.height,
                      width: MediaQuery.of(context).size.width,
                      child: _controller!.value.isInitialized
                          ? FittedBox(
                              fit: BoxFit.fill,
                              child: SizedBox(
                                  height: _controller!.value.size.height,
                                  width: _controller!.value.size.width,
                                  child: SizedBox()))
                          : Container())
                  : Container(),
              Positioned(
                  left: 10,
                  right: 10,
                  top: 20,
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      const BackButtonWidget(),
                      InkWell(
                          onTap: () {
                            setState(() {
                              ismute = !ismute;
                              ismute
                                  ? _controller!.setVolume(0.0)
                                  : _controller!.setVolume(30.0);
                            });
                            // _controller!.setVolume(0.0);
                          },
                          child: !ismute
                              ? const Icon(
                                  Icons.volume_down,
                                  color: Colors.white,
                                )
                              : const Icon(
                                  Icons.volume_off,
                                  color: Colors.white,
                                ))
                    ],
                  )),
              if (language.languagecode == 'ar')
                Positioned(bottom: 65, right: 30, child: _nameDetails())
              else
                Positioned(bottom: 65, left: 25, child: _nameDetails()),
              _controller != null
                  ? Padding(
                      padding: const EdgeInsetsDirectional.only(
                        bottom: 25,
                        top: 9,
                        start: 10,
                        end: 10,
                      ),
                      child: Row(
                        children: [
                          Expanded(
                            child: IconButton(
                                onPressed: () {
                                  _controller!.value.isPlaying
                                      ? _controller!.pause()
                                      : _controller!.play();
                                  setState(() {});
                                },
                                icon: Icon(
                                  _controller!.value.isPlaying
                                      ? Icons.pause
                                      : Icons.play_arrow,
                                  color: Colors.white,
                                )),
                          ),
                          const SizedBox(width: 10),
                          // Expanded(
                          //   flex: 8,
                          //   child: VideoProgressIndicator(
                          //     _controller!,
                          //     allowScrubbing: true,
                          //     colors: VideoProgressColors(
                          //         playedColor: _controller!.value.isInitialized
                          //             ? Colors.white
                          //             : const Color(0xFF27b4a8),
                          //         backgroundColor:
                          //             Colors.white.withOpacity(0.44)),
                          //   ),
                          // ),
                          const SizedBox(width: 10),
                          Expanded(
                            child: FittedBox(
                              fit: BoxFit.scaleDown,
                              child: Text(
                                _controller!.value.duration
                                    .toString()
                                    .substring(
                                        _controller!.value.duration
                                                .toString()
                                                .indexOf(":") +
                                            1,
                                        _controller!.value.duration
                                            .toString()
                                            .indexOf(".")),
                                maxLines: 1,
                                style: const TextStyle(color: Colors.white),
                              ),
                            ),
                          ),
                        ],
                      ),
                    )
                  : Container(),
              if (language.languagecode == 'ar')
                Positioned(bottom: 75, left: 20, child: _columnDetailsWidget())
              else
                Positioned(
                    bottom: 75, right: 20, child: _columnDetailsWidget()),
              isactions
                  ? JoinDiscussionCarRent(
                      onShare: () async {
                        try {
                          final String linkPathData =
                              '?id=${widget.id}&type=${'car-rental'}';
                          final dynamicLink =
                              await DynamicLinkHandler.createDynamicLink(
                            linkPathData,
                          );

                          log('sdfdfdsfsf ${dynamicLink.toString()}');

                          Share.share(dynamicLink.toString()).then((value) {
                            setState(() {
                              isactions = false;
                            });
                          });
                        } catch (e) {
                          log('Eerrrror ${e.toString()}');
                        }
                      },
                      onJoinDiscussion: () {
                        getdiscussions();
                      },
                      onCancel: () {
                        setState(() {
                          isactions = !isactions;
                        });
                      },
                      name: name)
                  : Container(),
            ],
          ),
        )));
  }

  Widget _columnDetailsWidget() {
    final Widget svg5 = SizedBox(
        width: 22,
        height: 25,
        child: SvgPicture.asset(
          'assets/Group 7408.svg',
          semanticsLabel: 'Acme Logo',
          fit: BoxFit.cover,
        ));
    final Widget svg6 = SizedBox(
        width: 30,
        height: 25,
        child: SvgPicture.asset(
          'assets/media_icon.svg',
          semanticsLabel: 'Acme Logo',
          width: 13,
          height: 13,
          fit: BoxFit.fill,
          color: Colors.white,
        ));
    return Column(
      children: [
        InkWell(
          onTap: () {
            _controller!.pause();
            authController.isLogged == true
                ? Navigator.of(context).pushReplacement(MaterialPageRoute(
                    builder: (BuildContext context) => SendRequestCarRental(
                        widget.id,
                        agentId: agentId,
                        startPrice: startprice,
                        privateDriverPrice: privateDriverPrice)))
                : snackbar(AppLocalizations.of(context)
                    .translate('Please login first'));
          },
          child: svg5,
        ),
        const SizedBox(height: 20),
        InkWell(
            onTap: () {
              moreimagesrelated(context, widget.id, _controller!);
            },
            child: svg6),
        const SizedBox(
          height: 20,
        ),
        authController.isLogged == true
            ? isfavouriate == 1
                ? InkWell(
                    onTap: () async {
                      GeneralResponse sucessinformation =
                          await Api.removecarrentfromfavourite(widget.id);
                      print(sucessinformation.code);
                      if (sucessinformation.code == "1") {
                        snackbar2(AppLocalizations.of(context)
                            .translate('remove from favourite successfuly'));
                        setState(() {
                          isfavouriate = 0;
                        });
                      } else {
                        snackbar(AppLocalizations.of(context).translate(
                            'Something went wrong, please try again later'));
                      }
                    },
                    child: const Icon(
                      Icons.favorite,
                      color: Colors.white,
                    ))
                : InkWell(
                    onTap: () async {
                      GeneralResponse sucessinformation =
                          await Api.addmaincategoryfavourite(widget.id);
                      print(sucessinformation.code);
                      if (sucessinformation.code == "1") {
                        snackbar2(AppLocalizations.of(context)
                            .translate('add to favourite successfuly'));
                        setState(() {
                          isfavouriate = 1;
                        });
                      } else {
                        snackbar(AppLocalizations.of(context).translate(
                            'Something went wrong, please try again later'));
                      }
                    },
                    child: const Icon(
                      Icons.favorite_outline,
                      color: Colors.white,
                    ))
            : InkWell(
                onTap: () async {
                  snackbar(AppLocalizations.of(context)
                      .translate('Please login first'));
                },
                child: const Icon(
                  Icons.favorite_outline,
                  color: Colors.white,
                )),
        const SizedBox(
          height: 10,
        ),
        GestureDetector(
            onTap: () {
              _controller!.pause();
              setState(() {
                isactions = !isactions;
              });
            },
            child: Container(
                color: Colors.transparent,
                child: const Icon(Icons.more_horiz,
                    color: Colors.white, size: 30)))
      ],
    );
  }

  Widget _nameDetails() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        name != null
            ? Text(
                name!,
                style: const TextStyle(
                    color: Colors.white, fontWeight: FontWeight.w700),
              )
            : const SizedBox(),
        const SizedBox(
          height: 5,
        ),
        SizedBox(
            width: MediaQuery.of(context).size.width * 0.75,
            child: Text.rich(
              TextSpan(
                style: const TextStyle(
                  color: Colors.white,
                ),
                children: [
                  TextSpan(
                    text: description != null
                        ? description!.length > maxChars
                            ? description?.substring(0, maxChars)
                            : description
                        : '',
                    style: const TextStyle(
                      fontWeight: FontWeight.normal,
                    ),
                  ),
                  TextSpan(
                    text:
                        '${AppLocalizations.of(context).translate('seemore')}',
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                    ),
                    recognizer: TapGestureRecognizer()
                      ..onTap = () {
                        _controller!.pause();
                        _controller!.pause();
                        carrentaldetaialsWidget(context,
                            id: widget.id,
                            agentId: agentId!,
                            description: description,
                            name: name,
                            startprice: startprice,
                            privateDriverPrice: privateDriverPrice,
                            phone: phone,
                            currencyController: currencyController,
                            type: type,
                            brand: brand,
                            feature: feature,
                            featureList: featureList,
                            year: year);
                      },
                  ),
                ],
              ),
              overflow: TextOverflow.ellipsis,
              maxLines: 1,
            ))
      ],
    );
  }
}
