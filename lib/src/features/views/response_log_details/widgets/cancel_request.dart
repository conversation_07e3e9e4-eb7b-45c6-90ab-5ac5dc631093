import 'package:flutter/material.dart';

import '../../../../core/localization/app_localizations.dart';

void cancelrequestLog(BuildContext context) {
  showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      enableDrag: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Padding(
            padding: EdgeInsets.only(
                bottom: MediaQuery.of(context).viewInsets.bottom),
            child: Container(
                height: MediaQuery.of(context).size.height * 0.3,
                decoration: new BoxDecoration(
                    color: const Color(0xffF5F6F7),
                    borderRadius: const BorderRadius.only(
                        topLeft: Radius.circular(25.0),
                        topRight: Radius.circular(25.0)),
                    border: Border.all(color: Colors.black, width: 1.0)),
                child: Column(
                  children: [
                    const SizedBox(
                      height: 10,
                    ),
                    Container(
                        height: 5, width: 50, color: const Color(0xffD2D4D6)),
                    const SizedBox(
                      height: 20,
                    ),
                    Center(
                        child: Text(
                      AppLocalizations.of(context).translate('Cancel Request'),
                      style: const TextStyle(fontWeight: FontWeight.bold),
                    )),
                    Container(
                      padding: const EdgeInsets.all(15),
                      child: Container(
                        decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(10)),
                        child: Container(
                          padding: const EdgeInsets.all(15),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              const SizedBox(
                                height: 20,
                              ),
                              Text(AppLocalizations.of(context).translate(
                                  'Are you sure you want to cancel holiday home request')),
                              const SizedBox(
                                height: 20,
                              ),
                              Center(
                                  child: Container(
                                      // padding: EdgeInsets.only(right: 20, left: 20),
                                      child: GestureDetector(
                                          onTap: () async {
                                            // _submit(rate.toString(), _comment.text);
                                          },
                                          child: Container(
                                            height: 50,
                                            width: MediaQuery.of(context)
                                                .size
                                                .width,
                                            decoration: BoxDecoration(
                                                color: const Color(0xFF27b4a8),
                                                borderRadius:
                                                    BorderRadius.circular(10)),
                                            child: Container(
                                                padding:
                                                    const EdgeInsets.all(10),
                                                child: Center(
                                                    child: Text(
                                                  AppLocalizations.of(context)
                                                      .translate(
                                                          'Yes Cancel Request'),
                                                  style: const TextStyle(
                                                      color: Colors.white),
                                                ))),
                                          )))),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ],
                )),
          ));
}
