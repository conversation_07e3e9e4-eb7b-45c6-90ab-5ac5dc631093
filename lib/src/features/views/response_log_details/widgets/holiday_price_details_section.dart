import 'package:flutter/material.dart';
import 'package:page/src/core/localization/app_localizations.dart';
import 'package:page/src/features/models/requests.dart';

class HolidayPriceDetails extends StatelessWidget {
  // final List periods;
  final String currency;
  // final String numNormalDays;
  // final num normalPrice;

  final Requests pricerequest;
  const HolidayPriceDetails(
      {required this.pricerequest, required this.currency});

  @override
  Widget build(BuildContext context) {
    final periods = pricerequest.periods ?? [];

    return Column(
      children: [
        ListView.separated(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: periods.length,
            itemBuilder: (context, index) {
              final period = periods[index];

              return Column(
                children: [
                  Container(
                    padding: const EdgeInsets.all(10),
                    decoration: BoxDecoration(
                        color: Colors.grey[200],
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: Colors.black12, width: 1.0)),
                    child: Text(
                      period['start_date'],
                      // ' - ' +
                      // period['end_date'] +
                      // " (${period['num_days']} ${AppLocalizations.of(context).translate('days')})",
                      style: const TextStyle(
                          color: Colors.black,
                          fontSize: 13,
                          fontWeight: FontWeight.bold),
                    ),
                  ),
                  const SizedBox(
                    height: 20,
                  ),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        AppLocalizations.of(context).translate('Day Price'),
                        style: const TextStyle(),
                      ),
                      Text(
                        '${period['price_day']?.toString() ?? '0'} $currency',
                        style: const TextStyle(
                            color: Color(0xff51565B),
                            fontSize: 13,
                            fontWeight: FontWeight.bold),
                      ),
                    ],
                  ),
                ],
              );
            },
            separatorBuilder: (context, index) => const SizedBox(
                  height: 20,
                )),
        const SizedBox(
          height: 20,
        ),
        // _NormalDaysPriceDetails(
        //   currency: currency,
        //   priceRequest: pricerequest,
        // ),
      ],
    );
  }
}

class _NormalDaysPriceDetails extends StatelessWidget {
  final String currency;
  final Requests priceRequest;

  const _NormalDaysPriceDetails({
    required this.currency,
    required this.priceRequest,
  });

  @override
  Widget build(BuildContext context) {
    final numNormalDays = priceRequest.numNormalDays ?? 0;
    final normalPrice = priceRequest.normalPrice ?? 0;

    return Column(
      children: [
        Container(
          padding: const EdgeInsets.all(10),
          decoration: BoxDecoration(
              color: Colors.grey[200],
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.black12, width: 1.0)),
          child: Text(
            "${AppLocalizations.of(context).translate('Normal Days')} ($numNormalDays ${AppLocalizations.of(context).translate('days')})",
            style: const TextStyle(
                color: Colors.black, fontSize: 13, fontWeight: FontWeight.bold),
          ),
        ),
        const SizedBox(
          height: 20,
        ),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              AppLocalizations.of(context).translate('Night Price'),
              style: const TextStyle(),
            ),
            Text(
              '$normalPrice $currency',
              style: const TextStyle(
                  color: Color(0xff51565B),
                  fontSize: 13,
                  fontWeight: FontWeight.bold),
            ),
          ],
        ),
      ],
    );
  }
}
