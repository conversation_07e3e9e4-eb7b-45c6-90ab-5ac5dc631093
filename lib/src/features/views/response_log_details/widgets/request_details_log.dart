import 'package:flutter/material.dart';
import 'package:page/src/features/models/requests.dart';

import '../../../../core/localization/app_localizations.dart';

class RequestDetailsLog extends StatelessWidget {
  final Requests pricerequest;
  const RequestDetailsLog({
    Key? key,
    required this.pricerequest,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final category = pricerequest.category?.id;
    return Container(
        padding: const EdgeInsets.only(left: 20, right: 20),
        child: Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: const BorderRadius.all(Radius.circular(5)),
                border: Border.all(color: Colors.black12, width: 1.0)),
            child: Column(children: [
              Container(
                  decoration: BoxDecoration(
                      color: Colors.grey[200],
                      borderRadius: const BorderRadius.all(Radius.circular(5)),
                      border: Border.all(color: Colors.black12, width: 1.0)),
                  height: 50,
                  child: Center(
                      child: Text(
                    AppLocalizations.of(context).translate('Request Details'),
                    style: const TextStyle(fontWeight: FontWeight.bold),
                  ))),
              const SizedBox(
                height: 20,
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    AppLocalizations.of(context).translate('Requested Date'),
                    style:
                        const TextStyle(color: Color(0xff51565B), fontSize: 13),
                  ),
                  pricerequest.requestedat != null
                      ? Text(
                          pricerequest.requestedat?.toString() ?? '',
                          style: const TextStyle(
                              color: Color(0xff51565B), fontSize: 13),
                        )
                      : Container(),
                ],
              ),
              const SizedBox(
                height: 20,
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    AppLocalizations.of(context).translate('Start Date'),
                    style:
                        const TextStyle(color: Color(0xff51565B), fontSize: 13),
                  ),
                  pricerequest.startDate != null
                      ? Text(
                          pricerequest.startDate?.toString() ?? '',
                          style: const TextStyle(
                              color: Color(0xff51565B), fontSize: 13),
                        )
                      : Container(),
                ],
              ),
              const SizedBox(
                height: 20,
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    AppLocalizations.of(context).translate('End Date'),
                    style:
                        const TextStyle(color: Color(0xff51565B), fontSize: 13),
                  ),
                  pricerequest.endDate != null
                      ? Text(
                          pricerequest.endDate?.toString() ?? '',
                          style: const TextStyle(
                              color: Color(0xff51565B), fontSize: 13),
                        )
                      : Container(),
                ],
              ),
              // const SizedBox(
              //   height: 20,
              // ),
              // category == AppConstants.carRentalsId
              //     ? Column(
              //         children: [
              //           Row(
              //             mainAxisAlignment: MainAxisAlignment.spaceBetween,
              //             children: [
              //               Text(
              //                 AppLocalizations.of(context)
              //                     .translate('Private Driver'),
              //                 style: const TextStyle(
              //                     color: Color(0xff51565B), fontSize: 13),
              //               ),
              //               pricerequest.privateDrop == 1
              //                   ? Text(
              //                       AppLocalizations.of(context)
              //                           .translate('yes'),
              //                       style: const TextStyle(
              //                           color: Color(0xff51565B), fontSize: 13),
              //                     )
              //                   : Text(
              //                       AppLocalizations.of(context)
              //                           .translate('no'),
              //                       style: const TextStyle(
              //                           color: Color(0xff51565B), fontSize: 13),
              //                     ),
              //             ],
              //           ),
              //           const SizedBox(
              //             height: 20,
              //           ),
              //           Row(
              //             mainAxisAlignment: MainAxisAlignment.spaceBetween,
              //             children: [
              //               Text(
              //                 AppLocalizations.of(context)
              //                     .translate('Pickup Drop Off'),
              //                 style: const TextStyle(
              //                     color: Color(0xff51565B), fontSize: 13),
              //               ),
              //               pricerequest.dropOff == 0
              //                   ? Text(
              //                       AppLocalizations.of(context)
              //                           .translate('Pick Up'),
              //                       style: const TextStyle(
              //                           color: Color(0xff51565B), fontSize: 13),
              //                     )
              //                   : Text(
              //                       AppLocalizations.of(context)
              //                           .translate('Drop Off'),
              //                       style: const TextStyle(
              //                           color: Color(0xff51565B), fontSize: 13),
              //                     ),
              //             ],
              //           ),
              //           const SizedBox(
              //             height: 20,
              //           ),
              //           Row(
              //             mainAxisAlignment: MainAxisAlignment.spaceBetween,
              //             children: [
              //               Text(
              //                 AppLocalizations.of(context)
              //                     .translate('location'),
              //                 style: const TextStyle(
              //                     color: Color(0xff51565B), fontSize: 13),
              //               ),
              //               pricerequest.location != null
              //                   ? Text(
              //                       pricerequest.location!,
              //                       style: const TextStyle(
              //                           color: Color(0xff51565B), fontSize: 13),
              //                     )
              //                   : Container(),
              //             ],
              //           ),
              //           const SizedBox(
              //             height: 20,
              //           ),
              //           Row(
              //             mainAxisAlignment: MainAxisAlignment.spaceBetween,
              //             children: [
              //               Text(
              //                 AppLocalizations.of(context).translate('note'),
              //                 style: const TextStyle(
              //                     color: Color(0xff51565B), fontSize: 13),
              //               ),
              //               const Spacer(),
              //               Flexible(
              //                 flex: 2,
              //                 child: Text(
              //                   pricerequest.userNote ?? '',
              //                   style: const TextStyle(
              //                       color: Color(0xff51565B), fontSize: 13),
              //                   textAlign: TextAlign.end,
              //                 ),
              //               )
              //             ],
              //           ),
              //         ],
              //       )
              //     : Container()
            ])));
  }
}
