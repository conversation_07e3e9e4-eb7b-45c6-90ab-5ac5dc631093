import 'package:flutter/material.dart';
import 'package:page/src/core/config/constants.dart';
import 'package:page/src/features/models/requests.dart';
import 'package:page/src/features/views/send_holiday_request/widgets/view_holiday_schedule_days.dart';

import '../../../../core/localization/app_localizations.dart';

class PriceRequestLog extends StatelessWidget {
  final Requests pricerequest;
  final currencyController;

  const PriceRequestLog(
      {Key? key, required this.pricerequest, required this.currencyController})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    final category = pricerequest.category?.id;

    return Container(
        padding: const EdgeInsets.only(left: 20, right: 20),
        child: Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: const BorderRadius.all(Radius.circular(5)),
                border: Border.all(color: Colors.black12, width: 1.0)),
            child: Container(
                child: Container(
                    // padding: EdgeInsets.all(20),
                    child: Column(
              children: [
                Container(
                    // padding: EdgeInsets.all(20),
                    child: Container(
                        child: Container(
                            decoration: BoxDecoration(
                                color: Colors.grey[200],
                                borderRadius:
                                    const BorderRadius.all(Radius.circular(5)),
                                border: Border.all(
                                    color: Colors.black12, width: 1.0)),
                            height: 50,
                            child: Center(
                                child: Text(
                              AppLocalizations.of(context)
                                  .translate('Price Breakdown'),
                              style:
                                  const TextStyle(fontWeight: FontWeight.bold),
                            ))))),
                const SizedBox(
                  height: 20,
                ),
                category == AppConstants.holidayHomesId
                    ? Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            AppLocalizations.of(context)
                                .translate('No Of days'),
                            style: const TextStyle(
                                color: Color(0xff51565B), fontSize: 13),
                          ),
                          pricerequest.days != null
                              ? Text(
                                  '${pricerequest.days == '0' ? '1' : pricerequest.days} Days',
                                  style: const TextStyle(
                                      color: Color(0xff51565B), fontSize: 13),
                                )
                              : Container(),
                        ],
                      )
                    : Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            AppLocalizations.of(context)
                                .translate('Number of Days'),
                            style: const TextStyle(
                                color: Color(0xff51565B), fontSize: 13),
                          ),
                          pricerequest.days != null
                              ? Text(
                                  pricerequest.days.toString() +
                                      AppLocalizations.of(context)
                                          .translate('nights'),
                                  style: const TextStyle(
                                      color: Color(0xff51565B), fontSize: 13),
                                )
                              : Container(),
                        ],
                      ),
                const SizedBox(
                  height: 20,
                ),
                category == AppConstants.holidayHomesId
                    ? Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            AppLocalizations.of(context)
                                .translate('No Of persons'),
                            style: const TextStyle(
                                color: Color(0xff51565B), fontSize: 13),
                          ),
                          pricerequest.numberOfPeople != null
                              ? Text(
                                  '${pricerequest.numberOfPeople} ${AppLocalizations.of(context).translate('Adult')}',
                                  style: const TextStyle(
                                      color: Color(0xff51565B), fontSize: 13),
                                )
                              : Container(),
                        ],
                      )
                    : Container(),
                const SizedBox(
                  height: 20,
                ),
                category == AppConstants.holidayHomesId
                    ? Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            AppLocalizations.of(context)
                                .translate('No Of children'),
                            style: const TextStyle(
                                color: Color(0xff51565B), fontSize: 13),
                          ),
                          Text(
                            '${pricerequest.numberOfChildren ?? 0} ${AppLocalizations.of(context).translate('Child')}',
                            style: const TextStyle(
                                color: Color(0xff51565B), fontSize: 13),
                          ),
                        ],
                      )
                    : Container(),
                const SizedBox(
                  height: 20,
                ),
                category == AppConstants.holidayHomesId
                    ? Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            AppLocalizations.of(context)
                                .translate('Number of Rooms'),
                            style: const TextStyle(
                                color: Color(0xff51565B), fontSize: 13),
                          ),
                          pricerequest.numOfRooms != null
                              ? Text(
                                  '${pricerequest.numOfRooms} ${AppLocalizations.of(context).translate('Room')}',
                                  style: const TextStyle(
                                      color: Color(0xff51565B), fontSize: 13),
                                )
                              : Container(),
                        ],
                      )
                    : Container(),
                category == AppConstants.holidayHomesId
                    ? const SizedBox(
                        height: 20,
                      )
                    : Container(),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      AppLocalizations.of(context).translate('Sub Total'),
                      style: const TextStyle(
                          color: Color(0xff51565B), fontSize: 13),
                    ),
                    pricerequest.subTotal != null
                        ? Text(
                            '${pricerequest.subTotal} ${currencyController.currency}',
                            style: const TextStyle(
                                color: Color(0xff51565B), fontSize: 13),
                          )
                        : Container(),
                  ],
                ),
                const SizedBox(
                  height: 20,
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      AppLocalizations.of(context).translate('Promo Subtotal'),
                      style: const TextStyle(
                          color: Color(0xff51565B), fontSize: 13),
                    ),
                    pricerequest.discount != null
                        ? Text(
                            '${pricerequest.discount} ${currencyController.currency}',
                            style: const TextStyle(
                                color: Color(0xff51565B), fontSize: 13),
                          )
                        : Container(),
                  ],
                ),
                const SizedBox(
                  height: 20,
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      AppLocalizations.of(context).translate('VAT'),
                      style: const TextStyle(
                          color: Color(0xff51565B), fontSize: 13),
                    ),
                    pricerequest.baseRateTax != null
                        ? Text(
                            pricerequest.baseRateTax!
                                    .round()
                                    .toStringAsFixed(2) +
                                ' ' +
                                "${currencyController.currency}",
                            style: const TextStyle(
                                color: Color(0xff51565B), fontSize: 13),
                          )
                        : Container(),
                  ],
                ),
                const SizedBox(
                  height: 20,
                ),
                // category == AppConstants.holidayHomesId
                //     ? Row(
                //         mainAxisAlignment: MainAxisAlignment.spaceBetween,
                //         children: [
                //           Text(
                //             AppLocalizations.of(context)
                //                 .translate('Tourism Fee'),
                //             style: const TextStyle(
                //                 color: Color(0xff51565B), fontSize: 13),
                //           ),
                //           pricerequest.fee != null
                //               ? Text(
                //                   '${pricerequest.fee!.round().toStringAsFixed(2)} ${currencyController.currency}',
                //                   style: const TextStyle(
                //                       color: Color(0xff51565B), fontSize: 13),
                //                 )
                //               : Container(),
                //         ],
                //       )
                //     : Container(),
                // category == AppConstants.holidayHomesId
                //     ? const SizedBox(
                //         height: 20,
                //       )
                //     : Container(),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      AppLocalizations.of(context).translate('total'),
                      style: const TextStyle(
                          color: Color(0xff51565B), fontSize: 13),
                    ),
                    pricerequest.total != null
                        ? Text(
                            '${pricerequest.total!.round().toStringAsFixed(2)} ${currencyController.currency}',
                            style: const TextStyle(
                                color: Color(0xff51565B), fontSize: 13),
                          )
                        : Container()
                  ],
                ),
                const SizedBox(
                  height: 20,
                ),
                InkWell(
                  onTap: () => showModalBottomSheet(
                      context: context,
                      backgroundColor: Colors.white,
                      builder: (_) {
                        return ViewHolidayPriceDaysDetails(
                          priceByDateDetails: pricerequest.periods
                                  ?.map(
                                    (e) => (
                                      ((e['start_date'] ?? '') as String),
                                      ((e['price_day'] ?? 0) as num)
                                    ),
                                  )
                                  .toList() ??
                              [],
                          currencyController: currencyController,
                        );
                      }),
                  child: Container(
                    width: double.infinity,
                    padding: const EdgeInsets.all(10),
                    decoration: BoxDecoration(
                        color: Colors.grey[200],
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: Colors.black12, width: 1.0)),
                    child: Center(
                      child: Text(
                        AppLocalizations.of(context).translate('Price Details'),
                        style: const TextStyle(
                            fontSize: 13,
                            fontWeight: FontWeight.bold,
                            decoration: TextDecoration.underline,
                            color: Color(0xff233549)),
                      ),
                    ),
                  ),
                ),

                // CarRentPriceDetailsSectionLog(),
                // if (category == AppConstants.holidayHomesId)
                //   HolidayPriceDetails(
                //     currency: currencyController.currency,
                //     pricerequest: pricerequest,
                //   )
                // else
                //   CarRentPriceDetails(
                //     currency: currencyController.currency,
                //     priceRequest: pricerequest,
                //   ),

                const SizedBox(
                  height: 20,
                ),
                // Row(
                //   mainAxisAlignment: MainAxisAlignment.spaceBetween,
                //   children: [
                //     Text(
                //       AppLocalizations.of(context)
                //           .translate('Agreement Total Price'),
                //       style: const TextStyle(
                //           color: Colors.black,
                //           fontSize: 13,
                //           fontWeight: FontWeight.bold),
                //     ),
                //     pricerequest.status != 'pending'
                //         ? pricerequest.total != null
                //             ? Text(
                //                 '${pricerequest.finalAmount!.toStringAsFixed(2)} ${currencyController.currency}',
                //                 style: const TextStyle(
                //                     color: Color(0xff51565B),
                //                     fontSize: 13,
                //                     fontWeight: FontWeight.bold),
                //               )
                //             : Container()
                //         : const Text('---'),
                //   ],
                // )
              ],
            )))));
  }
}
