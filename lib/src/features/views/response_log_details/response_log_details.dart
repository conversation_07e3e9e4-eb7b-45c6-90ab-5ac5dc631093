import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:page/src/core/utils/main_cached_image.dart';
import 'package:page/src/features/models/requests.dart';
import 'package:page/src/features/views/property_details/widgets/property_details.dart';
import 'package:url_launcher/url_launcher_string.dart';

import '../../../core/localization/app_localizations.dart';
import '../../controllers/currency_controller.dart';
import '../response_log/response_log.dart';

// ignore: must_be_immutable
class ResponseLogDetails extends StatefulWidget {
  final Requests request;

  const ResponseLogDetails(this.request, {super.key});

  @override
  _ResponseLogDetails createState() => _ResponseLogDetails();
}

class _ResponseLogDetails extends State<ResponseLogDetails> {
  CurrencyController currencyController = CurrencyController();
  bool isselected = true;
  bool isselected1 = false;
  bool isselected2 = false;

  // Map<String, dynamic>? pricerequest;
  int code = 0, code2 = 0;
  String msg = 'loading', msg2 = 'loading';

  @override
  void initState() {
    super.initState();
    currencyController.getcuurentcurrency(context);
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
        onWillPop: () {
          Navigator.of(context).push(
            MaterialPageRoute(builder: (context) => const ResponseLog()),
          );
          return Future.value(false);
        },
        child: SafeArea(
            child: Scaffold(
          appBar: AppBar(
            backgroundColor: const Color(0xFF27b4a8),
            centerTitle: true,
            title: Text(widget.request.name ?? ''),
          ),
          body: Column(
            children: [
              const SizedBox(
                height: 20,
              ),
              Container(
                  padding: const EdgeInsets.only(
                      top: 5, bottom: 5, right: 20, left: 20),
                  child: Container(
                    decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(20),
                        border: Border.all(
                            color: const Color(0xffF1F1F2), width: 1)),
                    child: Container(
                      padding: const EdgeInsets.all(20),
                      child: Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisAlignment: MainAxisAlignment.start,
                        children: [
                          ClipRRect(
                              borderRadius: BorderRadius.circular(5),
                              child: MainCachedImage(
                                widget.request.image ?? '',
                                height: 180,
                                width: 120,
                                fit: BoxFit.cover,
                              )),
                          const SizedBox(
                            width: 20,
                          ),
                          Expanded(
                              child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            mainAxisAlignment: MainAxisAlignment.start,
                            children: [
                              Row(
                                children: [
                                  Container(
                                      height: 30,
                                      width: 100,
                                      color: const Color(0xffF1F1F1),
                                      child: Center(
                                          child: Text(
                                        AppLocalizations.of(context)
                                            .translate('holidayHomes'),
                                        style: const TextStyle(
                                            color: Color(0xff191C1F),
                                            fontSize: 12),
                                      ))),
                                ],
                              ),
                              const SizedBox(
                                height: 10,
                              ),
                              Text(widget.request.name ?? '',
                                  style: const TextStyle(
                                      fontWeight: FontWeight.bold,
                                      color: Color(0xff191C1F),
                                      fontSize: 17)),
                              const SizedBox(
                                height: 10,
                              ),
                              Row(children: [
                                Text(AppLocalizations.of(context)
                                    .translate('Status')),
                                const SizedBox(
                                  width: 5,
                                ),
                                Text(
                                    AppLocalizations.of(context).translate(
                                        getStatusData<String?>(
                                                widget.request.status ?? '',
                                                true) ??
                                            ''),
                                    softWrap: true,
                                    style: TextStyle(
                                        fontSize: 12,
                                        color: getStatusData<MaterialColor>(
                                            widget.request.status ?? '',
                                            false))),
                              ]),
                              const SizedBox(
                                height: 5,
                              ),
                              Row(
                                children: [
                                  Text(
                                    '${AppLocalizations.of(context).translate('Note')}:',
                                    style: const TextStyle(
                                        color: Color(0xff51565B), fontSize: 13),
                                  ),
                                  const SizedBox(
                                    width: 5,
                                  ),
                                  widget.request.userNote != null
                                      ? Flexible(
                                          flex: 2,
                                          child: Text(
                                            widget.request.userNote ?? '',
                                            style: const TextStyle(
                                                color: Color(0xff51565B),
                                                fontSize: 13),
                                            textAlign: TextAlign.end,
                                          ),
                                        )
                                      : const Text('---'),
                                ],
                              ),
                              const SizedBox(
                                height: 5,
                              ),
                              Row(
                                children: [
                                  Text(
                                    '${AppLocalizations.of(context).translate('agent note')}:',
                                    style: const TextStyle(
                                        color: Color(0xff51565B), fontSize: 13),
                                  ),
                                  const SizedBox(
                                    width: 5,
                                  ),
                                  widget.request.status != 'pending'
                                      ? widget.request.agentNote != null
                                          ? Flexible(
                                              flex: 2,
                                              child: Text(
                                                widget.request.agentNote ?? '',
                                                style: const TextStyle(
                                                    color: Color(0xff51565B),
                                                    fontSize: 13),
                                                textAlign: TextAlign.end,
                                              ),
                                            )
                                          : Container()
                                      : const Text('---'),
                                ],
                              ),
                              const SizedBox(
                                height: 5,
                              ),
                              Row(
                                children: [
                                  Text(
                                    '${AppLocalizations.of(context).translate('Requested Date')}:',
                                    style: const TextStyle(
                                        color: Color(0xff51565B), fontSize: 13),
                                  ),
                                  const SizedBox(
                                    width: 5,
                                  ),
                                  widget.request.requestedat != null
                                      ? Text(
                                          widget.request.requestedat
                                                  ?.toString() ??
                                              '',
                                          style: const TextStyle(
                                              color: Color(0xff51565B),
                                              fontSize: 13),
                                        )
                                      : Container(),
                                ],
                              ),
                            ],
                          ))
                        ],
                      ),
                    ),
                  )),
              // const SizedBox(
              //   height: 20,
              // ),
              // RequestDetailsLog(
              //   pricerequest: widget.request,
              // ),
              // const SizedBox(
              //   height: 20,
              // ),
              // PriceRequestLog(
              //     pricerequest: widget.request,
              //     currencyController: currencyController),
              // : nodatafound(AppLocalizations.of(context)
              //     .translate('No Details to show'))
              // : buildErrorWidget(msg),
              Spacer(),
              // Row Of two icons ( one call agent and other one email agent)
              Container(
                  padding: const EdgeInsets.only(left: 20, right: 20),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      GestureDetector(
                          onTap: () async {
                            final String? phoneNumber = widget
                                .request.agent?.phone
                                ?.replaceAll(RegExp(r'[^0-9]'), '');

                            log('Call Phone $phoneNumber');

                            await callNumber(phoneNumber!);
                          },
                          child: Container(
                            height: 50,
                            width: MediaQuery.of(context).size.width / 2.5,
                            decoration: BoxDecoration(
                                color: const Color(0xFF27b4a8),
                                borderRadius: BorderRadius.circular(10)),
                            child: Container(
                                padding: const EdgeInsets.all(10),
                                child: Center(
                                    child: Row(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    const Icon(
                                      Icons.call,
                                      color: Colors.white,
                                    ),
                                    const SizedBox(
                                      width: 10,
                                    ),
                                    Text(
                                      AppLocalizations.of(context)
                                          .translate('Call Agent'),
                                      style:
                                          const TextStyle(color: Colors.white),
                                    ),
                                  ],
                                ))),
                          )),
                      GestureDetector(
                          onTap: () {
                            log('Email ${widget.request.agent?.email}');

                            launchUrlString(
                                "mailto:${widget.request.agent?.email}");
                          },
                          child: Container(
                            height: 50,
                            width: MediaQuery.of(context).size.width / 2.5,
                            decoration: BoxDecoration(
                                color: const Color(0xFF27b4a8),
                                borderRadius: BorderRadius.circular(10)),
                            child: Container(
                                padding: const EdgeInsets.all(10),
                                child: Center(
                                    child: Row(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    const Icon(
                                      Icons.email,
                                      color: Colors.white,
                                    ),
                                    const SizedBox(
                                      width: 10,
                                    ),
                                    Text(
                                      AppLocalizations.of(context)
                                          .translate('Email Agent'),
                                      style:
                                          const TextStyle(color: Colors.white),
                                    ),
                                  ],
                                ))),
                          ))
                    ],
                  )),
              const SizedBox(
                height: 20,
              ),
              // widget.request.status == "Pending"
              //     ? Container(
              //         padding: const EdgeInsets.only(left: 20, right: 20),
              //         child: Center(
              //             child: GestureDetector(
              //                 onTap: () async {
              //                   cancelrequestLog(context);
              //                 },
              //                 child: Container(
              //                   height: 50,
              //                   width: MediaQuery.of(context).size.width,
              //                   decoration: BoxDecoration(
              //                       color: const Color(0xFF27b4a8),
              //                       borderRadius: BorderRadius.circular(10)),
              //                   child: Container(
              //                       padding: const EdgeInsets.all(10),
              //                       child: Center(
              //                           child: Text(
              //                         AppLocalizations.of(context)
              //                             .translate('Cancel Request'),
              //                         style:
              //                             const TextStyle(color: Colors.white),
              //                       ))),
              //                 ))))
              //     : Container(),
              // const SizedBox(
              //   height: 20,
              // )
            ],
          ),
        )));
  }
}

T getStatusData<T>(String? status, isText) {
  if (status == "accepted") {
    return !isText ? Colors.green as T : "Responded" as T;
  }
  if (status == "pending") {
    return !isText ? Colors.orange as T : "New" as T;
  }
  if (status == "cancelled") {
    return !isText ? Colors.blueGrey as T : "Canceled" as T;
  }
  if (status == "denied") return !isText ? Colors.red as T : "Denied" as T;
  return Colors.transparent as T;
}
