import 'dart:developer';
import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:open_file/open_file.dart';
import 'package:path_provider/path_provider.dart';
import 'package:pdf/widgets.dart' as pdf;

import '../../../core/localization/app_localizations.dart';
import '../../../core/response/plan_response.dart';
import '../../../core/shared_widgets/shared_widgets.dart';
import '../../../core/utils/print_services.dart';
import '../../bloc/plan_bloc.dart';
import '../../controllers/currency_controller.dart';
import '../plans/my_plans.dart';
import 'widgets/add_item.dart';
import 'widgets/list_date.dart';
import 'widgets/plans/list_my_plan.dart';
import 'widgets/plans/list_plan_item.dart';

String? dateSelected;

// ignore: must_be_immutable
class MyplansDetails extends StatefulWidget {
  String name;
  int id;

  MyplansDetails(this.name, this.id, {super.key});

  @override
  _MyplansDetails createState() => _MyplansDetails();
}

class _MyplansDetails extends State<MyplansDetails> {
  CurrencyController currencyController = CurrencyController();

  bool isload = false;
  String? currentvalue2;
  final format = DateFormat("HH:mm");
  TextEditingController starttimeController = TextEditingController();
  bool isselected = true;

  @override
  void initState() {
    super.initState();

    //? To Get Days
    planbloc.getplanuserdates(widget.id).then((val) {
      setState(() {
        dateSelected = val.dates.isNotEmpty ? val.dates.first.date : '';
      });
      //? To Get Items & Schedules
      planbloc.getplanudetailsseritems(widget.id, dateSelected);
    });

    currencyController.getcuurentcurrency(context);
  }

  var date, date2, date3;

  String? planName;

  @override
  Widget build(BuildContext context) {
    log('asdaskdsad ${date} asdaf ${date2} asd ${date3}');

    return WillPopScope(
      onWillPop: () {
        Navigator.of(context).push(
          MaterialPageRoute(builder: (context) => const MyPlans()),
        );

        return Future.value(false);
      },
      child: SafeArea(
        child: Scaffold(
          appBar: AppBar(
            backgroundColor: const Color(0xFF27b4a8),
            centerTitle: true,
            title: Text(
              widget.name.isEmpty ? (planName ?? '') : widget.name,
              style: const TextStyle(
                  color: Colors.white,
                  fontSize: 20,
                  fontWeight: FontWeight.bold),
            ),
            actions: [
              InkWell(
                onTap: () {
                  _exportPdf(widget.id, context);
                },
                child: Container(
                  padding: const EdgeInsets.only(left: 10, right: 10),
                  child: const Icon(
                    Icons.share_outlined,
                    color: Colors.white,
                  ),
                ),
              ),
              // InkWell(
              //   onTap: () {
              //     _exportPdf2(widget.id, context);
              //   },
              //   child: Container(
              //     padding: const EdgeInsets.only(left: 10, right: 10),
              //     child: const Icon(
              //       Icons.share_outlined,
              //       color: Colors.blue,
              //     ),
              //   ),
              // ),
            ],
          ),
          body: ListView(
              shrinkWrap: true,
              // physics: ClampingScrollPhysics(),
              children: [
                Column(
                  children: [
                    const SizedBox(
                      height: 20,
                    ),
                    //! Days ------------------------------
                    StreamBuilder<PlanItemsResponse>(
                      stream: planbloc.subject6.stream,
                      builder:
                          (context, AsyncSnapshot<PlanItemsResponse> snapshot) {
                        if (snapshot.hasData) {
                          if (snapshot.data!.error.isNotEmpty) {
                            return buildErrorWidget(snapshot.data!.error);
                          }

                          if (mounted) {
                            WidgetsBinding.instance.addPostFrameCallback((_) {
                              if (planName == null) {
                                setState(() {
                                  planName ??= snapshot.data!.planName ?? '';
                                });
                              }
                              if (dateSelected == null) {
                                setState(() {
                                  dateSelected ??=
                                      snapshot.data!.dates.isNotEmpty
                                          ? snapshot.data!.dates[0].date
                                          : '';
                                });
                              }
                            });
                          }

                          log('snsfasf ${snapshot.data!.plans.length}');

                          return Container(
                              child: listdate(snapshot.data!,
                                  context: context,
                                  planbloc: planbloc,
                                  id: widget.id,
                                  isload: isload,
                                  date2: date2 ?? '',
                                  date3: date3 ?? '',
                                  date: date ?? '',
                                  isselected: isselected,
                                  setState: setState));
                        } else if (snapshot.hasError) {
                          return buildErrorWidget(snapshot.error.toString());
                        } else {
                          return Container(
                            // height: MediaQuery.of(context)
                            //     .size
                            //     .height,
                            child: buildLoadingWidget(),
                          );
                        }
                      },
                    ),
                    const SizedBox(
                      height: 10,
                    ),
                    //! Plan Items ------------------------------
                    StreamBuilder<PlanItemsResponse>(
                      stream: planbloc.subject4.stream,
                      builder:
                          (context, AsyncSnapshot<PlanItemsResponse> snapshot) {
                        if (snapshot.hasData) {
                          if (snapshot.data!.error.isNotEmpty) {
                            return buildErrorWidget(snapshot.data!.error);
                          }

                          return Column(
                            children: [
                              //? Items ------------------------------
                              listplanitem(
                                snapshot.data!,
                                context,
                                currencyController: currencyController,
                                isload: isload,
                                date2: date2,
                                id: widget.id,
                                format: format,
                                starttimeController: starttimeController,
                                date: date,
                              ),
                              const SizedBox(
                                height: 20,
                              ),
                              //? Schedules ------------------------------
                              Container(
                                  padding: const EdgeInsets.only(
                                      left: 10, right: 10),
                                  child: Container(
                                    height: 50,
                                    width: MediaQuery.of(context).size.width,
                                    decoration: BoxDecoration(
                                        borderRadius: BorderRadius.circular(10),
                                        color: Colors.grey[100]),
                                    child: Container(
                                        padding: const EdgeInsets.only(
                                            left: 20,
                                            right: 20,
                                            top: 15,
                                            bottom: 15),
                                        child: Text(
                                          AppLocalizations.of(context)
                                              .translate('Schedule Items'),
                                          style: const TextStyle(
                                              fontWeight: FontWeight.bold),
                                        )),
                                  )),
                              const SizedBox(
                                height: 20,
                              ),
                              Container(
                                  child: listmyplan(
                                snapshot.data!,
                                context,
                                isload: isload,
                                currencyController: currencyController,
                                id: widget.id,
                                date2: date2,
                                date3: date3,
                              ))
                            ],
                          );
                        } else if (snapshot.hasError) {
                          return buildErrorWidget(snapshot.error.toString());
                        } else {
                          return Container(
                              // height: MediaQuery.of(context)
                              //     .size
                              //     .height,
                              child: buildLoadingWidget());
                        }
                      },
                    ),

                    // StreamBuilder<PlanResponse>(
                    //   stream: planbloc.subject5.stream,
                    //   builder: (context, AsyncSnapshot<PlanResponse> snapshot) {
                    //     if (snapshot.hasData) {
                    //       if (snapshot.data!.error.isNotEmpty) {
                    //         return buildErrorWidget(snapshot.data!.error);
                    //       }
                    //       return Container(
                    //           child: listmyplan(
                    //         snapshot.data!,
                    //         context,
                    //         isload: isload,
                    //         currencyController: currencyController,
                    //         id: widget.id,
                    //         date2: date2,
                    //         date3: date3,
                    //       ));
                    //     } else if (snapshot.hasError) {
                    //       return buildErrorWidget(snapshot.error!.toString());
                    //     } else {
                    //       return Container(child: buildLoadingWidget());
                    //     }
                    //   },
                    // ),
                  ],
                )
              ]),
          bottomNavigationBar: Container(
            padding: const EdgeInsets.only(left: 20, right: 20, bottom: 20),
            child: GestureDetector(
              onTap: () async {
                additem(context, id: widget.id);
              },
              child: Container(
                height: 50,
                width: MediaQuery.of(context).size.width,
                decoration: BoxDecoration(
                    color: const Color(0xFF27b4a8),
                    borderRadius: BorderRadius.circular(5)),
                child: Container(
                  padding: const EdgeInsets.all(10),
                  child: Center(
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        const Icon(Icons.add, color: Colors.white),
                        Text(
                          AppLocalizations.of(context)
                              .translate('Add Items to Plan'),
                          style: const TextStyle(color: Colors.white),
                        )
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  // create pdf file
  _exportPdf(int id, BuildContext context) async {
    progrsss(context);
    pr!.show();

    try {
      pdf.Document document = await PrintingService.generatePDF(id, context);

      final doc = await document.save();

      Directory? dir = Platform.isAndroid
          ? await getExternalStorageDirectory()
          : await getApplicationSupportDirectory();

      final pdfFile = File("${dir?.path}/$planName.pdf");

      await compute(pdfFile.writeAsBytesSync, doc);

      pr!.hide();

      OpenFile.open(pdfFile.path);

      // convertToUnit8List.listen((event) {
      //   pdfFile.writeAsBytes(event);
      //
      //
      // });
    } catch (e) {
      pr!.hide();
      log('EERRR $e');
    }
  }

// _exportPdf2(int id, BuildContext context) async {
//   progrsss(context);
//   pr!.show();
//
//   try {
//     pdf.Document document = await PrintingService.generatePDF(id, context);
//
//     // final doc = await document.save();
//
//     // Directory? dir = Platform.isAndroid
//     //     ? await getExternalStorageDirectory()
//     //     : await getApplicationSupportDirectory();
//
//     // final pdfFile = File("${dir?.path}/$planName.pdf");
//     //
//     // await compute(pdfFile.writeAsBytesSync, doc);
//     //
//
//     pr!.hide();
//
//     // OpenFile.open(pdfFile.path);
//
//     Navigator.of(context).push(
//       MaterialPageRoute(
//         builder: (BuildContext context) => PrintPreviewPage(
//           document: document,
//         ),
//       ),
//     );
//   } catch (e) {
//     pr!.hide();
//     log('EERRR $e');
//   }
// }
}
