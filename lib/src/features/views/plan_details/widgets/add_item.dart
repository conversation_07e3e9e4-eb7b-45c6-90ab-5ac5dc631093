import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:provider/provider.dart';

import '../../../../core/localization/app_language.dart';
import '../../../../core/localization/app_localizations.dart';
import '../../activities/activities.dart';
import '../../car_rental/car_rental.dart';
import '../../holiday/holiday.dart';
import '../../hotels/hotels.dart';
import '../../restaurants/resturants.dart';
import '../../shops/shops.dart';

void additem(BuildContext context, {required int id, var time, var date}) {
  var lang =
      Provider.of<AppLanguage>(context, listen: false).appLocal.languageCode;

  showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      enableDrag: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Padding(
            padding: EdgeInsets.only(
                bottom: MediaQuery.of(context).viewInsets.bottom),
            child: Container(
                height: MediaQuery.of(context).size.height * 0.50,
                decoration: BoxDecoration(
                    color: const Color(0xffF5F6F7),
                    borderRadius: const BorderRadius.only(
                        topLeft: Radius.circular(25.0),
                        topRight: Radius.circular(25.0)),
                    border: Border.all(color: Colors.black, width: 1.0)),
                child: SingleChildScrollView(
                    child: Column(
                  children: [
                    const SizedBox(
                      height: 10,
                    ),
                    Container(
                        height: 5, width: 50, color: const Color(0xffD2D4D6)),
                    const SizedBox(
                      height: 20,
                    ),
                    Center(
                        child: Text(
                      AppLocalizations.of(context).translate('Choose Category'),
                      style: const TextStyle(fontWeight: FontWeight.bold),
                    )),
                    Container(
                      padding: const EdgeInsets.all(15),
                      child: Container(
                        decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(10)),
                        child: Container(
                          padding: const EdgeInsets.all(15),
                          child: Center(
                            child: SizedBox(
                                // height:
                                //     MediaQuery.of(context).size.height * 0.4,
                                width: MediaQuery.of(context).size.width,
                                child: Column(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    Center(
                                      child: Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.center,
                                        children: List.generate(
                                            3,
                                            (index) => GestureDetector(
                                                onTap: () {
                                                  if (category[index]['name'] ==
                                                      'Hotels') {
                                                    Navigator.pop(context);
                                                    Navigator.push(context,
                                                        MaterialPageRoute(
                                                            builder: (context) {
                                                      return Hotels(
                                                          id: id,
                                                          time: time,
                                                          date: date);
                                                    }));
                                                  }
                                                  if (category[index]['name'] ==
                                                      'Restaurants') {
                                                    Navigator.pop(context);

                                                    Navigator.push(context,
                                                        MaterialPageRoute(
                                                            builder: (context) {
                                                      return Restaurants(
                                                          id: id,
                                                          time: time,
                                                          date: date);
                                                    }));
                                                  }
                                                  if (category[index]['name'] ==
                                                      'Activities') {
                                                    Navigator.pop(context);

                                                    Navigator.push(context,
                                                        MaterialPageRoute(
                                                            builder: (context) {
                                                      return Activites(
                                                          id: id,
                                                          time: time,
                                                          date: date);
                                                    }));
                                                  }
                                                },
                                                child: Container(
                                                    padding:
                                                        const EdgeInsets.only(
                                                            right: 10),
                                                    child: Row(children: [
                                                      Column(children: [
                                                        const SizedBox(
                                                          width: 5,
                                                        ),
                                                        Container(
                                                            decoration: BoxDecoration(
                                                                color: const Color(
                                                                    0xff1C2127),
                                                                borderRadius:
                                                                    BorderRadius
                                                                        .circular(
                                                                            5)),
                                                            height: 100,
                                                            width: MediaQuery.of(
                                                                        context)
                                                                    .size
                                                                    .width *
                                                                0.25,
                                                            padding:
                                                                const EdgeInsets
                                                                        .only(
                                                                    top: 10),
                                                            child: Column(
                                                                children: [
                                                                  Center(
                                                                      child: Center(
                                                                          child: Container(
                                                                              height: 50,
                                                                              width: 50,
                                                                              decoration: BoxDecoration(color: const Color(0xff343c45), borderRadius: BorderRadius.circular(120)),
                                                                              child: Center(
                                                                                  child: ClipRRect(
                                                                                      borderRadius: BorderRadius.circular(5),
                                                                                      child: Image.asset(
                                                                                        category[index]['icon']!,
                                                                                        height: 30,
                                                                                        width: 20,
                                                                                      )))))),
                                                                  const SizedBox(
                                                                    height: 10,
                                                                  ),
                                                                  Center(
                                                                      child: lang ==
                                                                              "en"
                                                                          ? Text(
                                                                              category[index]['name']!,
                                                                              style: const TextStyle(color: Colors.white),
                                                                            )
                                                                          : Text(
                                                                              category[index]['name_ar']!,
                                                                              style: const TextStyle(color: Colors.white),
                                                                            ))
                                                                ])),
                                                        const SizedBox(
                                                          height: 20,
                                                        ),
                                                      ])
                                                    ])))),
                                      ),
                                    ),
                                    Center(
                                      child: Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.center,
                                        children: List.generate(
                                            3,
                                            (index) => GestureDetector(
                                                onTap: () {
                                                  if (category2[index]
                                                          ['name'] ==
                                                      'Shops') {
                                                    Navigator.push(context,
                                                        MaterialPageRoute(
                                                            builder: (context) {
                                                      return Shops(
                                                          id: id,
                                                          time: time,
                                                          date: date);
                                                    }));
                                                  }
                                                  if (category2[index]
                                                          ['name'] ==
                                                      'Holiday Home') {
                                                    Navigator.push(context,
                                                        MaterialPageRoute(
                                                            builder: (context) {
                                                      return HolidayHomePage(
                                                          id: id,
                                                          time: time,
                                                          date: date);
                                                    }));
                                                  }
                                                  if (category2[index]
                                                          ['name'] ==
                                                      'Car Rental') {
                                                    Navigator.push(context,
                                                        MaterialPageRoute(
                                                            builder: (context) {
                                                      return CarRental(
                                                          id: id,
                                                          time: time,
                                                          date: date);
                                                    }));
                                                  }
                                                },
                                                child: Container(
                                                    padding:
                                                        const EdgeInsets.only(
                                                            right: 10),
                                                    child: Row(children: [
                                                      Column(children: [
                                                        const SizedBox(
                                                          width: 5,
                                                        ),
                                                        Container(
                                                            decoration: BoxDecoration(
                                                                color: const Color(
                                                                    0xff1C2127),
                                                                borderRadius:
                                                                    BorderRadius
                                                                        .circular(
                                                                            5)),
                                                            height: 100,
                                                            width: MediaQuery.of(
                                                                        context)
                                                                    .size
                                                                    .width *
                                                                0.25,
                                                            padding:
                                                                const EdgeInsets
                                                                        .only(
                                                                    top: 10),
                                                            child: Column(
                                                                children: [
                                                                  Center(
                                                                      child: Center(
                                                                          child: Container(
                                                                              height: 50,
                                                                              width: 50,
                                                                              decoration: BoxDecoration(color: const Color(0xff343c45), borderRadius: BorderRadius.circular(120)),
                                                                              child: Center(
                                                                                  child: ClipRRect(
                                                                                      borderRadius: BorderRadius.circular(5),
                                                                                      child: category2[index]['name'] == "Coffee Shops"
                                                                                          ? SvgPicture.asset(
                                                                                              category2[index]['icon']!,
                                                                                              color: Colors.white,
                                                                                              height: 25,
                                                                                              width: 20,
                                                                                            )
                                                                                          : Image.asset(
                                                                                              category2[index]['icon']!,
                                                                                              height: 30,
                                                                                              width: 20,
                                                                                            )))))),
                                                                  const SizedBox(
                                                                    height: 10,
                                                                  ),
                                                                  Center(
                                                                      child: lang ==
                                                                              "en"
                                                                          ? Text(
                                                                              category2[index]['name']!,
                                                                              style: const TextStyle(color: Colors.white),
                                                                            )
                                                                          : Text(
                                                                              category2[index]['name_ar']!,
                                                                              style: const TextStyle(color: Colors.white),
                                                                            ))
                                                                ])),
                                                        const SizedBox(
                                                          height: 20,
                                                        ),
                                                      ])
                                                    ])))),
                                      ),
                                    ),
                                  ],
                                )),
                          ),
                        ),
                      ),
                    ),
                  ],
                ))),
          ));
}

List<Map<String, String>> category = [
  {
    'icon': 'assets/noun_Hotel_3321668.png',
    'name': "Hotels",
    "name_ar": "الفنادق"
  },
  {
    'icon': 'assets/icons8-food.png',
    'name': "Restaurants",
    "name_ar": "المطاعم"
  },
  {
    'icon': 'assets/icons8-surf.png',
    'name': "Activities",
    "name_ar": "النشاطات"
  },
];
List<Map<String, String>> category2 = [
  {
    'icon': 'assets/icons8-shop.svg',
    'name': "Coffee Shops",
    "name_ar": "الكوفي شوب"
  },
  {
    'icon': 'assets/icons8-car.png',
    'name': "Car Rental",
    "name_ar": "السيارات"
  },
  {'icon': 'assets/home2.png', 'name': "Holiday Home", "name_ar": "المنازل"},
];
