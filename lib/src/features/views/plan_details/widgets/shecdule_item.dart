import 'dart:developer';

import 'package:datetime_picker_formfield/datetime_picker_formfield.dart';
import 'package:flutter/material.dart';

import '../../../../core/localization/app_localizations.dart';
import '../../../../core/response/generalResponse.dart';
import '../../../../core/services/api.dart';
import '../../../../core/shared_widgets/shared_widgets.dart';
import '../../../bloc/plan_bloc.dart';
import '../my_plans_details.dart';

void deleteshecduleitem(
  int planItemId, {
  required BuildContext context,
  required isload,
  required id,
}) {
  showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      enableDrag: true,
      backgroundColor: Colors.transparent,
      builder: (context) =>
          StatefulBuilder(builder: (context, StateSetter stateSetter) {
            return Padding(
                padding: EdgeInsets.only(
                    bottom: MediaQuery.of(context).viewInsets.bottom),
                child: Container(
                  height: MediaQuery.of(context).size.height * 0.3,
                  decoration: BoxDecoration(
                      color: const Color(0xffF5F6F7),
                      borderRadius: const BorderRadius.only(
                          topLeft: Radius.circular(25.0),
                          topRight: Radius.circular(25.0)),
                      border: Border.all(color: Colors.black, width: 1.0)),
                  child: SingleChildScrollView(
                      child: Column(
                    children: [
                      const SizedBox(
                        height: 10,
                      ),
                      Container(
                          height: 5, width: 50, color: const Color(0xffD2D4D6)),
                      const SizedBox(
                        height: 20,
                      ),
                      Center(
                          child: Text(
                        AppLocalizations.of(context)
                            .translate('Delete Item From Plan'),
                        style: const TextStyle(fontWeight: FontWeight.bold),
                      )),
                      Container(
                        padding: const EdgeInsets.all(15),
                        child: Container(
                          decoration: BoxDecoration(
                              color: Colors.white,
                              borderRadius: BorderRadius.circular(10)),
                          child: Container(
                            padding: const EdgeInsets.all(15),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                const SizedBox(
                                  height: 20,
                                ),
                                Text(AppLocalizations.of(context).translate(
                                    'Are you sure you want to delete item from plan')),
                                const SizedBox(
                                  height: 20,
                                ),
                                !isload
                                    ? Center(
                                        child: Container(
                                            // padding: EdgeInsets.only(right: 20, left: 20),
                                            child: GestureDetector(
                                                onTap: () async {
                                                  stateSetter(() {
                                                    isload = !isload;
                                                  });

                                                  log('kasjldkasd ${planItemId} IItt ${id}');

                                                  GeneralResponse
                                                      sucessinformation =
                                                      await Api.deleteitem(
                                                          planItemId,
                                                          isSchedule: true);

                                                  print(sucessinformation.code);
                                                  if (sucessinformation.code ==
                                                      "1") {
                                                    snackbar2(AppLocalizations
                                                            .of(context)
                                                        .translate(
                                                            'Delete Item successfuly'));
                                                    Navigator.pop(context);
                                                    planbloc
                                                        .getplanudetailsseritems(
                                                            id, "");
                                                  } else {
                                                    snackbar(AppLocalizations
                                                            .of(context)
                                                        .translate(
                                                            'Something went wrong, please try again later'));
                                                  }
                                                  stateSetter(() {
                                                    isload = !isload;
                                                  });
                                                },
                                                child: Container(
                                                  height: 50,
                                                  width: MediaQuery.of(context)
                                                      .size
                                                      .width,
                                                  decoration: BoxDecoration(
                                                      color: const Color(
                                                          0xffE04E4D),
                                                      borderRadius:
                                                          BorderRadius.circular(
                                                              10)),
                                                  child: Container(
                                                      padding:
                                                          const EdgeInsets.all(
                                                              10),
                                                      child: Center(
                                                          child: Text(
                                                        AppLocalizations.of(
                                                                context)
                                                            .translate(
                                                                'Yes Delete Item'),
                                                        style: const TextStyle(
                                                            color:
                                                                Colors.white),
                                                      ))),
                                                ))))
                                    : buildLoadingWidget(),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ],
                  )),
                ));
          }));
}

void scheduleitem(
  BuildContext context, {
  required int? itemid,
  required date,
  required starttimeController,
  required format,
  required id,
  required date2,
  required isload,
}) {
  log('asdasdasd ${dateSelected}');
  showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      enableDrag: true,
      backgroundColor: Colors.transparent,
      builder: (context) =>
          StatefulBuilder(builder: (context, StateSetter stateSetter) {
            return Padding(
                padding: EdgeInsets.only(
                    bottom: MediaQuery.of(context).viewInsets.bottom),
                child: Container(
                  height: MediaQuery.of(context).size.height * 0.4,
                  decoration: BoxDecoration(
                      color: const Color(0xffF5F6F7),
                      borderRadius: const BorderRadius.only(
                          topLeft: Radius.circular(25.0),
                          topRight: Radius.circular(25.0)),
                      border: Border.all(color: Colors.black, width: 1.0)),
                  child: SingleChildScrollView(
                      child: Column(
                    children: [
                      const SizedBox(
                        height: 10,
                      ),
                      Container(
                          height: 5, width: 50, color: const Color(0xffD2D4D6)),
                      const SizedBox(
                        height: 20,
                      ),
                      Text(AppLocalizations.of(context)
                          .translate('Schedule Items')),
                      Container(
                        padding: const EdgeInsets.all(15),
                        child: Container(
                          decoration: BoxDecoration(
                              color: Colors.white,
                              borderRadius: BorderRadius.circular(10)),
                          child: Container(
                            padding: const EdgeInsets.all(15),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                const SizedBox(
                                  height: 10,
                                ),
                                date != null
                                    ? Text(
                                        date,
                                      )
                                    : Container(),
                                const SizedBox(
                                  height: 20,
                                ),
                                Container(
                                    // width: 120,
                                    height: 50,
                                    padding: const EdgeInsets.symmetric(
                                        horizontal: 8),
                                    decoration: BoxDecoration(
                                      borderRadius: BorderRadius.circular(5),
                                      border: Border.all(
                                          color: Colors.black12, width: 1.0),
                                    ),
                                    child: DateTimeField(
                                      format: format,
                                      initialValue: DateTime.now(),
                                      controller: starttimeController,
                                      decoration: const InputDecoration(
                                          hintStyle: TextStyle(
                                              color: Colors.grey, fontSize: 12),
                                          prefixIcon: Icon(Icons.alarm),
                                          prefixIconConstraints: BoxConstraints(
                                            minWidth: 15,
                                            minHeight: 15,
                                          ),
                                          contentPadding: EdgeInsets.symmetric(
                                              vertical: 12, horizontal: 16),
                                          border: InputBorder.none),
                                      onShowPicker:
                                          (context, currentValue) async {
                                        final time = await showTimePicker(
                                          context: context,
                                          initialTime: TimeOfDay.fromDateTime(
                                              currentValue ?? DateTime.now()),
                                        );
                                        return DateTimeField.convert(time);
                                      },
                                    )),
                                const SizedBox(
                                  height: 20,
                                ),
                                !isload
                                    ? Center(
                                        child: GestureDetector(
                                          onTap: () async {
                                            stateSetter(() {
                                              isload = !isload;
                                            });
                                            GeneralResponse sucessinformation =
                                                await Api.addtimetoitem(
                                                    starttimeController.text,
                                                    itemid!,
                                                    id,
                                                    dateSelected);

                                            if (sucessinformation.code == "1") {
                                              await planbloc
                                                  .getplanudetailsseritems(
                                                      id, date2);
                                              // await planbloc
                                              //     .getplandetailsschedules(
                                              //         id, date2);
                                              snackbar2(AppLocalizations.of(
                                                      context)
                                                  .translate(
                                                      'time added successfuly'));
                                              Navigator.pop(context);
                                            } else {
                                              snackbar(AppLocalizations.of(
                                                      context)
                                                  .translate(
                                                      'Something went wrong, please try again later'));
                                            }
                                            stateSetter(() {
                                              isload = !isload;
                                            });

                                            Navigator.of(context).push(
                                              MaterialPageRoute(
                                                builder:
                                                    (BuildContext context) =>
                                                        MyplansDetails(
                                                  // data.plans[index],
                                                  // data.plans[index].name!,
                                                  '', id,
                                                ),
                                              ),
                                            );
                                            // _submit(rate.toString(), _comment.text);
                                          },
                                          child: Container(
                                            height: 50,
                                            width: MediaQuery.of(context)
                                                .size
                                                .width,
                                            decoration: BoxDecoration(
                                                color: const Color(0xFF27b4a8),
                                                borderRadius:
                                                    BorderRadius.circular(10)),
                                            child: Container(
                                              padding: const EdgeInsets.all(10),
                                              child: Center(
                                                child: Text(
                                                  AppLocalizations.of(context)
                                                      .translate(
                                                          'Set Activity to Time'),
                                                  style: const TextStyle(
                                                      color: Colors.white),
                                                ),
                                              ),
                                            ),
                                          ),
                                        ),
                                      )
                                    : buildLoadingWidget(),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ],
                  )),
                ));
          }));
}
