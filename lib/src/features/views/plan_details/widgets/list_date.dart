import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:page/src/features/views/plan_details/my_plans_details.dart';

import '../../../../core/localization/app_localizations.dart';
import '../../../../core/response/generalResponse.dart';
import '../../../../core/response/plan_response.dart';
import '../../../../core/services/api.dart';
import '../../../../core/shared_widgets/shared_widgets.dart';
import '../../../bloc/plan_bloc.dart';

Widget listdate(
  PlanItemsResponse data, {
  required BuildContext context,
  required PlanBloc planbloc,
  required int id,
  required isload,
  required String date2,
  required String date3,
  required String date,
  required bool isselected,
  required setState,
}) {
  return SizedBox(
      height: 60,
      child: ListView.builder(
        shrinkWrap: true,
        physics: const ClampingScrollPhysics(),
        scrollDirection: Axis.horizontal,
        itemBuilder: (BuildContext ctxt, int index) {
          date3 = data.dates[0].date!;

          print('date 2 $date2');
          date =
              "${AppLocalizations.of(context).translate('day')}${data.dates[index].day}(${data.dates[index].datedisplay})";
          return GestureDetector(
              onTap: () {
                setState(() {
                  date2 = data.dates[index].date!;
                  date =
                      "Day ${data.dates[index].day}(${data.dates[index].datedisplay})";
                });
                planbloc.getplanudetailsseritems(id, date2);
                // setState(() {
                data.dates[index].isselected = !data.dates[index].isselected;
                setState(() {
                  for (int y = 0; y < data.dates.length; y++) {
                    data.dates[y].isselected = false;
                  }
                });
                setState(() {
                  isselected = false;
                  data.dates[index].isselected = true;
                  dateSelected = data.dates[index].date!;

                  log('asfasfasfasfagggDDDD $dateSelected');
                });
                // });
                // print(data.dates[index].isselected);
              },
              child: Container(
                  padding: const EdgeInsets.only(left: 10),
                  child: Container(
                      width: 150,
                      padding: const EdgeInsets.only(left: 10, right: 10),
                      decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(10),
                          color: const Color(0xffF2F2F2),
                          border: Border.all(
                              width: 1,
                              color: isselected == true && index == 0
                                  ? const Color(0xffE6CB9C)
                                  : data.dates[index].isselected
                                      ? const Color(0xffE6CB9C)
                                      : const Color(0xffF2F2F2))),
                      child: FittedBox(
                        fit: BoxFit.scaleDown,
                        child: Column(
                          // crossAxisAlignment: CrossAxisAlignment.,
                          mainAxisSize: MainAxisSize.max,
                          children: [
                            Row(
                              mainAxisAlignment: MainAxisAlignment.end,
                              children: [
                                GestureDetector(
                                    onTap: () {
                                      deleteday(context,
                                          date: data.dates[index].date!,
                                          isload: isload!,
                                          id: id);
                                    },
                                    child: Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.center,
                                      children: const [Text('x')],
                                    )),
                              ],
                            ),
                            Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                data.dates[index].day != null
                                    ? Text(
                                        AppLocalizations.of(context)
                                                .translate('day') +
                                            data.dates[index].day.toString(),
                                        style: const TextStyle(
                                            fontWeight: FontWeight.bold,
                                            fontSize: 12),
                                      )
                                    : Container(),
                              ],
                            ),
                            Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                data.dates[index].datedisplay != null
                                    ? Text(data.dates[index].datedisplay!,
                                        style: const TextStyle(
                                            color: Color(0xff51565B),
                                            fontSize: 12))
                                    : Container()
                              ],
                            )
                          ],
                        ),
                      ))));
        },
        itemCount: data.dates.length,
      ));
}

void deleteday(BuildContext context,
    {required date, required isload, required int id}) {
  showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      enableDrag: true,
      backgroundColor: Colors.transparent,
      builder: (context) =>
          StatefulBuilder(builder: (context, StateSetter stateSetter) {
            return Padding(
              padding: EdgeInsets.only(
                  bottom: MediaQuery.of(context).viewInsets.bottom),
              child: Container(
                  height: MediaQuery.of(context).size.height * 0.3,
                  decoration: BoxDecoration(
                      color: const Color(0xffF5F6F7),
                      borderRadius: const BorderRadius.only(
                          topLeft: Radius.circular(25.0),
                          topRight: Radius.circular(25.0)),
                      border: Border.all(color: Colors.black, width: 1.0)),
                  child: SingleChildScrollView(
                      child: Column(
                    children: [
                      const SizedBox(
                        height: 10,
                      ),
                      Container(
                          height: 5, width: 50, color: const Color(0xffD2D4D6)),
                      const SizedBox(
                        height: 20,
                      ),
                      Center(
                          child: Text(
                        AppLocalizations.of(context).translate('Delete Day'),
                        style: const TextStyle(fontWeight: FontWeight.bold),
                      )),
                      Container(
                        padding: const EdgeInsets.all(15),
                        child: Container(
                          decoration: BoxDecoration(
                              color: Colors.white,
                              borderRadius: BorderRadius.circular(10)),
                          child: Container(
                            padding: const EdgeInsets.all(15),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                const SizedBox(
                                  height: 20,
                                ),
                                Text(AppLocalizations.of(context).translate(
                                    'Are you sure you want to delete Day')),
                                const SizedBox(
                                  height: 20,
                                ),
                                !isload
                                    ? Center(
                                        child: Container(
                                            // padding: EdgeInsets.only(right: 20, left: 20),
                                            child: GestureDetector(
                                                onTap: () async {
                                                  stateSetter(() {
                                                    isload = !isload;
                                                  });
                                                  GeneralResponse
                                                      sucessinformation =
                                                      await Api.deleteday(
                                                          date, id);

                                                  print(sucessinformation.code);
                                                  if (sucessinformation.code ==
                                                      "1") {
                                                    snackbar2(AppLocalizations
                                                            .of(context)
                                                        .translate(
                                                            'Delete Item successfuly'));
                                                    Navigator.pop(context);
                                                    planbloc
                                                        .getplanudetailsseritems(
                                                            id, "");
                                                    // planbloc
                                                    //     .getplandetailsschedules(
                                                    //         id, "");
                                                  } else {
                                                    snackbar(AppLocalizations
                                                            .of(context)
                                                        .translate(
                                                            'Something went wrong, please try again later'));
                                                  }
                                                  stateSetter(() {
                                                    isload = !isload;
                                                  });
                                                },
                                                child: Container(
                                                  height: 50,
                                                  width: MediaQuery.of(context)
                                                      .size
                                                      .width,
                                                  decoration: BoxDecoration(
                                                      color: const Color(
                                                          0xffE04E4D),
                                                      borderRadius:
                                                          BorderRadius.circular(
                                                              10)),
                                                  child: Container(
                                                      padding:
                                                          const EdgeInsets.all(
                                                              10),
                                                      child: Center(
                                                          child: Text(
                                                        AppLocalizations.of(
                                                                context)
                                                            .translate(
                                                                'Yes Delete Day'),
                                                        style: const TextStyle(
                                                            color:
                                                                Colors.white),
                                                      ))),
                                                ))))
                                    : buildLoadingWidget(),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ],
                  ))),
            );
          }));
}
