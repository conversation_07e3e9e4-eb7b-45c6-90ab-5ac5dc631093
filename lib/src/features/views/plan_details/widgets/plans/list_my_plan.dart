import 'package:dotted_line/dotted_line.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../../../../core/localization/app_language.dart';
import '../../../../../core/localization/app_localizations.dart';
import '../../../../../core/response/plan_response.dart';
import '../../../../../core/shared_widgets/shared_widgets.dart';
import '../shecdule_item.dart';

Widget listmyplan(
  PlanItemsResponse data,
  BuildContext context, {
  required isload,
  required currencyController,
  required id,
  required date2,
  required date3,
}) {
  var lang =
      Provider.of<AppLanguage>(context, listen: false).appLocal.languageCode;

  return data.schedules.isNotEmpty
      ? ListView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          // scrollDirection: Axis.vertical,
          itemBuilder: (BuildContext ctxt, int index) {
            return GestureDetector(
                onTap: () {},
                child: Container(
                  padding: const EdgeInsets.only(
                      top: 20, bottom: 5, right: 20, left: 20),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      Container(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisAlignment: MainAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                Container(
                                  height: 20,
                                  width: 20,
                                  decoration: BoxDecoration(
                                      color: const Color(0xffE6CB9C),
                                      borderRadius: BorderRadius.circular(20)),
                                  child: Container(
                                      padding: const EdgeInsets.all(7),
                                      child: Container(
                                          height: 5,
                                          width: 5,
                                          decoration: BoxDecoration(
                                              color: Colors.white,
                                              borderRadius:
                                                  BorderRadius.circular(15)))),
                                ),
                                const SizedBox(
                                  width: 10,
                                ),
                                data.schedules[index].time != null
                                    ? Text(
                                        data.schedules[index].time,
                                        style: const TextStyle(
                                            fontWeight: FontWeight.bold,
                                            fontSize: 12),
                                      )
                                    : Container(),
                              ],
                            ),
                          ],
                        ),
                      ),
                      SizedBox(
                        height: 230,
                        child: ListView(
                          scrollDirection: Axis.horizontal,
                          children: [
                            Container(
                                padding:
                                    const EdgeInsets.only(left: 10, right: 10),
                                child: const DottedLine(
                                  direction: Axis.vertical,
                                  lineLength: 237,
                                  lineThickness: 1.0,
                                  dashLength: 4.0,
                                  dashColor: Color(0xffE6CB9C),
                                  dashRadius: 0.0,
                                  dashGapLength: 4.0,
                                  dashGapColor: Colors.transparent,
                                  dashGapRadius: 0.0,
                                )),
                            // SizedBox(width: 10,),
                            SizedBox(
                                height: 230,
                                child: Stack(
                                  children: [
                                    Container(
                                        padding: const EdgeInsets.only(
                                            right: 5, top: 10),
                                        child: Row(children: [
                                          const SizedBox(
                                            width: 10,
                                          ),
                                          const SizedBox(
                                            width: 5,
                                          ),
                                          SizedBox(
                                              width: MediaQuery.of(context)
                                                      .size
                                                      .width *
                                                  0.35,
                                              child: ClipRRect(
                                                  borderRadius:
                                                      BorderRadius.circular(10),
                                                  child: Image.network(
                                                    data.schedules[index]
                                                        .image!,
                                                    fit: BoxFit.cover,
                                                    height: 210,
                                                  ))),
                                        ])),
                                    Container(
                                        padding: EdgeInsets.only(
                                            right: lang == "en" ? 5 : 20,
                                            top: 15,
                                            left: 15),
                                        child: GestureDetector(
                                            onTap: () {},
                                            child: Container(
                                              height: 205,
                                              width: MediaQuery.of(context)
                                                      .size
                                                      .width *
                                                  0.35,
                                              decoration: BoxDecoration(
                                                  borderRadius:
                                                      BorderRadius.circular(5),
                                                  gradient: LinearGradient(
                                                    end: Alignment.bottomCenter,
                                                    begin: Alignment.center,
                                                    colors: <Color>[
                                                      Colors.transparent,
                                                      //  Colors.white.withOpacity(0.5),
                                                      Colors.black
                                                          .withOpacity(0.7)
                                                    ],
                                                  )),
                                            ))),
                                    Positioned(
                                      bottom: 20,
                                      left: 20,
                                      child: Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          SizedBox(
                                            width: MediaQuery.of(context)
                                                    .size
                                                    .width *
                                                0.33,
                                            child: Text(
                                              data.schedules[index].name!,
                                              style: const TextStyle(
                                                  color: Colors.white,
                                                  fontSize: 12),
                                            ),
                                          ),
                                          Row(
                                            children: [
                                              data.schedules[index]
                                                          .startprice !=
                                                      null
                                                  ? Text(
                                                      '${data.schedules[index].startprice}',
                                                      style: const TextStyle(
                                                          color: Colors.white,
                                                          fontSize: 9),
                                                    )
                                                  : Container(),
                                              if (data.schedules[index]
                                                          .startprice !=
                                                      null &&
                                                  data.schedules[index]
                                                          .endprie !=
                                                      null)
                                                const Text(
                                                  ' - ',
                                                  style: TextStyle(
                                                      color: Colors.white,
                                                      fontSize: 9),
                                                ),
                                              data.schedules[index].endprie !=
                                                      null
                                                  ? Text(
                                                      '${data.schedules[index].endprie}',
                                                      style: const TextStyle(
                                                          color: Colors.white,
                                                          fontSize: 9),
                                                    )
                                                  : Container(),
                                              if (data.schedules[index]
                                                          .startprice !=
                                                      null ||
                                                  data.schedules[index]
                                                          .endprie !=
                                                      null)
                                                Text(
                                                  ' ${currencyController.currency}',
                                                  style: const TextStyle(
                                                      color: Colors.white,
                                                      fontSize: 9),
                                                )
                                            ],
                                          )
                                        ],
                                      ),
                                    ),
                                    Positioned(
                                      top: 15,
                                      right: lang == "en" ? 10 : 30,

                                      child: InkWell(
                                          onTap: () {
                                            deleteshecduleitem(
                                                data.schedules[index].id!,
                                                context: context,
                                                isload: isload,
                                                id: id);
                                          },
                                          child: Container(
                                              height: 25,
                                              width: 25,
                                              decoration: BoxDecoration(
                                                  borderRadius:
                                                      BorderRadius.circular(25),
                                                  color: Colors.grey
                                                      .withOpacity(0.5)),
                                              child: Center(
                                                child: Icon(
                                                  Icons.delete,
                                                  size: 16,
                                                  color: Colors.white
                                                      .withOpacity(0.5),
                                                ),
                                              ))),
                                      // SizedBox(width:MediaQuery.of(context).size.width * 0.20 ,),
                                      // Spacer(),
                                    ),
                                  ],
                                )),
                            // ListView.builder(
                            //   shrinkWrap: true,
                            //   physics: const ClampingScrollPhysics(),
                            //   scrollDirection: Axis.horizontal,
                            //   itemBuilder: (BuildContext ctxt, int index2) {
                            //     return Stack(
                            //       children: [
                            //         Container(
                            //             padding: const EdgeInsets.only(
                            //                 right: 5, top: 10),
                            //             child: Row(children: [
                            //               const SizedBox(
                            //                 width: 10,
                            //               ),
                            //               const SizedBox(
                            //                 width: 5,
                            //               ),
                            //               SizedBox(
                            //                   width: MediaQuery.of(context)
                            //                           .size
                            //                           .width *
                            //                       0.35,
                            //                   child: ClipRRect(
                            //                       borderRadius:
                            //                           BorderRadius.circular(
                            //                               10),
                            //                       child: MainCachedImage(
                            //                         data.schedules[index]
                            //                             .image!,
                            //                         fit: BoxFit.cover,
                            //                         height: 210,
                            //                       ))),
                            //             ])),
                            //         Container(
                            //             padding: EdgeInsets.only(
                            //                 right: lang == "en" ? 5 : 20,
                            //                 top: 15,
                            //                 left: 15),
                            //             child: GestureDetector(
                            //                 onTap: () {},
                            //                 child: Container(
                            //                   height: 205,
                            //                   width: MediaQuery.of(context)
                            //                           .size
                            //                           .width *
                            //                       0.35,
                            //                   decoration: BoxDecoration(
                            //                       borderRadius:
                            //                           BorderRadius.circular(
                            //                               5),
                            //                       gradient: LinearGradient(
                            //                         end: Alignment
                            //                             .bottomCenter,
                            //                         begin: Alignment.center,
                            //                         colors: <Color>[
                            //                           Colors.transparent,
                            //                           //  Colors.white.withOpacity(0.5),
                            //                           Colors.black
                            //                               .withOpacity(0.7)
                            //                         ],
                            //                       )),
                            //                 ))),
                            //         Positioned(
                            //           bottom: 20,
                            //           left: 20,
                            //           child: Column(
                            //             crossAxisAlignment:
                            //                 CrossAxisAlignment.start,
                            //             children: [
                            //               SizedBox(
                            //                 width: MediaQuery.of(context)
                            //                         .size
                            //                         .width *
                            //                     0.33,
                            //                 child: Text(
                            //                   data.schedules[index]
                            //                       .name!,
                            //                   style: const TextStyle(
                            //                       color: Colors.white,
                            //                       fontSize: 12),
                            //                 ),
                            //               ),
                            //               Row(
                            //                 children: [
                            //                   data
                            //                               .plans[index]
                            //
                            //                               .startprice !=
                            //                           null
                            //                       ? Text(
                            //                           '${data.schedules[index].startprice}',
                            //                           style:
                            //                               const TextStyle(
                            //                                   color: Colors
                            //                                       .white,
                            //                                   fontSize: 9),
                            //                         )
                            //                       : Container(),
                            //                   if (data
                            //                               .plans[index]
                            //
                            //                               .startprice !=
                            //                           null &&
                            //                       data
                            //                               .plans[index]
                            //
                            //                               .endprie !=
                            //                           null)
                            //                     const Text(
                            //                       ' - ',
                            //                       style: TextStyle(
                            //                           color: Colors.white,
                            //                           fontSize: 9),
                            //                     ),
                            //                   data
                            //                               .plans[index]
                            //
                            //                               .endprie !=
                            //                           null
                            //                       ? Text(
                            //                           '${data.schedules[index].endprie}',
                            //                           style:
                            //                               const TextStyle(
                            //                                   color: Colors
                            //                                       .white,
                            //                                   fontSize: 9),
                            //                         )
                            //                       : Container(),
                            //                   if (data
                            //                               .plans[index]
                            //
                            //                               .startprice !=
                            //                           null ||
                            //                       data
                            //                               .plans[index]
                            //
                            //                               .endprie !=
                            //                           null)
                            //                     Text(
                            //                       ' ${currencyController.currency}',
                            //                       style: const TextStyle(
                            //                           color: Colors.white,
                            //                           fontSize: 9),
                            //                     )
                            //                 ],
                            //               )
                            //             ],
                            //           ),
                            //         ),
                            //         Positioned(
                            //           top: 15,
                            //           right: lang == "en" ? 10 : 30,
                            //
                            //           child: InkWell(
                            //               onTap: () {
                            //                 deleteshecduleitem(
                            //                     data.schedules[index]
                            //                         .id!,
                            //                     context: context,
                            //                     isload: isload,
                            //                     id: id);
                            //               },
                            //               child: Container(
                            //                   height: 25,
                            //                   width: 25,
                            //                   decoration: BoxDecoration(
                            //                       borderRadius:
                            //                           BorderRadius.circular(
                            //                               25),
                            //                       color: Colors.grey
                            //                           .withOpacity(0.5)),
                            //                   child: Center(
                            //                     child: Icon(
                            //                       Icons.delete,
                            //                       size: 16,
                            //                       color: Colors.white
                            //                           .withOpacity(0.5),
                            //                     ),
                            //                   ))),
                            //           // SizedBox(width:MediaQuery.of(context).size.width * 0.20 ,),
                            //           // Spacer(),
                            //         ),
                            //       ],
                            //     );
                            //   },
                            //   itemCount: data.schedules[index].items?.length,
                            // )
                            // ),
                            // GestureDetector(
                            //     onTap: () {
                            //       print('date 2 when i select item $date2');
                            //       additem(context,
                            //           id: id,
                            //           time: data.schedules[index].time!,
                            //           date: date2 != null ? date2 : date3);
                            //     },
                            //     child: Container(
                            //       padding: const EdgeInsets.only(
                            //           left: 5, right: 5, top: 10, bottom: 20),
                            //       child: Container(
                            //         height: 150,
                            //         width: 100,
                            //         decoration: BoxDecoration(
                            //             color: const Color(0xffF1F1F1),
                            //             borderRadius:
                            //                 BorderRadius.circular(10)),
                            //         child: Column(
                            //           mainAxisAlignment:
                            //               MainAxisAlignment.center,
                            //           children: [
                            //             const Icon(Icons.add),
                            //             Text(AppLocalizations.of(context)
                            //                 .translate('Add Item'))
                            //           ],
                            //         ),
                            //       ),
                            //     ))
                          ],
                        ),
                      )
                    ],
                  ),
                ));
          },
          itemCount: data.schedules.length,
        )
      : nodatafound(AppLocalizations.of(context).translate('No items to show'));
}
