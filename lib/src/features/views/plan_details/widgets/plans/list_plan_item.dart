import 'package:flutter/material.dart';

import '../../../../../core/localization/app_localizations.dart';
import '../../../../../core/response/plan_response.dart';
import '../../../../../core/shared_widgets/shared_widgets.dart';
import '../shecdule_item.dart';

Widget listplanitem(
  PlanItemsResponse data,
  BuildContext context, {
  required currencyController,
  required isload,
  required date2,
  required id,
  required format,
  required starttimeController,
  required date,
}) {
  return Column(
    children: [
      data.plans.isNotEmpty
          ? Container(
              padding: const EdgeInsets.only(left: 10, right: 10),
              height: 230,
              width: MediaQuery.of(context).size.width,
              child: ListView.builder(
                shrinkWrap: true,
                physics: const ClampingScrollPhysics(),
                scrollDirection: Axis.horizontal,
                itemBuilder: (BuildContext ctxt, int index) {
                  return data.plans.isNotEmpty
                      ? GestureDetector(
                          onTap: () {
                            //  Navigator.of(context).pushReplacement(
                            //             MaterialPageRoute(
                            //                 builder:
                            //                     (BuildContext context) =>
                            //                         ChewieDemo()));
                          },
                          child: data.plans.isNotEmpty
                              ? Stack(
                                  children: [
                                    Row(children: [
                                      Column(children: [
                                        const SizedBox(
                                          width: 5,
                                        ),
                                        Row(children: [
                                          // for (var i = 0;
                                          //     i <
                                          //         data.plans
                                          //             .length;
                                          //     i++)
                                          Stack(
                                            children: [
                                              Container(
                                                  padding:
                                                      const EdgeInsets.only(
                                                          left: 5, right: 5),
                                                  child: Container(
                                                      child: ClipRRect(
                                                          borderRadius:
                                                              BorderRadius
                                                                  .circular(10),
                                                          child: Image.network(
                                                            data.plans[index]
                                                                .image!,
                                                            height: 220,
                                                            width: MediaQuery.of(
                                                                        context)
                                                                    .size
                                                                    .width *
                                                                0.35,
                                                            fit: BoxFit.cover,
                                                          )))),
                                              Container(
                                                  padding:
                                                      const EdgeInsets.only(
                                                          left: 5, right: 5),
                                                  child: GestureDetector(
                                                      onTap: () {},
                                                      child: Container(
                                                        height: 220,
                                                        width: MediaQuery.of(
                                                                    context)
                                                                .size
                                                                .width *
                                                            0.35,
                                                        decoration:
                                                            BoxDecoration(
                                                                borderRadius:
                                                                    BorderRadius
                                                                        .circular(
                                                                            5),
                                                                gradient:
                                                                    LinearGradient(
                                                                  end: Alignment
                                                                      .bottomCenter,
                                                                  begin: Alignment
                                                                      .center,
                                                                  colors: <Color>[
                                                                    Colors
                                                                        .transparent,
                                                                    //  Colors.white.withOpacity(0.5),
                                                                    Colors.black
                                                                        .withOpacity(
                                                                            0.7)
                                                                  ],
                                                                )),
                                                      ))),
                                              Positioned(
                                                bottom: 12,
                                                left: 10,
                                                child: Column(
                                                  crossAxisAlignment:
                                                      CrossAxisAlignment.start,
                                                  children: [
                                                    SizedBox(
                                                      width:
                                                          MediaQuery.of(context)
                                                                  .size
                                                                  .width *
                                                              0.33,
                                                      child: Text(
                                                        data.plans[index].name!,
                                                        style: const TextStyle(
                                                            color: Colors.white,
                                                            fontSize: 12),
                                                      ),
                                                    ),
                                                    Row(
                                                      children: [
                                                        data.plans[index]
                                                                    .startprice !=
                                                                null
                                                            ? Text(
                                                                '${data.plans[index].startprice}',
                                                                style: const TextStyle(
                                                                    color: Colors
                                                                        .white,
                                                                    fontSize:
                                                                        9),
                                                              )
                                                            : Container(),
                                                        if (data.plans[index]
                                                                    .startprice !=
                                                                null &&
                                                            data.plans[index]
                                                                    .endprie !=
                                                                null)
                                                          const Text(
                                                            ' - ',
                                                            style: TextStyle(
                                                                color: Colors
                                                                    .white,
                                                                fontSize: 9),
                                                          ),
                                                        data.plans[index]
                                                                    .endprie !=
                                                                null
                                                            ? Text(
                                                                '${data.plans[index].endprie}',
                                                                style: const TextStyle(
                                                                    color: Colors
                                                                        .white,
                                                                    fontSize:
                                                                        9),
                                                              )
                                                            : Container(),
                                                        if (data.plans[index]
                                                                    .startprice !=
                                                                null ||
                                                            data.plans[index]
                                                                    .endprie !=
                                                                null)
                                                          Text(
                                                            ' ${currencyController.currency}',
                                                            style:
                                                                const TextStyle(
                                                                    color: Colors
                                                                        .white,
                                                                    fontSize:
                                                                        9),
                                                          )
                                                      ],
                                                    )
                                                  ],
                                                ),
                                              ),
                                              Positioned(
                                                top: 10,
                                                right: 10,
                                                left: 10,
                                                child: Row(
                                                  mainAxisAlignment:
                                                      MainAxisAlignment
                                                          .spaceBetween,
                                                  children: [
                                                    InkWell(
                                                        onTap: () {
                                                          scheduleitem(
                                                            context,
                                                            itemid: data
                                                                .plans[index]
                                                                .videoId!,
                                                            date: date,
                                                            starttimeController:
                                                                starttimeController,
                                                            format: format,
                                                            id: id,
                                                            date2: date2,
                                                            isload: isload,
                                                          );
                                                        },
                                                        child: Container(
                                                            height: 25,
                                                            width: 25,
                                                            decoration: BoxDecoration(
                                                                borderRadius:
                                                                    BorderRadius
                                                                        .circular(
                                                                            25),
                                                                color: Colors
                                                                    .grey
                                                                    .withOpacity(
                                                                        0.5)),
                                                            child: Center(
                                                              child: Icon(
                                                                Icons.timer,
                                                                size: 16,
                                                                color: Colors
                                                                    .white
                                                                    .withOpacity(
                                                                        0.5),
                                                              ),
                                                            ))),
                                                    InkWell(
                                                        onTap: () {
                                                          deleteshecduleitem(
                                                              data.plans[index]
                                                                  .id!,
                                                              context: context,
                                                              isload: isload,
                                                              id: id);
                                                        },
                                                        child: Container(
                                                            height: 25,
                                                            width: 25,
                                                            decoration: BoxDecoration(
                                                                borderRadius:
                                                                    BorderRadius
                                                                        .circular(
                                                                            25),
                                                                color: Colors
                                                                    .grey
                                                                    .withOpacity(
                                                                        0.5)),
                                                            child: Center(
                                                              child: Icon(
                                                                Icons.delete,
                                                                size: 16,
                                                                color: Colors
                                                                    .white
                                                                    .withOpacity(
                                                                        0.5),
                                                              ),
                                                            ))),
                                                    // SizedBox(width:MediaQuery.of(context).size.width * 0.20 ,),
                                                    // Spacer(),
                                                  ],
                                                ),
                                              ),
                                            ],
                                          ),
                                          const SizedBox(
                                            width: 10,
                                          )
                                        ])
                                      ])
                                    ])
                                  ],
                                )
                              : nodatafound(AppLocalizations.of(context)
                                  .translate('No items to show')))
                      : nodatafound(AppLocalizations.of(context)
                          .translate('No items to show'));
                },
                itemCount: data.plans.length,
              ))
          : nodatafound(
              AppLocalizations.of(context).translate('No items to show'))
    ],
  );
}
