import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:page/src/core/config/constants.dart';
import 'package:page/src/core/localization/app_language.dart';
import 'package:page/src/core/utils/main_cached_image.dart';
import 'package:page/src/features/views/account/widgets/prices_widgets.dart';
import 'package:provider/provider.dart';

import '../../../../core/localization/app_localizations.dart';
import '../../../../core/response/category_response.dart';
import '../../../../core/response/generalResponse.dart';
import '../../../../core/services/api.dart';
import '../../../../core/shared_widgets/shared_widgets.dart';
import '../../../bloc/profile_bloc.dart';
import '../../ad_details/video_widget.dart';
import '../../car_rent_details/car_rent_details.dart';
import '../../holiday_home_details/holiday_home_details.dart';
import 'build_reels_widget.dart';

void favouirtelist(
  BuildContext context, {
  required issel,
  required issel2,
  required currencyController,
  required page,
}) {
  showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) =>
          StatefulBuilder(builder: (context, StateSetter stateSetter) {
            return Padding(
                padding: EdgeInsets.only(
                    bottom: MediaQuery.of(context).viewInsets.bottom),
                child: Container(
                  height: MediaQuery.of(context).size.height * 0.9,
                  decoration: BoxDecoration(
                      color: const Color(0xffF5F6F7),
                      borderRadius: const BorderRadius.only(
                          topLeft: Radius.circular(25.0),
                          topRight: Radius.circular(25.0)),
                      border: Border.all(color: Colors.black, width: 1.0)),
                  child: SingleChildScrollView(
                      child: Column(
                    children: [
                      const SizedBox(
                        height: 10,
                      ),
                      Container(
                          height: 5, width: 50, color: const Color(0xffD2D4D6)),
                      const SizedBox(
                        height: 20,
                      ),
                      Center(
                          child: Text(
                        AppLocalizations.of(context).translate('Favoritelist'),
                        style: const TextStyle(fontWeight: FontWeight.bold),
                      )),
                      // Container(
                      //     padding: const EdgeInsets.all(15),
                      //     child: Container(
                      //         decoration: BoxDecoration(
                      //             color: Colors.white,
                      //             borderRadius: BorderRadius.circular(10)),
                      //         child: Container(
                      //             padding: const EdgeInsets.only(
                      //                 left: 15, right: 15, top: 5, bottom: 5),
                      //             child: Row(
                      //               mainAxisAlignment:
                      //                   MainAxisAlignment.spaceAround,
                      //               children: [
                      //                 InkWell(
                      //                     onTap: () {
                      //                       stateSetter(() {
                      //                         issel = true;
                      //                         issel2 = false;
                      //                       });
                      //                     },
                      //                     child: Container(
                      //                       width: 160,
                      //                       padding: const EdgeInsets.only(
                      //                           top: 10,
                      //                           bottom: 10,
                      //                           left: 20,
                      //                           right: 20),
                      //                       color: issel == true
                      //                           ? const Color(0xffD8B77F)
                      //                           : Colors.white,
                      //                       child: Text(
                      //                         AppLocalizations.of(context)
                      //                             .translate('Categories'),
                      //                         style: TextStyle(
                      //                             color: issel == true
                      //                                 ? Colors.white
                      //                                 : Colors.black),
                      //                       ),
                      //                     )),
                      //                 InkWell(
                      //                     onTap: () {
                      //                       stateSetter(() {
                      //                         issel2 = true;
                      //                         issel = false;
                      //                       });
                      //                     },
                      //                     child: Container(
                      //                       width: 150,
                      //                       padding: const EdgeInsets.only(
                      //                           top: 10,
                      //                           bottom: 10,
                      //                           left: 30,
                      //                           right: 30),
                      //                       color: issel2 == true
                      //                           ? const Color(0xffD8B77F)
                      //                           : Colors.white,
                      //                       child: Text(
                      //                           AppLocalizations.of(context)
                      //                               .translate('Reels'),
                      //                           style: TextStyle(
                      //                               color: issel2 == true
                      //                                   ? Colors.white
                      //                                   : Colors.black)),
                      //                     )),
                      //               ],
                      //             )))),
                      Container(
                        padding: const EdgeInsets.all(15),
                        child: Container(
                          width: MediaQuery.of(context).size.width,
                          decoration: BoxDecoration(
                              color: Colors.white,
                              borderRadius: BorderRadius.circular(10)),
                          child: Container(
                              padding: const EdgeInsets.all(15),
                              child:
                                  // issel2 == true
                                  //     ? StreamBuilder<CategoryFavoriteResponse>(
                                  //         stream: bloc2.subject3.stream,
                                  //         builder: (context,
                                  //             AsyncSnapshot<
                                  //                     CategoryFavoriteResponse>
                                  //                 snapshot) {
                                  //           if (snapshot.hasData) {
                                  //             if (snapshot.data!.error.isNotEmpty) {
                                  //               return buildErrorWidget(
                                  //                   snapshot.data!.error);
                                  //             }
                                  //             return Container(
                                  //                 child: buildReelsWidget(
                                  //                     snapshot.data!, context,
                                  //                     currencyController:
                                  //                         currencyController,
                                  //                     page: page));
                                  //           } else if (snapshot.hasError) {
                                  //             return buildErrorWidget(
                                  //                 snapshot.error.toString());
                                  //           } else {
                                  //             return Container(
                                  //                 child: buildLoadingWidget());
                                  //           }
                                  //         },
                                  //       )
                                  //     :
                                  StreamBuilder<CategoryFavoriteResponse>(
                                stream: bloc2.subject3.stream,
                                builder: (context,
                                    AsyncSnapshot<CategoryFavoriteResponse>
                                        snapshot) {
                                  if (snapshot.hasData) {
                                    if (snapshot.data!.error.isNotEmpty) {
                                      return buildErrorWidget(
                                          snapshot.data!.error);
                                    }
                                    return Container(
                                        child: buildCategoryWidget(
                                            snapshot.data!, context,
                                            issel: issel,
                                            issel2: issel2,
                                            currencyController:
                                                currencyController,
                                            page: page));
                                  } else if (snapshot.hasError) {
                                    return buildErrorWidget(
                                        snapshot.error.toString());
                                  } else {
                                    return Container(
                                        // height: MediaQuery.of(context)
                                        //     .size
                                        //     .height,
                                        child: buildLoadingWidget());
                                  }
                                },
                              )),
                        ),
                      ),
                    ],
                  )),
                ));
          }));
}

Widget buildCategoryWidget(CategoryFavoriteResponse data, BuildContext context,
    {required issel,
    required issel2,
    required currencyController,
    required page}) {
  // final CurrencyController currencyController = CurrencyController();

  // currencyController.getcuurentcurrency(context);

  return data.category.isNotEmpty
      ? StaggeredGridView.countBuilder(
          staggeredTileBuilder: (index) {
            return const StaggeredTile.fit(1);
          },
          shrinkWrap: true,
          padding: const EdgeInsets.symmetric(horizontal: 10.0, vertical: 20.0),
          crossAxisCount: 2,
          //  physics: NeverScrollableScrollPhysics(),
          crossAxisSpacing: 5,
          mainAxisSpacing: 8,
          itemCount: data.category.length,
          primary: false,
          itemBuilder: (context, index) {
            var lang = Provider.of<AppLanguage>(context, listen: false)
                .appLocal
                .languageCode;
            final isEnglish = lang == 'en';

            final nameWithPrice = Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  data.category[index].name ?? '',
                  style: const TextStyle(color: Colors.white, fontSize: 12),
                ),
                const SizedBox(
                  height: 5,
                ),
                priceWithCurrency(context,
                    categoryFavorite: data.category[index],
                    currencyController: currencyController)
              ],
            );
            log('asfsafas ${data.category[index].description}');

            return Column(children: [
              const SizedBox(
                width: 5,
              ),
              Stack(children: [
                ClipRRect(
                    borderRadius: BorderRadius.circular(5),
                    child: data.category[index].images != null
                        ? MainCachedImage(
                            data.category[index].images!,
                            height: 263,
                            width: MediaQuery.of(context).size.width * 0.4,
                            fit: BoxFit.fill,
                          )
                        : Container()),
                GestureDetector(
                    onTap: () {
                      if (data.category[index].type ==
                          AppConstants.holidayHomesId.toString()) {
                        Navigator.of(context).push(MaterialPageRoute(
                            builder: (BuildContext context) =>
                                HoldayHomeDetails(
                                  video: data.category[index],
                                )));
                      } else if (data.category[index].type ==
                          AppConstants.carRentalsId.toString()) {
                        Navigator.of(context).push(MaterialPageRoute(
                            builder: (BuildContext context) =>
                                CarRentDetailsVideoWidget(
                                  video: data.category[index],
                                )));
                      } else {
                        Navigator.of(context).push(MaterialPageRoute(
                            builder: (BuildContext context) => VideoViewWidget(
                                  video: data.category[index],
                                )));
                      }
                    },
                    child: Container(
                      height: 263,
                      width: MediaQuery.of(context).size.width * 0.44,
                      decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(5),
                          gradient: LinearGradient(
                            end: Alignment.bottomCenter,
                            begin: Alignment.center,
                            colors: <Color>[
                              Colors.transparent,
                              //  Colors.white.withOpacity(0.5),
                              Colors.black.withOpacity(0.7)
                            ],
                          )),
                    )),
                Positioned(
                  top: 10,
                  right: 10,
                  child: InkWell(
                      onTap: () async {
                        GeneralResponse sucessinformation =
                            await Api.removemaincategoryfromfavourite(
                                data.category[index].id!);

                        print(sucessinformation.code);
                        if (sucessinformation.code == "1") {
                          snackbar2(AppLocalizations.of(context)
                              .translate('remove from favourite successfuly'));

                          await bloc2.getfavouritecategory(page, 20);

                          favouirtelist(context,
                              issel: issel,
                              issel2: issel2,
                              currencyController: currencyController,
                              page: page);
                        } else {
                          snackbar(AppLocalizations.of(context).translate(
                              'Something went wrong, please try again later'));
                        }
                      },
                      child: Container(
                          height: 30,
                          width: 30,
                          decoration: BoxDecoration(
                              color: const Color(0xff4d5e72).withOpacity(0.2),
                              borderRadius:
                                  const BorderRadius.all(Radius.circular(30))),
                          child: const Center(
                              child: Icon(Icons.favorite,
                                  color: Colors.white, size: 20)))),
                ),
                if (isEnglish)
                  Positioned(bottom: 12, left: 10, child: nameWithPrice)
                else
                  Positioned(bottom: 12, right: 10, child: nameWithPrice),
              ])
            ]);
          })
      : buildnodatafavourite(
          AppLocalizations.of(context).translate('No Items in Favorite List'),
          AppLocalizations.of(context).translate('Add itsms to check later'));
}
