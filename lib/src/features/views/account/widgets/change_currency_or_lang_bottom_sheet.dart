import 'package:flutter/material.dart';
import 'package:flutter_phoenix/flutter_phoenix.dart';
import 'package:page/src/features/views/home/<USER>';
import 'package:page/src/features/views/splash_screen/services/splash_services.dart';
import 'package:page/src/features/views/splash_screen/view/first_splash.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../../../app.dart';
import '../../../../core/localization/app_language.dart';
import '../../../models/model_select.dart';

class ChangeCurrencyOrLangBottomSheet extends StatefulWidget {
  final List<ModelSelect> langList;
  final String name;
  final String type;

  const ChangeCurrencyOrLangBottomSheet(this.langList, this.name, this.type,
      {super.key});

  @override
  _BottomNavgationBar createState() => _BottomNavgationBar();
}

class _BottomNavgationBar extends State<ChangeCurrencyOrLangBottomSheet> {
  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    var appLanguage = Provider.of<AppLanguage>(context);

    return SingleChildScrollView(
      child: Container(
          decoration: const BoxDecoration(
            borderRadius: BorderRadius.only(
                topRight: Radius.circular(15), topLeft: Radius.circular(15)),
            color: Color(0xFFF5F6F7),
          ),
          alignment: Alignment.center,
          child: Column(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              const SizedBox(
                height: 15,
              ),
              Container(
                decoration: BoxDecoration(
                  color: const Color(0xFFD2D4D6),
                  borderRadius: BorderRadius.circular(30),
                ),
                width: 36,
                height: 4,
              ),
              Padding(
                padding: const EdgeInsets.fromLTRB(0, 25, 0, 10),
                child: Text(
                  widget.name,
                  style: const TextStyle(
                      color: Color(
                        0xff191C1F,
                      ),
                      fontWeight: FontWeight.bold,
                      fontSize: 16),
                ),
              ),
              Padding(
                  padding: const EdgeInsets.fromLTRB(15, 15, 15, 20),
                  child: Container(
                    decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(10),
                        color: Colors.white),
                    child: Padding(
                        padding: const EdgeInsets.fromLTRB(15, 15, 15, 15),
                        child: ListView.builder(
                          itemBuilder: (context, index) {
                            return Column(
                              children: [
                                SizedBox(
                                    width: MediaQuery.of(context).size.width,
                                    child: InkWell(
                                      onTap: () async {
                                        SharedPreferences prefs =
                                            await SharedPreferences
                                                .getInstance();
                                        setState(() {
                                          for (int i = 0;
                                              i < widget.langList.length;
                                              i++) {
                                            widget.langList[i].check = false;
                                          }
                                          if (widget.type == 'Language') {
                                            var languagecode = prefs
                                                .getString('language_code');
                                            if (languagecode == "ar") {
                                              appLanguage.changeLanguage(
                                                  const Locale("en"));
                                              widget.langList[0].check = true;
                                            } else {
                                              appLanguage.changeLanguage(
                                                  const Locale("ar"));
                                              widget.langList[1].check = true;
                                            }
                                          }
                                          if (widget.type == 'Currency') {
                                            prefs.setString('currency',
                                                widget.langList[index].name);
                                            // var current_ = _prefs
                                            //   .getString('language_code');
                                          }

                                          widget.langList[index].check = true;
                                        });

                                        for (var property in properties) {
                                          videoPlayerController.removeWhere(
                                              (key, value) =>
                                                  key == property.id);
                                        }
                                        Phoenix.rebirth(context);
                                        // Navigator.of(context).pushReplacement(
                                        //   MaterialPageRoute(
                                        //     builder: (BuildContext context) => MyApp(
                                        //       appLanguage: appLanguage,
                                        //       languageCode: widget.langList[index].name,
                                        //     ),
                                        //   ),
                                        // );
                                        Navigator.of(context).push(
                                            MaterialPageRoute(
                                                builder:
                                                    (BuildContext context) =>
                                                         MyApp(
                                                          appLanguage: appLanguage,
                                                           languageCode: widget.langList[index].name,
                                                        )));
                                      },
                                      child: Padding(
                                        padding: const EdgeInsets.only(
                                            top: 10, bottom: 10),
                                        child: Row(
                                          mainAxisAlignment:
                                              MainAxisAlignment.spaceBetween,
                                          children: [
                                            Text(
                                              widget.langList[index].name,
                                              style: widget.langList[index]
                                                          .check ==
                                                      true
                                                  ? const TextStyle(
                                                      color: Color(
                                                        0xff191C1F,
                                                      ),
                                                      fontWeight:
                                                          FontWeight.bold,
                                                      fontSize: 12)
                                                  : const TextStyle(
                                                      color: Color(
                                                        0xff191C1F,
                                                      ),
                                                      fontSize: 12),
                                            ),
                                            widget.langList[index].check == true
                                                ? const Icon(Icons.check,
                                                    color: Color(0xFFD8B77F))
                                                : const Visibility(
                                                    child: Text(''),
                                                    visible: false,
                                                  )
                                          ],
                                        ),
                                      ),
                                    )),
                                widget.langList.length.isFinite
                                    ? const Visibility(
                                        child: Text(''),
                                        visible: false,
                                      )
                                    : Padding(
                                        padding: const EdgeInsets.fromLTRB(
                                            0, 10, 0, 10),
                                        child: Container(
                                          width:
                                              MediaQuery.of(context).size.width,
                                          height: 1,
                                          color: const Color(0xFFF1F1F1),
                                        ),
                                      ),
                              ],
                            );
                          },
                          itemCount: widget.langList.length,
                          shrinkWrap: true,
                          physics: const NeverScrollableScrollPhysics(),
                        )),
                  ))
            ],
          )),
    );
  }
}
