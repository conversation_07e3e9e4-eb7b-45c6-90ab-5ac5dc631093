import 'package:flutter/material.dart';
import 'package:page/src/core/localization/app_localizations.dart';

import '../../../../core/utils/print_services.dart';

class AboutUsPage extends StatelessWidget {
  const AboutUsPage({super.key});

  // Team members data arranged for 3-3-2 grid layout (<PERSON> and <PERSON> last)
  static const List<Map<String, String>> _teamMembers = [
    {
      'image': 'assets/images/zaid.jpg',
      'name': '<PERSON><PERSON><PERSON>',
      'languages': 'English/Arabic',
    },
    {
      'image': 'assets/images/momen.jpg',
      'name': '<PERSON><PERSON>',
      'languages': 'English/Urdu',
    },
    {
      'image': 'assets/images/mohamed_yehia.jpg',
      'name': '<PERSON>',
      'languages': 'English/Arabic',
    },
    {
      'image': 'assets/images/fahdi.jpg',
      'name': 'Fahdy <PERSON>',
      'languages': 'Urdu/Hindi/Punjabi/English',
    },
    {
      'image': 'assets/images/mohamed_mahgoub.jpg',
      'name': '<PERSON>',
      'languages': 'English/Arabic',
    },
    {
      'image': 'assets/images/mohamed_salem.jpg',
      'name': '<PERSON>',
      'languages': 'English/Arabic',
    },
    {
      'image': 'assets/images/tmara.jpg',
      'name': 'Tamara Bakir',
      'languages': 'English',
    },
    {
      'image': 'assets/images/sonia.jpg',
      'name': 'Sonia Ouakdi',
      'languages': 'Arabic/English/French',
    },
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: const Color(0xFF27b4a8),
        surfaceTintColor: Colors.transparent,
        centerTitle: true,
        title: Text(
          AppLocalizations.of(context).translate('About Us'),
          style: TextStyle(
            fontFamily: isEng(context) ? 'Roboto' : 'Tajawal',
          ),
        ),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.only(
          left: 20,
          right: 20,
          bottom: 20,
        ),
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Image.asset(
                'assets/images/about_1.webp',
                width: double.infinity,
                height: 300,
              ),
              const SizedBox(height: 20),
              Text(
                AppLocalizations.of(context).translate('About Us Title 1'),
                style:
                    const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 20),
              Text(
                AppLocalizations.of(context)
                    .translate('About Us Description 1'),
                style: const TextStyle(
                  fontSize: 16,
                ),
                textAlign: TextAlign.center,
              ),

              //? Second Section
              const SizedBox(height: 20),
              ClipRRect(
                borderRadius: BorderRadius.circular(12),
                child: Image.asset(
                  'assets/images/about_2.jpg',
                  width: double.infinity,
                  height: 320,
                  fit: BoxFit.fill,
                ),
              ),
              const SizedBox(height: 20),
              Text(
                AppLocalizations.of(context).translate('About Us Title 2'),
                style:
                    const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 20),

              Text(
                AppLocalizations.of(context)
                    .translate('About Us Description 2'),
                style: const TextStyle(
                  fontSize: 16,
                ),
                textAlign: TextAlign.center,
              ),

              const SizedBox(height: 20),

              // Team Members Grid Layout (3-3-2)
              _buildTeamMembersGrid(context),

              const SizedBox(height: 20),

              ClipRRect(
                borderRadius: BorderRadius.circular(12),
                child: Image.asset(
                  'assets/images/cover_image.jpeg',
                  width: double.infinity,
                  height: 320,
                  fit: BoxFit.fill,
                ),
              ),
              const SizedBox(height: 20),
              Text(
                AppLocalizations.of(context)
                    .translate('About Us Description 3'),
                style: const TextStyle(
                  fontSize: 16,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildTeamMembersGrid(BuildContext context) {
    return Column(
      children: [
        // First row - 3 members
        _buildTeamRow(context, _teamMembers.sublist(0, 3)),
        const SizedBox(height: 20),

        // Second row - 3 members
        _buildTeamRow(context, _teamMembers.sublist(3, 6)),
        const SizedBox(height: 20),

        // Third row - 2 members (Tamara and Sonia)
        _buildTeamRow(context, _teamMembers.sublist(6, 8)),
      ],
    );
  }

  Widget _buildTeamRow(
      BuildContext context, List<Map<String, String>> members) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children:
          members.map((member) => _buildTeamMember(context, member)).toList(),
    );
  }

  Widget _buildTeamMember(BuildContext context, Map<String, String> member) {
    return Expanded(
      child: Container(
        margin: const EdgeInsets.symmetric(horizontal: 8),
        child: Column(
          children: [
            ClipRRect(
              borderRadius: BorderRadius.circular(60),
              child: Image.asset(
                member['image']!,
                width: 120,
                height: 120,
                fit: BoxFit.cover,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              AppLocalizations.of(context).translate(member['name']!),
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
            const SizedBox(height: 4),
            Text(
              AppLocalizations.of(context).translate(member['languages']!),
              style: const TextStyle(
                fontSize: 12,
                color: Colors.grey,
              ),
              textAlign: TextAlign.center,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }
}
