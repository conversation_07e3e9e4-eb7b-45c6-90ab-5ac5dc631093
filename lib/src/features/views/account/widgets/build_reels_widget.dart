// import 'package:flutter/material.dart';
// import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
// import 'package:page/src/core/utils/main_cached_image.dart';
// import 'package:page/src/features/views/account/widgets/prices_widgets.dart';
// import 'package:provider/provider.dart';
//
// import '../../../../core/config/constants.dart';
// import '../../../../core/localization/app_language.dart';
// import '../../../../core/localization/app_localizations.dart';
// import '../../../../core/response/category_response.dart';
// import '../../../../core/response/generalResponse.dart';
// import '../../../../core/services/api.dart';
// import '../../../../core/shared_widgets/shared_widgets.dart';
// import '../../../bloc/profile_bloc.dart';
// import '../../ad_details/video_widget.dart';
// import '../../car_rent_details/car_rent_details.dart';
// import '../../holiday_home_details/holiday_home_details.dart';
//
// Widget buildReelsWidget(
//   CategoryFavoriteResponse data,
//   BuildContext context, {
//   required currencyController,
//   required page,
// }) {
//   return data.vibes.isNotEmpty
//       ? StaggeredGridView.countBuilder(
//           staggeredTileBuilder: (index) {
//             return const StaggeredTile.fit(1);
//           },
//           shrinkWrap: true,
//           padding: const EdgeInsets.symmetric(horizontal: 10.0, vertical: 20.0),
//           crossAxisCount: 2,
//           //  physics: NeverScrollableScrollPhysics(),
//           crossAxisSpacing: 5,
//           mainAxisSpacing: 8,
//           itemCount: data.vibes.length,
//           primary: false,
//           itemBuilder: (context, index) {
//             var lang = Provider.of<AppLanguage>(context, listen: false)
//                 .appLocal
//                 .languageCode;
//             final isEnglish = lang == 'en';
//
//             final nameWithPrice = Column(
//               crossAxisAlignment: CrossAxisAlignment.start,
//               children: [
//                 Text(
//                   data.vibes[index].name!,
//                   style: const TextStyle(color: Colors.white, fontSize: 12),
//                 ),
//                 const SizedBox(height: 5),
//                 priceWithCurrency(context,
//                     categoryFavorite: data.vibes[index],
//                     currencyController: currencyController)
//               ],
//             );
//
//             return Column(children: [
//               const SizedBox(
//                 width: 5,
//               ),
//               Stack(children: [
//                 Container(
//                     child: Container(
//                         child: ClipRRect(
//                             borderRadius: BorderRadius.circular(5),
//                             child: data.vibes[index].image != null
//                                 ? MainCachedImage(
//                                     data.vibes[index].image!,
//                                     height: 263,
//                                     width:
//                                         MediaQuery.of(context).size.width * 0.4,
//                                     fit: BoxFit.fill,
//                                   )
//                                 : Container()))),
//                 GestureDetector(
//                     onTap: () {
//                       if (data.vibes[index].type ==
//                           AppConstants.holidayHomesId.toString()) {
//                         Navigator.of(context).push(MaterialPageRoute(
//                             builder: (BuildContext context) =>
//                                 HoldayHomeDetails(
//                                   video: data.vibes[index],
//                                 )));
//                       } else if (data.vibes[index].type ==
//                           AppConstants.carRentalsId.toString()) {
//                         Navigator.of(context).push(MaterialPageRoute(
//                             builder: (BuildContext context) =>
//                                 CarRentDetailsVideoWidget(
//                                   video: data.vibes[index],
//                                 )));
//                       } else {
//                         Navigator.of(context).push(MaterialPageRoute(
//                             builder: (BuildContext context) => VideoViewWidget(
//                                   video: data.vibes[index],
//                                 )));
//                       }
//                     },
//                     child: Container(
//                       height: 263,
//                       width: MediaQuery.of(context).size.width * 0.4,
//                       decoration: BoxDecoration(
//                           borderRadius: BorderRadius.circular(5),
//                           gradient: LinearGradient(
//                             end: Alignment.bottomCenter,
//                             begin: Alignment.center,
//                             colors: <Color>[
//                               Colors.transparent,
//                               //  Colors.white.withOpacity(0.5),
//                               Colors.black.withOpacity(0.7)
//                             ],
//                           )),
//                     )),
//                 if (isEnglish)
//                   Positioned(bottom: 12, left: 10, child: nameWithPrice)
//                 else
//                   Positioned(bottom: 12, right: 10, child: nameWithPrice),
//                 Positioned(
//                   top: 10,
//                   right: 10,
//                   child: InkWell(
//                       onTap: () async {
//                         GeneralResponse sucessinformation =
//                             await Api.removemainreelfavourite(
//                                 data.vibes[index].id!);
//                         print(sucessinformation.code);
//                         if (sucessinformation.code == "1") {
//                           snackbar2(AppLocalizations.of(context)
//                               .translate('remove from favourite successfuly'));
//                           bloc2.getfavouritecategory(page, 20);
//                         } else {
//                           snackbar(AppLocalizations.of(context).translate(
//                               'Something went wrong, please try again later'));
//                         }
//                       },
//                       child: Container(
//                           height: 30,
//                           width: 30,
//                           decoration: BoxDecoration(
//                               color: const Color(0xff4d5e72).withOpacity(0.2),
//                               borderRadius:
//                                   const BorderRadius.all(Radius.circular(30))),
//                           child: const Center(
//                               child: Icon(Icons.favorite,
//                                   color: Colors.white, size: 20)))),
//                 ),
//               ])
//             ]);
//           })
//       : buildnodatafavourite(
//           AppLocalizations.of(context).translate('No Items in Favorite List'),
//           AppLocalizations.of(context).translate('Add itsms to check later'));
// }
