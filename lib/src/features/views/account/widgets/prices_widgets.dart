import 'package:flutter/material.dart';
import 'package:page/src/core/config/constants.dart';
import 'package:page/src/core/localization/app_language.dart';
import 'package:page/src/core/localization/app_localizations.dart';
import 'package:page/src/features/models/video_model.dart';
import 'package:provider/provider.dart';

lang(context) =>
    Provider.of<AppLanguage>(context, listen: false).appLocal.languageCode;

Widget priceWithCurrency(
  BuildContext context, {
  required VideoModel categoryFavorite,
  required currencyController,
}) {
  final type = int.tryParse(categoryFavorite.type ?? '') ?? '';

  final isHotels = type == AppConstants.hotelsId;
  final isRestaurants = type == AppConstants.restaurantsId;
  final isActivities = type == AppConstants.activitiesId;
  final isHolidayHomes = type == AppConstants.holidayHomesId;
  final isCarRentals = type == AppConstants.carRentalsId;

  if (isHotels) {
    return hotelsTitleWithPrice(context,
        startPrice: categoryFavorite.startprice,
        endPrice: categoryFavorite.endprice,
        currencyController: currencyController);
  } else if (isRestaurants) {
    return restaurantsTitleWithPrice(context,
        startPrice: categoryFavorite.startprice,
        currencyController: currencyController);
  } else if (isActivities) {
    activitiesTitleWithPrice(context,
        startPrice: categoryFavorite.startprice,
        currencyController: currencyController);
  } else if (isHolidayHomes) {
    return holidayHomesTitleWithPrice(context,
        startPrice: categoryFavorite.startprice,
        currencyController: currencyController);
  } else if (isCarRentals) {
    return carRentalsTitleWithPrice(context,
        startPrice: categoryFavorite.price,
        currencyController: currencyController);
  }

  return const SizedBox.shrink();
}

Widget hotelsTitleWithPrice(BuildContext context,
    {required num? startPrice,
    required num? endPrice,
    required currencyController}) {
  final isEnglish = lang(context) == 'en';

  if (isEnglish) {
    return Text(
      '${AppLocalizations.of(context).translate('Price Range')}\n${startPrice!.toStringAsFixed(0)}-${endPrice!.toStringAsFixed(0)} ${currencyController.currency}',
      style: const TextStyle(color: Colors.white, fontSize: 12),
    );
  } else {
    return Text(
      '${AppLocalizations.of(context).translate('Price Range')}\n${endPrice!.toStringAsFixed(0)}-${startPrice!.toStringAsFixed(0)} ${currencyController.currency}',
      style: const TextStyle(color: Colors.white, fontSize: 12),
    );
  }
}

Widget restaurantsTitleWithPrice(BuildContext context,
    {required num? startPrice, required currencyController}) {
  return Text(
    '${AppLocalizations.of(context).translate('Average price')} ${startPrice!.toStringAsFixed(0)} ${currencyController.currency}',
    style: const TextStyle(color: Colors.white, fontSize: 12),
  );
}

Widget activitiesTitleWithPrice(BuildContext context,
    {required num? startPrice, required currencyController}) {
  return Text(
    '${AppLocalizations.of(context).translate('Starting')} ${startPrice?.toStringAsFixed(0)} ${currencyController.currency}',
    style: const TextStyle(color: Colors.white, fontSize: 12),
  );
}

Widget holidayHomesTitleWithPrice(BuildContext context,
    {required num? startPrice, required currencyController}) {
  return Text(
    '${num.tryParse(startPrice!.toString())!.toStringAsFixed(0)} ${currencyController.currency}',
    style: const TextStyle(color: Colors.white, fontSize: 12),
  );
}

Widget carRentalsTitleWithPrice(BuildContext context,
    {required num? startPrice, required currencyController}) {
  return Text(
    '${AppLocalizations.of(context).translate('Day Price')} ${startPrice?.toStringAsFixed(0)} ${currencyController.currency}',
    style: const TextStyle(color: Colors.white, fontSize: 12),
  );
}
