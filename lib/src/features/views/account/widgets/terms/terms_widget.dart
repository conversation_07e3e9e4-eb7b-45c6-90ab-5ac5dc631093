import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../../../../core/localization/app_language.dart';
import '../../../../../core/localization/app_localizations.dart';
import '../about_us.dart';
import 'legal_policy.dart';

class TermsWidget extends StatelessWidget {
  final String? data;
  final onTap;

  const TermsWidget({Key? key, required this.data, required this.onTap})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    var lang =
        Provider.of<AppLanguage>(context, listen: false).appLocal.languageCode;

    return Container(
        padding: const EdgeInsets.only(left: 20, right: 20),
        child: Container(
          padding: const EdgeInsets.all(15),
          color: Colors.white,
          child: Column(
            children: [
              // Row(
              //   children: [
              //     Column(
              //       crossAxisAlignment: CrossAxisAlignment.start,
              //       children: [
              //         Text(
              //           'Support',
              //           style: TextStyle(
              //               color: Color(
              //                 0xff191C1F,
              //               ),
              //               fontWeight: FontWeight.bold,
              //               fontSize: 12),
              //         ),
              //         SizedBox(
              //           height: 10,
              //         ),
              //         Text(
              //           'Frequently Asked Questions',
              //           style: TextStyle(
              //               color: Color(0xff8B959E), fontSize: 12),
              //         )
              //       ],
              //     ),
              //     Spacer(),
              //     Icon(Icons.keyboard_arrow_right, color: Color(0xff191C1F))
              //   ],
              // ),
              const SizedBox(
                height: 20,
              ),
              InkWell(
                  onTap: () {
                    Navigator.of(context).push(MaterialPageRoute(
                        builder: (BuildContext context) =>
                            const AboutUsPage()));
                  },
                  child: Row(
                    children: [
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            AppLocalizations.of(context).translate('About Us'),
                            style: const TextStyle(
                                color: Color(
                                  0xff191C1F,
                                ),
                                fontWeight: FontWeight.bold,
                                fontSize: 12),
                          ),
                          const SizedBox(
                            height: 10,
                          ),
                          Text(
                            AppLocalizations.of(context)
                                .translate('About Aqar Dubai'),
                            style: const TextStyle(
                                color: Color(0xff8B959E), fontSize: 12),
                          )
                        ],
                      ),
                      const Spacer(),
                      lang == 'en'
                          ? const Icon(Icons.keyboard_arrow_right,
                              color: Color(0xff191C1F))
                          : const Icon(Icons.keyboard_arrow_left,
                              color: Color(0xff191C1F))
                    ],
                  )),
              const SizedBox(
                height: 20,
              ),
              InkWell(
                  onTap: () {
                    legalPolicy(context, data: data);
                  },
                  child: Row(
                    children: [
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            AppLocalizations.of(context)
                                .translate('LegalPolicy'),
                            style: const TextStyle(
                                color: Color(
                                  0xff191C1F,
                                ),
                                fontWeight: FontWeight.bold,
                                fontSize: 12),
                          ),
                          const SizedBox(
                            height: 10,
                          ),
                          Text(
                            AppLocalizations.of(context)
                                .translate('TermsAndConditions'),
                            style: const TextStyle(
                                color: Color(0xff8B959E), fontSize: 12),
                          )
                        ],
                      ),
                      const Spacer(),
                      lang == 'en'
                          ? const Icon(Icons.keyboard_arrow_right,
                              color: Color(0xff191C1F))
                          : const Icon(Icons.keyboard_arrow_left,
                              color: Color(0xff191C1F))
                    ],
                  )),
              const SizedBox(
                height: 20,
              ),
              InkWell(
                  onTap: onTap,
                  child: Row(
                    children: [
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            AppLocalizations.of(context).translate('ContactUs'),
                            style: const TextStyle(
                                color: Color(
                                  0xff191C1F,
                                ),
                                fontWeight: FontWeight.bold,
                                fontSize: 12),
                          ),
                          const SizedBox(
                            height: 10,
                          ),
                          Text(
                            AppLocalizations.of(context)
                                .translate('ContactUsViaMessage'),
                            style: const TextStyle(
                                color: Color(0xff8B959E), fontSize: 12),
                          )
                        ],
                      ),
                      const Spacer(),
                      lang == 'en'
                          ? const Icon(Icons.keyboard_arrow_right,
                              color: Color(0xff191C1F))
                          : const Icon(Icons.keyboard_arrow_left,
                              color: Color(0xff191C1F))
                    ],
                  )),
            ],
          ),
        ));
  }
}
