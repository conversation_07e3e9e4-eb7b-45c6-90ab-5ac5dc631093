import 'package:flutter/material.dart';

import '../../../../../core/localization/app_localizations.dart';
import '../../../../../core/response/profile_response.dart';
import '../../../../../core/shared_widgets/shared_widgets.dart';
import '../../../../bloc/profile_bloc.dart';
import '../../account.dart';
import 'widgets/account_fields.dart';

// ignore: must_be_immutable
class Accountinformation extends StatefulWidget {
  Map<String, dynamic>? data;
  Accountinformation({this.data});
  @override
  _Accountinformation createState() => _Accountinformation();
}

class _Accountinformation extends State<Accountinformation> {
  @override
  void initState() {
    super.initState();
    bloc2.getProfile();
  }

  final GlobalKey<ScaffoldState> scaffoldKey = GlobalKey<ScaffoldState>();
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();

  bool isvisible = false;

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
        onWillPop: () {
          Navigator.of(context).push(
            MaterialPageRoute(builder: (context) => const Account()),
          );

          return Future.value(false);
        },
        child: Scaffold(
            appBar: AppBar(
              backgroundColor: const Color(0xFF27b4a8),
              leading: IconButton(
                icon: const Icon(Icons.arrow_back),
                onPressed: () {
                  Navigator.of(context).push(
                    MaterialPageRoute(builder: (context) => const Account()),
                  );
                },
              ),
              centerTitle: true,
              title: Text(
                  AppLocalizations.of(context).translate('Accountinformation')),
            ),
            body: StreamBuilder<ProfileResponse>(
              stream: bloc2.subject.stream,
              builder: (context, AsyncSnapshot<ProfileResponse> snapshot) {
                if (snapshot.hasData) {
                  if (snapshot.data!.error.isNotEmpty) {
                    return buildErrorWidget(snapshot.data!.error);
                  }
                  return AccountFields(
                    data: snapshot.data!,
                  );
                } else if (snapshot.hasError) {
                  return buildErrorWidget(snapshot.error!.toString());
                } else {
                  return SizedBox(
                      height: MediaQuery.of(context).size.height,
                      child: buildLoadingWidget());
                }
              },
            )));
  }
}
