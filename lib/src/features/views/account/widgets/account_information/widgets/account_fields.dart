import 'package:country_pickers/country.dart';
import 'package:country_pickers/utils/utils.dart';
import 'package:flutter/material.dart';
import 'package:lottie/lottie.dart';

import '../../../../../../core/localization/app_localizations.dart';
import '../../../../../../core/response/profile_response.dart';
import '../../../../../../core/shared_widgets/country_picker.dart';
import '../../../../../../core/shared_widgets/shared_widgets.dart';
import '../../../../../bloc/profile_bloc.dart';
import '../../../../register/widgets/verify_code/verfiy_code.dart';
import '../../change_password/change_password.dart';

class AccountFields extends StatefulWidget {
  final ProfileResponse data;

  const AccountFields({
    Key? key,
    required this.data,
  }) : super(key: key);

  @override
  State<AccountFields> createState() => _AccountFieldsState();
}

class _AccountFieldsState extends State<AccountFields> {
  bool isLoading = false;
  bool _validatephone = false;

  TextEditingController passwordController = TextEditingController();
  TextEditingController phoneController = TextEditingController();
  TextEditingController nameController = TextEditingController();
  TextEditingController emailController = TextEditingController();

  @override
  void initState() {
    super.initState();
    if (widget.data.results['phone_code'] != null) {
      _selectedCountry = CountryPickerUtils.getCountryByPhoneCode(
          widget.data.results['phone_code'].toString().replaceAll('+', ''));
    }
  }

  Country _selectedCountry = CountryPickerUtils.getCountryByIsoCode('AE');

  @override
  Widget build(BuildContext context) {
    if (nameController.text.isEmpty) {
      nameController.text = widget.data.results['fullname'];
    }
    if (emailController.text.isEmpty) {
      emailController.text = widget.data.results['email'];
    }
    if (phoneController.text.isEmpty) {
      phoneController.text = widget.data.results['phone'];
    }

    return ListView(
        padding: const EdgeInsets.only(left: 20, right: 20, top: 20),
        shrinkWrap: true,
        children: [
          Text(
            AppLocalizations.of(context).translate('FullName'),
            style: const TextStyle(
              fontSize: 16,
            ),
          ),
          const SizedBox(
            height: 10,
          ),
          Container(
              height: 60,
              decoration: BoxDecoration(
                  color: Colors.white, borderRadius: BorderRadius.circular(3)),
              child: Container(
                  decoration: BoxDecoration(
                      borderRadius: const BorderRadius.all(Radius.circular(5)),
                      border: Border.all(color: Colors.black12, width: 1.0)),
                  child: TextFormField(
                    controller: nameController,
                    decoration: InputDecoration(
                        contentPadding:
                            const EdgeInsets.only(left: 20, right: 20, top: 10),
                        errorText: _validatephone
                            ? 'Please insert phone number'
                            : null,
                        hintText:
                            AppLocalizations.of(context).translate('FullName'),
                        hintStyle:
                            const TextStyle(color: Colors.grey, fontSize: 16),
                        border: InputBorder.none),
                  ))),
          const SizedBox(
            height: 20,
          ),
          Text(
            AppLocalizations.of(context).translate('EmailAdress'),
            style: const TextStyle(
              fontSize: 16,
            ),
          ),
          const SizedBox(
            height: 10,
          ),
          Container(
              height: 60,
              decoration: BoxDecoration(
                  color: Colors.white, borderRadius: BorderRadius.circular(3)),
              child: Container(
                  decoration: BoxDecoration(
                      borderRadius: const BorderRadius.all(Radius.circular(5)),
                      border: Border.all(color: Colors.black12, width: 1.0)),
                  child: TextFormField(
                    controller: emailController,
                    decoration: InputDecoration(
                        contentPadding:
                            const EdgeInsets.only(left: 20, right: 20, top: 10),
                        errorText: _validatephone
                            ? 'Please insert phone number'
                            : null,
                        hintText: AppLocalizations.of(context)
                            .translate('EmailAdress'),
                        hintStyle:
                            const TextStyle(color: Colors.grey, fontSize: 16),
                        border: InputBorder.none),
                  ))),
          const SizedBox(
            height: 20,
          ),
          Text(AppLocalizations.of(context).translate('PhoneNumber')),
          const SizedBox(
            height: 10,
          ),
          Container(
              width: MediaQuery.of(context).size.width,
              height: 50,
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(5),
                border: Border.all(color: Colors.black12, width: 1.0),
              ),
              child: Container(
                  // width: MediaQuery.of(context).size.width * 0.4,
                  decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(3)),
                  child: TextFormField(
                    // initialValue: '123456789',
                    // readOnly: true,

                    keyboardType: TextInputType.number,
                    controller: phoneController,
                    decoration: InputDecoration(
                        prefixIcon: GestureDetector(
                          onTap: () {
                            showCountryPickerDialog(context, (Country country) {
                              setState(() {
                                _selectedCountry = country;
                              });
                            });
                          },
                          child: countryCodeField(
                            context,
                            _selectedCountry,
                          ),
                        ),
                        contentPadding:
                            const EdgeInsets.only(left: 20, right: 20, top: 10),
                        errorText: _validatephone
                            ? 'Please insert phone number'
                            : null,
                        hintText: AppLocalizations.of(context)
                            .translate('PhoneNumber'),
                        hintStyle:
                            const TextStyle(color: Colors.grey, fontSize: 16),
                        border: InputBorder.none),
                  ))),
          const SizedBox(
            height: 20,
          ),
          GestureDetector(
              onTap: () {
                Navigator.of(context).push(MaterialPageRoute(
                    builder: (BuildContext context) => ChangePassword(
                          email: widget.data.results['email'],
                        )));
              },
              child: Container(
                padding: const EdgeInsets.all(15),
                decoration: const BoxDecoration(
                  color: Color(0xffFFFFFF),
                  borderRadius: BorderRadius.all(Radius.circular(5)),
                ),
                child: Column(
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        Text(
                          AppLocalizations.of(context)
                              .translate('ChangePassword'),
                          style: const TextStyle(
                              fontWeight: FontWeight.bold, fontSize: 12),
                        ),
                        const SizedBox(
                          height: 10,
                        ),
                        const Spacer(),
                        const Icon(
                          Icons.keyboard_arrow_right,
                        )
                      ],
                    ),
                  ],
                ),
              )),
          const SizedBox(
            height: 20,
          ),
          !isLoading
              ? Container(
                  height: 55,
                  decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(3)),
                  child: GestureDetector(
                      onTap: () async {
                        setState(() {
                          isLoading = true;
                        });
                        dynamic sucessinformation = await bloc2.editProfile(
                            nameController.text,
                            emailController.text,
                            phoneController.text,
                            '+${_selectedCountry.phoneCode.toString()}');
                        print(sucessinformation);
                        setState(() {
                          isLoading = false;
                        });
                        if (sucessinformation['code'] == 1) {
                          Navigator.of(context).push(MaterialPageRoute(
                              builder: (context) => VerfiyCode(
                                    _selectedCountry.phoneCode,
                                    phoneController.text,
                                    nameController.text,
                                    isUpdateProfile: true,
                                  )));
                          // snackbar2(AppLocalizations.of(context)
                          //     .translate('Profile was updated successfuly'));
                          // Future.delayed(const Duration(seconds: 2), () {
                          //   Navigator.pushReplacement(
                          //     context,
                          //     MaterialPageRoute(builder: (context) {
                          //       return Account();
                          //     }),
                          //   );
                          // });
                        } else {
                          snackbar(AppLocalizations.of(context).translate(
                              'Something went wrong, please try again later'));
                        }
                      },
                      child: Container(
                          padding: const EdgeInsets.only(left: 20, right: 20),
                          decoration: BoxDecoration(
                              color: const Color(0xFF27b4a8),
                              borderRadius: BorderRadius.circular(10),
                              border: Border.all(
                                  color: Colors.black12, width: 1.0)),
                          child: Center(
                            child: Text(
                              AppLocalizations.of(context).translate('save'),
                              style: const TextStyle(
                                  color: Colors.white, fontSize: 20),
                            ),
                          ))))
              : Center(
                  child: Lottie.asset('assets/59218-progress-indicator.json',
                      height: 50, width: 50)),
        ]);
  }
}
