import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:provider/provider.dart';

import '../../../../core/localization/app_language.dart';
import '../../../../core/localization/app_localizations.dart';
import '../../login/login.dart';
import '../../projects/projects_screen.dart';
import '../../register/register.dart';
import '../../response_log/response_log.dart';

class AccountTopSection extends StatelessWidget {
  final islogin;
  final onTapFav;

  const AccountTopSection(
      {Key? key, required this.islogin, required this.onTapFav})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    var lang =
        Provider.of<AppLanguage>(context, listen: false).appLocal.languageCode;

    final Widget sendRequest = SizedBox(
        width: 22,
        height: 25,
        child: SvgPicture.asset(
          'assets/Group 7408.svg',
          semanticsLabel: 'Acme Logo',
          color: Colors.black,
          fit: BoxFit.cover,
        ));

    return islogin
        ? Positioned(
            top: 80,
            left: 20,
            right: 20,
            child: Container(
                height: MediaQuery.of(context).size.height / 4,
                decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(5),
                    color: Colors.white),
                child: Container(
                  padding: const EdgeInsets.all(20),
                  child: SingleChildScrollView(
                      child: Column(
                    children: [
                      InkWell(
                        onTap: () {
                          Navigator.of(context).push(MaterialPageRoute(
                              builder: (BuildContext context) =>
                                  const ResponseLog()));
                        },
                        child: Row(
                          children: [
                            sendRequest,
                            const SizedBox(
                              width: 10,
                            ),
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  AppLocalizations.of(context)
                                      .translate('ResponsesLog'),
                                  style: const TextStyle(
                                      color: Color(
                                        0xff191C1F,
                                      ),
                                      fontWeight: FontWeight.bold,
                                      fontSize: 12),
                                ),
                                // const SizedBox(
                                //   height: 7,
                                // ),
                                // Text(
                                //   AppLocalizations.of(context).translate(
                                //       'Pendingrequests,approved requests'),
                                //   style: const TextStyle(
                                //       color: Color(0xff8B959E), fontSize: 12),
                                // )
                              ],
                            ),
                            const Spacer(),
                            lang == 'en'
                                ? const Icon(Icons.keyboard_arrow_right,
                                    color: Color(0xff191C1F))
                                : const Icon(Icons.keyboard_arrow_left,
                                    color: Color(0xff191C1F))
                          ],
                        ),
                      ),
                      const SizedBox(
                        height: 20,
                      ),
                      InkWell(
                        onTap: () {
                          Navigator.of(context).push(MaterialPageRoute(
                              builder: (BuildContext context) =>
                                  const NewProjectScreen()));
                        },
                        child: Row(
                          children: [
                            sendRequest,
                            const SizedBox(
                              width: 10,
                            ),
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  AppLocalizations.of(context)
                                      .translate('New Project'),
                                  style: const TextStyle(
                                      color: Color(
                                        0xff191C1F,
                                      ),
                                      fontWeight: FontWeight.bold,
                                      fontSize: 12),
                                ),
                                const SizedBox(
                                  height: 7,
                                ),
                                Text(
                                  '${AppLocalizations.of(context).translate('Coming Soon')}...',
                                  style: const TextStyle(
                                      color: Color(0xff8B959E), fontSize: 12),
                                )
                              ],
                            ),
                            const Spacer(),
                            lang == 'en'
                                ? const Icon(Icons.keyboard_arrow_right,
                                    color: Color(0xff191C1F))
                                : const Icon(Icons.keyboard_arrow_left,
                                    color: Color(0xff191C1F))
                          ],
                        ),
                      ),
                      const SizedBox(
                        height: 20,
                      ),
                      InkWell(
                        onTap: onTapFav,
                        child: Row(
                          children: [
                            // Star Icon
                            const Icon(
                              Icons.star,
                            ),
                            const SizedBox(
                              width: 10,
                            ),
                            Expanded(
                              flex: 8,
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    AppLocalizations.of(context)
                                        .translate('Favoritelist'),
                                    style: const TextStyle(
                                        color: Color(
                                          0xff191C1F,
                                        ),
                                        fontWeight: FontWeight.bold,
                                        fontSize: 12),
                                  ),
                                  const SizedBox(
                                    height: 7,
                                  ),
                                  Text(
                                    AppLocalizations.of(context).translate(
                                        'Seeallplacesthatareaddedtofavoritelist'),
                                    style: const TextStyle(
                                        color: Color(0xff8B959E), fontSize: 12),
                                  )
                                ],
                              ),
                            ),
                            const Spacer(),
                            Flexible(
                              child: lang == 'en'
                                  ? const Icon(Icons.keyboard_arrow_right,
                                      color: Color(0xff191C1F))
                                  : const Icon(Icons.keyboard_arrow_left,
                                      color: Color(0xff191C1F)),
                            )
                          ],
                        ),
                      ),
                      const SizedBox(
                        height: 20,
                      ),
                    ],
                  )),
                )))
        : Positioned(
            top: 80,
            left: 20,
            right: 20,
            child: Container(
                decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(5),
                    color: Colors.white),
                child: Container(
                  padding: const EdgeInsets.all(20),
                  child: SingleChildScrollView(
                      child: Column(
                    children: [
                      Center(
                          child: GestureDetector(
                              onTap: () async {
                                Navigator.of(context).push(MaterialPageRoute(
                                    builder: (BuildContext context) =>
                                        const Login()));
                              },
                              child: Container(
                                height: 50,
                                width: MediaQuery.of(context).size.width,
                                decoration: BoxDecoration(
                                    color: const Color(0xFF27b4a8),
                                    borderRadius: BorderRadius.circular(5)),
                                child: Container(
                                    padding: const EdgeInsets.all(5),
                                    child: Center(
                                        child: Text(
                                      AppLocalizations.of(context)
                                          .translate('Login'),
                                      style:
                                          const TextStyle(color: Colors.white),
                                    ))),
                              ))),
                      const SizedBox(
                        height: 20,
                      ),
                      Center(
                        child: Text(
                          AppLocalizations.of(context)
                              .translate('DontHaveAnAccountYet'),
                          style: const TextStyle(fontSize: 16),
                        ),
                      ),
                      Center(
                          child: GestureDetector(
                              onTap: () {
                                Navigator.push(
                                  context,
                                  MaterialPageRoute(builder: (context) {
                                    return FirstRegister();
                                  }),
                                );
                              },
                              child: Text(
                                AppLocalizations.of(context)
                                    .translate('RegisterFromHere'),
                                style: const TextStyle(
                                    fontSize: 16, color: Color(0xff0852AB)),
                              )))
                    ],
                  )),
                )));
  }
}
