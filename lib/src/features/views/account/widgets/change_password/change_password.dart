import 'package:flutter/material.dart';

import '../../../../../core/localization/app_localizations.dart';
import '../../../../../core/response/generalResponse.dart';
import '../../../../../core/services/api.dart';
import '../../../../../core/shared_widgets/shared_widgets.dart';

class ChangePassword extends StatefulWidget {
  final String? email;
  const ChangePassword({super.key, this.email});

  @override
  _ChangePassword createState() => _ChangePassword();
}

class _ChangePassword extends State<ChangePassword> {
  @override
  void initState() {
    super.initState();
  }

  final GlobalKey<ScaffoldState> scaffoldKey = GlobalKey<ScaffoldState>();
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  bool _validatephone = false;
  bool isvisible = false;
  TextEditingController passwordController = TextEditingController();
  TextEditingController password2Controller = TextEditingController();
  TextEditingController password3Controller = TextEditingController();
  bool isLoading = false;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: AppBar(
          backgroundColor: const Color(0xFF27b4a8),
          centerTitle: true,
          title: Text(AppLocalizations.of(context).translate('ChangePassword')),
        ),
        body: Container(
            padding: const EdgeInsets.only(left: 20, right: 20),
            child: SingleChildScrollView(
                child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                  const SizedBox(
                    height: 20,
                  ),
                  Form(
                      key: _formKey,
                      child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              AppLocalizations.of(context)
                                  .translate('Current Password'),
                              style: const TextStyle(
                                fontSize: 13,
                              ),
                            ),
                            const SizedBox(
                              height: 10,
                            ),
                            Container(
                                height: 60,
                                decoration: BoxDecoration(
                                    color: Colors.white,
                                    borderRadius: BorderRadius.circular(3)),
                                child: Container(
                                    decoration: BoxDecoration(
                                        borderRadius: const BorderRadius.all(
                                            Radius.circular(5)),
                                        border: Border.all(
                                            color: Colors.black12, width: 1.0)),
                                    child: TextFormField(
                                      controller: passwordController,
                                      decoration: InputDecoration(
                                          contentPadding: const EdgeInsets.only(
                                              left: 20, right: 20, top: 10),
                                          errorText: _validatephone
                                              ? 'Please insert phone number'
                                              : null,
                                          hintText: AppLocalizations.of(context)
                                              .translate('Current Password'),
                                          hintStyle: const TextStyle(
                                              color: Colors.grey, fontSize: 16),
                                          border: InputBorder.none),
                                    ))),
                            const SizedBox(
                              height: 20,
                            ),
                            Text(
                              AppLocalizations.of(context)
                                  .translate('New Password'),
                              style: const TextStyle(
                                fontSize: 13,
                              ),
                            ),
                            const SizedBox(
                              height: 10,
                            ),
                            Container(
                                height: 60,
                                decoration: BoxDecoration(
                                    color: Colors.white,
                                    borderRadius: BorderRadius.circular(3)),
                                child: Container(
                                    decoration: BoxDecoration(
                                        borderRadius: const BorderRadius.all(
                                            Radius.circular(5)),
                                        border: Border.all(
                                            color: Colors.black12, width: 1.0)),
                                    child: TextFormField(
                                      controller: password2Controller,
                                      decoration: InputDecoration(
                                          contentPadding: const EdgeInsets.only(
                                              left: 20, right: 20, top: 10),
                                          errorText: _validatephone
                                              ? 'Please insert phone number'
                                              : null,
                                          hintText: AppLocalizations.of(context)
                                              .translate('New Password'),
                                          hintStyle: const TextStyle(
                                              color: Colors.grey, fontSize: 16),
                                          border: InputBorder.none),
                                    ))),
                            const SizedBox(
                              height: 20,
                            ),
                            Text(
                              AppLocalizations.of(context)
                                  .translate('Confirm New Password'),
                              style: const TextStyle(
                                fontSize: 13,
                              ),
                            ),
                            const SizedBox(
                              height: 10,
                            ),
                            Container(
                                height: 60,
                                decoration: BoxDecoration(
                                    color: Colors.white,
                                    borderRadius: BorderRadius.circular(3)),
                                child: Container(
                                    decoration: BoxDecoration(
                                        borderRadius: const BorderRadius.all(
                                            Radius.circular(5)),
                                        border: Border.all(
                                            color: Colors.black12, width: 1.0)),
                                    child: TextFormField(
                                      controller: password3Controller,
                                      decoration: InputDecoration(
                                          contentPadding: const EdgeInsets.only(
                                              left: 20, right: 20, top: 10),
                                          errorText: _validatephone
                                              ? 'Please insert phone number'
                                              : null,
                                          hintText: AppLocalizations.of(context)
                                              .translate(
                                                  'Confirm New Password'),
                                          hintStyle: const TextStyle(
                                              color: Colors.grey, fontSize: 16),
                                          border: InputBorder.none),
                                    ))),
                            const SizedBox(
                              height: 20,
                            ),
                            Container(
                                height: 55,
                                decoration: BoxDecoration(
                                    color: Colors.white,
                                    borderRadius: BorderRadius.circular(3)),
                                child: GestureDetector(
                                    onTap: () async {
                                      if (passwordController.text.isEmpty) {
                                        snackbar(AppLocalizations.of(context)
                                            .translate(
                                                'Please enter the  current password field'));

                                        return;
                                      }
                                      if (password2Controller.text.isEmpty) {
                                        snackbar(AppLocalizations.of(context)
                                            .translate(
                                                'Please enter the new password field'));

                                        return;
                                      }
                                      if (password3Controller.text.isEmpty) {
                                        snackbar(AppLocalizations.of(context)
                                            .translate(
                                                'Please enter the confirm new password field'));

                                        return;
                                      }
                                      if (passwordController.text.length < 6) {
                                        snackbar(AppLocalizations.of(context)
                                            .translate(
                                                'The cuurent password must be six characters or more'));

                                        return;
                                      }
                                      if (password2Controller.text.length < 6) {
                                        snackbar(AppLocalizations.of(context)
                                            .translate(
                                                'The new password must be six characters or more'));

                                        return;
                                      }
                                      if (password3Controller.text.length < 6) {
                                        snackbar(AppLocalizations.of(context)
                                            .translate(
                                                'The confirm password must be six characters or more'));

                                        return;
                                      }
                                      GeneralResponse sucessinformation =
                                          await Api.changepassword(widget.email,
                                              password2Controller.text);

                                      print(sucessinformation.code);
                                      if (sucessinformation.code == "1") {
                                        snackbar2(AppLocalizations.of(context)
                                            .translate(
                                                'passwordChangedsuccessfullly'));

                                        Navigator.pop(context);
                                      } else {
                                        snackbar(AppLocalizations.of(context)
                                            .translate(
                                                'Something went wrong, please try again later'));
                                      }
                                    },
                                    child: Container(
                                        padding: const EdgeInsets.only(
                                            left: 20, right: 20),
                                        decoration: BoxDecoration(
                                            color: const Color(0xFF27b4a8),
                                            borderRadius:
                                                BorderRadius.circular(10),
                                            border: Border.all(
                                                color: Colors.black12,
                                                width: 1.0)),
                                        child: Center(
                                          child: Text(
                                            AppLocalizations.of(context)
                                                .translate('save'),
                                            style: const TextStyle(
                                                color: Colors.white,
                                                fontSize: 20),
                                          ),
                                        )))),
                          ])),
                  const SizedBox(
                    height: 20,
                  ),
                ]))));
  }
}
