// import 'dart:developer';
//
// import 'package:flutter/gestures.dart';
// import 'package:flutter/material.dart';
// import 'package:flutter_svg/flutter_svg.dart';
// import 'package:google_cloud_translation/google_cloud_translation.dart';
// import 'package:page/src/features/views/home/<USER>';
// import 'package:page/src/features/views/property_details/widgets/discussion.dart';
// import 'package:page/src/features/views/property_details/widgets/property_details.dart';
// import 'package:page/src/features/views/property_details/widgets/show_interest.dart';
// import 'package:share_plus/share_plus.dart';
// import 'package:video_player/video_player.dart';
//
// import '../../../core/localization/app_localizations.dart';
// import '../../../core/response/generalResponse.dart';
// import '../../../core/services/api.dart';
// import '../../../core/shared_widgets/back_button.dart';
// import '../../../core/shared_widgets/reel_images.dart';
// import '../../../core/shared_widgets/shared_widgets.dart';
// import '../../../core/utils/dynamic_links.dart';
// import '../../controllers/auth_controller.dart';
// import '../../controllers/currency_controller.dart';
// import '../../controllers/language_controller.dart';
// import '../../models/video_model.dart';
// import '../../models/discussions.dart';
// import '../story/widgets/see_more.dart';
// import 'widgets/property_actions.dart';
//
// // ignore: must_be_immutable
// class PropertyDetails extends StatefulWidget {
//   final VideoDetailsModel? video;
//
//   PropertyDetails({super.key, required this.video});
//
//   @override
//   State<StatefulWidget> createState() {
//     return _PropertyDetails();
//   }
// }
//
// class _PropertyDetails extends State<PropertyDetails> {
//   VideoPlayerController? get _controller => videoPlayerController[id];
//   int imageindex = 0;
//   bool isdiscussion = false;
//   int? imageindex2;
//   TranslationModel? _translated;
//   String? text;
//   int index2 = -1;
//   bool isactions = false;
//   TextEditingController note = TextEditingController();
//   TextEditingController discussioncontroller = TextEditingController();
//   LanguageController language = LanguageController();
//   List<Categoryimages> images = [];
//   List<Categoryreels> videos = [];
//   List<Discussions> dis = [];
//   int pagenumber = 1;
//   int? code;
//   String msg = 'Loading';
//   AuthController authController = AuthController();
//   CurrencyController currencyController = CurrencyController();
//   bool isload = false;
//
//   bool ismute = false;
//
//   int get id => widget.video!.id!;
//
//   String? get video => widget.video?.video;
//
//   String? get name => widget.video?.name;
//
//   String? get label => widget.video?.category?.name;
//
//   String? get phone => widget.video?.phone;
//
//   int? get rating => widget.video?.rating;
//
//   String? get description => widget.video?.description;
//
//   String? get location => widget.video?.locationName;
//
//   String? get website => widget.video?.website;
//
//   String? get instagram => widget.video?.instagram;
//
//   String? get greviewlink => widget.video?.greviewlink;
//
//   String? get greviewName => widget.video?.greviewName;
//
//   String? get category => widget.video?.category?.name;
//
//   num? get startprice => widget.video?.startprice;
//
//   num? get endprice => widget.video?.endprice;
//
//   num? get price => widget.video?.price;
//
//   double? get lat => widget.video?.latitude;
//
//   double? get lng => widget.video?.longitude;
//
//   int? get agentId => widget.video?.agent?.id;
//
//   String? get agentName => widget.video?.agent?.name;
//
//   int? get size => widget.video?.size;
//
//   String? get roomnumber => widget.video?.rooms;
//
//   String? get type => widget.video?.type;
//
//   int? get isfavouriate => widget.video?.isFav;
//
//   int? get startsize => widget.video?.startSize;
//
//   int? get endsize => widget.video?.endSize;
//
//   set isfavouriate(isfav) {
//     isfavouriate = isfav;
//   }
//
//   // moreimagesrelated() async {
//   //   await Api.getluxuryimages(id).then((value) {
//   //     value != null
//   //         ? setState(() {
//   //             images.addAll(value.results);
//   //
//   //             isload = true;
//   //
//   //             buildLuxuryAlbomGalary(context, _controller!, value.results);
//   //           })
//   //         // ignore: unnecessary_statements
//   //         : null;
//   //   });
//   // }
//
//   getdiscussions() async {
//     progrsss(context);
//     pr!.show();
//     dis.clear();
//     await Api.getluxurydiscussions(pagenumber, 20, id).then((value) {
//       value != null
//           ? setState(() {
//               dis.addAll(value.dscussions);
//               code = value.code;
//               msg = value.msg;
//
//               // isload = true;
//             })
//           // ignore: unnecessary_statements
//           : null;
//       propertyDiscussion(context,
//           text: text,
//           code: code,
//           msg: msg,
//           translated: _translated,
//           dis: dis,
//           isdiscussion: isdiscussion,
//           discussioncontroller: discussioncontroller,
//           authController: authController, onJoinDiscussion: () async {
//         setState(() {
//           isdiscussion = true;
//         });
//         GeneralResponse sucessinformation =
//             await Api.sendluxurydiscussion(id, discussioncontroller.text);
//         print(sucessinformation.code);
//         if (sucessinformation.code == "1") {
//           snackbar2(AppLocalizations.of(context)
//               .translate('add discussion successfuly'));
//           setState(() {
//             dis = [];
//             getdiscussions();
//             discussioncontroller.text = "";
//             // isfav = true;
//           });
//         } else {
//           snackbar(AppLocalizations.of(context)
//               .translate('Something went wrong, please try again later'));
//         }
//         discussioncontroller.text = "";
//         setState(() {
//           isdiscussion = false;
//         });
//       }, index2: index2);
//     });
//   }
//
//   getmorerelatedreels() async {
//     await Api.getluxuryreels(id).then((value) {
//       value != null
//           ? setState(() {
//               videos.addAll(value.results);
//
//               isload = true;
//             })
//           // ignore: unnecessary_statements
//           : null;
//     });
//     //  return buildmoreimage();
//   }
//
//   @override
//   void initState() {
//     initPlayer();
//     getmorerelatedreels();
//     authController.isloggedin();
//     currencyController.getcuurentcurrency(context);
//     language.getcuurentlanguage();
//
//     super.initState();
//   }
//
//   void initPlayer() async {
//     if (!(videoPlayerController[id]?.value.isInitialized ?? true)) {
//       await videoPlayerController[id]?.initialize();
//     }
//
//     await videoPlayerController[id]?.play();
//
//     setState(() {
//       isload = true;
//     });
//   }
//
//   // void getdetails() async {
//   //   await Api.getluxurydetails(id).then((value) {
//   //     value != null
//   //         ? setState(() {
//   //             video = value.results['video'].toString();
//   //             name = value.results['name'];
//   //             description = value.results['description'];
//   //             location = value.results['location'] != null
//   //                 ? value.results['location']['name']
//   //                 : '';
//   //             startprice = value.results['price'];
//   //
//   //             lat = value.results['latitude'].toString().toDouble();
//   //             lng = value.results['longitude'].toString().toDouble();
//   //             isfavouriate = value.results['is_favorite'] == true ? 1 : 0;
//   //             type = value.results['type'] != null
//   //                 ? language.languagecode == 'en'
//   //                     ? value.results['type']['name']['en']
//   //                     : value.results['type']['name']['ar']
//   //                 : '';
//   //             ;
//   //             roomnumber = value.results['rooms'];
//   //             startsize = value.results['start_size'];
//   //             endsize = value.results['end_size'];
//   //             website = value.results['website'];
//   //             instagram = value.results['instagram'];
//   //             phone = value.results['phone'];
//   //             log('Dataaaasdsfsf ${value.results}');
//   //             print(video!);
//   //             try {
//   //               _controller =
//   //                   VideoPlayerController.network(Uri.parse(video!).toString())
//   //                     ..initialize().then((_) {
//   //                       // Ensure the first frame is shown after the video is initialized, even before the play button has been pressed.
//   //                       setState(() {});
//   //                     });
//   //               isload = true;
//   //             } catch (e) {
//   //               Navigator.pop(context);
//   //               snackbar(
//   //                   AppLocalizations.of(context).translate('error occured'));
//   //               print(e);
//   //             }
//   //           })
//   //         // ignore: unnecessary_statements
//   //         : null;
//   //     _controller!.play();
//   //   });
//   // }
//
//   @override
//   void dispose() {
//     _controller?.pause();
//
//     super.dispose();
//   }
//
//   @override
//   Widget build(BuildContext context) {
//     SizedBox(
//         width: 30,
//         height: 25,
//         child: SvgPicture.asset(
//           'assets/media_icon.svg',
//           semanticsLabel: 'Acme Logo',
//           width: 13,
//           height: 13,
//           fit: BoxFit.fill,
//           color: Colors.white,
//         ));
//     return WillPopScope(
//         onWillPop: () {
//           if (_controller!.value.isPlaying) {
//             _controller!.pause();
//             // _controller = null;
//             // _controller?.dispose();
//           }
//           setState(() {
//             isactions = false;
//           });
//           return Future.value(true);
//         },
//         child: SafeArea(
//             child: Scaffold(
//           body: Stack(
//             alignment: Alignment.bottomCenter,
//             children: <Widget>[
//               _controller != null
//                   ? SizedBox(
//                       height: MediaQuery.of(context).size.height,
//                       width: MediaQuery.of(context).size.width,
//                       child: _controller!.value.isInitialized
//                           ? FittedBox(
//                               fit: BoxFit.fill,
//                               child: SizedBox(
//                                   height: _controller!.value.size.height,
//                                   width: _controller!.value.size.width,
//                                   child: VideoPlayer(_controller!)))
//                           : Container())
//                   : Container(),
//               Positioned(
//                   left: 10,
//                   right: 10,
//                   top: 20,
//                   child: Row(
//                     mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                     children: [
//                       const BackButtonWidget(),
//                       InkWell(
//                           onTap: () {
//                             setState(() {
//                               ismute = !ismute;
//                               ismute
//                                   ? _controller!.setVolume(0.0)
//                                   : _controller!.setVolume(30.0);
//                             });
//                             // _controller.setVolume(0.0);
//                           },
//                           child: !ismute
//                               ? const Icon(
//                                   Icons.volume_down,
//                                   color: Colors.white,
//                                 )
//                               : const Icon(
//                                   Icons.volume_off,
//                                   color: Colors.white,
//                                 ))
//                     ],
//                   )),
//               if (language.languagecode == 'ar')
//                 Positioned(bottom: 65, right: 30, child: _nameDetails())
//               else
//                 Positioned(bottom: 65, left: 25, child: _nameDetails()),
//               _controller != null
//                   ? Padding(
//                       padding: const EdgeInsetsDirectional.only(
//                         bottom: 25,
//                         top: 9,
//                         start: 10,
//                         end: 10,
//                       ),
//                       child: Row(
//                         children: [
//                           Expanded(
//                             child: IconButton(
//                                 onPressed: () {
//                                   _controller!.value.isPlaying
//                                       ? _controller!.pause()
//                                       : _controller!.play();
//                                   setState(() {});
//                                 },
//                                 icon: Icon(
//                                   _controller!.value.isPlaying
//                                       ? Icons.pause
//                                       : Icons.play_arrow,
//                                   color: Colors.white,
//                                 )),
//                           ),
//                           Expanded(
//                             flex: 8,
//                             child: VideoProgressIndicator(
//                               _controller!,
//                               allowScrubbing: true,
//                               colors: VideoProgressColors(
//                                   playedColor: _controller!.value.isInitialized
//                                       ? Colors.white
//                                       : const Color(0xFF27b4a8),
//                                   backgroundColor:
//                                       Colors.white.withOpacity(0.44)),
//                             ),
//                           ),
//                           const SizedBox(width: 5),
//                           Expanded(
//                             child: FittedBox(
//                               fit: BoxFit.scaleDown,
//                               child: Text(
//                                 _controller!.value.duration
//                                     .toString()
//                                     .substring(
//                                         _controller!.value.duration
//                                                 .toString()
//                                                 .indexOf(":") +
//                                             1,
//                                         _controller!.value.duration
//                                             .toString()
//                                             .indexOf(".")),
//                                 style: const TextStyle(color: Colors.white),
//                                 maxLines: 1,
//                               ),
//                             ),
//                           ),
//                         ],
//                       ),
//                     )
//                   : Container(),
//               if (language.languagecode == 'ar')
//                 Positioned(bottom: 75, left: 20, child: _columnDetailsWidget())
//               else
//                 Positioned(
//                     bottom: 75, right: 20, child: _columnDetailsWidget()),
//               isactions
//                   ? PropertyActions(
//                       lat: lat,
//                       lng: lng,
//                       onShare: () async {
//                         try {
//                           final String linkPathData =
//                               '?id=${id}&type=${'properties'}';
//                           final dynamicLink =
//                               await DynamicLinkHandler.createDynamicLink(
//                             linkPathData,
//                           );
//
//                           log('sdfdfdsfsf ${dynamicLink.toString()}');
//
//                           Share.share(dynamicLink.toString()).then((value) {
//                             setState(() {
//                               isactions = false;
//                             });
//                           });
//                         } catch (e) {
//                           log('Eerrrror ${e.toString()}');
//                         }
//                       },
//                       onJoinDiscussion: () {
//                         getdiscussions();
//                       },
//                       onCancel: () {
//                         setState(() {
//                           isactions = !isactions;
//                         });
//                       },
//                       name: name!)
//                   : Container(),
//             ],
//           ),
//         )));
//   }
//
//   Widget _nameDetails() {
//     return Column(
//       crossAxisAlignment: CrossAxisAlignment.start,
//       children: [
//         name != null
//             ? Text(
//                 name!,
//                 style: const TextStyle(color: Colors.white),
//               )
//             : Container(),
//         const SizedBox(
//           height: 5,
//         ),
//         SizedBox(
//           width: MediaQuery.of(context).size.width * 0.75,
//           child: Text.rich(
//             TextSpan(
//               style: const TextStyle(
//                 color: Colors.white,
//               ),
//               children: [
//                 TextSpan(
//                   text: description != null
//                       ? description!.length > maxChars
//                           ? description?.substring(0, maxChars)
//                           : description
//                       : '',
//                   style: const TextStyle(
//                     fontWeight: FontWeight.normal,
//                   ),
//                 ),
//                 TextSpan(
//                   text: AppLocalizations.of(context).translate('seemore'),
//                   style: const TextStyle(
//                     fontWeight: FontWeight.bold,
//                   ),
//                   recognizer: TapGestureRecognizer()
//                     ..onTap = () {
//                       _controller!.pause();
//                       luxuryPropertyDetails(context,
//                           currencyController: currencyController,
//                           location: location,
//                           startsize: startsize,
//                           endsize: endsize,
//                           description: description,
//                           roomnumber: roomnumber,
//                           startprice: startprice,
//                           type: type,
//                           lat: lat,
//                           phone: phone,
//                           website: website,
//                           instagram: instagram,
//                           lng: lng);
//                     },
//                 ),
//               ],
//             ),
//             overflow: TextOverflow.ellipsis,
//             maxLines: 1,
//           ),
//         )
//       ],
//     );
//   }
//
//   Widget _columnDetailsWidget() {
//     final Widget svg6 = SizedBox(
//         width: 30,
//         height: 25,
//         child: SvgPicture.asset(
//           'assets/media_icon.svg',
//           semanticsLabel: 'Acme Logo',
//           width: 13,
//           height: 13,
//           fit: BoxFit.fill,
//           color: Colors.white,
//         ));
//     final Widget svg5 = SizedBox(
//         width: 22,
//         height: 25,
//         child: SvgPicture.asset(
//           'assets/Group 7408.svg',
//           semanticsLabel: 'Acme Logo',
//           fit: BoxFit.cover,
//         ));
//     return Column(
//       children: [
//         InkWell(
//             onTap: () {
//               _controller!.pause();
//               showInterest(context, id: id, note: note);
//             },
//             child: svg5),
//         const SizedBox(
//           height: 20,
//         ),
//         _favoriteButton(),
//         const SizedBox(
//           height: 20,
//         ),
//         InkWell(
//           onTap: () {
//             moreimagesrelated(context, id, _controller!);
//           },
//           child: svg6,
//         ),
//         const SizedBox(
//           height: 20,
//         ),
//         GestureDetector(
//             onTap: () {
//               _controller!.pause();
//               setState(() {
//                 isactions = !isactions;
//               });
//
//               //
//             },
//             child: Container(
//                 // height:30,
//                 // width:30,
//                 color: Colors.transparent,
//                 child: const Icon(Icons.more_horiz,
//                     color: Colors.white, size: 30)))
//       ],
//     );
//   }
//
//   Widget _favoriteButton() {
//     return authController.islogin == true
//         ? isfavouriate == 1
//             ? InkWell(
//                 onTap: () async {
//                   GeneralResponse sucessinformation =
//                       await Api.removeluxuryfromfavourite(id);
//                   print(sucessinformation.code);
//                   if (sucessinformation.code == "1") {
//                     snackbar2(AppLocalizations.of(context)
//                         .translate('remove from favourite successfuly'));
//                     setState(() {
//                       isfavouriate = 0;
//                       // isfav = true;
//                     });
//                   } else {
//                     snackbar(AppLocalizations.of(context).translate(
//                         'Something went wrong, please try again later'));
//                   }
//                 },
//                 child: const Icon(
//                   Icons.favorite,
//                   color: Colors.white,
//                 ))
//             : InkWell(
//                 onTap: () async {
//                   GeneralResponse sucessinformation =
//                       await Api.addluxuryfavourite(id);
//                   print(sucessinformation.code);
//                   if (sucessinformation.code == "1") {
//                     snackbar2(AppLocalizations.of(context)
//                         .translate('add to favourite successfuly'));
//                     setState(() {
//                       isfavouriate = 1;
//                     });
//                   } else {
//                     snackbar(AppLocalizations.of(context).translate(
//                         'Something went wrong, please try again later'));
//                   }
//                   // snackbar('This service has not been activated');
//                 },
//                 child: const Icon(
//                   Icons.favorite_outline,
//                   color: Colors.white,
//                 ))
//         : InkWell(
//             onTap: () async {
//               snackbar(
//                   AppLocalizations.of(context).translate('Please login first'));
//
//               // snackbar('This service has not been activated');
//             },
//             child: const Icon(
//               Icons.favorite_outline,
//               color: Colors.white,
//             ));
//   }
// }
