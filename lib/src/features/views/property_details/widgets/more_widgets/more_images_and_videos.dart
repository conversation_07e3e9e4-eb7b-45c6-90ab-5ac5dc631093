import 'package:flutter/material.dart';

import '../../../../../core/localization/app_localizations.dart';
import '../../../../models/video_model.dart';
import 'more_reels_related.dart';

void moreimagesandvideo(BuildContext context,
    {required List<Categoryimages> images,
    required List<Categoryreels> videos,
    required VoidCallback onTap}) {
  showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Padding(
          padding:
              EdgeInsets.only(bottom: MediaQuery.of(context).viewInsets.bottom),
          child: Container(
            height: MediaQuery.of(context).size.height * 0.30,
            decoration: BoxDecoration(
                color: const Color(0xffF5F6F7),
                borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(25.0),
                    topRight: Radius.circular(25.0)),
                border: Border.all(color: Colors.black, width: 1.0)),
            child: SingleChildScrollView(
                child: Column(
              children: [
                const SizedBox(
                  height: 10,
                ),
                Container(height: 5, width: 30, color: const Color(0xffD2D4D6)),
                const SizedBox(
                  height: 20,
                ),
                Center(
                    child: Text(
                  AppLocalizations.of(context).translate('MoreImagesandReels'),
                  style: const TextStyle(fontWeight: FontWeight.bold),
                )),
                Container(
                  padding: const EdgeInsets.all(15),
                  child: Container(
                    decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(10)),
                    child: Container(
                      padding: const EdgeInsets.all(15),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          InkWell(
                              onTap: onTap,
                              child: Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  Text(AppLocalizations.of(context)
                                      .translate('Morerelatedimages')),
                                  const Icon(Icons.keyboard_arrow_right)
                                ],
                              )),
                          const SizedBox(
                            height: 20,
                          ),
                          InkWell(
                              onTap: () {
                                morereelsrelated(context,
                                    images: images, videos: videos);
                              },
                              child: Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  Text(AppLocalizations.of(context)
                                      .translate('MoreRelatedReels')),
                                  const Icon(Icons.keyboard_arrow_right)
                                ],
                              )),
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            )),
          )));
}
