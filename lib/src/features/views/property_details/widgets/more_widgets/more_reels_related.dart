import 'package:flutter/material.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:page/src/core/utils/main_cached_image.dart';

import '../../../../../core/localization/app_localizations.dart';
import '../../../../../core/shared_widgets/shared_widgets.dart';
import '../../../../models/video_model.dart';
import '../../../area_details/area_details.dart';

void morereelsrelated(
  BuildContext context, {
  required List<Categoryimages> images,
  required List<Categoryreels> videos,
}) {
  Widget imagesListWidget() {
    return StaggeredGridView.countBuilder(
        staggeredTileBuilder: (index) {
          return const StaggeredTile.fit(1);
        },
        shrinkWrap: true,
        padding: const EdgeInsets.symmetric(horizontal: 10.0, vertical: 20.0),
        crossAxisCount: 2,
        //  physics: NeverScrollableScrollPhysics(),
        crossAxisSpacing: 5,
        mainAxisSpacing: 8,
        itemCount: videos.length,
        primary: false,
        itemBuilder: (context, index) {
          return Column(children: [
            const SizedBox(
              width: 5,
            ),
            Stack(children: [
              Container(
                  child: Container(
                      child: ClipRRect(
                borderRadius: BorderRadius.circular(5),
                child: MainCachedImage(
                  images[index].name!,
                  height: 240,
                  width: MediaQuery.of(context).size.width * 0.4,
                  fit: BoxFit.fill,
                ),
              ))),
              Positioned(
                  bottom: 12,
                  left: 10,
                  child: Column(
                    children: [
                      Text(
                        videos[0].title!,
                        style:
                            const TextStyle(color: Colors.white, fontSize: 12),
                      ),
                    ],
                  )),
              GestureDetector(
                  onTap: () {
                    Navigator.of(context).push(MaterialPageRoute(
                        builder: (BuildContext context) => AreaDetails(
                            videos[index].video!, videos[index].title!)));
                  },
                  child: Container(
                    height: 240,
                    width: MediaQuery.of(context).size.width * 0.4,
                    decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(5),
                        gradient: LinearGradient(
                          // end: Alignment.bottomCenter,
                          // begin: Alignment.center,
                          begin: const FractionalOffset(0, 0),
                          end: const FractionalOffset(0, 1),
                          colors: <Color>[
                            Colors.grey.withOpacity(0.2),
                            Colors.grey.withOpacity(0.7)
                          ],
                        )),
                  )),
            ])
          ]);
        });
  }

  Widget videosListWidget() {
    return StaggeredGridView.countBuilder(
        staggeredTileBuilder: (index) {
          return const StaggeredTile.fit(1);
        },
        shrinkWrap: true,
        padding: const EdgeInsets.symmetric(horizontal: 10.0, vertical: 20.0),
        crossAxisCount: 2,
        //  physics: NeverScrollableScrollPhysics(),
        crossAxisSpacing: 5,
        mainAxisSpacing: 8,
        itemCount: videos.length,
        primary: false,
        itemBuilder: (context, index) {
          var imageslength = images.length;
          num index2 = 0;
          if (index > 0) {
            index2 = imageslength - index;
          }
          return Column(children: [
            const SizedBox(
              width: 5,
            ),
            Stack(children: [
              Container(
                  child: Container(
                      child: ClipRRect(
                borderRadius: BorderRadius.circular(5),
                child: index2 > 0
                    ? MainCachedImage(
                        images[index].name!,
                        height: 240,
                        width: MediaQuery.of(context).size.width * 0.4,
                        fit: BoxFit.fill,
                      )
                    : MainCachedImage(
                        images.last.name!,
                        height: 240,
                        width: MediaQuery.of(context).size.width * 0.4,
                        fit: BoxFit.fill,
                      ),
              ))),
              Positioned(
                  bottom: 12,
                  left: 10,
                  child: Column(
                    children: [
                      Text(
                        videos[0].title!,
                        style:
                            const TextStyle(color: Colors.white, fontSize: 12),
                      ),
                    ],
                  )),
              GestureDetector(
                  onTap: () {
                    Navigator.of(context).push(MaterialPageRoute(
                        builder: (BuildContext context) => AreaDetails(
                            videos[index].video!, videos[index].title!)));
                  },
                  child: Container(
                    height: 240,
                    width: MediaQuery.of(context).size.width * 0.4,
                    decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(5),
                        gradient: LinearGradient(
                          // end: Alignment.bottomCenter,
                          // begin: Alignment.center,
                          begin: const FractionalOffset(0, 0),
                          end: const FractionalOffset(0, 1),
                          colors: <Color>[
                            Colors.grey.withOpacity(0.2),
                            Colors.grey.withOpacity(0.7)
                          ],
                        )),
                  )),
            ])
          ]);
        });
  }

  showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) =>
          StatefulBuilder(builder: (context, StateSetter stateSetter) {
            return Padding(
                padding: EdgeInsets.only(
                    bottom: MediaQuery.of(context).viewInsets.bottom),
                child: Container(
                  height: MediaQuery.of(context).size.height * 0.80,
                  decoration: BoxDecoration(
                      color: const Color(0xffF5F6F7),
                      borderRadius: const BorderRadius.only(
                          topLeft: Radius.circular(25.0),
                          topRight: Radius.circular(25.0)),
                      border: Border.all(color: Colors.black, width: 1.0)),
                  child: SingleChildScrollView(
                      child: Column(
                    children: [
                      const SizedBox(
                        height: 10,
                      ),
                      Container(
                          height: 5, width: 50, color: const Color(0xffD2D4D6)),
                      const SizedBox(
                        height: 20,
                      ),
                      Center(
                          child: Text(
                        AppLocalizations.of(context)
                            .translate('MoreRelatedReels'),
                        style: const TextStyle(fontWeight: FontWeight.bold),
                      )),
                      Container(
                        padding: const EdgeInsets.all(15),
                        child: Container(
                          decoration: BoxDecoration(
                              color: Colors.white,
                              borderRadius: BorderRadius.circular(10)),
                          child: Container(
                            padding: const EdgeInsets.all(15),
                            child: videos.length > 0
                                ? Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      const SizedBox(
                                        height: 20,
                                      ),
                                      images.length >= videos.length
                                          ? imagesListWidget()
                                          : videosListWidget(),
                                      const SizedBox(
                                        height: 20,
                                      ),
                                    ],
                                  )
                                : nodatafound(AppLocalizations.of(context)
                                    .translate('No Reels to show')),
                          ),
                        ),
                      ),
                    ],
                  )),
                ));
          }));
}
