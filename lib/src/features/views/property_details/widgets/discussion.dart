import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:google_cloud_translation/google_cloud_translation.dart';
import 'package:lottie/lottie.dart';
import 'package:provider/provider.dart';

import '../../../../core/localization/app_language.dart';
import '../../../../core/localization/app_localizations.dart';
import '../../../../core/shared_widgets/shared_widgets.dart';

void propertyDiscussion(
  BuildContext context, {
  required text,
  required code,
  required msg,
  required translated,
  required dis,
  required isdiscussion,
  required discussioncontroller,
  required authController,
  required onJoinDiscussion,
  required index2,
}) {
  pr!.hide();
  final Widget svg3 = SizedBox(
      width: 20,
      height: 20,
      child: SvgPicture.asset(
        'assets/Group 6310.svg',
        semanticsLabel: 'Acme Logo',
        fit: BoxFit.cover,
      ));
  var lang =
      Provider.of<AppLanguage>(context, listen: false).appLocal.languageCode;

  showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder:
          (context) =>
              StatefulBuilder(builder: (context, StateSetter stateSetter) {
                return Padding(
                    padding: EdgeInsets.only(
                        bottom: MediaQuery.of(context).viewInsets.bottom),
                    child: Container(
                      height: MediaQuery.of(context).size.height * 0.70,
                      decoration: BoxDecoration(
                          color: const Color(0xffF5F6F7),
                          borderRadius: const BorderRadius.only(
                              topLeft: Radius.circular(25.0),
                              topRight: Radius.circular(25.0)),
                          border: Border.all(color: Colors.black, width: 1.0)),
                      child: SingleChildScrollView(
                          child: Column(
                        children: [
                          const SizedBox(
                            height: 10,
                          ),
                          Container(
                              height: 5,
                              width: 50,
                              color: const Color(0xffD2D4D6)),
                          const SizedBox(
                            height: 20,
                          ),
                          Center(
                              child: Text(
                            AppLocalizations.of(context)
                                .translate('Discussion'),
                            style: const TextStyle(fontWeight: FontWeight.bold),
                          )),
                          Container(
                            padding: const EdgeInsets.all(15),
                            child: Container(
                              decoration: BoxDecoration(
                                  color: Colors.white,
                                  borderRadius: BorderRadius.circular(10)),
                              child: Container(
                                padding: const EdgeInsets.all(15),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    code == 0 && msg == 'Loading'
                                        ? buildLoadingWidget()
                                        : code == 1
                                            ? dis.length > 0
                                                ? Container(
                                                    height:
                                                        MediaQuery.of(context)
                                                                .size
                                                                .height *
                                                            0.5,
                                                    child: ListView.builder(
                                                        shrinkWrap: true,
                                                        physics:
                                                            const ScrollPhysics(),
                                                        scrollDirection:
                                                            Axis.vertical,
                                                        itemBuilder:
                                                            (BuildContext ctxt,
                                                                int index) {
                                                          translated = TranslationModel(
                                                              translatedText:
                                                                  dis[index]
                                                                      .comment,
                                                              detectedSourceLanguage:
                                                                  '');
                                                          return Column(
                                                              crossAxisAlignment:
                                                                  CrossAxisAlignment
                                                                      .start,
                                                              children: [
                                                                Row(
                                                                  children: [
                                                                    dis[index].fullname !=
                                                                            null
                                                                        ? Container(
                                                                            height:
                                                                                50,
                                                                            width:
                                                                                50,
                                                                            decoration:
                                                                                BoxDecoration(
                                                                              borderRadius: BorderRadius.circular(50),
                                                                              color: const Color(0xffE4E4E4),
                                                                            ),
                                                                            child: Center(
                                                                                child: dis[index].fullname.length > 1
                                                                                    ? Text(
                                                                                        dis[index].fullname.substring(0, 2),
                                                                                        style: const TextStyle(color: Color(0xffB7B7B7)),
                                                                                      )
                                                                                    : Text(
                                                                                        dis[index].fullname,
                                                                                        style: const TextStyle(color: Color(0xffB7B7B7)),
                                                                                      )))
                                                                        : Container(),
                                                                    const SizedBox(
                                                                      width: 20,
                                                                    ),
                                                                    Column(
                                                                      crossAxisAlignment:
                                                                          CrossAxisAlignment
                                                                              .start,
                                                                      children: [
                                                                        dis[index].fullname !=
                                                                                null
                                                                            ? Text(dis[index].fullname,
                                                                                style: const TextStyle(fontWeight: FontWeight.bold))
                                                                            : Container(),
                                                                      ],
                                                                    ),
                                                                    const Spacer(),
                                                                    InkWell(
                                                                        onTap:
                                                                            () async {
                                                                          translated = await translated.translate(
                                                                              text: dis[index].comment,
                                                                              to: lang);
                                                                          stateSetter(
                                                                              () {
                                                                            text =
                                                                                translated.translatedText;
                                                                            index2 =
                                                                                index;
                                                                          });
                                                                          if (text ==
                                                                              dis[index].comment) {
                                                                            snackbar2(lang == 'ar'
                                                                                ? '${AppLocalizations.of(context).translate('your language is')} العربية'
                                                                                : '${AppLocalizations.of(context).translate('your language is')} English');
                                                                          }
                                                                        },
                                                                        child:
                                                                            svg3)
                                                                  ],
                                                                ),
                                                                const SizedBox(
                                                                  height: 10,
                                                                ),
                                                                index == index2
                                                                    ? text !=
                                                                            null
                                                                        ? text !=
                                                                                translated.translatedText
                                                                            ? Text(text)
                                                                            : Text(text)
                                                                        : Text(
                                                                            translated.translatedText,
                                                                            softWrap:
                                                                                true,
                                                                          )
                                                                    : Text(
                                                                        translated
                                                                            .translatedText,
                                                                        softWrap:
                                                                            true,
                                                                      ),
                                                                Container(
                                                                    padding: const EdgeInsets
                                                                            .only(
                                                                        left:
                                                                            10,
                                                                        right:
                                                                            10),
                                                                    child:
                                                                        Divider(
                                                                      color: Colors
                                                                              .grey[
                                                                          100],
                                                                    )),
                                                              ]);
                                                        },
                                                        itemCount: dis.length))
                                                : nodatafound(AppLocalizations
                                                        .of(context)
                                                    .translate(
                                                        'No discussions to show'))
                                            : buildErrorWidget(msg),
                                    const SizedBox(
                                      height: 20,
                                    ),
                                    Container(
                                        height: 50,
                                        decoration: BoxDecoration(
                                            color: Colors.grey[100],
                                            borderRadius:
                                                BorderRadius.circular(3)),
                                        child: Container(
                                            decoration: BoxDecoration(
                                                borderRadius:
                                                    const BorderRadius.all(
                                                        Radius.circular(5)),
                                                border: Border.all(
                                                    color: Colors.black12,
                                                    width: 1.0)),
                                            child: TextFormField(
                                              controller: discussioncontroller,
                                              decoration: InputDecoration(
                                                  suffixIcon: Padding(
                                                      padding:
                                                          const EdgeInsets.only(
                                                              top: 10,
                                                              bottom: 10,
                                                              right: 10,
                                                              left: 10),
                                                      child: authController
                                                                  .isLogged ==
                                                              true
                                                          ? InkWell(
                                                              onTap:
                                                                  onJoinDiscussion,
                                                              child: Container(
                                                                  height: 20,
                                                                  width: 30,
                                                                  decoration: BoxDecoration(
                                                                      color: const Color(
                                                                          0xffF1F1F1),
                                                                      borderRadius:
                                                                          BorderRadius.circular(
                                                                              20)),
                                                                  child: !isdiscussion
                                                                      ? const Center(
                                                                          child: Icon(Icons.send,
                                                                              size:
                                                                                  18))
                                                                      : Center(
                                                                          child:
                                                                              Lottie.asset('assets/59218-progress-indicator.json', height: 20, width: 20))))
                                                          : InkWell(
                                                              onTap: () async {
                                                                snackbar(AppLocalizations.of(
                                                                        context)
                                                                    .translate(
                                                                        'Please login first'));
                                                              },
                                                              child: Container(height: 20, width: 30, decoration: BoxDecoration(color: const Color(0xffF1F1F1), borderRadius: BorderRadius.circular(20)), child: const Center(child: Icon(Icons.send, size: 18))))),
                                                  contentPadding: const EdgeInsets.only(left: 20, right: 20, top: 10),
                                                  hintText: AppLocalizations.of(context).translate('Joindiscussion'),
                                                  hintStyle: const TextStyle(color: Colors.grey, fontSize: 12),
                                                  border: InputBorder.none),
                                            )))
                                  ],
                                ),
                              ),
                            ),
                          ),
                        ],
                      )),
                    ));
              }));
}
