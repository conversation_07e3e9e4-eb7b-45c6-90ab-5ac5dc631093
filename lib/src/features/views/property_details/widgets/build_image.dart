import 'package:flutter/material.dart';
import 'package:page/src/core/utils/main_cached_image.dart';

import '../../../../core/localization/app_localizations.dart';
import '../../../../core/shared_widgets/shared_widgets.dart';
import '../../../models/video_model.dart';

buildmoreimage(
  BuildContext context, {
  required List<Categoryimages> images,
  required int imageindex,
  required int? imageindex2,
  required bool isload,
}) {
  showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => isload
          ? StatefulBuilder(builder: (context, StateSetter stateSetter) {
              stateSetter(() {
                imageindex2 = imageindex + 1;
              });
              return Padding(
                  padding: EdgeInsets.only(
                      bottom: MediaQuery.of(context).viewInsets.bottom),
                  child: Container(
                    height: MediaQuery.of(context).size.height * 0.80,
                    decoration: BoxDecoration(
                        color: const Color(0xffF5F6F7),
                        borderRadius: const BorderRadius.only(
                            topLeft: Radius.circular(25.0),
                            topRight: Radius.circular(25.0)),
                        border: Border.all(color: Colors.black, width: 1.0)),
                    child: SingleChildScrollView(
                        child: Column(
                      children: [
                        const SizedBox(
                          height: 10,
                        ),
                        Container(
                            height: 5,
                            width: 50,
                            color: const Color(0xffD2D4D6)),
                        const SizedBox(
                          height: 20,
                        ),
                        Center(
                            child: Text(
                          AppLocalizations.of(context)
                              .translate('Morerelatedimages'),
                          style: const TextStyle(fontWeight: FontWeight.bold),
                        )),
                        Container(
                          padding: const EdgeInsets.all(15),
                          child: Container(
                            decoration: BoxDecoration(
                                color: Colors.white,
                                borderRadius: BorderRadius.circular(10)),
                            child: Container(
                              padding: const EdgeInsets.all(15),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  const SizedBox(
                                    height: 20,
                                  ),
                                  Container(
                                      padding: const EdgeInsets.only(
                                        left: 50,
                                        right: 50,
                                      ),
                                      child: Container(
                                          decoration: BoxDecoration(
                                              borderRadius:
                                                  BorderRadius.circular(5)),
                                          height: MediaQuery.of(context)
                                                  .size
                                                  .height *
                                              0.3,
                                          child: ClipRRect(
                                              borderRadius:
                                                  BorderRadius.circular(5),
                                              child: MainCachedImage(
                                                images[imageindex].name!,
                                                fit: BoxFit.fill,
                                                width: MediaQuery.of(context)
                                                    .size
                                                    .width,
                                                height: MediaQuery.of(context)
                                                        .size
                                                        .height *
                                                    0.3,
                                              )))),
                                  const SizedBox(
                                    height: 20,
                                  ),
                                  Center(
                                      child: Text("$imageindex2" +
                                          "/" +
                                          images.length.toString())),
                                  const SizedBox(
                                    height: 20,
                                  ),
                                  Container(
                                      height:
                                          MediaQuery.of(context).size.height *
                                              0.2,
                                      width: MediaQuery.of(context).size.width,
                                      child: ListView.builder(
                                        shrinkWrap: true,
                                        physics: const ClampingScrollPhysics(),
                                        scrollDirection: Axis.horizontal,
                                        itemBuilder:
                                            (BuildContext ctxt, int index) {
                                          return GestureDetector(
                                              onTap: () {
                                                stateSetter(() {
                                                  imageindex = index;
                                                });
                                              },
                                              child: Stack(
                                                children: [
                                                  Container(
                                                    padding:
                                                        const EdgeInsets.only(
                                                            right: 10),
                                                    child: Row(
                                                      children: [
                                                        Column(children: [
                                                          const SizedBox(
                                                            width: 5,
                                                          ),
                                                          Stack(children: [
                                                            Container(
                                                                child:
                                                                    Container(
                                                                        decoration: BoxDecoration(
                                                                            borderRadius: BorderRadius.circular(
                                                                                5),
                                                                            border: imageindex == index
                                                                                ? Border.all(width: 2, color: const Color(0xffE2CBA2))
                                                                                : null),
                                                                        child: ClipRRect(
                                                                          borderRadius:
                                                                              BorderRadius.circular(5),
                                                                          child:
                                                                              MainCachedImage(
                                                                            images[index].name!,
                                                                            height:
                                                                                MediaQuery.of(context).size.height * 0.15,
                                                                            width:
                                                                                MediaQuery.of(context).size.width * 0.25,
                                                                            fit:
                                                                                BoxFit.cover,
                                                                          ),
                                                                        ))),
                                                          ])
                                                        ])
                                                      ],
                                                    ),
                                                  )
                                                ],
                                              ));
                                        },
                                        itemCount: images.length,
                                      )),
                                ],
                              ),
                            ),
                          ),
                        ),
                      ],
                    )),
                  ));
            })
          : nodatafound(
              AppLocalizations.of(context).translate('No images to show')));
}
