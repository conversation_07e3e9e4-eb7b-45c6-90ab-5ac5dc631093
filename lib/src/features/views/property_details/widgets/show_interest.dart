import 'package:flutter/material.dart';

import '../../../../core/localization/app_localizations.dart';
import '../../../../core/response/generalResponse.dart';
import '../../../../core/services/api.dart';
import '../../../../core/shared_widgets/shared_widgets.dart';

void showInterest(
  BuildContext context, {
  required int id,
  required TextEditingController note,
  required bool isLogged,
}) {
  showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Padding(
          padding:
              EdgeInsets.only(bottom: MediaQuery.of(context).viewInsets.bottom),
          child: Container(
            height: MediaQuery.of(context).size.height * 0.50,
            decoration: BoxDecoration(
                color: const Color(0xffF5F6F7),
                borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(25.0),
                    topRight: Radius.circular(25.0)),
                border: Border.all(color: Colors.black, width: 1.0)),
            child: SingleChildScrollView(
                child: Column(
              children: [
                const SizedBox(
                  height: 10,
                ),
                Container(height: 5, width: 50, color: const Color(0xffD2D4D6)),
                const SizedBox(
                  height: 20,
                ),
                Center(
                    child: Text(
                  AppLocalizations.of(context).translate('Show Interest'),
                  style: const TextStyle(fontWeight: FontWeight.bold),
                )),
                Container(
                  padding: const EdgeInsets.all(15),
                  child: Container(
                    decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(10)),
                    child: Container(
                      padding: const EdgeInsets.all(15),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(AppLocalizations.of(context)
                              .translate('Get in touch with the agent')),
                          const SizedBox(
                            height: 20,
                          ),
                          Container(
                              height: 100,
                              decoration: BoxDecoration(
                                  color: Colors.white,
                                  borderRadius: BorderRadius.circular(3)),
                              child: Container(
                                  decoration: BoxDecoration(
                                      borderRadius: const BorderRadius.all(
                                          Radius.circular(5)),
                                      border: Border.all(
                                          color: Colors.black12, width: 1.0)),
                                  child: TextFormField(
                                    controller: note,
                                    decoration: InputDecoration(
                                        contentPadding: const EdgeInsets.only(
                                            left: 20, right: 20, bottom: 10),
                                        hintText: AppLocalizations.of(context)
                                            .translate(
                                                'Write down your message'),
                                        hintStyle: const TextStyle(
                                            color: Color(0xffB7B7B7),
                                            fontSize: 16),
                                        border: InputBorder.none),
                                  ))),
                          const SizedBox(
                            height: 20,
                          ),
                          Center(
                              child: Container(
                                  // padding: EdgeInsets.only(right: 20, left: 20),
                                  child: GestureDetector(
                                      onTap: () async {
                                        if (!isLogged) {
                                          snackbar(AppLocalizations.of(context)
                                              .translate(
                                                  'Login to send your inquiry'));
                                          return;
                                        }
                                        GeneralResponse sucessinformation =
                                            await Api.sendluxuryinterests(
                                                id, note.text);
                                        print(sucessinformation.code);
                                        if (sucessinformation.code == "1") {
                                          snackbar2(AppLocalizations.of(context)
                                              .translate(
                                                  'message added successfuly'));
                                          note.text = "";
                                          Navigator.pop(context);
                                        } else {
                                          snackbar(AppLocalizations.of(context)
                                              .translate(
                                                  'Something went wrong, please try again later'));
                                        }
                                      },
                                      child: Container(
                                        height: 50,
                                        width:
                                            MediaQuery.of(context).size.width,
                                        decoration: BoxDecoration(
                                            color: const Color(0xFF27b4a8),
                                            borderRadius:
                                                BorderRadius.circular(10)),
                                        child: Container(
                                            padding: const EdgeInsets.all(10),
                                            child: Center(
                                                child: Text(
                                              AppLocalizations.of(context)
                                                  .translate('send'),
                                              style: const TextStyle(
                                                  color: Colors.white),
                                            ))),
                                      )))),
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            )),
          )));
}
