import 'package:flutter/material.dart';
import 'package:flutter_rating_bar/flutter_rating_bar.dart';
import 'package:flutter_svg/flutter_svg.dart';

import '../../../../core/localization/app_localizations.dart';

void review(BuildContext context) {
  final Widget svg3 = SizedBox(
      width: 20,
      height: 20,
      child: SvgPicture.asset(
        'assets/Group 6310.svg',
        semanticsLabel: 'Acme Logo',
        fit: BoxFit.cover,
      ));
  showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Padding(
          padding:
              EdgeInsets.only(bottom: MediaQuery.of(context).viewInsets.bottom),
          child: Container(
            height: MediaQuery.of(context).size.height * 0.50,
            decoration: BoxDecoration(
                color: const Color(0xffF5F6F7),
                borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(25.0),
                    topRight: Radius.circular(25.0)),
                border: Border.all(color: Colors.black, width: 1.0)),
            child: SingleChildScrollView(
                child: Column(
              children: [
                const SizedBox(
                  height: 10,
                ),
                Container(height: 5, width: 50, color: const Color(0xffD2D4D6)),
                const SizedBox(
                  height: 20,
                ),
                Center(
                    child: Text(
                  AppLocalizations.of(context).translate('Reviews'),
                  style: const TextStyle(fontWeight: FontWeight.bold),
                )),
                Container(
                  padding: const EdgeInsets.all(15),
                  child: Container(
                    decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(10)),
                    child: Container(
                      padding: const EdgeInsets.all(15),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Container(
                                  height: 50,
                                  width: 50,
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(50),
                                    color: const Color(0xffE4E4E4),
                                  ),
                                  child: const Center(
                                      child: Text(
                                    'LR',
                                    style: TextStyle(color: Color(0xffB7B7B7)),
                                  ))),
                              const SizedBox(
                                width: 20,
                              ),
                              Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  const Text('Lisa Robbin',
                                      style: TextStyle(
                                          fontWeight: FontWeight.bold)),
                                  const SizedBox(
                                    height: 10,
                                  ),
                                  Row(
                                    children: [
                                      RatingBar.builder(
                                        initialRating: 5,
                                        onRatingUpdate: (value) {},
                                        minRating: 1,
                                        direction: Axis.horizontal,
                                        allowHalfRating: true,
                                        itemCount: 5,
                                        itemSize: 16.0,
                                        itemPadding:
                                            const EdgeInsets.only(left: 2),
                                        itemBuilder: (context, _) => const Icon(
                                            Icons.star,
                                            color: Color(0xffF2BA24),
                                            size: 12),
                                        unratedColor: const Color(0xff556477),
                                        // onRatingUpdate: (rating) {
                                        //   print(rating);
                                        // },
                                      ),
                                      const Text(
                                        '10 Days ago',
                                        style:
                                            TextStyle(color: Color(0xff51565B)),
                                      )
                                    ],
                                  )
                                ],
                              ),
                              const Spacer(),
                              svg3
                            ],
                          ),
                          const SizedBox(
                            height: 10,
                          ),
                          const Text(
                            'This place is everything you need for the best stay, perfect location, view and atmosphere.',
                            softWrap: true,
                          ),
                          Container(
                              padding:
                                  const EdgeInsets.only(left: 10, right: 10),
                              child: Divider(
                                color: Colors.grey[100],
                              )),
                          const SizedBox(
                            height: 10,
                          ),
                          Row(
                            children: [
                              Container(
                                  height: 50,
                                  width: 50,
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(50),
                                    color: const Color(0xffE4E4E4),
                                  ),
                                  child: const Center(
                                      child: Text(
                                    'LR',
                                    style: TextStyle(color: Color(0xffB7B7B7)),
                                  ))),
                              const SizedBox(
                                width: 20,
                              ),
                              Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  const Text('Yolla Jackpot',
                                      style: TextStyle(
                                          fontWeight: FontWeight.bold)),
                                  const SizedBox(
                                    height: 10,
                                  ),
                                  Row(
                                    children: [
                                      RatingBar.builder(
                                        onRatingUpdate: (value) {},
                                        initialRating: 5,

                                        minRating: 1,
                                        direction: Axis.horizontal,
                                        allowHalfRating: true,
                                        itemCount: 5,
                                        itemSize: 16.0,
                                        itemPadding:
                                            const EdgeInsets.only(left: 2),
                                        itemBuilder: (context, _) => const Icon(
                                            Icons.star,
                                            color: Color(0xffF2BA24),
                                            size: 12),
                                        unratedColor: const Color(0xff556477),
                                        // onRatingUpdate: (rating) {
                                        //   print(rating);
                                        // },
                                      ),
                                      const Text(
                                        '10 Days ago',
                                        style:
                                            TextStyle(color: Color(0xff51565B)),
                                      )
                                    ],
                                  )
                                ],
                              ),
                              const Spacer(),
                              svg3
                            ],
                          ),
                          const SizedBox(
                            height: 10,
                          ),
                          const Text(
                            'This place is everything you need for the best stay, perfect location, view and atmosphere.',
                            softWrap: true,
                          ),
                          const SizedBox(
                            height: 20,
                          ),
                          const Center(
                              child: Text(
                            'See All Reviews',
                            style: TextStyle(color: Color(0xff0852AB)),
                          ))
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            )),
          )));
}
