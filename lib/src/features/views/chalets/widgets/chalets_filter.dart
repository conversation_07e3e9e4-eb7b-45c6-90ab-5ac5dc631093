import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:page/src/features/controllers/content_controller.dart';

import '../../../../core/localization/app_localizations.dart';
import '../../../controllers/currency_controller.dart';
import '../../../models/content.dart';
import '../../../models/locations.dart';
import '../chalets.dart';

void filterChalets(
  BuildContext context, {
  required VoidCallback onApply,
  // required VoidCallback onReset,
  required ContentController contentController,
  required CurrencyController currencyController,
}) {
  showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) =>
          StatefulBuilder(builder: (context, StateSetter stateSetter) {
            return Padding(
                padding: EdgeInsets.only(
                    bottom: MediaQuery.of(context).viewInsets.bottom),
                child: Container(
                  height: MediaQuery.of(context).size.height * 0.50,
                  decoration: BoxDecoration(
                      color: const Color(0xffF5F6F7),
                      borderRadius: const BorderRadius.only(
                          topLeft: Radius.circular(25.0),
                          topRight: Radius.circular(25.0)),
                      border: Border.all(color: Colors.black, width: 1.0)),
                  child: SingleChildScrollView(
                      child: Column(
                    children: [
                      const SizedBox(
                        height: 10,
                      ),
                      Container(
                          height: 5, width: 50, color: const Color(0xffD2D4D6)),
                      const SizedBox(
                        height: 20,
                      ),
                      Padding(
                          padding: const EdgeInsets.only(left: 20, right: 20),
                          child: Stack(
                            children: [
                              Align(
                                alignment:
                                    AppLocalizations.of(context).locale ==
                                            const Locale('ar')
                                        ? Alignment.centerLeft
                                        : Alignment.centerRight,
                                child: GestureDetector(
                                    onTap: () {
                                      stateSetter(() {
                                        currentChaletsValue2 = null;
                                        currentChaletsValue3 = null;
                                      });
                                    },
                                    child: Text(
                                      AppLocalizations.of(context)
                                          .translate('reset'),
                                      style: const TextStyle(
                                          color: Color(0xff51565B)),
                                    )),
                              ),
                              Center(
                                child: Text(
                                  AppLocalizations.of(context)
                                      .translate('filter'),
                                  style: const TextStyle(
                                      fontWeight: FontWeight.bold),
                                  textAlign: TextAlign.center,
                                ),
                              )
                            ],
                          )),
                      Container(
                        padding: const EdgeInsets.all(15),
                        child: Container(
                          decoration: BoxDecoration(
                              color: Colors.white,
                              borderRadius: BorderRadius.circular(10)),
                          child: Container(
                            padding: const EdgeInsets.all(15),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  AppLocalizations.of(context)
                                      .translate('location'),
                                  style: const TextStyle(fontSize: 13),
                                ),
                                const SizedBox(
                                  height: 10,
                                ),
                                Container(
                                    // width: 120,
                                    height: 50,
                                    decoration: BoxDecoration(
                                      borderRadius: BorderRadius.circular(5),
                                      border: Border.all(
                                          color: Colors.black12, width: 1.0),
                                    ),
                                    child: DropdownButton<int>(
                                        isExpanded: true,
                                        hint: Padding(
                                          padding: const EdgeInsets.only(
                                              left: 20, right: 20),
                                          child: Text(
                                            AppLocalizations.of(context)
                                                .translate('Choose Location'),
                                            style: const TextStyle(
                                                color: Color(0xffB7B7B7)),
                                          ),
                                        ),
                                        value: currentChaletsValue2,
                                        underline: const SizedBox(),
                                        iconEnabledColor: Colors.black,
                                        items: ContentController.locations
                                            .map((Locations value) {
                                          return DropdownMenuItem<int>(
                                            value: value.id,
                                            child: Container(
                                                padding: const EdgeInsets.only(
                                                    left: 10, right: 10),
                                                child: Text(
                                                  value.name!,
                                                  style: const TextStyle(
                                                      fontSize: 16),
                                                )),
                                          );
                                        }).toList(),
                                        onChanged: (value) {
                                          stateSetter(() {
                                            log('valueasddis $value');
                                            currentChaletsValue2 = value;
                                          });
                                        })),
                                const SizedBox(height: 30),
                                ContentController.types.isNotEmpty
                                    ? Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          Text(
                                            AppLocalizations.of(context)
                                                .translate('type'),
                                            style:
                                                const TextStyle(fontSize: 13),
                                          ),
                                          const SizedBox(
                                            height: 10,
                                          ),
                                          Container(
                                              // width: 120,
                                              height: 50,
                                              decoration: BoxDecoration(
                                                borderRadius:
                                                    BorderRadius.circular(5),
                                                border: Border.all(
                                                    color: Colors.black12,
                                                    width: 1.0),
                                              ),
                                              child: DropdownButton<int>(
                                                  isExpanded: true,
                                                  hint: Padding(
                                                    padding:
                                                        const EdgeInsets.only(
                                                            left: 20,
                                                            right: 20),
                                                    child: Text(
                                                      AppLocalizations.of(
                                                              context)
                                                          .translate('type'),
                                                      style: const TextStyle(
                                                          color: Color(
                                                              0xffB7B7B7)),
                                                    ),
                                                  ),
                                                  value: currentChaletsValue3,
                                                  underline: const SizedBox(),
                                                  iconEnabledColor:
                                                      Colors.black,
                                                  items: ContentController.types
                                                      .map((Types value) {
                                                    return DropdownMenuItem<
                                                        int>(
                                                      value: value.id,
                                                      child: Container(
                                                          padding:
                                                              const EdgeInsets
                                                                  .only(
                                                                  left: 10,
                                                                  right: 10),
                                                          child: Text(
                                                            value.name!,
                                                            style:
                                                                const TextStyle(
                                                                    fontSize:
                                                                        16),
                                                          )),
                                                    );
                                                  }).toList(),
                                                  onChanged: (value) {
                                                    stateSetter(() {
                                                      currentChaletsValue3 =
                                                          value;
                                                    });
                                                  })),
                                        ],
                                      )
                                    : Center(
                                        child: Text(AppLocalizations.of(context)
                                            .translate('No types to show'))),
                                const SizedBox(height: 30),
                                Center(
                                    child: GestureDetector(
                                        onTap: onApply,
                                        child: Container(
                                          height: 50,
                                          width:
                                              MediaQuery.of(context).size.width,
                                          decoration: BoxDecoration(
                                              color: const Color(0xFF27b4a8),
                                              borderRadius:
                                                  BorderRadius.circular(10)),
                                          child: Container(
                                              padding: const EdgeInsets.all(10),
                                              child: Center(
                                                  child: Text(
                                                AppLocalizations.of(context)
                                                    .translate('Apply Filter'),
                                                style: const TextStyle(
                                                    color: Colors.white),
                                              ))),
                                        ))),
                                const SizedBox(height: 20),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ],
                  )),
                ));
          }));
}
