import 'package:flutter/material.dart';
import 'package:page/src/core/response/offers_response.dart';
import 'package:page/src/features/bloc/promo_coded_bloc.dart';
import 'package:page/src/features/views/account/account.dart';

import '../../../core/localization/app_localizations.dart';
import '../../../core/shared_widgets/shared_widgets.dart';
import 'widgets/list_offers.dart';

class Offers extends StatefulWidget {
  const Offers({super.key});

  @override
  _Offers createState() => _Offers();
}

class _Offers extends State<Offers> {
  @override
  void initState() {
    super.initState();
    offersBloc.getoffers(page, 20);
  }

  int page = 0;

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: () async {
        Navigator.push(
            context, MaterialPageRoute(builder: (context) => const Account()));
        return false;
      },
      child: SafeArea(
          child: Scaffold(
              appBar: AppBar(
                backgroundColor: const Color(0xFF27b4a8),
                centerTitle: true,
                title: Text(
                    AppLocalizations.of(context).translate('Trending Offers')),
              ),
              body: StreamBuilder<OffersResponse>(
                stream: offersBloc.subject.stream,
                builder: (context, AsyncSnapshot<OffersResponse> snapshot) {
                  if (snapshot.hasData) {
                    if (snapshot.data!.error.isNotEmpty) {
                      return buildErrorWidget(snapshot.data!.error);
                    }
                    return ListOffers(offers: snapshot.data!.offers);
                  } else if (snapshot.hasError) {
                    return buildErrorWidget(snapshot.error.toString());
                  } else {
                    return buildLoadingWidget();
                  }
                },
              ))),
    );
  }
}
