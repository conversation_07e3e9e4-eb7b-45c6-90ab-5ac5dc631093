import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:page/src/features/models/offer_model.dart';
import 'package:page/src/features/views/offers/widgets/videos/offer_videos.dart';

import '../../../../core/localization/app_localizations.dart';
import '../../../../core/shared_widgets/shared_widgets.dart';

class ListOffers extends StatelessWidget {
  final List<OfferModel> offers;

  const ListOffers({super.key, required this.offers});

  @override
  Widget build(BuildContext context) {
    return offers.isNotEmpty
        ? ListView.builder(
            shrinkWrap: true,
            padding: const EdgeInsets.all(16),
            itemBuilder: (BuildContext ctxt, int index) {
              return OfferCard(offer: offers[index]);
            },
            itemCount: offers.length,
          )
        : nodatafound(
            AppLocalizations.of(context).translate('No Offers to show'));
  }
}

class OfferCard extends StatelessWidget {
  final OfferModel offer;

  const OfferCard({super.key, required this.offer});

  @override
  Widget build(BuildContext context) {
    final language = Localizations.localeOf(context).languageCode;

    final agentWithCode = SizedBox(
      width: MediaQuery.sizeOf(context).width * 0.7,
      child: FittedBox(
        fit: BoxFit.scaleDown,
        alignment:
            language == 'en' ? Alignment.centerLeft : Alignment.centerRight,
        child: Row(
          children: [
            Row(
              children: [
                Text(
                  tr(context, 'Agent') + ': ',
                  style: const TextStyle(fontSize: 14),
                ),
                Text(
                  offer.agent?.name ?? '',
                  style: const TextStyle(
                      fontWeight: FontWeight.bold, fontSize: 14),
                ),
              ],
            ),
            const SizedBox(
              width: 10,
            ),
            const Text('|'),
            const SizedBox(
              width: 10,
            ),
            Row(
              children: [
                Text(
                  tr(context, 'Code') + ': ',
                  style: const TextStyle(fontSize: 14),
                ),
                Text(offer.code ?? '',
                    style: const TextStyle(
                        fontWeight: FontWeight.bold, fontSize: 14)),
                IconButton(
                  onPressed: () {
                    Clipboard.setData(ClipboardData(text: offer.code ?? ''));

                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text(tr(context, 'Copied to Clipboard')),
                      ),
                    );
                  },
                  icon: const Icon(
                    Icons.copy,
                    size: 18,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );

    final titleWithDescription = SizedBox(
      width: MediaQuery.sizeOf(context).width * 0.7,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            offer.title ?? '',
            style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
          ),
          Row(
            children: [
              Flexible(
                child: Text(offer.description ?? '',
                    style: const TextStyle(
                      color: Color(0xff8B959E),
                      fontSize: 14,
                    )),
              ),
              const SizedBox(
                width: 10,
              ),
              GestureDetector(
                onTap: () {
                  if (offer.agent == null) {
                    snackbar(AppLocalizations.of(context).translate(
                        'Something went wrong, please try again later'));
                    return;
                  }
                  Navigator.push(context, MaterialPageRoute(
                    builder: (context) {
                      return MainOfferVideosPage(agent: offer.agent!);
                    },
                  ));
                },
                child: Stack(
                  alignment: Alignment.bottomCenter,
                  children: [
                    Text(
                      tr(context, 'Check Items'),
                      style: const TextStyle(
                        color: Color(0xffE0C28A),
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    Container(
                      height: 1,
                      width: 77,
                      transform: Matrix4.translationValues(0, -3, 0),
                      color: const Color(0xffE0C28A),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.only(top: 6),
            child: CircleAvatar(
              radius: 27,
              backgroundColor: const Color(0xffE0C28A),
              child: Padding(
                padding: const EdgeInsets.only(top: 3),
                child: Text(
                  '${offer.discount}%',
                  style: const TextStyle(
                      color: Colors.white,
                      fontSize: 16,
                      fontWeight: FontWeight.bold),
                ),
              ),
            ),
          ),
          const SizedBox(
            width: 10,
          ),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              titleWithDescription,
              Container(
                  transform: Matrix4.translationValues(0.0, -10, 0.0), //here
                  child: agentWithCode),
            ],
          ),
        ],
      ),
    );
  }
}
