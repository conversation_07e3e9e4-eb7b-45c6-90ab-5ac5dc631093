import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:page/src/core/config/constants.dart';
import 'package:page/src/core/localization/app_localizations.dart';
import 'package:page/src/core/services/api.dart';
import 'package:page/src/core/shared_widgets/shared_widgets.dart';
import 'package:page/src/features/models/agents.dart';
import 'package:page/src/features/models/video_model.dart';
import 'package:page/src/features/views/offers/widgets/videos/widgets/offer_list.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

class MainOfferVideosPage extends StatefulWidget {
  final AgentModel agent;

  const MainOfferVideosPage({
    super.key,
    required this.agent,
  });

  @override
  _HolidayName createState() => _HolidayName();
}

class _HolidayName extends State<MainOfferVideosPage> {
  int pagenumber = 1;
  int code = 0, code2 = 0;
  String msg = 'loading', msg2 = 'loading';

  List<VideoModel> results = [];
  final RefreshController _refreshControllerwait =
      RefreshController(initialRefresh: false);

  @override
  void initState() {
    super.initState();
    getcategory(pagenumber, "");
  }

  @override
  Widget build(BuildContext context) {
    final isHolidayHomeAgent = widget.agent.isHolidayHomeAgent!;

    return SafeArea(
        child: Scaffold(
      appBar: AppBar(
        backgroundColor: const Color(0xFF27b4a8),
        centerTitle: true,
        title: isHolidayHomeAgent
            ? Text(AppLocalizations.of(context).translate('holidayhome'))
            : Text(AppLocalizations.of(context).translate('carrental')),
      ),
      body: SmartRefresher(
        enablePullDown: true,
        enablePullUp: true,
        header: ClassicHeader(
          refreshingIcon: buildLoadingWidget(),
        ),
        footer: CustomFooter(
          builder: (BuildContext context, LoadStatus? mode) {
            Widget body;
            if (mode == LoadStatus.loading) {
              body = const CupertinoActivityIndicator();
            } else if (mode == LoadStatus.failed) {
              body = const Text("Load Failed!Click retry!",
                  style: TextStyle(color: Color(0xff233549)));
            } else if (mode == LoadStatus.canLoading) {
              body = const Text("release to load more",
                  style: TextStyle(color: Color(0xff233549)));
            } else {
              body = const Text("No more Data",
                  style: TextStyle(color: Color(0xff233549)));
            }
            return Center(child: body);
          },
        ),
        controller: _refreshControllerwait,
        onRefresh: _onRefresh,
        onLoading: _onLoading,
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const SizedBox(
                height: 10,
              ),
              code2 == 0 && msg2 == 'loading'
                  ? buildLoadingWidget()
                  : code2 == 1
                      ? results.isNotEmpty
                          ? OfferList(
                              results,
                              isHolidayHomeAgent: isHolidayHomeAgent,
                            )
                          : nodatafound(AppLocalizations.of(context).translate(
                              isHolidayHomeAgent
                                  ? 'No Holiday Homes to show'
                                  : 'No car rental to show'))
                      : buildErrorWidget(msg2)
            ],
          ),
        ),
      ),
    ));
  }

  getcategory(int page, String key) async {
    final isHolidayHomeAgent = widget.agent.isHolidayHomeAgent!;

    final catId = isHolidayHomeAgent
        ? AppConstants.holidayHomesId.toString()
        : AppConstants.carRentalsId.toString();

    await Api.getmainCategory(page, 10, key, catId, agentId: widget.agent.id)
        .then((value) {
      value != null
          ? setState(() {
              code2 = value.code;
              msg2 = value.error;
              results.addAll(value.category);
            })
          // ignore: unnecessary_statements
          : null;
    });
  }

  void _onRefresh() async {
    await Future.delayed(const Duration(milliseconds: 1000));
    results.clear();

    pagenumber = 1;
    getcategory(pagenumber, "");
    _refreshControllerwait.refreshCompleted();
  }

  void _onLoading() async {
    pagenumber = pagenumber + 1;
    getcategory(pagenumber, "");
    _refreshControllerwait.loadComplete();
  }
}
