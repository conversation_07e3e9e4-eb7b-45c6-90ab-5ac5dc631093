import 'package:flutter/material.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:page/src/core/localization/app_language.dart';
import 'package:page/src/core/localization/app_localizations.dart';
import 'package:page/src/core/utils/main_cached_image.dart';
import 'package:page/src/features/controllers/auth_controller.dart';
import 'package:page/src/features/controllers/currency_controller.dart';
import 'package:page/src/features/models/video_model.dart';
import 'package:page/src/features/views/car_rent_details/car_rent_details.dart';
import 'package:page/src/features/views/holiday_home_details/holiday_home_details.dart';
import 'package:provider/provider.dart';

class OfferList extends StatefulWidget {
  final List<VideoModel> results;
  final bool isHolidayHomeAgent;

  const OfferList(
    this.results, {
    Key? key,
    required this.isHolidayHomeAgent,
  }) : super(key: key);

  @override
  State<OfferList> createState() => _OfferListState();
}

class _OfferListState extends State<OfferList> {
  AuthController authController = AuthController();
  CurrencyController currencyController = CurrencyController();

  @override
  void initState() {
    // TODO: implement initState
    super.initState();

    authController.isloggedin();

    currencyController.getcuurentcurrency(context);
  }

  @override
  Widget build(BuildContext context) {
    var lang =
        Provider.of<AppLanguage>(context, listen: false).appLocal.languageCode;

    final isEnglish = lang == 'en';

    return StaggeredGridView.countBuilder(
        staggeredTileBuilder: (index) {
          return const StaggeredTile.fit(1);
        },
        shrinkWrap: true,
        padding: const EdgeInsets.symmetric(horizontal: 10.0, vertical: 20.0),
        crossAxisCount: 2,
        physics: const NeverScrollableScrollPhysics(),
        mainAxisSpacing: 8,
        itemCount: widget.results.length,
        primary: false,
        itemBuilder: (context, index) {
          Widget titleWithPriceWidget() {
            return Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                widget.results[index].name != null
                    ? SizedBox(
                        width: MediaQuery.of(context).size.width * 0.3,
                        child: Text(
                          widget.results[index].name!,
                          style: const TextStyle(
                              color: Colors.white,
                              fontWeight: FontWeight.w700,
                              fontSize: 12),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                      )
                    : Container(),
                const SizedBox(height: 5),
                if (widget.results != null &&
                    widget.results.isNotEmpty &&
                    widget.results[index].startprice != null) ...[
                  SizedBox(
                    width: MediaQuery.of(context).size.width * 0.3,
                    child: Builder(builder: (context) {
                      if (widget.isHolidayHomeAgent) {
                        return Text(
                          '${num.tryParse(widget.results[index].startprice!.toString())!.toStringAsFixed(0)} ${currencyController.currency}',
                          style: const TextStyle(
                              color: Colors.white, fontSize: 12),
                        );
                      }
                      return Text(
                        '${AppLocalizations.of(context).translate('Day Price')} ${widget.results[index].startprice?.toStringAsFixed(0)} ${currencyController.currency}',
                        style:
                            const TextStyle(color: Colors.white, fontSize: 12),
                      );
                    }),
                  )
                ],
              ],
            );
          }

          void onTap() {
            if (widget.isHolidayHomeAgent) {
              Navigator.of(context).push(MaterialPageRoute(
                  builder: (BuildContext context) => HoldayHomeDetails(
                        video: widget.results[index],
                      )));
            } else {
              Navigator.of(context).push(MaterialPageRoute(
                  builder: (BuildContext context) => CarRentDetailsVideoWidget(
                        video: widget.results[index],
                      )));
            }
          }

          return Column(
            children: [
              Stack(children: [
                GestureDetector(
                    onTap: onTap,
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(5),
                      child: MainCachedImage(
                        widget.results[index].images!,
                        height: 288,
                        width: MediaQuery.of(context).size.width * 0.45,
                        fit: BoxFit.fitHeight,
                      ),
                    )),
                GestureDetector(
                    onTap: onTap,
                    child: Container(
                      height: 288,
                      width: MediaQuery.of(context).size.width * 0.45,
                      decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(5),
                          gradient: LinearGradient(
                            end: Alignment.bottomCenter,
                            begin: Alignment.center,
                            colors: <Color>[
                              Colors.transparent,
                              //  Colors.white.withOpacity(0.5),
                              Colors.black.withOpacity(0.7)
                            ],
                          )),
                    )),
                // Positioned(
                //     top: 10,
                //     right: 10,
                //     child: GestureDetector(
                //       onTap: () {
                //         authController.isLogged == true
                //             ? listmyplan(context, "hoilday_home",
                //                 widget.results[index].id!)
                //             : snackbar(AppLocalizations.of(context)
                //                 .translate('Please login first'));
                //       },
                //       child: Container(
                //           height: 30,
                //           width: 30,
                //           decoration: BoxDecoration(
                //               color: const Color(0xff4d5e72).withOpacity(0.5),
                //               borderRadius:
                //                   const BorderRadius.all(Radius.circular(30))),
                //           child: const Center(
                //               child: Icon(
                //             Icons.add,
                //             color: Colors.white,
                //           ))),
                //     )),
                if (!isEnglish)
                  Positioned(
                      bottom: 12, right: 10, child: titleWithPriceWidget())
                else
                  Positioned(
                      bottom: 12, left: 10, child: titleWithPriceWidget()),
                if (!widget.isHolidayHomeAgent)
                  Positioned(
                      top: 10,
                      right: 10,
                      left: 10,
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          if (widget.results[index].year != null &&
                              widget.results[index].year!.isNotEmpty)
                            Container(
                                height: 20,
                                width: 40,
                                decoration: const BoxDecoration(
                                    color: Colors.white,
                                    borderRadius:
                                        BorderRadius.all(Radius.circular(5))),
                                child: Center(
                                    child: Text(
                                        widget.results[index].year.toString())))
                          else
                            const Spacer(),
                        ],
                      )),
              ]),
            ],
          );
        });
  }
}
