// ignore_for_file: prefer_typing_uninitialized_variables

import 'dart:developer';

import 'package:flutter/foundation.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:google_cloud_translation/google_cloud_translation.dart';
import 'package:page/src/core/config/constants.dart';
import 'package:page/src/core/shared_widgets/reel_images.dart';
import 'package:page/src/core/utils/dynamic_links.dart';
import 'package:page/src/core/utils/logger.dart';
import 'package:page/src/features/views/ad_details/services/cached_video_services.dart';
import 'package:page/src/features/views/ad_details/widgets/discussion.dart';
import 'package:page/src/features/views/property_details/widgets/property_actions.dart';
import 'package:page/src/features/views/property_details/widgets/property_details.dart';
import 'package:page/src/features/views/property_details/widgets/show_interest.dart';
import 'package:page/src/features/views/splash_screen/services/splash_services.dart';
import 'package:share_plus/share_plus.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:video_player/video_player.dart';

import '../../../core/localization/app_localizations.dart';
import '../../../core/response/generalResponse.dart';
import '../../../core/services/api.dart';
import '../../../core/shared_widgets/back_button.dart';
import '../../../core/shared_widgets/shared_widgets.dart';
import '../../controllers/auth_controller.dart';
import '../../controllers/currency_controller.dart';
import '../../controllers/language_controller.dart';
import '../../models/discussions.dart';
import '../../models/reviews.dart';
import '../../models/video_model.dart';
import '../story/widgets/see_more.dart';
import 'widgets/ad_details.dart';
import 'widgets/riview.dart';
import 'widgets/sections.dart';

bool isactions = false;
TextEditingController note = TextEditingController();

TextEditingController discussionController = TextEditingController();

const int cacheDisposeDuration = 3;

class VideoViewWidget extends StatefulWidget {
  final VideoModel? video;

  // final int? id;
  final int? planId;
  final date;
  final time;

  const VideoViewWidget({
    super.key,
    this.video,
    // this.id,
    this.planId,
    this.date,
    this.time,
  });

  @override
  State<StatefulWidget> createState() {
    return _VideoWidget();
  }
}

class _VideoWidget extends State<VideoViewWidget> {
  int? get id => widget.video?.id;

  // get _controller => videoPlayerControllers[id!];
  VideoPlayerController? _controller;

  // videoPlayerController[id!];
  int imageindex = 0;
  int? imageindex2;
  bool isdiscussion = false;
  Translation? _translation;
  TranslationModel? _translated;
  String? text;
  int index2 = -1;
  AuthController authController = AuthController();
  CurrencyController currencyController = CurrencyController();
  List<Categoryimages> images = [];
  List<Discussions> dis = [];
  List<Reviews> reviews = [];
  List<Categoryreels> videos = [];
  int pagenumber = 1;
  int? code;
  String msg = 'Loading';
  LanguageController language = LanguageController();
  bool isload = false;

  String? get video => widget.video?.video;

  // AppLanguage().appLocal.languageCode == 'en'
  // ? widget.video?.video
  // : widget.video?.videoAr ?? widget.video?.video;

  // {
  //   if (widget.video?.category?.id == AppConstants.propertiesId) {
  //     final isEnglish = AppLanguage().appLocal.languageCode == 'en';
  //
  //     log('afafssaf ${isEnglish}');
  //     if (isEnglish) {
  //       return widget.video?.video;
  //     } else {
  //       return widget.video?.videoAr ?? widget.video?.video;
  //     }
  //   }
  //   return widget.video?.video;
  //   return null;
  //   // var lang =
  //   //     Provider.of<AppLanguage>(context, listen: true).appLocal.languageCode;
  //   // final isEnglish = lang == 'en';
  // }

  String? get name => widget.video?.name;

  String? get label => widget.video?.category?.nameEn;

  String? get phone => widget.video?.phone;

  int? get rating => widget.video?.rating;

  String? get description => widget.video?.description;

  String? get location => widget.video?.locationName;

  String? get website => widget.video?.website;

  String? get whatsapp => widget.video?.whatsapp;

  String? get instagram => widget.video?.instagram;

  String? get greviewlink => widget.video?.greviewlink;

  String? get greviewName => widget.video?.greviewName;

  String? get category => widget.video?.category?.name;

  num? get startprice => widget.video?.startprice;

  num? get endprice => widget.video?.endprice;

  num? get price => widget.video?.price;

  double? get lat => widget.video?.latitude;

  double? get lng => widget.video?.longitude;

  int? get isfavouriate => widget.video?.isFav;

  set isfavouriate(int? value) => widget.video?.isFav = value;

  int? get agentId => widget.video?.agent?.id;

  String? get agentName => widget.video?.agent?.name;

  int? get size => widget.video?.size;

  String? get roomnumber => widget.video?.rooms;

  String? get type => widget.video?.type;

  int? get startsize => widget.video?.startSize;

  int? get endsize => widget.video?.endSize;

  bool get isProperties =>
      widget.video?.category?.id == AppConstants.propertiesId;

  bool ismute = false;

  // get chewieController => ChewieController(
  //       videoPlayerControllers: videoPlayerControllers[id!]!,
  //       autoPlay: true,
  //       showControls: false,
  //       looping: true,
  //       aspectRatio: 20 / 23,
  //     );

  Future<String?> getLangCode() async {
    SharedPreferences _prefs = await SharedPreferences.getInstance();
    var currentlanguagecode = _prefs.getString('language_code');

    return currentlanguagecode;
  }

  @override
  void initState() {
    super.initState();

    // log('asfasfafs ${video} akfjaslf ${widget.video?.video}');

    initPlayer();

    // getmorerelatedreels();
    authController.isloggedin();
    currencyController.getcuurentcurrency(context);
    language.getcuurentlanguage();
    _translation = Translation(
      apiKey: 'AIzaSyCXOO147BdbuceLIl8Z8D5Jxru2Vjhqd4Q',
    );

    setState(() {});
  }

  // bool hasError = false;

  final cachedVideoServices = CachedVideoServices();

  void initPlayer() async {
    Future.delayed(const Duration(seconds: cacheDisposeDuration), () async {
      if (!(_controller?.value.isInitialized ?? false)) {
        await Future.microtask(() => cachedVideoServices.disposeCache());
      }
    });

    final fileInfo = await Future.microtask(
        () => cachedVideoServices.checkVideoIfCached(video!));

    if (fileInfo == null) {
      _controller = VideoPlayerController.networkUrl(
        Uri.parse(video!),
      );

      Future.microtask(() => cachedVideoServices.cacheVideo(video!));

      Log.i('CachedVideoServices: $video');
    } else {
      _controller = VideoPlayerController.file(fileInfo.file);

      Log.w('AlreadyCached: $video');
    }

    if (_controller?.value.isInitialized ?? false) {
      _controller?.play();
    } else {
      try {
        await Future.microtask(() => _controller?.initialize());

        _controller?.play();
      } catch (e) {
        Log.e('ErrorOccurred: $video');
      }
    }

    setState(() {
      isload = true;
    });
  }

  @override
  void dispose() {
    _controller?.pause();
    _controller?.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final isEng = isEnglish(context);

    return WillPopScope(
      onWillPop: () {
        print("out");
        SplashServices().initControllersForHomeData();
        if (_controller!.value.isPlaying) {
          _controller!.pause();
        }
        setState(() {
          isactions = false;
        });
        return Future.value(true);
      },
      child: SafeArea(
        child: Scaffold(
          backgroundColor: Colors.black,
          body: Center(
            child: SizedBox(
              width: kIsWeb ? 500 : null,
              child: Stack(
                alignment: Alignment.bottomCenter,
                children: <Widget>[
                  _controller != null
                      ? SizedBox(
                          height: MediaQuery.sizeOf(context).height,
                          width: MediaQuery.sizeOf(context).width,
                          child: _controller!.value.isInitialized
                              ? VideoPlayer(_controller!)
                              : const SizedBox())
                      : const SizedBox(),

                  // if (!(_controller?.value.isInitialized ?? false)) ...[
                  //   Positioned.fill(
                  //     child: Shimmer.fromColors(
                  //         baseColor: Colors.black.withOpacity(0.5),
                  //         highlightColor: Colors.grey.withOpacity(0.5),
                  //         child: Container(
                  //           color: Colors.black.withOpacity(0.5),
                  //         )),
                  //   ),
                  // ],

                  // SizedBox(
                  //   height: MediaQuery.sizeOf(context).height,
                  //   width: MediaQuery.sizeOf(context).width,
                  //   child: YoYoPlayer(
                  //     aspectRatio: 16 / 9,
                  //     url: video!,
                  //     // "https://test-streams.mux.dev/x36xhzz/x36xhzz.m3u8",
                  //     videoStyle: const VideoStyle(
                  //       progressIndicatorColors: VideoProgressColors(
                  //         playedColor: Color(0xffE0C28A),
                  //       ),
                  //     ),
                  //     videoLoadingStyle:
                  //     VideoLoadingStyle(loading: buildLoadingWidget()),
                  //     allowCacheFile: true,
                  //     autoPlayVideoAfterInit: true,
                  //     onCacheFileCompleted: (videoInfo) {
                  //       log('Cache Completedasdasfasfsa');
                  //     },
                  //   ),
                  // )

                  // SizedBox(
                  //   height: MediaQuery.sizeOf(context).height,
                  //   width: MediaQuery.sizeOf(context).width,
                  //   child: YoYoPlayer(
                  //     aspectRatio: 16 / 9,
                  //     url: video!,
                  //     // "https://test-streams.mux.dev/x36xhzz/x36xhzz.m3u8",
                  //     videoStyle: const VideoStyle(
                  //       progressIndicatorColors: VideoProgressColors(
                  //         playedColor: Color(0xffE0C28A),
                  //       ),
                  //     ),
                  //     videoLoadingStyle:
                  //         VideoLoadingStyle(loading: buildLoadingWidget()),
                  //     allowCacheFile: true,
                  //     autoPlayVideoAfterInit: true,
                  //     onCacheFileCompleted: (videoInfo) {
                  //       log('Cache Completedasdasfasfsa');
                  //     },
                  //   ),
                  // ),

                  Positioned(
                      left: 10,
                      right: 10,
                      top: 20,
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          BackButtonWidget(
                            onTap: () {
                              SplashServices().initControllersForHomeData();
                              if (_controller!.value.isPlaying) {
                                _controller!.pause();
                              }
                              setState(() {
                                isactions = false;
                              });
                            },
                          ),
                          InkWell(
                              onTap: () {
                                setState(() {
                                  ismute = !ismute;
                                  ismute
                                      ? _controller!.setVolume(0.0)
                                      : _controller!.setVolume(30.0);
                                });
                                // _controller.setVolume(0.0);
                              },
                              child: !ismute
                                  ? const Icon(
                                      Icons.volume_down,
                                      color: Colors.white,
                                    )
                                  : const Icon(
                                      Icons.volume_off,
                                      color: Colors.white,
                                    ))
                        ],
                      )),
                  if (!isEng)
                    Positioned(bottom: 65, right: 30, child: _nameDetails())
                  else
                    Positioned(bottom: 65, left: 25, child: _nameDetails()),
                  _controller != null
                      ? Padding(
                          padding: const EdgeInsetsDirectional.only(
                            bottom: 25,
                            top: 9,
                            start: 10,
                            end: 10,
                          ),
                          child: Row(
                            children: [
                              Expanded(
                                child: IconButton(
                                    onPressed: () {
                                      _controller!.value.isPlaying
                                          ? _controller!.pause()
                                          : _controller!.play();
                                      setState(() {});
                                    },
                                    icon: Icon(
                                      _controller!.value.isPlaying
                                          ? Icons.pause
                                          : Icons.play_arrow,
                                      color: Colors.white,
                                    )),
                              ),
                              const SizedBox(width: 10),
                              Expanded(
                                flex: 8,
                                child: VideoProgressIndicator(
                                  _controller!,
                                  allowScrubbing: true,
                                  colors: VideoProgressColors(
                                      playedColor:
                                          _controller!.value.isInitialized
                                              ? Colors.white
                                              : const Color(0xFF27b4a8),
                                      backgroundColor:
                                          Colors.white.withOpacity(0.44)),
                                ),
                              ),
                              const SizedBox(width: 10),
                              Expanded(
                                child: FittedBox(
                                  fit: BoxFit.scaleDown,
                                  child: Text(
                                    _controller!.value.duration
                                        .toString()
                                        .substring(
                                            _controller!.value.duration
                                                    .toString()
                                                    .indexOf(":") +
                                                1,
                                            _controller!.value.duration
                                                .toString()
                                                .indexOf(".")),
                                    maxLines: 1,
                                    style: const TextStyle(color: Colors.white),
                                  ),
                                ),
                              ),
                            ],
                          ),
                        )
                      : Container(),
                  if (!isEng)
                    Positioned(
                        bottom: 75,
                        left: 20,
                        child: isProperties
                            ? _propertiesColumnDetailsWidget()
                            : _columnDetailsWidget())
                  else
                    Positioned(
                        bottom: 75,
                        right: 20,
                        child: isProperties
                            ? _propertiesColumnDetailsWidget()
                            : _columnDetailsWidget()),
                  isactions
                      ? isProperties
                          ? PropertyActions(
                              lat: lat,
                              lng: lng,
                              onShare: () async {
                                try {
                                  final String linkPathData =
                                      '?id=${id}&type=${'properties'}';
                                  final dynamicLink = await DynamicLinkHandler
                                      .createDynamicLink(
                                    linkPathData,
                                  );

                                  log('sdfdfdsfsf ${dynamicLink.toString()}');

                                  Share.share(dynamicLink.toString())
                                      .then((value) {
                                    setState(() {
                                      isactions = false;
                                    });
                                  });
                                } catch (e) {
                                  log('Eerrrror ${e.toString()}');
                                }
                              },
                              onJoinDiscussion: () {
                                getdiscussions();
                              },
                              onCancel: () {
                                setState(() {
                                  isactions = !isactions;
                                });
                              },
                              name: name!)
                          : AdSections(
                              lat: lat,
                              lng: lng,
                              onShare: () async {
                                try {
                                  final String linkPathData =
                                      '?id=${id ?? 0}&type=${widget.video?.category?.name ?? ''}&planId=${widget.planId ?? 0}${widget.date != null ? '&date=${widget.date}' : ''}${widget.time != null ? '&time=${widget.time}' : ''}';
                                  final dynamicLink = await DynamicLinkHandler
                                      .createDynamicLink(
                                    linkPathData,
                                  );

                                  log('sdfdfdsfsf ${dynamicLink.toString()}');

                                  Share.share(dynamicLink.toString())
                                      .then((value) {
                                    setState(() {
                                      isactions = false;
                                    });
                                  });
                                } catch (e) {
                                  log('Eerrrror ${e.toString()}');
                                }
                              },
                              isfavouriate: isfavouriate ?? 0,
                              onAddFavourite: () async {
                                GeneralResponse sucessinformation =
                                    await Api.addmaincategoryfavourite(id!);
                                print(sucessinformation.code);
                                if (sucessinformation.code == "1") {
                                  snackbar2(AppLocalizations.of(context)
                                      .translate('addtofav'));
                                  setState(() {
                                    isfavouriate = 1;
                                  });
                                } else {
                                  snackbar(AppLocalizations.of(context).translate(
                                      'Something went wrong, please try again later'));
                                }
                              },
                              onRemoveFavourite: () async {
                                GeneralResponse sucessinformation =
                                    await Api.removemaincategoryfromfavourite(
                                  id!,
                                );
                                print(sucessinformation.code);
                                if (sucessinformation.code == "1") {
                                  snackbar2(AppLocalizations.of(context)
                                      .translate(
                                          'remove from favourite successfuly'));
                                  setState(() {
                                    isfavouriate = 0;
                                    // isfav = true;
                                  });
                                } else {
                                  snackbar(AppLocalizations.of(context).translate(
                                      'Something went wrong, please try again later'));
                                }
                              },
                              onJoinDiscussion: () {
                                getdiscussions();
                              },
                              onCancel: () {
                                setState(() {
                                  isactions = !isactions;
                                });
                              },
                            )
                      : Container(),

                  //? linear progress indicator
                  if (!(_controller?.value.isInitialized ?? false))
                    Positioned(
                      bottom: 40,
                      right: 0,
                      left: 0,
                      child: LinearProgressIndicator(
                        backgroundColor: Colors.black.withOpacity(0.8),
                        valueColor: AlwaysStoppedAnimation<Color>(
                          Colors.white.withOpacity(0.3),
                        ),
                      ),
                    ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _nameDetails() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        name != null
            ? Text(
                name!,
                style: const TextStyle(
                    color: Colors.white, fontWeight: FontWeight.w700),
              )
            : Container(),
        SizedBox(
          width: MediaQuery.of(context).size.width * 0.75,
          child: Row(
            children: <Widget>[
              Flexible(
                child: FittedBox(
                  fit: BoxFit.scaleDown,
                  child: Text.rich(
                    TextSpan(
                      style: const TextStyle(
                        color: Colors.white,
                      ),
                      children: [
                        TextSpan(
                          text: description != null
                              ? description!.length > maxChars
                                  ? description?.substring(0, maxChars)
                                  : description
                              : '',
                          style: const TextStyle(
                            fontWeight: FontWeight.normal,
                          ),
                        ),
                        TextSpan(
                          text:
                              AppLocalizations.of(context).translate('seemore'),
                          style: const TextStyle(
                            fontWeight: FontWeight.bold,
                          ),
                          recognizer: TapGestureRecognizer()
                            ..onTap = () {
                              _controller?.pause();
                              if (isProperties) {
                                propertyDetails(context,
                                    currencyController: currencyController,
                                    location: location,
                                    startsize: startsize,
                                    endsize: endsize,
                                    description: description,
                                    roomnumber: roomnumber,
                                    startprice: startprice,
                                    type: type,
                                    lat: lat,
                                    phone: phone,
                                    website: website,
                                    whatsapp: whatsapp,
                                    instagram: instagram,
                                    lng: lng);
                              } else {
                                adDetails(
                                  context,
                                  title: name ?? '',
                                  label: label,
                                  category: category,
                                  website: website,
                                  phone: phone,
                                  rating: rating,
                                  currencyController: currencyController,
                                  startprice: startprice,
                                  endPrice: endprice,
                                  price: price,
                                  video: video ?? '',
                                  location: location,
                                  instagram: instagram,
                                  description: description,
                                  lat: lat,
                                  lng: lng,
                                );
                              }
                            },
                        ),
                      ],
                    ),
                    maxLines: 1,
                  ),
                ),
              ),
            ],
          ),
        )
      ],
    );
  }

  Widget _columnDetailsWidget() {
    final Widget svg6 = SizedBox(
        width: 30,
        height: 25,
        child: SvgPicture.asset(
          'assets/media_icon.svg',
          semanticsLabel: 'Acme Logo',
          width: 13,
          height: 13,
          fit: BoxFit.fill,
          color: Colors.white,
        ));
    return Column(
      children: [
        // InkWell(
        //     onTap: () {
        //       _controller!.pause();
        //       authController.isLogged == true
        //           ? widget.time != null && widget.time != ""
        //               ? planController.additemtoplan(widget.date,
        //                   'maincategory', id!, widget.planId!, context,
        //                   time: widget.time)
        //               : widget.planId != null
        //                   ? listmyplandetails(context, widget.planId!,
        //                       "maincategory", id!, "yes")
        //                   : listmyplan(context, "maincategory", id!)
        //           : snackbar(AppLocalizations.of(context)
        //               .translate('Please login first'));
        //     },
        //     child: const Icon(
        //       Icons.add,
        //       color: Colors.white,
        //     )),
        const SizedBox(
          height: 20,
        ),
        InkWell(
            onTap: () {
              _controller!.pause();

              moreimagesrelated(context, id!, _controller!);
            },
            child: svg6),
        const SizedBox(
          height: 20,
        ),
        InkWell(
            onTap: () {
              getreviews();

              _controller!.pause();
            },
            child: SvgPicture.asset("assets/comments_insta.svg",
                color: Colors.white, width: 28)),
        const SizedBox(
          height: 20,
        ),
        GestureDetector(
            onTap: () {
              _controller!.pause();
              setState(() {
                isactions = !isactions;
              });
            },
            child: Container(
                color: Colors.transparent,
                child: const Icon(Icons.more_horiz,
                    color: Colors.white, size: 30)))
      ],
    );
  }

  Widget _propertiesColumnDetailsWidget() {
    final Widget svg6 = SizedBox(
        width: 30,
        height: 25,
        child: SvgPicture.asset(
          'assets/media_icon.svg',
          semanticsLabel: 'Acme Logo',
          width: 13,
          height: 13,
          fit: BoxFit.fill,
          color: Colors.white,
        ));
    final Widget svg5 = SizedBox(
        width: 22,
        height: 25,
        child: SvgPicture.asset(
          'assets/Group 7408.svg',
          semanticsLabel: 'Acme Logo',
          fit: BoxFit.cover,
        ));
    return Column(
      children: [
        InkWell(
            onTap: () {
              _controller!.pause();
              showInterest(context,
                  id: id!,
                  note: note,
                  isLogged: authController.isLogged == true);
            },
            child: svg5),
        const SizedBox(
          height: 20,
        ),
        _favoriteButton(),
        const SizedBox(
          height: 20,
        ),
        InkWell(
          onTap: () {
            moreimagesrelated(context, id!, _controller!);
          },
          child: svg6,
        ),
        const SizedBox(
          height: 20,
        ),
        GestureDetector(
            onTap: () {
              _controller!.pause();
              setState(() {
                isactions = !isactions;
              });

              //
            },
            child: Container(
                // height:30,
                // width:30,
                color: Colors.transparent,
                child: const Icon(Icons.more_horiz,
                    color: Colors.white, size: 30)))
      ],
    );
  }

  Widget _favoriteButton() {
    return authController.isLogged == true
        ? isfavouriate == 1
            ? InkWell(
                onTap: () async {
                  GeneralResponse sucessinformation =
                      await Api.removeluxuryfromfavourite(id!);
                  print(sucessinformation.code);
                  if (sucessinformation.code == "1") {
                    snackbar2(AppLocalizations.of(context)
                        .translate('remove from favourite successfuly'));
                    setState(() {
                      isfavouriate = 0;
                      // isfav = true;
                    });
                  } else {
                    snackbar(AppLocalizations.of(context).translate(
                        'Something went wrong, please try again later'));
                  }
                },
                child: const Icon(
                  Icons.favorite,
                  color: Colors.white,
                ))
            : InkWell(
                onTap: () async {
                  GeneralResponse sucessinformation =
                      await Api.addluxuryfavourite(id!);
                  print(sucessinformation.code);
                  if (sucessinformation.code == "1") {
                    snackbar2(AppLocalizations.of(context)
                        .translate('add to favourite successfuly'));
                    setState(() {
                      isfavouriate = 1;
                    });
                  } else {
                    snackbar(AppLocalizations.of(context).translate(
                        'Something went wrong, please try again later'));
                  }
                  // snackbar('This service has not been activated');
                },
                child: const Icon(
                  Icons.favorite_outline,
                  color: Colors.white,
                ))
        : InkWell(
            onTap: () async {
              snackbar(
                  AppLocalizations.of(context).translate('Please login first'));

              // snackbar('This service has not been activated');
            },
            child: const Icon(
              Icons.favorite_outline,
              color: Colors.white,
            ));
  }

  getdiscussions() async {
    progrsss(context);
    pr!.show();
    dis.clear();
    await Api.getmainCategorydiscussions(pagenumber, 20, id!).then((value) {
      value != null
          ? setState(() {
              dis.addAll(value.dscussions);
              code = value.code;
              msg = value.msg;
            })
          // ignore: unnecessary_statements
          : null;

      discussion(
        context,
        code: code,
        msg: msg,
        dis: dis,
        translated: _translated,
        lang: language.languagecode,
        text: text,
        isdiscussion: isdiscussion,
        index2: index2,
        onSend: () async {
          setState(() {
            isdiscussion = true;
          });

          GeneralResponse sucessinformation =
              await Api.sendmaincategorydiscussion(
                  id!, discussionController.text);

          print(sucessinformation.code);
          if (sucessinformation.code == "1") {
            snackbar2(AppLocalizations.of(context)
                .translate('add discussion successfuly'));
            setState(() {
              dis = [];
              discussionController.text = "";
              getdiscussions();
              discussionController.text = "";
              // isfav = true;
            });
          } else {
            snackbar(AppLocalizations.of(context)
                .translate('Something went wrong, please try again later'));
          }
          discussionController.text = "";
          setState(() {
            isdiscussion = false;
          });
          print("fssfsfstteeteteee");
          print(isdiscussion);
        },
      );
    });
  }

  getreviews() async {
    reviews.clear();
    progrsss(context);
    pr!.show();

    await Api.getreviews(id!, 'main_category').then((value) {
      value != null
          ? setState(() {
              reviews.addAll(value.reviews);
              code = value.code;
              msg = value.msg;

              // isload = true;
              pr!.hide();
            })
          // ignore: unnecessary_statements
          : null;
      review(context,
          code: code,
          msg: msg,
          reviews: reviews,
          greviewlink: greviewlink,
          greviewName: greviewName);
    });
  }

  getmorerelatedreels() async {
    await Api.getmainCategoryreels(id!).then((value) {
      value != null
          ? setState(() {
              videos.addAll(value.results);

              isload = true;
            })
          // ignore: unnecessary_statements
          : null;
    });
    //  return buildmoreimage();
  }
}
