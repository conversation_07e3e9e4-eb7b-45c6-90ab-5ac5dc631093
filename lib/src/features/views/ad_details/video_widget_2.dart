// import 'package:flutter/material.dart';
// import 'package:flutter_vlc_player/flutter_vlc_player.dart';
// import 'package:page/src/features/models/video_model.dart';
//
// class VideoListWidget extends StatefulWidget {
//   final VideoDetailsModel? video;
//
//   const VideoListWidget({Key? key, this.video}) : super(key: key);
//
//   @override
//   _VideoListWidgetState createState() => _VideoListWidgetState();
// }
//
// class _VideoListWidgetState extends State<VideoListWidget> {
//   VideoDetailsModel? get videoListData => widget.video;
//
//   late VlcPlayerController _videoPlayerController;
//
//   Future<void> initializePlayer() async {}
//
//   @override
//   void initState() {
//     super.initState();
//
//     _videoPlayerController = VlcPlayerController.network(
//       videoListData!.video!,
//       // 'https://media.w3.org/2010/05/sintel/trailer.mp4',
//       // hwAcc: HwAcc.full,
//       autoPlay: true,
//       options: VlcPlayerOptions(),
//     );
//   }
//
//   @override
//   void dispose() async {
//     super.dispose();
//     await _videoPlayerController.stopRendererScanning();
//     await _videoPlayerController.dispose();
//   }
//
//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//         body: Center(
//       child: SizedBox(
//         height: MediaQuery.of(context).size.height,
//         width: MediaQuery.of(context).size.width,
//         child: VlcPlayer(
//           controller: _videoPlayerController,
//           aspectRatio: 1,
//           // 16 / 9,
//           placeholder: const Center(child: CircularProgressIndicator()),
//         ),
//       ),
//     ));
//   }
// }
