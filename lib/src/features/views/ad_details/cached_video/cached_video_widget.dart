// import 'dart:developer';
//
// import 'package:flutter/material.dart';
// import 'package:flutter_cache_manager/flutter_cache_manager.dart';
// import 'package:page/src/features/models/video_model.dart';
// import 'package:page/src/features/views/home/<USER>';
// import 'package:video_player/video_player.dart';
//
// class CachedVideoWidget extends StatefulWidget {
//   final VideoDetailsModel? video;
//
//   const CachedVideoWidget({Key? key, required this.video}) : super(key: key);
//
//   @override
//   State<CachedVideoWidget> createState() => _CachedVideoWidgetState();
// }
//
// class _CachedVideoWidgetState extends State<CachedVideoWidget> {
//   //player controller
//   // VideoPlayerController? _controller;
//   int? get id => widget.video?.id;
//
//   VideoPlayerController? get _controller => videoPlayerController[id!];
//
//   @override
//   void initState() {
//     super.initState();
//     //initialize player
//     initializePlayer(widget.video!.video!);
//   }
//
// //Initialize Video Player
//   void initializePlayer(String url) async {
//     final fileInfo = await checkCacheFor(url);
//     if (fileInfo == null) {
//       // _controller = VideoPlayerController.networkUrl(Uri.parse(url));
//       _controller!.initialize().then((value) {
//         cachedForUrl(url);
//         setState(() {
//           _controller!.play();
//         });
//       });
//     } else {
//       log('PPPPPL from Local');
//       final file = fileInfo.file;
//       // _controller = VideoPlayerController.file(file);
//       _controller!.initialize().then((value) {
//         setState(() {
//           _controller!.play();
//         });
//       });
//     }
//   }
//
// //: check for cache
//   Future<FileInfo?> checkCacheFor(String url) async {
//     final FileInfo? value = await DefaultCacheManager().getFileFromCache(url);
//     return value;
//   }
//
// //:cached Url Data
//   void cachedForUrl(String url) async {
//     await DefaultCacheManager().getSingleFile(url).then((value) {
//       print('downloaded successfully done for $url');
//     });
//   }
//
// //:Dispose
//   @override
//   void dispose() {
//     if (_controller != null) {
//       _controller!.dispose();
//     }
//     super.dispose();
//   }
//
//   @override
//   Widget build(BuildContext context) {
//     return (_controller == null)
//         ? const Text('wait..')
//         : ((_controller!.value.isInitialized)
//             ? VideoPlayer(_controller!)
//             : const Text('Loading...'));
//   }
// }
