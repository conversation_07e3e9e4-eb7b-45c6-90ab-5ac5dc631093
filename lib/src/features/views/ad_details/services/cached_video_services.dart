import 'package:flutter_cache_manager/flutter_cache_manager.dart';
import 'package:page/src/core/utils/logger.dart';

class CachedVideoServices {
  var _cacheManager = DefaultCacheManager();

  //? check for cache
  Future<FileInfo?> checkVideoIfCached(String url) async {
    final FileInfo? value = await _cacheManager.getFileFromCache(url);
    return value;
  }

  //? cached Url Data
  Future<void> cacheVideo(String url) async {
    Log.w('Caching started for $url \n ${DateTime.now().toIso8601String()}');
    await _cacheManager.getSingleFile(url).then((value) {
      Log.f(
          'Downloaded successfully done for $url \n ${DateTime.now().toIso8601String()}');
    });
  }

  Future<void> disposeCache({String? url}) async {
    await _cacheManager.dispose();

    Log.e('Cache disposed successfully for $url');
  }

  //? re init cache
  Future<void> reInitCache() async {
    _cacheManager = DefaultCacheManager();

    Log.w('Cache re initialized successfully');
  }
}
