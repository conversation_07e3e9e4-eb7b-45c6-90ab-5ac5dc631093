// ignore_for_file: prefer_typing_uninitialized_variables

import 'dart:developer';

import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:google_cloud_translation/google_cloud_translation.dart';
import 'package:page/src/core/extensions/extensions.dart';
import 'package:page/src/features/views/ad_details/widgets/discussion.dart';
import 'package:page/src/features/views/ad_details/widgets/sections.dart';
import 'package:share_plus/share_plus.dart';
import 'package:video_player/video_player.dart';

import '../../../core/localization/app_localizations.dart';
import '../../../core/response/generalResponse.dart';
import '../../../core/services/api.dart';
import '../../../core/shared_widgets/back_button.dart';
import '../../../core/shared_widgets/reel_images.dart';
import '../../../core/shared_widgets/shared_widgets.dart';
import '../../../core/utils/dynamic_links.dart';
import '../../controllers/auth_controller.dart';
import '../../controllers/currency_controller.dart';
import '../../controllers/language_controller.dart';
import '../../models/discussions.dart';
import '../../models/reviews.dart';
import '../../models/video_model.dart';
import '../story/widgets/see_more.dart';
import 'widgets/ad_details.dart';
import 'widgets/riview.dart';

bool isactions = false;
TextEditingController note = TextEditingController();

TextEditingController discussioncontroller = TextEditingController();

class FromSharedLinkVideoWidget extends StatefulWidget {
  final int? id;
  final String? type;
  final int? planId;
  final date;
  final time;
  final bool isFeatured;

  const FromSharedLinkVideoWidget(
      {super.key,
      this.id,
      this.type,
      this.planId,
      this.date,
      this.time,
      this.isFeatured = false});

  @override
  State<StatefulWidget> createState() {
    return _VideoWidget();
  }
}

class _VideoWidget extends State<FromSharedLinkVideoWidget> {
  VideoPlayerController? _controller;
  int imageindex = 0;
  int? imageindex2;
  bool isdiscussion = false;
  Translation? _translation;
  TranslationModel? _translated;
  String? text;
  int index2 = -1;
  AuthController authController = AuthController();
  CurrencyController currencyController = CurrencyController();
  List<Categoryimages> images = [];
  List<Discussions> dis = [];
  List<Reviews> reviews = [];
  List<Categoryreels> videos = [];
  int pagenumber = 0;
  int? code;
  String msg = 'Loading';
  LanguageController language = LanguageController();
  bool isload = false;
  String? video;
  String? name;
  var rating;
  String? description,
      location,
      phone,
      website,
      instagram,
      greviewlink,
      greviewName,
      category,
      label;
  var startprice, endprice, price;
  double? lat, lng;
  int isfavouriate = 0;
  bool ismute = false;
  bool isfav = false;

  int? mainCategoryId;

  @override
  void initState() {
    super.initState();

    getdetails();
    getmorerelatedreels();
    authController.isloggedin();
    currencyController.getcuurentcurrency(context);
    print("type:ads_details");
    language.getcuurentlanguage();
    _translation = Translation(
      apiKey: 'AIzaSyCXOO147BdbuceLIl8Z8D5Jxru2Vjhqd4Q',
    );

    log('widget type ${widget.type} widget id ${widget.id} widget plan id ${widget.planId} widget date ${widget.date} widget time ${widget.time}');
  }

  void getdetails() async {
    log('idddd ${widget.id}');

    await Api.getmainCategorydetails(widget.id!).then((value) {
      value != null
          ? setState(() {
              print("value.results ${value.results['label']}");
              video = value.results['video'];
              name = value.results['name'];
              description = value.results['description'];
              location = value.results['location'] != null
                  ? value.results['location']['name']
                  : '';
              startprice = value.results['startprice'];
              endprice = value.results['endprice'];
              price = value.results['price'];
              lat = value.results['latitude'].toString().toDouble();
              lng = value.results['longitude'].toString().toDouble();
              isfavouriate = value.results['is_favorite'] == true ? 1 : 0;
              website = value.results['website'];
              instagram = value.results['instagram'];
              phone = value.results['phone'];
              rating = value.results['rating'] ?? 0;
              label = value.results['category']['name_en'];
              category = value.results['category']['name'];
              log('rareadser ${value.results['rating']}}');
              greviewlink = value.results['review_link'];
              greviewName = value.results['review_name'];

              print(name);
              print('picel url video ${video ?? ''}');
              try {
                _controller =
                    VideoPlayerController.networkUrl(Uri.parse(video!))
                      ..initialize().then((_) {
                        // Ensure the first frame is shown after the video is initialized, even before the play button has been pressed.
                        setState(() {});
                      });
                isload = true;
              } catch (e) {
                Navigator.pop(context);
                snackbar(
                    AppLocalizations.of(context).translate('error occured'));
                print(e);
              }
            })
          // ignore: unnecessary_statements
          : null;
      _controller!.play();
    });

    print("ooosofs");
    print(isload);
  }

  @override
  void dispose() {
    _controller?.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: () {
        print("out");
        if (_controller!.value.isPlaying) {
          _controller!.pause();
          _controller = null;
          _controller?.dispose();
        }
        setState(() {
          isactions = false;
        });
        return Future.value(true);
      },
      child: SafeArea(
        child: Scaffold(
          body: Stack(
            alignment: Alignment.bottomCenter,
            children: <Widget>[
              _controller != null
                  ? SizedBox(
                      height: MediaQuery.of(context).size.height,
                      width: MediaQuery.of(context).size.width,
                      child: _controller!.value.isInitialized
                          ? FittedBox(
                              fit: BoxFit.fill,
                              child: SizedBox(
                                  height: _controller!.value.size.height,
                                  width: _controller!.value.size.width,
                                  child: VideoPlayer(_controller!)))
                          : Container())
                  : Container(),
              Positioned(
                  left: 10,
                  right: 10,
                  top: 20,
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      const BackButtonWidget(),
                      InkWell(
                          onTap: () {
                            setState(() {
                              ismute = !ismute;
                              ismute
                                  ? _controller!.setVolume(0.0)
                                  : _controller!.setVolume(30.0);
                            });
                            // _controller.setVolume(0.0);
                          },
                          child: !ismute
                              ? const Icon(
                                  Icons.volume_down,
                                  color: Colors.white,
                                )
                              : const Icon(
                                  Icons.volume_off,
                                  color: Colors.white,
                                ))
                    ],
                  )),
              if (language.languagecode == 'ar')
                Positioned(bottom: 65, right: 30, child: _nameDetails())
              else
                Positioned(bottom: 65, left: 25, child: _nameDetails()),
              _controller != null
                  ? Padding(
                      padding: const EdgeInsetsDirectional.only(
                        bottom: 25,
                        top: 9,
                        start: 10,
                        end: 10,
                      ),
                      child: Row(
                        textDirection: TextDirection.ltr,
                        children: [
                          Expanded(
                            child: IconButton(
                                onPressed: () {
                                  _controller!.value.isPlaying
                                      ? _controller!.pause()
                                      : _controller!.play();
                                  setState(() {});
                                },
                                icon: Icon(
                                  _controller!.value.isPlaying
                                      ? Icons.pause
                                      : Icons.play_arrow,
                                  color: Colors.white,
                                )),
                          ),
                          Expanded(
                            flex: 7,
                            child: Directionality(
                              textDirection: TextDirection.ltr,
                              child: VideoProgressIndicator(
                                _controller!,
                                allowScrubbing: true,
                                colors: VideoProgressColors(
                                    playedColor:
                                        _controller!.value.isInitialized
                                            ? Colors.white
                                            : const Color(0xFF27b4a8),
                                    backgroundColor:
                                        Colors.white.withOpacity(0.44)),
                              ),
                            ),
                          ),
                          const SizedBox(width: 10),
                          Expanded(
                            child: FittedBox(
                              fit: BoxFit.scaleDown,
                              child: Text(
                                _controller!.value.duration
                                    .toString()
                                    .substring(
                                        _controller!.value.duration
                                                .toString()
                                                .indexOf(":") +
                                            1,
                                        _controller!.value.duration
                                            .toString()
                                            .indexOf(".")),
                                style: const TextStyle(color: Colors.white),
                                maxLines: 1,
                              ),
                            ),
                          ),
                          const SizedBox(width: 10),
                        ],
                      ),
                    )
                  : Container(),
              if (language.languagecode == 'ar')
                Positioned(bottom: 75, left: 20, child: _columnDetailsWidget())
              else
                Positioned(
                    bottom: 75, right: 20, child: _columnDetailsWidget()),
              isactions
                  ? AdSections(
                      lat: lat,
                      lng: lng,
                      onShare: () async {
                        try {
                          final String linkPathData =
                              '?id=${widget.id ?? 0}&type=${widget.type ?? ''}&planId=${widget.planId ?? 0}${widget.date != null ? '&date=${widget.date}' : ''}${widget.time != null ? '&time=${widget.time}' : ''}';
                          final dynamicLink =
                              await DynamicLinkHandler.createDynamicLink(
                            linkPathData,
                          );

                          log('sdfdfdsfsf ${dynamicLink.toString()}');

                          Share.share(dynamicLink.toString()).then((value) {
                            setState(() {
                              isactions = false;
                            });
                          });
                        } catch (e) {
                          log('Eerrrror ${e.toString()}');
                        }
                      },
                      isfavouriate: isfavouriate,
                      onAddFavourite: () async {
                        late int id;

                        if (widget.isFeatured) {
                          id = mainCategoryId!;
                        } else {
                          id = widget.id!;
                        }

                        GeneralResponse sucessinformation =
                            await Api.addmaincategoryfavourite(id);
                        print(sucessinformation.code);
                        if (sucessinformation.code == "1") {
                          snackbar2(AppLocalizations.of(context)
                              .translate('addtofav'));
                          setState(() {
                            isfavouriate = 1;
                          });
                        } else {
                          snackbar(AppLocalizations.of(context).translate(
                              'Something went wrong, please try again later'));
                        }
                      },
                      onRemoveFavourite: () async {
                        late int id;

                        if (widget.isFeatured) {
                          id = mainCategoryId!;
                        } else {
                          id = widget.id!;
                        }
                        GeneralResponse sucessinformation =
                            await Api.removemaincategoryfromfavourite(
                          id,
                        );
                        print(sucessinformation.code);
                        if (sucessinformation.code == "1") {
                          snackbar2(AppLocalizations.of(context)
                              .translate('remove from favourite successfuly'));
                          setState(() {
                            isfavouriate = 0;
                            // isfav = true;
                          });
                        } else {
                          snackbar(AppLocalizations.of(context).translate(
                              'Something went wrong, please try again later'));
                        }
                      },
                      onJoinDiscussion: () {
                        getdiscussions();
                      },
                      onCancel: () {
                        setState(() {
                          isactions = !isactions;
                        });
                      },
                    )
                  : Container(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _nameDetails() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        name != null
            ? Text(
                name!,
                style: const TextStyle(
                    color: Colors.white, fontWeight: FontWeight.w700),
              )
            : Container(),
        SizedBox(
          width: MediaQuery.of(context).size.width * 0.75,
          child: Row(
            children: <Widget>[
              Flexible(
                child: FittedBox(
                  fit: BoxFit.scaleDown,
                  child: Text.rich(
                    TextSpan(
                      style: const TextStyle(
                        color: Colors.white,
                      ),
                      children: [
                        TextSpan(
                          text: description != null
                              ? description!.length > maxChars
                                  ? description?.substring(0, maxChars)
                                  : description
                              : '',
                          style: const TextStyle(
                            fontWeight: FontWeight.normal,
                          ),
                        ),
                        TextSpan(
                          text:
                              AppLocalizations.of(context).translate('seemore'),
                          style: const TextStyle(
                            fontWeight: FontWeight.bold,
                          ),
                          recognizer: TapGestureRecognizer()
                            ..onTap = () {
                              _controller!.pause();
                              adDetails(
                                context,
                                label: label,
                                website: website,
                                phone: phone,
                                rating: rating,
                                currencyController: currencyController,
                                startprice: startprice,
                                endPrice: endprice,
                                price: price,
                                location: location,
                                instagram: instagram,
                                description: description,
                                video: video ?? '',
                                lat: lat,
                                lng: lng,
                                title: name!,
                              );
                            },
                        ),
                      ],
                    ),
                    maxLines: 1,
                  ),
                ),
              ),
            ],
          ),
        )
      ],
    );
  }

  Widget _columnDetailsWidget() {
    final Widget svg6 = SizedBox(
        width: 30,
        height: 25,
        child: SvgPicture.asset(
          'assets/media_icon.svg',
          semanticsLabel: 'Acme Logo',
          width: 13,
          height: 13,
          fit: BoxFit.fill,
          color: Colors.white,
        ));
    return Column(
      children: [
        // InkWell(
        //     onTap: () {
        //       _controller!.pause();
        //       authController.isLogged == true
        //           ? widget.time != null && widget.time != ""
        //               ? planController.additemtoplan(widget.date,
        //                   'maincategory', widget.id!, widget.planId!, context,
        //                   time: widget.time)
        //               : widget.planId != null
        //                   ? listmyplandetails(context, widget.planId!,
        //                       "maincategory", widget.id!, "yes")
        //                   : listmyplan(context, "maincategory", widget.id!)
        //           : snackbar(AppLocalizations.of(context)
        //               .translate('Please login first'));
        //     },
        //     child: const Icon(
        //       Icons.add,
        //       color: Colors.white,
        //     )),
        const SizedBox(
          height: 20,
        ),
        InkWell(
            onTap: () {
              late int id;

              if (widget.isFeatured) {
                id = mainCategoryId!;
              } else {
                id = widget.id!;
              }

              moreimagesrelated(context, id, _controller!);
            },
            child: svg6),
        const SizedBox(
          height: 20,
        ),
        InkWell(
            onTap: () {
              getreviews();
            },
            child: SvgPicture.asset("assets/comments_insta.svg",
                color: Colors.white, width: 28)),
        const SizedBox(
          height: 20,
        ),
        GestureDetector(
            onTap: () {
              _controller!.pause();
              setState(() {
                isactions = !isactions;
              });
            },
            child: Container(
                color: Colors.transparent,
                child: const Icon(Icons.more_horiz,
                    color: Colors.white, size: 30)))
      ],
    );
  }

  getdiscussions() async {
    progrsss(context);
    pr!.show();
    dis.clear();
    await Api.getmainCategorydiscussions(pagenumber, 20, widget.id!)
        .then((value) {
      value != null
          ? setState(() {
              dis.addAll(value.dscussions);
              code = value.code;
              msg = value.msg;
            })
          // ignore: unnecessary_statements
          : null;

      discussion(
        context,
        code: code,
        msg: msg,
        dis: dis,
        translated: _translated,
        lang: language.languagecode,
        text: text,
        isdiscussion: isdiscussion,
        index2: index2,
        onSend: () async {
          setState(() {
            isdiscussion = true;
          });

          GeneralResponse sucessinformation =
              await Api.sendmaincategorydiscussion(
                  widget.id!, discussioncontroller.text);
          print(sucessinformation.code);
          if (sucessinformation.code == "1") {
            snackbar2(AppLocalizations.of(context)
                .translate('add discussion successfuly'));
            setState(() {
              dis = [];
              discussioncontroller.text = "";
              getdiscussions();
              discussioncontroller.text = "";
              // isfav = true;
            });
          } else {
            snackbar(AppLocalizations.of(context)
                .translate('Something went wrong, please try again later'));
          }
          discussioncontroller.text = "";
          setState(() {
            isdiscussion = false;
          });
          print("fssfsfstteeteteee");
          print(isdiscussion);
        },
      );
    });
  }

  getreviews() async {
    reviews.clear();
    progrsss(context);
    pr!.show();

    late int id;
    if (widget.isFeatured) {
      id = mainCategoryId!;
    } else {
      id = widget.id!;
    }

    await Api.getreviews(id, 'main_category').then((value) {
      value != null
          ? setState(() {
              reviews.addAll(value.reviews);
              code = value.code;
              msg = value.msg;

              // isload = true;
              pr!.hide();
            })
          // ignore: unnecessary_statements
          : null;
      review(context,
          code: code,
          msg: msg,
          reviews: reviews,
          greviewlink: greviewlink,
          greviewName: greviewName);
    });
  }

  getmorerelatedreels() async {
    await Api.getmainCategoryreels(widget.id!).then((value) {
      value != null
          ? setState(() {
              videos.addAll(value.results);

              isload = true;
            })
          // ignore: unnecessary_statements
          : null;
    });
    //  return buildmoreimage();
  }
}
