import 'package:flutter/material.dart';
import 'package:page/src/features/views/holiday_home_details/widgets/details.dart';

import '../../../../core/localization/app_localizations.dart';
import '../../../../core/shared_widgets/shared_widgets.dart';
import '../../../controllers/auth_controller.dart';

class AdSections extends StatelessWidget {
  final int isfavouriate;
  final String? name;
  final VoidCallback onAddFavourite;
  final VoidCallback onRemoveFavourite;
  final VoidCallback onJoinDiscussion;
  final VoidCallback onCancel;
  final VoidCallback onShare;

  final lat;
  final lng;
  const AdSections(
      {Key? key,
      required this.isfavouriate,
      this.name,
      required this.onAddFavourite,
      required this.onRemoveFavourite,
      required this.onJoinDiscussion,
      required this.onShare,
      required this.lat,
      required this.lng,
      required this.onCancel})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    final authController = AuthController();

    return GestureDetector(
      onTap: onCancel,
      child: Container(
        height: MediaQuery.of(context).size.height,
        width: MediaQuery.of(context).size.width,
        color: Colors.grey.withOpacity(0.4),
        child: Container(
            padding: const EdgeInsets.only(
              left: 10,
              right: 10,
              bottom: 10,
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(12),
                    color: Colors.grey[100],
                  ),
                  width: MediaQuery.of(context).size.width,
                  child: Column(
                    children: [
                      const SizedBox(
                        height: 20,
                      ),
                      authController.isLogged == true
                          ? isfavouriate == 1
                              ? InkWell(
                                  onTap: onRemoveFavourite,
                                  child: Text(
                                    AppLocalizations.of(context)
                                        .translate("remove from favourite"),
                                    style: const TextStyle(color: Colors.red),
                                  ))
                              : InkWell(
                                  onTap: onAddFavourite,
                                  child: Text(
                                    AppLocalizations.of(context)
                                        .translate("add to favourite"),
                                    style: const TextStyle(
                                        color: Color(0xff007AFF), fontSize: 18),
                                  ))
                          : InkWell(
                              onTap: () async {
                                snackbar(AppLocalizations.of(context)
                                    .translate('Please login first'));
                              },
                              child: Text(
                                AppLocalizations.of(context)
                                    .translate("add to favourite"),
                                style: const TextStyle(
                                    color: Color(0xff007AFF), fontSize: 18),
                              )),
                      const SizedBox(
                        height: 10,
                      ),
                      Divider(
                        color: Colors.green[100],
                      ),
                      const SizedBox(
                        height: 10,
                      ),
                      InkWell(
                        onTap: onJoinDiscussion,
                        child: Container(
                            width: MediaQuery.of(context).size.width,
                            child: Center(
                                child: Text(
                              AppLocalizations.of(context)
                                  .translate('Joindiscussion'),
                              style: const TextStyle(
                                  color: Color(0xff007AFF), fontSize: 18),
                            ))),
                      ),
                      const SizedBox(
                        height: 10,
                      ),
                      Divider(
                        color: Colors.green[100],
                      ),
                      const SizedBox(
                        height: 10,
                      ),
                      InkWell(
                        onTap: () {
                          if (lat != null && lng != null) {
                            navigateToMap(context, lat: lat, lng: lng);
                          }
                          // lng != null
                          //     ? Navigator.of(context).push(MaterialPageRoute(
                          //         builder: (BuildContext context) =>
                          //             OpenMap(lat, lng)))
                          //     : snackbar(AppLocalizations.of(context)
                          //         .translate('no location found'));
                        },
                        child: Container(
                            width: MediaQuery.of(context).size.width,
                            child: Center(
                              child: Text(
                                  AppLocalizations.of(context)
                                      .translate('Viewlocationongooglemaps'),
                                  style: const TextStyle(
                                      color: Color(0xff007AFF), fontSize: 18)),
                            )),
                      ),
                      const SizedBox(
                        height: 10,
                      ),
                      Divider(
                        color: Colors.green[100],
                      ),
                      const SizedBox(
                        height: 10,
                      ),
                      InkWell(
                          onTap: onShare,
                          child: SizedBox(
                              width: MediaQuery.of(context).size.width,
                              child: Center(
                                  child: Text(
                                AppLocalizations.of(context).translate('Share'),
                                style: const TextStyle(
                                    color: Color(0xff007AFF), fontSize: 18),
                              )))),
                      const SizedBox(
                        height: 20,
                      ),
                    ],
                  ),
                ),
                const SizedBox(
                  height: 10,
                ),
                InkWell(
                    onTap: onCancel,
                    child: Container(
                      height: 50,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(12),
                        color: Colors.white,
                      ),
                      width: MediaQuery.of(context).size.width,
                      child: Center(
                        child: Text(
                          AppLocalizations.of(context).translate('Cancel'),
                          style: const TextStyle(
                              color: Color(0xff007AFF), fontSize: 18),
                        ),
                      ),
                    ))
              ],
            )),
      ),
    );
  }
}
