import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter_rating_bar/flutter_rating_bar.dart';
import 'package:flutter_svg/svg.dart';
import 'package:page/src/core/response/main_category_response.dart';
import 'package:page/src/core/utils/main_cached_image.dart';
import 'package:page/src/features/models/video_model.dart';
import 'package:page/src/features/views/ad_details/video_widget.dart';
import 'package:page/src/features/views/holiday_home_details/widgets/details.dart';
import 'package:provider/provider.dart';
import 'package:url_launcher/url_launcher_string.dart';

import '../../../../core/localization/app_language.dart';
import '../../../../core/localization/app_localizations.dart';
import '../../property_details/widgets/property_details.dart';

Future<void> adDetails(BuildContext context,
    {required String? label,
    required String title,
    required String? website,
    required String? phone,
    required rating,
    required currencyController,
    required startprice,
    required endPrice,
    required price,
    required String? location,
    required String? instagram,
    required String? description,
    required double? lat,
    required double? lng,
    String? image,
    required String video,
    String? category,
    bool fromVibes = false,
    int? id}) {
  final fromMap = id != null;
  final Widget svg = SizedBox(
      width: 25,
      height: 25,
      child: SvgPicture.asset(
        'assets/icons8-website.svg',
        semanticsLabel: 'Acme Logo',
        fit: BoxFit.cover,
      ));

  var lang =
      Provider.of<AppLanguage>(context, listen: false).appLocal.languageCode;

  final isHotels = label == 'Hotels';

  final isChalets = label == 'Chalets';

  return showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Padding(
          padding:
              EdgeInsets.only(bottom: MediaQuery.of(context).viewInsets.bottom),
          child: Container(
            height: MediaQuery.of(context).size.height * 0.70,
            decoration: BoxDecoration(
                color: const Color(0xffF5F6F7),
                borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(25.0),
                    topRight: Radius.circular(25.0)),
                border: Border.all(color: Colors.black, width: 1.0)),
            child: SingleChildScrollView(
                child: Column(
              children: [
                const SizedBox(
                  height: 10,
                ),
                Container(height: 5, width: 50, color: const Color(0xffD2D4D6)),
                const SizedBox(
                  height: 20,
                ),
                Center(
                    child: Text(
                  title,
                  // AppLocalizations.of(context).translate('More Details'),
                  style: const TextStyle(fontWeight: FontWeight.bold),
                )),
                Container(
                  padding: const EdgeInsets.all(15),
                  child: Container(
                    decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(10)),
                    child: Container(
                      padding: const EdgeInsets.all(15),
                      child: Column(
                        children: [
                          Row(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              if (image != null) ...[
                                SizedBox(
                                  height: 140,
                                  width: 100,
                                  child: ClipRRect(
                                    borderRadius: BorderRadius.circular(5),
                                    child: image.isNotEmpty
                                        ? MainCachedImage(
                                            image,
                                            fit: BoxFit.cover,
                                          )
                                        : Image.asset(
                                            'assets/Mask Group 2.png',
                                            height: 200,
                                            width: 100,
                                            fit: BoxFit.cover,
                                          ),
                                  ),
                                ),
                                const SizedBox(
                                  width: 20,
                                ),
                              ],
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Row(
                                      children: [
                                        Container(
                                            height: 35,
                                            width: 100,
                                            decoration: BoxDecoration(
                                                color: const Color(0xffF1F1F1),
                                                borderRadius:
                                                    BorderRadius.circular(7)),
                                            child: Center(
                                                child: FittedBox(
                                                    fit: BoxFit.scaleDown,
                                                    child: Text(
                                                      category ?? '',
                                                      style: const TextStyle(
                                                          fontWeight:
                                                              FontWeight.bold),
                                                    )))),
                                        const SizedBox(
                                          width: 20,
                                        ),
                                        if (isHotels)
                                          RatingBar.builder(
                                            onRatingUpdate: (value) {},
                                            initialRating: rating.toDouble(),
                                            minRating: 1,
                                            direction: Axis.horizontal,
                                            allowHalfRating: true,
                                            itemCount: 5,
                                            itemSize: 12.0,
                                            itemPadding:
                                                const EdgeInsets.only(left: 2),
                                            itemBuilder: (context, _) =>
                                                const Icon(Icons.star,
                                                    color: Color(0xffF2BA24),
                                                    size: 12),
                                            unratedColor:
                                                const Color(0xff556477),
                                          ),
                                      ],
                                    ),
                                    const SizedBox(
                                      height: 10,
                                    ),
                                    Row(
                                      children: [
                                        Text(
                                            '${AppLocalizations.of(context).translate('location')}:'),
                                        const SizedBox(
                                          width: 4,
                                        ),
                                        location != null
                                            ? Text(
                                                location,
                                                style: const TextStyle(
                                                    fontWeight:
                                                        FontWeight.bold),
                                              )
                                            : Container()
                                      ],
                                    ),
                                    const SizedBox(
                                      height: 10,
                                    ),
                                    label != 'Coffee shops' &&
                                            label != 'Destinations'
                                        ? SizedBox(
                                            width: MediaQuery.of(context)
                                                .size
                                                .width,
                                            child: Row(
                                              children: [
                                                label == 'Restaurants'
                                                    ? Text(
                                                        '${AppLocalizations.of(context).translate('Average price')}: ',
                                                      )
                                                    : label == 'Activities'
                                                        ? Text(
                                                            '${AppLocalizations.of(context).translate('Starting')}: ')
                                                        : label ==
                                                                'Holiday Homes'
                                                            ? Text(
                                                                '${AppLocalizations.of(context).translate('price')}: ')
                                                            : Text(
                                                                '${AppLocalizations.of(context).translate('Price Range')}:'),
                                                Expanded(
                                                  flex: 2,
                                                  child: Row(
                                                    children: [
                                                      startprice != null &&
                                                              label !=
                                                                  'Restaurants' &&
                                                              label !=
                                                                  'Properties'
                                                          ? Flexible(
                                                              flex: 2,
                                                              child: FittedBox(
                                                                fit: BoxFit
                                                                    .scaleDown,
                                                                child: Text(
                                                                  '${startprice.toString()} ${isHotels ? '- ${endPrice.toString()} ${currencyController.currency}' : currencyController.currency}',
                                                                  style: const TextStyle(
                                                                      fontWeight:
                                                                          FontWeight
                                                                              .bold),
                                                                ),
                                                              ),
                                                            )
                                                          : const SizedBox(),
                                                      if (label ==
                                                          'Restaurants')
                                                        Flexible(
                                                          child: Text(
                                                            '${startprice.toString()} ${currencyController.currency}',
                                                            style: const TextStyle(
                                                                fontWeight:
                                                                    FontWeight
                                                                        .bold),
                                                          ),
                                                        ),
                                                      if (label == 'Properties')
                                                        Flexible(
                                                          child: Text(
                                                            '${price.toString()} ${currencyController.currency}',
                                                            style: const TextStyle(
                                                                fontWeight:
                                                                    FontWeight
                                                                        .bold),
                                                          ),
                                                        ),
                                                      Text(
                                                        label == 'Restaurants' ||
                                                                label ==
                                                                    'Activities' ||
                                                                startprice ==
                                                                    null
                                                            ? " ${AppLocalizations.of(context).translate('Per person')}"
                                                            : AppLocalizations
                                                                    .of(context)
                                                                .translate(
                                                                    'per night'),
                                                        style: const TextStyle(
                                                            fontSize: 11,
                                                            fontWeight:
                                                                FontWeight
                                                                    .bold),
                                                      ),
                                                    ],
                                                  ),
                                                ),
                                              ],
                                            ),
                                          )
                                        : Container(),
                                    const SizedBox(
                                      height: 10,
                                    ),
                                    const Divider(
                                      color: Colors.black12,
                                    ),
                                    const SizedBox(
                                      height: 10,
                                    ),
                                    description != null
                                        ? Text(
                                            description,
                                            softWrap: true,
                                          )
                                        : Container(),
                                    const SizedBox(
                                      height: 10,
                                    ),
                                    Divider(
                                      color: Colors.grey[100],
                                    ),
                                    const SizedBox(
                                      height: 10,
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                          InkWell(
                              onTap: () async {
                                // ignore: unnecessary_statements
                                website != null
                                    ? launchUrlString(website.contains('https')
                                        ? website
                                        : "https://$website")
                                    : null;
                              },
                              child: Container(
                                  // padding: EdgeInsets.only(left: 20, right: 20),
                                  decoration: BoxDecoration(
                                      color: Colors.white,
                                      border: Border.all(color: Colors.black12),
                                      borderRadius: BorderRadius.circular(5)),
                                  child: Container(
                                    padding: const EdgeInsets.all(10),
                                    child: Column(
                                      children: [
                                        Row(
                                          children: [
                                            svg,
                                            const SizedBox(width: 10),
                                            website != null
                                                ? Text(
                                                    AppLocalizations.of(context)
                                                        .translate('website'),
                                                    style: const TextStyle(
                                                        fontWeight:
                                                            FontWeight.bold,
                                                        fontSize: 12),
                                                  )
                                                : Text(AppLocalizations.of(
                                                        context)
                                                    .translate(
                                                        'no website found')),
                                            const SizedBox(
                                              height: 10,
                                            ),
                                            const Spacer(),
                                            lang == 'en'
                                                ? const Icon(
                                                    Icons.keyboard_arrow_right,
                                                    color: Colors.black26)
                                                : const Icon(
                                                    Icons.keyboard_arrow_left,
                                                    color: Colors.black26)
                                          ],
                                        ),
                                      ],
                                    ),
                                  ))),
                          const SizedBox(
                            height: 10,
                          ),
                          InkWell(
                              onTap: () async {
                                instagram != null
                                    ? launchUrlString(
                                        instagram.contains('https')
                                            ? instagram
                                            : "https://$instagram")
                                    : null;
                              },
                              child: Container(
                                  // padding: EdgeInsets.only(left: 10, right: 10),
                                  decoration: BoxDecoration(
                                      color: Colors.white,
                                      border: Border.all(color: Colors.black12),
                                      borderRadius: BorderRadius.circular(5)),
                                  child: Container(
                                    padding: const EdgeInsets.all(10),
                                    child: Column(
                                      children: [
                                        Row(
                                          // mainAxisAlignment: MainAxisAlignment.center,
                                          // crossAxisAlignment: CrossAxisAlignment.center,
                                          children: [
                                            Image.asset(
                                              'assets/instagram.jpg',
                                              height: 20,
                                              width: 20,
                                            ),
                                            const SizedBox(width: 10),
                                            instagram != null
                                                ? Text(
                                                    AppLocalizations.of(context)
                                                        .translate(
                                                            'instagram page'),
                                                    style: const TextStyle(
                                                        fontWeight:
                                                            FontWeight.bold,
                                                        fontSize: 12),
                                                  )
                                                : Text(AppLocalizations.of(
                                                        context)
                                                    .translate(
                                                        'no instagram page found')),
                                            const SizedBox(
                                              height: 10,
                                            ),
                                            const Spacer(),
                                            lang == 'en'
                                                ? const Icon(
                                                    Icons.keyboard_arrow_right,
                                                    color: Colors.black26)
                                                : const Icon(
                                                    Icons.keyboard_arrow_left,
                                                    color: Colors.black26)
                                          ],
                                        ),
                                      ],
                                    ),
                                  ))),
                          const SizedBox(
                            height: 10,
                          ),
                          Center(
                              child: GestureDetector(
                                  onTap: () async {
                                    if (lat != null && lng != null) {
                                      navigateToMap(context,
                                          lat: lat, lng: lng);
                                    }
                                  },
                                  child: Container(
                                    height: 40,
                                    width: MediaQuery.of(context).size.width,
                                    decoration: BoxDecoration(
                                        color: const Color(0xffF3F7FB),
                                        borderRadius: BorderRadius.circular(5)),
                                    child: Container(
                                        padding: const EdgeInsets.all(5),
                                        child: Center(
                                            child: Text(
                                          AppLocalizations.of(context).translate(
                                              'View Location on Google maps'),
                                          style: const TextStyle(
                                              color: Color(0xff0852AB)),
                                        ))),
                                  ))),
                          const SizedBox(
                            height: 10,
                          ),
                          Center(
                              child: Container(
                            child: GestureDetector(
                                onTap: () async {
                                  try {
                                    final String? phoneNumber = phone
                                        ?.replaceAll(RegExp(r'[^0-9]'), '');

                                    log('Call Phone $phoneNumber');

                                    await callNumber(phoneNumber!);
                                    // await launchUrlString("tel:$phoneNumber");
                                  } catch (e) {
                                    log('Cannot open $e');
                                  }
                                },
                                child: Container(
                                  height: 40,
                                  width: MediaQuery.of(context).size.width,
                                  decoration: BoxDecoration(
                                      color: const Color(0xffF3F7FB),
                                      borderRadius: BorderRadius.circular(5)),
                                  child: Container(
                                      padding: const EdgeInsets.all(5),
                                      child: Center(
                                        child: Text(
                                          isChalets
                                              ? AppLocalizations.of(context)
                                                  .translate('Call For Booking')
                                              : '${AppLocalizations.of(context).translate('Call')} $category',
                                          style: const TextStyle(
                                              color: Color(0xff0852AB)),
                                        ),
                                      )),
                                )),
                          )),
                          if (fromMap || fromVibes) ...[
                            const SizedBox(
                              height: 10,
                            ),
                            OpenVideoDetails(
                              video: VideoModel(
                                id: id,
                                name: title,
                                description: description,
                                startprice: startprice,
                                endprice: endPrice,
                                price: price,
                                images: image,
                                video: video,
                                website: website,
                                instagram: instagram,
                                locationName: location,
                                category: MainCategoryModel(
                                  name: category,
                                  nameEn: label,
                                ),
                                photo: image,
                                label: label,
                                numberofstarts: 0,
                                latitude: lat,
                                longitude: lng,
                                phone: phone,
                                rating: rating,
                                privateDriverPrice: 0,
                              ),
                            )
                          ]
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            )),
          )));
}

class OpenVideoDetails extends StatelessWidget {
  final VideoModel? video;

  const OpenVideoDetails({
    Key? key,
    required this.video,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Center(
        child: GestureDetector(
            onTap: () {
              Navigator.of(context).push(MaterialPageRoute(
                  builder: (BuildContext context) =>
                      VideoViewWidget(video: video)));
            },
            child: Container(
              height: 40,
              width: MediaQuery.of(context).size.width,
              decoration: BoxDecoration(
                  color: const Color(0xFF27b4a8),
                  borderRadius: BorderRadius.circular(5)),
              child: Container(
                  padding: const EdgeInsets.all(10),
                  child: Center(
                      child: Text(
                    AppLocalizations.of(context).translate('Watch Video'),
                    style: const TextStyle(color: Colors.white),
                  ))),
            )));
  }
}
