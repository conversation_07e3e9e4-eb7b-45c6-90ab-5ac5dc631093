import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:page/src/core/shared_widgets/bottom_navgation_bar.dart';
import 'package:page/src/core/shared_widgets/shared_widgets.dart';
import 'package:page/src/core/utils/main_cached_image.dart';
import 'package:page/src/core/utils/print_services.dart';
import 'package:page/src/features/views/login/login.dart';
import 'package:page/src/features/views/register/register.dart';
import 'package:page/src/features/views/response_log/widgets/filter.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../../core/localization/app_localizations.dart';
import '../../../core/services/api.dart';
import '../../models/requests.dart';
import '../account/account.dart';
import '../response_log_details/response_log_details.dart';

String? categoryFilter;

class ResponseLog extends StatefulWidget {
  final bool fromBottomNav;

  const ResponseLog({Key? key, this.fromBottomNav = false}) : super(key: key);

  @override
  _ResponseLog createState() => _ResponseLog();
}

class _ResponseLog extends State<ResponseLog> {
  @override
  void initState() {
    super.initState();
    isLoggedIn();
    getallrequest(page, 20, null);
  }

  bool isLogged = false;

  void isLoggedIn() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();

    var islogged = prefs.getBool('is_logged');

    if (islogged == true) {
      print("DFfehehetet");

      setState(() {
        isLogged = true;
      });
    } else {
      setState(() {
        isLogged = false;
      });
    }
  }

  int page = 0;
  List<Requests> reqests = [];
  int code = 0, code2 = 0;
  String msg = 'loading', msg2 = 'loading';
  bool isselected = true;
  bool isselected1 = false;
  bool isselected2 = false;
  final RefreshController _refreshControllerwait =
      RefreshController(initialRefresh: false);

  getallrequest(int page, int size, [String? category]) async {
    reqests.clear();
    await Api.getallrequestsusers(page, 50, category).then((value) {
      value != null
          ? setState(() {
              code = value.code;
              msg = value.error;
              reqests.addAll(value.plans);
            })
          // ignore: unnecessary_statements
          : null;
    });
  }

  void _onRefresh() async {
    await Future.delayed(const Duration(milliseconds: 1000));
    reqests.clear();
    page = 1;
    getallrequest(page, 20, null);

    _refreshControllerwait.refreshCompleted();
  }

  void _onLoading() async {
    page = page + 2;
    getallrequest(page, 20);
    _refreshControllerwait.loadComplete();
  }

  Widget buildresponse() {
    return Expanded(
        child: ListView.builder(
      shrinkWrap: true,
      physics: const ScrollPhysics(),
      // scrollDirection: Axis.vertical,
      itemBuilder: (BuildContext ctxt, int index) {
        return GestureDetector(
          onTap: () {
            Navigator.of(context).push(MaterialPageRoute(
                builder: (BuildContext context) =>
                    ResponseLogDetails(reqests[index])));
          },
          child: Container(
              padding:
                  const EdgeInsets.only(top: 5, bottom: 5, right: 20, left: 20),
              child: Container(
                decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(20),
                    border:
                        Border.all(color: const Color(0xffF1F1F2), width: 1)),
                child: Container(
                  padding: const EdgeInsets.all(20),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      ClipRRect(
                          borderRadius: BorderRadius.circular(5),
                          child: MainCachedImage(
                            reqests[index].image ?? '',
                            height: 120,
                            width: 90,
                            fit: BoxFit.cover,
                          )),
                      const SizedBox(
                        width: 20,
                      ),
                      Expanded(
                          child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisAlignment: MainAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Container(
                                  height: 30,
                                  width: 100,
                                  color: const Color(0xffF1F1F1),
                                  child: Center(
                                      child: Text(
                                    AppLocalizations.of(context)
                                        .translate('holidayHomes'),
                                    style: const TextStyle(
                                        color: Color(0xff191C1F), fontSize: 12),
                                  ))),
                            ],
                          ),
                          const SizedBox(
                            height: 10,
                          ),
                          Text(reqests[index].name ?? '',
                              style: const TextStyle(
                                  fontWeight: FontWeight.bold,
                                  color: Color(0xff191C1F),
                                  fontSize: 17)),
                          const SizedBox(
                            height: 10,
                          ),
                          Row(children: [
                            Text(AppLocalizations.of(context)
                                .translate('Status')),
                            const SizedBox(
                              width: 5,
                            ),
                            Text(
                                AppLocalizations.of(context).translate(
                                    getStatusData<String?>(
                                            reqests[index].status ?? '',
                                            true) ??
                                        ''),
                                softWrap: true,
                                style: TextStyle(
                                    fontSize: 12,
                                    color: getStatusData<MaterialColor>(
                                        reqests[index].status ?? '', false))),
                          ]),
                          const SizedBox(
                            height: 10,
                          ),
                          // reqests[index].status == "pending"
                          //     ?
                          Text(
                              '${AppLocalizations.of(context).translate('Requested on')} ${reqests[index].requestedat ?? ''}',
                              style: const TextStyle(fontSize: 12))
                          // : Text(
                          //     '${AppLocalizations.of(context).translate('Approved on')} ${reqests[index].requestedat ?? ''}',
                          //     style: const TextStyle(fontSize: 12))
                        ],
                      ))
                    ],
                  ),
                ),
              )),
        );
      },
      itemCount: reqests.length,
    ));
  }

  @override
  Widget build(BuildContext context) {
    final Widget svg2 = SizedBox(
        width: 20,
        height: 20.0,
        child: SvgPicture.asset(
          'assets/filter.svg',
          semanticsLabel: 'Acme Logo',
          fit: BoxFit.cover,
        ));
    return WillPopScope(
        onWillPop: () {
          Navigator.of(context).push(
            MaterialPageRoute(builder: (context) => const Account()),
          );

          return Future.value(false);
        },
        child: SafeArea(
            child: Scaffold(
          bottomNavigationBar: CustomBottomNavgationBar(3),
          appBar: AppBar(
            automaticallyImplyLeading: widget.fromBottomNav ? false : true,
            backgroundColor: const Color(0xFF27b4a8),
            centerTitle: true,
            title: Text(
              AppLocalizations.of(context).translate('ResponsesLog'),
              style: TextStyle(
                fontFamily: isEng(context) ? 'Roboto' : 'Tajawal',
              ),
            ),
          ),
          body: Builder(builder: (context) {
            if (isLogged == false) {
              return Center(
                child: Container(
                  padding: const EdgeInsets.all(20),
                  child: SingleChildScrollView(
                      child: Column(
                    children: [
                      Text(
                          '${AppLocalizations.of(context).translate('Please login first')} !',
                          style: const TextStyle(fontSize: 20)),
                      const SizedBox(
                        height: 70,
                      ),
                      Center(
                          child: GestureDetector(
                              onTap: () async {
                                Navigator.of(context).push(MaterialPageRoute(
                                    builder: (BuildContext context) =>
                                        const Login()));
                              },
                              child: Container(
                                height: 50,
                                width: MediaQuery.of(context).size.width,
                                decoration: BoxDecoration(
                                    color: const Color(0xFF27b4a8),
                                    borderRadius: BorderRadius.circular(5)),
                                child: Container(
                                    padding: const EdgeInsets.all(5),
                                    child: Center(
                                        child: Text(
                                      AppLocalizations.of(context)
                                          .translate('Login'),
                                      style:
                                          const TextStyle(color: Colors.white),
                                    ))),
                              ))),
                      const SizedBox(
                        height: 20,
                      ),
                      Center(
                        child: Text(
                          AppLocalizations.of(context)
                              .translate('DontHaveAnAccountYet'),
                          style: const TextStyle(fontSize: 16),
                        ),
                      ),
                      Center(
                          child: GestureDetector(
                              onTap: () {
                                Navigator.push(
                                  context,
                                  MaterialPageRoute(builder: (context) {
                                    return FirstRegister();
                                  }),
                                );
                              },
                              child: Text(
                                AppLocalizations.of(context)
                                    .translate('RegisterFromHere'),
                                style: const TextStyle(
                                    fontSize: 16, color: Color(0xff0852AB)),
                              )))
                    ],
                  )),
                ),
              );
            }
            return Column(
              children: [
                const SizedBox(
                  height: 20,
                ),
                Container(
                    padding: const EdgeInsets.only(left: 20, right: 20),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          AppLocalizations.of(context)
                              .translate('All Requests'),
                          style: const TextStyle(color: Color(0xff51565B)),
                        ),
                        // GestureDetector(
                        //   onTap: () {
                        //     filterResponseLog(
                        //       context,
                        //       isselected: isselected,
                        //       setState: setState,
                        //       isselected1: isselected1,
                        //       isselected2: isselected2,
                        //       onFilter: () async {
                        //         log('KSKASKD ${categoryFilter}');
                        //
                        //         Navigator.of(context).pop();
                        //
                        //         getallrequest(page, 20, categoryFilter);
                        //       },
                        //     );
                        //     // filter(context);
                        //   },
                        //   child: svg2,
                        // )
                      ],
                    )),
                const SizedBox(
                  height: 20,
                ),
                code == 0 && msg == 'loading'
                    ? buildLoadingWidget()
                    : code == 1
                        ? reqests.isNotEmpty
                            ? buildresponse()
                            : nodatafound(AppLocalizations.of(context)
                                .translate('No Responses Log to show'))
                        : buildErrorWidget(msg)
              ],
            );
          }),
        )));
  }
}
