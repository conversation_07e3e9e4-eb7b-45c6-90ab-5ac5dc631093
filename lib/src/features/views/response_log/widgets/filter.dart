import 'package:flutter/material.dart';
import 'package:page/src/core/config/constants.dart';
import 'package:page/src/features/views/projects/projects_screen.dart';

import '../../../../core/localization/app_localizations.dart';

void filterResponseLog(
  BuildContext context, {
  required setState,
  required isselected,
  required isselected1,
  required isselected2,
  required onFilter,
}) {
  showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) =>
          StatefulBuilder(builder: (context, StateSetter stateSetter) {
            return Padding(
                padding: EdgeInsets.only(
                    bottom: MediaQuery.of(context).viewInsets.bottom),
                child: Container(
                  height: MediaQuery.of(context).size.height * 0.50,
                  decoration: BoxDecoration(
                      color: const Color(0xffF5F6F7),
                      borderRadius: const BorderRadius.only(
                          topLeft: Radius.circular(25.0),
                          topRight: Radius.circular(25.0)),
                      border: Border.all(color: Colors.black, width: 1.0)),
                  child: SingleChildScrollView(
                      child: Column(
                    children: [
                      const SizedBox(
                        height: 10,
                      ),
                      Container(
                          height: 5, width: 50, color: const Color(0xffD2D4D6)),
                      const SizedBox(
                        height: 20,
                      ),
                      Container(
                          padding: const EdgeInsets.only(left: 20, right: 20),
                          child: Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Container(
                                  height: 10,
                                  color: Colors.transparent,
                                ),
                                Text(
                                  AppLocalizations.of(context)
                                      .translate('Categories'),
                                  style: const TextStyle(
                                      fontWeight: FontWeight.bold),
                                ),
                                GestureDetector(
                                    onTap: () {
                                      stateSetter(() {
                                        isselected1 = false;
                                        isselected2 = false;
                                        isselected = false;
                                        categoryFilter = null;
                                        setState(() {});
                                      });
                                    },
                                    child: Text(
                                      AppLocalizations.of(context)
                                          .translate('reset'),
                                      style: const TextStyle(
                                          color: Color(0xff51565B)),
                                    ))
                              ])),
                      Container(
                        padding: const EdgeInsets.all(15),
                        child: Container(
                          decoration: BoxDecoration(
                              color: Colors.white,
                              borderRadius: BorderRadius.circular(10)),
                          child: Container(
                            padding: const EdgeInsets.all(15),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                InkWell(
                                    onTap: () {
                                      stateSetter(() {
                                        isselected1 = false;
                                        isselected2 = false;
                                        isselected = true;
                                        categoryFilter = null;
                                        setState(() {});
                                      });
                                    },
                                    child: Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.spaceBetween,
                                      children: [
                                        Text(AppLocalizations.of(context)
                                            .translate('All Requests')),
                                        isselected == true
                                            ? const Icon(
                                                Icons.check,
                                                color: Color(0xffD8B77F),
                                              )
                                            : Container()
                                      ],
                                    )),
                                const SizedBox(
                                  height: 10,
                                ),
                                const Divider(color: Colors.grey),
                                const SizedBox(
                                  height: 10,
                                ),
                                InkWell(
                                  onTap: () {
                                    stateSetter(() {
                                      isselected1 = true;
                                      isselected2 = false;
                                      isselected = false;
                                      categoryFilter = AppConstants
                                          .holidayHomesId
                                          .toString();
                                      setState(() {});
                                    });
                                  },
                                  child: Row(
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                    children: [
                                      Text(AppLocalizations.of(context)
                                          .translate('holidayhome')),
                                      isselected1 == true
                                          ? const Icon(
                                              Icons.check,
                                              color: Color(0xffD8B77F),
                                            )
                                          : Container()
                                    ],
                                  ),
                                ),
                                const SizedBox(
                                  height: 10,
                                ),
                                const Divider(color: Colors.grey),
                                const SizedBox(
                                  height: 10,
                                ),

                                // InkWell(
                                //     onTap: () {
                                //       stateSetter(() {
                                //         isselected1 = false;
                                //         isselected2 = true;
                                //         isselected = false;
                                //         categoryFilter = AppConstants
                                //             .carRentalsId
                                //             .toString();
                                //         setState(() {});
                                //       });
                                //     },
                                //     child: Row(
                                //       mainAxisAlignment:
                                //           MainAxisAlignment.spaceBetween,
                                //       children: [
                                //         Text(AppLocalizations.of(context)
                                //             .translate('carrental')),
                                //         isselected2 == true
                                //             ? const Icon(
                                //                 Icons.check,
                                //                 color: Color(0xffD8B77F),
                                //               )
                                //             : Container()
                                //       ],
                                //     )),
                                // const SizedBox(
                                //   height: 20,
                                // ),
                                Center(
                                    child: Container(
                                        // padding: EdgeInsets.only(right: 20, left: 20),
                                        child: GestureDetector(
                                            onTap: onFilter,
                                            child: Container(
                                              height: 50,
                                              width: MediaQuery.of(context)
                                                  .size
                                                  .width,
                                              decoration: BoxDecoration(
                                                  color:
                                                      const Color(0xFF27b4a8),
                                                  borderRadius:
                                                      BorderRadius.circular(
                                                          10)),
                                              child: Container(
                                                  padding:
                                                      const EdgeInsets.all(10),
                                                  child: Center(
                                                      child: Text(
                                                    AppLocalizations.of(context)
                                                        .translate(
                                                            'Apply Filter'),
                                                    style: const TextStyle(
                                                        color: Colors.white),
                                                  ))),
                                            )))),
                                const SizedBox(height: 20),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ],
                  )),
                ));
          }));
}
