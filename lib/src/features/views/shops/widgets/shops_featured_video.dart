import 'package:flutter/material.dart';
import 'package:page/src/core/utils/main_cached_image.dart';
import 'package:page/src/features/models/video_model.dart';
import 'package:provider/provider.dart';

import '../../../../core/localization/app_language.dart';
import '../../../controllers/auth_controller.dart';
import '../../../controllers/currency_controller.dart';
import '../../ad_details/video_widget.dart';

class ShopsFeaturedVideo extends StatelessWidget {
  final List<VideoModel> featuredvideo;
  final int? id;
  final String? date;
  final String? time;
  final CurrencyController currencyController;

  const ShopsFeaturedVideo({
    Key? key,
    required this.featuredvideo,
    required this.id,
    required this.date,
    required this.time,
    required this.currencyController,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    AuthController authController = AuthController();
    var lang =
        Provider.of<AppLanguage>(context, listen: false).appLocal.languageCode;

    final isEnglish = lang == 'en';

    return Container(
        padding: const EdgeInsets.only(left: 20, right: 20),
        height: 230,
        width: MediaQuery.of(context).size.width,
        child: ListView.builder(
          shrinkWrap: true,
          physics: const ClampingScrollPhysics(),
          scrollDirection: Axis.horizontal,
          itemBuilder: (BuildContext ctxt, int index) {
            Widget titleWidget() {
              return Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  SizedBox(
                    width: MediaQuery.of(context).size.width * 0.3,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          featuredvideo[index].name ?? '',
                          style: const TextStyle(
                              color: Colors.white,
                              fontWeight: FontWeight.w700,
                              fontSize: 12),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                        if (featuredvideo[index].locationName!.isNotEmpty)
                          Text(
                            featuredvideo[index].locationName ?? '',
                            style: const TextStyle(
                                color: Colors.white,
                                fontWeight: FontWeight.w700,
                                fontSize: 12),
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                          ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 5),
                ],
              );
            }

            return GestureDetector(
                onTap: () {
                  Navigator.of(context).push(MaterialPageRoute(
                      builder: (BuildContext context) => VideoViewWidget(
                          video: featuredvideo[index],
                          planId: id,
                          date: date,
                          time: time)));
                },
                child: Stack(
                  children: [
                    Padding(
                        padding: const EdgeInsets.only(right: 10),
                        child: Stack(children: [
                          ClipRRect(
                            borderRadius: BorderRadius.circular(5),
                            child: featuredvideo[index].photo != null
                                ? MainCachedImage(
                                    featuredvideo[index].photo!,
                                    height: 230,
                                    width: MediaQuery.of(context).size.width *
                                        0.35,
                                    fit: BoxFit.fitHeight,
                                  )
                                : Container(),
                          ),
                          GestureDetector(
                              onTap: () {
                                Navigator.of(context).push(MaterialPageRoute(
                                    builder: (BuildContext context) =>
                                        VideoViewWidget(
                                            video: featuredvideo[index],
                                            planId: id,
                                            date: date,
                                            time: time)));
                              },
                              child: Container(
                                height: 230,
                                width: MediaQuery.of(context).size.width * 0.35,
                                decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(5),
                                    gradient: LinearGradient(
                                      end: Alignment.bottomCenter,
                                      begin: Alignment.center,
                                      colors: <Color>[
                                        Colors.transparent,
                                        Colors.black.withOpacity(0.7)
                                      ],
                                    )),
                              )),
                          if (!isEnglish)
                            Positioned(
                                bottom: 12, right: 10, child: titleWidget())
                          else
                            Positioned(
                                bottom: 12, left: 10, child: titleWidget()),
                          // Positioned(
                          //     top: 10,
                          //     right: 10,
                          //     child: GestureDetector(
                          //       onTap: () {
                          //         authController.isLogged == true
                          //             ? time != null && time != ""
                          //                 ? planController.additemtoplan(
                          //                     date,
                          //                     'maincategory',
                          //                     featuredvideo[index].id!,
                          //                     id!,
                          //                     context,
                          //                     time: time)
                          //                 : id != null
                          //                     ? listmyplandetails(
                          //                         context,
                          //                         id!,
                          //                         "maincategory",
                          //                         featuredvideo[index].id!,
                          //                         "yes")
                          //                     : listmyplan(
                          //                         context,
                          //                         "maincategory",
                          //                         featuredvideo[index].id!)
                          //             : snackbar(AppLocalizations.of(context)
                          //                 .translate('Please login first'));
                          //       },
                          //       child: Container(
                          //           height: 30,
                          //           width: 30,
                          //           decoration: BoxDecoration(
                          //               color: const Color(0xff4d5e72)
                          //                   .withOpacity(0.5),
                          //               borderRadius: const BorderRadius.all(
                          //                   Radius.circular(30))),
                          //           child: const Center(
                          //               child: Icon(
                          //             Icons.add,
                          //             color: Colors.white,
                          //           ))),
                          //     ))
                        ]))
                  ],
                ));
          },
          itemCount: featuredvideo.length,
        ));
  }
}
