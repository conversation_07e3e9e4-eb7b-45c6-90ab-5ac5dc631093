import 'dart:developer';

import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../../core/localization/app_localizations.dart';
import '../../../core/shared_widgets/shared_widgets.dart';
import '../../bloc/auth_blok.dart';
import '../../repository/notification_controller.dart';
import '../home/<USER>';
import 'widgets/login_buttons.dart';
import 'widgets/login_fields.dart';

class Login extends StatefulWidget {
  const Login({super.key});

  @override
  _Login createState() => _Login();
}

bool isLoadingLogin = false;

class _Login extends State<Login> {
  @override
  void initState() {
    if (!kIsWeb) {
      FirebaseMessaging firebaseMessaging = FirebaseMessaging.instance;
      firebaseMessaging.getToken().then((value) {
        setState(() {
          token = value;
        });
      });
    }
    super.initState();
  }

  final GlobalKey<ScaffoldState> scaffoldKey = GlobalKey<ScaffoldState>();
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();

  TextEditingController emailController = TextEditingController(
      text: kDebugMode ? '<EMAIL>' : ''); //? Demo
  // TextEditingController(text: kDebugMode ? '<EMAIL>' : '');//? Live
  TextEditingController passwordController =
      TextEditingController(text: kDebugMode ? '123456' : ''); //? Demo
  // TextEditingController(text: kDebugMode ? 'Dubaipage9999' : '');//? Live

  TextEditingController phoneController = TextEditingController();

  NotificationController? notificationController;
  String? token;

  @override
  Widget build(BuildContext context) {
    final Widget svg1 = SizedBox(
      height: 150,
      width: 150,
      child: Image.asset(
        'assets/images/splash.png',
        width: 150,
      ),
    );

    return SafeArea(
        child: Scaffold(
            body: Container(
                padding: const EdgeInsets.only(left: 20, right: 20),
                child: SingleChildScrollView(
                    child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                      GestureDetector(
                        onTap: () {
                          Navigator.pop(context);
                        },
                        child: Container(
                          padding: const EdgeInsets.only(top: 10),
                          child: const Icon(Icons.arrow_back),
                        ),
                      ),
                      Center(
                        child: svg1,
                      ),
                      20.verticalSpace,
                      Text(
                        AppLocalizations.of(context).translate('EmailAdress'),
                        style: const TextStyle(fontSize: 13),
                      ),
                      10.verticalSpace,
                      Form(
                          key: _formKey,
                          child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                //! Login Fields (Email & Password) & Forget Password
                                LoginFields(
                                  emailController: emailController,
                                  passwordController: passwordController,
                                ),

                                //! Login Buttons
                                LoginButtons(
                                  onLogin: () => submit(
                                    emailController.text,
                                    passwordController.text,
                                  ),
                                ),
                              ])),
                      20.verticalSpace,
                    ]))),
            extendBody: true,
            resizeToAvoidBottomInset: true));
  }

  void submit(
    String email,
    String password,
  ) async {
    if (emailController.text == "") {
      snackbar(
          AppLocalizations.of(context).translate('Please Enter Your Email'));

      return;
    }
    RegExp(r'^.+@[a-zA-Z]+\.{1}[a-zA-Z]+(\.{0,1}[a-zA-Z]+)$')
        .hasMatch(emailController.text);
    if (passwordController.text.isEmpty) {
      snackbar(
          AppLocalizations.of(context).translate('Please Enter Your Password'));

      return;
    }
    if (passwordController.text.length < 6) {
      snackbar(AppLocalizations.of(context)
          .translate('The password must be six characters or more'));

      return;
    } else {
      setState(() {
        isLoadingLogin = true;
      });

      final Map<String, dynamic> successInformation =
          await bloc.login(email, password, token);

      if (successInformation['code'] == 1) {
        SharedPreferences prefs = await SharedPreferences.getInstance();

        String token = successInformation['data']['access_token'];
        prefs.setInt('user_id', successInformation['data']['id']);
        prefs.setString('token', token);
        prefs.setBool('is_logged', true);
        Navigator.pushReplacement(
          context,
          MaterialPageRoute(
            builder: (context) => const Home(),
          ),
        );
      } else {
        if (successInformation['msg'] == null) {
          snackbar(AppLocalizations.of(context)
              .translate('Something went wrong, please try again later'));
        } else {
          log('asdjansdjasdn ${successInformation}');
          snackbar(successInformation['msg']);
        }
      }
    }

    setState(() {
      isLoadingLogin = false;
    });
  }
}
