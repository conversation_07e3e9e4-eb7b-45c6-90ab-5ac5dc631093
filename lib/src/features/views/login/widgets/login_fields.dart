import 'package:flutter/material.dart';

import '../../../../core/localization/app_localizations.dart';
import 'forget_password/forget_password.dart';

class LoginFields extends StatefulWidget {
  final TextEditingController emailController;
  final TextEditingController passwordController;

  const LoginFields(
      {Key? key,
      required this.emailController,
      required this.passwordController})
      : super(key: key);

  @override
  State<LoginFields> createState() => _LoginFieldsState();
}

class _LoginFieldsState extends State<LoginFields> {
  bool _validatephone = false;
  bool isvisible = false;

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: <Widget>[
        Container(
            height: 50,
            decoration: BoxDecoration(
                color: Colors.white, borderRadius: BorderRadius.circular(3)),
            child: Container(
                decoration: BoxDecoration(
                    borderRadius: const BorderRadius.all(Radius.circular(5)),
                    border: Border.all(color: Colors.black12, width: 1.0)),
                child: Text<PERSON><PERSON><PERSON><PERSON>(
                  keyboardType: TextInputType.emailAdd<PERSON>,
                  controller: widget.emailController,
                  decoration: InputDecoration(
                      contentPadding:
                          const EdgeInsets.only(left: 20, right: 20, bottom: 5),
                      errorText:
                          _validatephone ? 'Please insert phone number' : null,
                      hintText:
                          AppLocalizations.of(context).translate('EmailAdress'),
                      hintStyle:
                          const TextStyle(color: Colors.grey, fontSize: 12),
                      border: InputBorder.none),
                ))),
        const SizedBox(
          height: 20,
        ),
        Text(
          AppLocalizations.of(context).translate('Password'),
          style: const TextStyle(
            fontSize: 13,
          ),
        ),
        const SizedBox(
          height: 10,
        ),
        Container(
            height: 50,
            decoration: BoxDecoration(
                color: Colors.white, borderRadius: BorderRadius.circular(3)),
            child: Container(
                decoration: BoxDecoration(
                    borderRadius: const BorderRadius.all(Radius.circular(5)),
                    border: Border.all(color: Colors.black12, width: 1.0)),
                child: TextFormField(
                  obscureText: !isvisible ? true : false,
                  controller: widget.passwordController,
                  decoration: InputDecoration(
                      suffixIconConstraints: const BoxConstraints(
                        minHeight: 50,
                      ),
                      contentPadding:
                          const EdgeInsets.only(left: 20, right: 20, top: 8),
                      errorText:
                          _validatephone ? 'Please insert password' : null,
                      hintText:
                          AppLocalizations.of(context).translate('Password'),
                      suffixIcon: Container(
                          padding: const EdgeInsets.only(
                              left: 10, right: 10, top: 5),
                          child: GestureDetector(
                              onTap: () {
                                setState(() {
                                  isvisible = !isvisible;
                                });
                              },
                              child: const Icon(Icons.remove_red_eye))),
                      hintStyle:
                          const TextStyle(color: Colors.grey, fontSize: 12),
                      border: InputBorder.none),
                ))),
        const SizedBox(
          height: 20,
        ),
        Center(
          child: GestureDetector(
            onTap: () {
              forgetpassword(context, setState, _validatephone);
            },
            child: Container(
                child: Text(
              AppLocalizations.of(context).translate('ForgotPassword'),
              style: const TextStyle(
                  fontSize: 16,
                  color: Color(0xff0852AB),
                  fontWeight: FontWeight.bold),
            )),
          ),
        ),
        const SizedBox(
          height: 40,
        ),
      ],
    );
  }
}
