import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:lottie/lottie.dart';

import '../../../../core/localization/app_localizations.dart';
import '../../register/register.dart';
import '../login.dart';

class LoginButtons extends StatelessWidget {
  final VoidCallback onLogin;

  const LoginButtons({Key? key, required this.onLogin}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final Widget svg2 = SizedBox(
        width: 30,
        height: 10,
        child: SvgPicture.asset(
          'assets/right-arrow.svg',
          semanticsLabel: 'Acme Logo',
          // fit: BoxFit.cover,
        ));

    return Column(
      children: <Widget>[
        !isLoadingLogin
            ? Container(
                height: 50,
                decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(3)),
                child: GestureDetector(
                    onTap: onLogin,
                    child: Container(
                        padding: const EdgeInsets.only(left: 20, right: 20),
                        decoration: BoxDecoration(
                            color: const Color(0xFF27b4a8),
                            borderRadius: BorderRadius.circular(10),
                            border:
                                Border.all(color: Colors.black12, width: 1.0)),
                        child: Center(
                            child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              AppLocalizations.of(context).translate('Login'),
                              style: const TextStyle(
                                  color: Colors.white, fontSize: 16),
                            ),
                            const SizedBox(
                              width: 15,
                            ),
                            svg2
                          ],
                        )))))
            : Center(
                child: Lottie.asset('assets/59218-progress-indicator.json',
                    height: 50, width: 50)),
        const SizedBox(
          height: 30,
        ),
        Center(
          child: Text(
            AppLocalizations.of(context).translate('DontHaveAnAccountYet'),
            style: const TextStyle(fontSize: 16),
          ),
        ),
        Center(
            child: GestureDetector(
                onTap: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(builder: (context) {
                      return FirstRegister();
                    }),
                  );
                },
                child: Text(
                  AppLocalizations.of(context).translate('RegisterFromHere'),
                  style:
                      const TextStyle(fontSize: 16, color: Color(0xff0852AB)),
                ))),
      ],
    );
  }
}
