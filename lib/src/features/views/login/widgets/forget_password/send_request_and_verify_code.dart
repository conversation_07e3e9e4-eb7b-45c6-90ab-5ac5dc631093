import 'package:flutter/material.dart';
import 'package:page/src/core/localization/app_localizations.dart';
import 'package:pin_code_fields/pin_code_fields.dart';
import 'package:progress_dialog_null_safe/progress_dialog_null_safe.dart';

import '../../../../../core/shared_widgets/shared_widgets.dart';
import '../../../../bloc/auth_blok.dart';
import '../../login.dart';
import 'forget_password.dart';

void verfiycode(BuildContext context, setState, validatePhone) {
  showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Padding(
            padding: EdgeInsets.only(
                bottom: MediaQuery.of(context).viewInsets.bottom),
            child: Container(
                height: MediaQuery.of(context).size.height * 0.50,
                decoration: new BoxDecoration(
                    color: Color(0xffF5F6F7),
                    borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(25.0),
                        topRight: Radius.circular(25.0)),
                    border: Border.all(color: Colors.black, width: 1.0)),
                child: Column(
                  children: [
                    SizedBox(
                      height: 10,
                    ),
                    Container(height: 5, width: 50, color: Color(0xffD2D4D6)),
                    SizedBox(
                      height: 20,
                    ),
                    Center(
                        child: Text(
                      AppLocalizations.of(context)
                          .translate('Enter 4digits OTP'),
                      style: TextStyle(fontWeight: FontWeight.bold),
                    )),
                    Container(
                      padding: EdgeInsets.all(15),
                      child: Container(
                        decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(10)),
                        child: Container(
                          padding: EdgeInsets.all(15),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(AppLocalizations.of(context).translate(
                                  'Pleaseenteryourregisteredemailinordertosendyoua4digitOTPtoresetyourpassword')),
                              SizedBox(
                                height: 20,
                              ),
                              Container(
                                padding: EdgeInsets.only(left: 60, right: 60),
                                height: 50,
                                child: Directionality(
                                  textDirection: TextDirection.ltr,
                                  child: PinCodeTextField(
                                    keyboardType: TextInputType.number,
                                    autoFocus: true,
                                    length: 4,
                                    obscureText: false,
                                    animationType: AnimationType.fade,
                                    pinTheme: PinTheme(
                                        shape: PinCodeFieldShape.box,
                                        activeFillColor: Color(0xffF1F1F1),
                                        activeColor: Color(0xffF1F1F1),
                                        inactiveColor: Color(0xffF1F1F1),
                                        inactiveFillColor: Color(0xffF1F1F1),
                                        selectedFillColor: Colors.white,
                                        selectedColor: Colors.white,
                                        fieldHeight: 50,
                                        fieldWidth: 50,
                                        // disabledColor:
                                        //     Color(0xff009695),
                                        borderWidth: 1),
                                    cursorColor: Color(0xff00579F),
                                    animationDuration:
                                        Duration(milliseconds: 300),
                                    enableActiveFill: true,
                                    controller: codeController,
                                    onCompleted: (_) => null,
                                    appContext: context,
                                    onChanged: (value) => () {},
                                  ),
                                ),
                              ),
                              SizedBox(height: 20),
                              Center(
                                  child: Container(
                                      // padding: EdgeInsets.only(right: 20, left: 20),
                                      child: GestureDetector(
                                          onTap: () async {
                                            verfiycodeuser(
                                                context,
                                                setState,
                                                validatePhone,
                                                emailresetpasswordController
                                                    .text,
                                                codeController.text);
                                            // requestforgetpassword(context);
                                          },
                                          child: Container(
                                            height: 50,
                                            width: MediaQuery.of(context)
                                                .size
                                                .width,
                                            decoration: BoxDecoration(
                                                color: Color(0xff233549),
                                                borderRadius:
                                                    BorderRadius.circular(10)),
                                            child: Container(
                                                padding: EdgeInsets.all(10),
                                                child: Center(
                                                    child: Text(
                                                  AppLocalizations.of(context)
                                                      .translate('Continue'),
                                                  style: TextStyle(
                                                      color: Colors.white),
                                                ))),
                                          )))),
                              SizedBox(height: 10),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ],
                )),
          ));
}

void sendrequestpassword(
  BuildContext context,
  setState,
  validatePhone,
  String email,
) async {
  final pr = ProgressDialog(context);

  print(emailresetpasswordController.text.isEmpty);
  if (emailresetpasswordController.text.isEmpty) {
    snackbar(
        AppLocalizations.of(context).translate('Please enter the  name field'));

    return;
  } else {
    setState(() {
      isLoadingLogin = true;
    });
    progrsss(context);
    pr.show();
    final Map<String, dynamic> successInformation =
        await bloc.sendrequestpassword(email);
    print("success");
    print(successInformation);
    pr.hide();
    if (successInformation['code'] == 1) {
      verfiycode(context, setState, validatePhone);
    } else {
      if (successInformation['msg'] == null) {
        snackbar(AppLocalizations.of(context)
            .translate('Something went wrong, please try again later'));
      } else {
        snackbar(successInformation['msg']);
      }
    }
  }

  setState(() {
    isLoadingLogin = false;
  });
}

void verfiycodeuser(BuildContext context, setState, validatePhone, String email,
    String code) async {
  print(emailresetpasswordController.text.isEmpty);
  if (emailresetpasswordController.text.isEmpty) {
    snackbar(
        AppLocalizations.of(context).translate('Please enter the  name field'));

    return;
  } else {
    setState(() {
      isLoadingLogin = true;
    });

    pr!.show();
    final Map<String, dynamic> successInformation =
        await bloc.verfiycode(email, code);
    print("success");
    print(successInformation);
    pr!.hide();
    if (successInformation['code'] == 1) {
      requestforgetpassword(context, setState, validatePhone);
    } else {
      if (successInformation['msg'] == null) {
        snackbar(AppLocalizations.of(context)
            .translate('Something went wrong, please try again later'));
      } else {
        snackbar(successInformation['msg']);
      }
    }
  }

  setState(() {
    isLoadingLogin = false;
  });
}
