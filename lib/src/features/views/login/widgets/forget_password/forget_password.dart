import 'package:flutter/material.dart';
import 'package:page/src/core/localization/app_localizations.dart';

import '../../../../../core/shared_widgets/shared_widgets.dart';
import '../../../../bloc/auth_blok.dart';
import '../../login.dart';
import 'send_request_and_verify_code.dart';

TextEditingController emailresetpasswordController = TextEditingController();
TextEditingController codeController = TextEditingController();
TextEditingController newpassword = TextEditingController();
TextEditingController confirmpasswordController = TextEditingController();

void forgetpassword(BuildContext context, setState, validatePhone) {
  showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      enableDrag: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Padding(
            padding: EdgeInsets.only(
                bottom: MediaQuery.of(context).viewInsets.bottom),
            child: Container(
                height: MediaQuery.of(context).size.height * 0.50,
                decoration: BoxDecoration(
                    color: const Color(0xffF5F6F7),
                    borderRadius: const BorderRadius.only(
                        topLeft: Radius.circular(25.0),
                        topRight: Radius.circular(25.0)),
                    border: Border.all(color: Colors.black, width: 1.0)),
                child: Column(
                  children: [
                    const SizedBox(
                      height: 10,
                    ),
                    Container(
                        height: 5, width: 50, color: const Color(0xffD2D4D6)),
                    const SizedBox(
                      height: 20,
                    ),
                    Center(
                        child: Text(
                      AppLocalizations.of(context).translate('ForgotPassword'),
                      style: const TextStyle(fontWeight: FontWeight.bold),
                    )),
                    Container(
                      padding: const EdgeInsets.all(15),
                      child: Container(
                        decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(10)),
                        child: Container(
                          padding: const EdgeInsets.all(15),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(AppLocalizations.of(context).translate(
                                  'Pleaseenteryourregisteredemailinordertosendyoua4digitOTPtoresetyourpassword')),
                              const SizedBox(
                                height: 20,
                              ),
                              Text(
                                AppLocalizations.of(context)
                                    .translate('EmailAdress'),
                                style: const TextStyle(
                                    fontSize: 16, fontWeight: FontWeight.bold),
                              ),
                              const SizedBox(
                                height: 10,
                              ),
                              Container(
                                  height: 60,
                                  decoration: BoxDecoration(
                                      color: Colors.white,
                                      borderRadius: BorderRadius.circular(3)),
                                  child: Container(
                                      decoration: BoxDecoration(
                                          borderRadius: const BorderRadius.all(
                                              Radius.circular(5)),
                                          border: Border.all(
                                              color: Colors.black12,
                                              width: 1.0)),
                                      child: TextFormField(
                                        controller:
                                            emailresetpasswordController,
                                        decoration: InputDecoration(
                                            contentPadding:
                                                const EdgeInsets.only(
                                                    left: 20,
                                                    right: 20,
                                                    top: 10),
                                            errorText: validatePhone
                                                ? '<EMAIL>'
                                                : null,
                                            hintText:
                                                AppLocalizations.of(context)
                                                    .translate('EmailAdress'),
                                            hintStyle: const TextStyle(
                                                color: Colors.grey,
                                                fontSize: 16),
                                            border: InputBorder.none),
                                      ))),
                              const SizedBox(height: 20),
                              Center(
                                  child: GestureDetector(
                                      onTap: () async {
                                        sendrequestpassword(
                                            context,
                                            setState,
                                            validatePhone,
                                            emailresetpasswordController.text);
                                      },
                                      child: Container(
                                        height: 50,
                                        width:
                                            MediaQuery.of(context).size.width,
                                        decoration: BoxDecoration(
                                            color: const Color(0xFF27b4a8),
                                            borderRadius:
                                                BorderRadius.circular(10)),
                                        child: Container(
                                            padding: const EdgeInsets.all(10),
                                            child: Center(
                                                child: Text(
                                              AppLocalizations.of(context)
                                                  .translate('Send4digitsOTP'),
                                              style: const TextStyle(
                                                  color: Colors.white),
                                            ))),
                                      ))),
                              const SizedBox(height: 20),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ],
                )),
          ));
}

void requestforgetpassword(BuildContext context, setState, validatephone) {
  showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Padding(
          padding:
              EdgeInsets.only(bottom: MediaQuery.of(context).viewInsets.bottom),
          child: Container(
            height: MediaQuery.of(context).size.height * 0.50,
            decoration: BoxDecoration(
                color: const Color(0xffF5F6F7),
                borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(25.0),
                    topRight: Radius.circular(25.0)),
                border: Border.all(color: Colors.black, width: 1.0)),
            child: SingleChildScrollView(
                child: Column(
              children: [
                const SizedBox(
                  height: 10,
                ),
                Container(height: 5, width: 50, color: const Color(0xffD2D4D6)),
                const SizedBox(
                  height: 20,
                ),
                Center(
                    child: Text(
                  AppLocalizations.of(context).translate('EnterNewPassword'),
                  style: const TextStyle(fontWeight: FontWeight.bold),
                )),
                Container(
                  padding: const EdgeInsets.all(15),
                  child: Container(
                    decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(10)),
                    child: Container(
                      padding: const EdgeInsets.all(15),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(AppLocalizations.of(context).translate(
                              'Enter a new password that is easy to remember')),
                          const SizedBox(
                            height: 20,
                          ),
                          Text(
                            AppLocalizations.of(context)
                                .translate('New Password'),
                            style: const TextStyle(
                                fontSize: 16, color: Color(0xff8B959E)),
                          ),
                          const SizedBox(
                            height: 10,
                          ),
                          Container(
                              height: 40,
                              decoration: BoxDecoration(
                                  color: Colors.white,
                                  borderRadius: BorderRadius.circular(3)),
                              child: Container(
                                  decoration: BoxDecoration(
                                      borderRadius: const BorderRadius.all(
                                          Radius.circular(5)),
                                      border: Border.all(
                                          color: Colors.black12, width: 1.0)),
                                  child: TextFormField(
                                    obscureText: true,
                                    controller: newpassword,
                                    decoration: InputDecoration(
                                        contentPadding: const EdgeInsets.only(
                                            left: 20, right: 20, bottom: 10),
                                        errorText: validatephone
                                            ? 'New Password'
                                            : null,
                                        hintText: AppLocalizations.of(context)
                                            .translate('New Password'),
                                        hintStyle: const TextStyle(
                                            color: Color(0xffB7B7B7),
                                            fontSize: 16),
                                        border: InputBorder.none),
                                  ))),
                          const SizedBox(
                            height: 20,
                          ),
                          Text(
                            AppLocalizations.of(context)
                                .translate('confirmNewPassword'),
                            style: const TextStyle(
                                fontSize: 16, color: Color(0xff8B959E)),
                          ),
                          const SizedBox(
                            height: 10,
                          ),
                          Container(
                              height: 40,
                              decoration: BoxDecoration(
                                  color: Colors.white,
                                  borderRadius: BorderRadius.circular(3)),
                              child: Container(
                                  decoration: BoxDecoration(
                                      borderRadius: const BorderRadius.all(
                                          Radius.circular(5)),
                                      border: Border.all(
                                          color: Colors.black12, width: 1.0)),
                                  child: TextFormField(
                                    obscureText: true,
                                    controller: confirmpasswordController,
                                    decoration: InputDecoration(
                                        contentPadding: const EdgeInsets.only(
                                            left: 20, right: 20, bottom: 10),
                                        errorText: validatephone
                                            ? 'Confirm Password'
                                            : null,
                                        hintText: AppLocalizations.of(context)
                                            .translate('confirmNewPassword'),
                                        hintStyle: const TextStyle(
                                            color: Color(0xffB7B7B7),
                                            fontSize: 16),
                                        border: InputBorder.none),
                                  ))),
                          const SizedBox(height: 20),
                          Center(
                              child: Container(
                                  // padding: EdgeInsets.only(right: 20, left: 20),
                                  child: GestureDetector(
                                      onTap: () async {
                                        resetpassword(
                                            context,
                                            setState,
                                            emailresetpasswordController.text,
                                            newpassword.text,
                                            confirmpasswordController.text);

                                        // _submit(rate.toString(), _comment.text);
                                      },
                                      child: Container(
                                        height: 50,
                                        width:
                                            MediaQuery.of(context).size.width,
                                        decoration: BoxDecoration(
                                            color: const Color(0xFF27b4a8),
                                            borderRadius:
                                                BorderRadius.circular(10)),
                                        child: Container(
                                            padding: const EdgeInsets.all(10),
                                            child: Center(
                                                child: Text(
                                              AppLocalizations.of(context)
                                                  .translate('Reset Password'),
                                              style: const TextStyle(
                                                  color: Colors.white),
                                            ))),
                                      )))),
                          const SizedBox(height: 20),
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            )),
          )));
}

void resetsuccess(BuildContext context) {
  showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Padding(
          padding:
              EdgeInsets.only(bottom: MediaQuery.of(context).viewInsets.bottom),
          child: Container(
            height: MediaQuery.of(context).size.height * 0.50,
            decoration: BoxDecoration(
                color: const Color(0xffF5F6F7),
                borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(25.0),
                    topRight: Radius.circular(25.0)),
                border: Border.all(color: Colors.black, width: 1.0)),
            child: SingleChildScrollView(
                child: Column(
              children: [
                const SizedBox(
                  height: 10,
                ),
                Container(height: 5, width: 50, color: const Color(0xffD2D4D6)),
                const SizedBox(
                  height: 20,
                ),
                Container(
                  padding: const EdgeInsets.all(15),
                  child: Container(
                    decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(10)),
                    child: Container(
                      padding: const EdgeInsets.only(top: 30),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          Container(
                            height: 40,
                            width: 40,
                            decoration: const BoxDecoration(
                                borderRadius:
                                    BorderRadius.all(Radius.circular(40)),
                                color: Colors.green),
                            child: const Center(
                              child: Icon(
                                Icons.check,
                                color: Colors.white,
                              ),
                            ),
                          ),
                          const SizedBox(
                            height: 20,
                          ),
                          Text(
                            AppLocalizations.of(context)
                                .translate('passwordChangedsuccessfullly'),
                            style: const TextStyle(
                                fontWeight: FontWeight.bold, fontSize: 16),
                          ),
                          const SizedBox(
                            height: 20,
                          ),
                          Text(
                            AppLocalizations.of(context)
                                .translate('pleaseusenewpasswordtologin'),
                            style: const TextStyle(
                                fontSize: 13, color: Color(0xff51565B)),
                          ),
                          const SizedBox(
                            height: 30,
                          ),
                          Center(
                              child: Container(
                                  padding: const EdgeInsets.only(
                                      right: 20, left: 20),
                                  child: GestureDetector(
                                      onTap: () async {
                                        // submit(emailController.text,
                                        //     passwordController.text);
                                        Navigator.of(context).pushReplacement(
                                            MaterialPageRoute(
                                                builder:
                                                    (BuildContext context) =>
                                                        const Login()));
                                        // verfiycode(context);
                                        // _submit(rate.toString(), _comment.text);
                                      },
                                      child: Container(
                                        height: 50,
                                        width:
                                            MediaQuery.of(context).size.width,
                                        decoration: BoxDecoration(
                                            color: const Color(0xFF27b4a8),
                                            borderRadius:
                                                BorderRadius.circular(10)),
                                        child: Container(
                                            padding: const EdgeInsets.all(10),
                                            child: Center(
                                                child: Text(
                                              AppLocalizations.of(context)
                                                  .translate('Login'),
                                              style: const TextStyle(
                                                  color: Colors.white),
                                            ))),
                                      )))),
                          const SizedBox(height: 20),
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            )),
          )));
}

void resetpassword(BuildContext context, setState, String email,
    String password, String confirmpassword) async {
  if (emailresetpasswordController.text.isEmpty) {
    snackbar(
        AppLocalizations.of(context).translate('Please enter the  name field'));

    return;
  }

  if (newpassword.text.isEmpty) {
    snackbar(AppLocalizations.of(context)
        .translate('Please enter the password field'));

    return;
  }
  if (confirmpasswordController.text.isEmpty) {
    snackbar(AppLocalizations.of(context)
        .translate('Please enter the confirm new password field'));

    return;
  }
  if (confirmpasswordController.text != newpassword.text) {
    snackbar(AppLocalizations.of(context)
        .translate('password and  confirm new password not matched'));

    return;
  } else {
    setState(() {
      isLoadingLogin = true;
    });

    pr!.show();
    final Map<String, dynamic> successInformation =
        await bloc.resetpassword(email, password);
    print("success");
    print(successInformation);
    pr!.hide();
    if (successInformation['code'] == 1) {
      resetsuccess(context);
    } else {
      if (successInformation['msg'] == null) {
        snackbar(AppLocalizations.of(context)
            .translate('Something went wrong, please try again later'));
      } else {
        snackbar(successInformation['msg']);
      }
    }
  }

  setState(() {
    isLoadingLogin = false;
  });
}
