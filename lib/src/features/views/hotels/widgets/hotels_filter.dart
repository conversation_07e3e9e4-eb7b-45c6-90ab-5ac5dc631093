import 'package:flutter/material.dart';
import 'package:page/src/features/controllers/content_controller.dart';

import '../../../../core/localization/app_localizations.dart';
import '../../../controllers/currency_controller.dart';
import '../../../models/locations.dart';
import '../hotels.dart';

void filterHotels(
  BuildContext context, {
  required VoidCallback onApply,
  required ContentController contentController,
  required CurrencyController currencyController,
}) {
  hotelsF_id.clear();
  showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) =>
          StatefulBuilder(builder: (context, StateSetter stateSetter) {
            return Padding(
                padding: EdgeInsets.only(
                    bottom: MediaQuery.of(context).viewInsets.bottom),
                child: Container(
                  height: MediaQuery.of(context).size.height * 0.7,
                  decoration: BoxDecoration(
                      color: const Color(0xffF5F6F7),
                      borderRadius: const BorderRadius.only(
                          topLeft: Radius.circular(25.0),
                          topRight: Radius.circular(25.0)),
                      border: Border.all(color: Colors.black, width: 1.0)),
                  child: SingleChildScrollView(
                      child: Column(
                    children: [
                      const SizedBox(
                        height: 10,
                      ),
                      Container(
                          height: 5, width: 50, color: const Color(0xffD2D4D6)),
                      const SizedBox(
                        height: 20,
                      ),
                      Container(
                          padding: const EdgeInsets.only(left: 20, right: 20),
                          child: Stack(
                            children: [
                              Align(
                                alignment:
                                    AppLocalizations.of(context).locale ==
                                            const Locale('ar')
                                        ? Alignment.centerLeft
                                        : Alignment.centerRight,
                                child: GestureDetector(
                                    onTap: () {
                                      stateSetter(() {
                                        hotelsCurrentvalue2 = null;
                                        hotelsStarts.clear();
                                        hotelsCurrentvalue4 = null;
                                        hotelsF_id.clear();

                                        hotelsCurrentRangeValues = RangeValues(
                                            hotelsMinprice, hotelsMaxprice);
                                      });
                                    },
                                    child: Text(
                                      AppLocalizations.of(context)
                                          .translate('reset'),
                                      style: const TextStyle(
                                          color: Color(0xff51565B)),
                                    )),
                              ),
                              Center(
                                child: Text(
                                  AppLocalizations.of(context)
                                      .translate('filter'),
                                  style: const TextStyle(
                                      fontWeight: FontWeight.bold),
                                  textAlign: TextAlign.center,
                                ),
                              )
                            ],
                          )),
                      Container(
                        padding: const EdgeInsets.all(15),
                        child: Container(
                          decoration: BoxDecoration(
                              color: Colors.white,
                              borderRadius: BorderRadius.circular(10)),
                          child: Container(
                            padding: const EdgeInsets.all(15),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  AppLocalizations.of(context)
                                      .translate('location'),
                                  style: const TextStyle(fontSize: 13),
                                ),
                                const SizedBox(
                                  height: 10,
                                ),
                                ContentController.locations.isNotEmpty
                                    ? Container(
                                        height: 50,
                                        decoration: BoxDecoration(
                                          borderRadius:
                                              BorderRadius.circular(5),
                                          border: Border.all(
                                              color: Colors.black12,
                                              width: 1.0),
                                        ),
                                        child: DropdownButton<int>(
                                            isExpanded: true,
                                            hint: Padding(
                                              padding: const EdgeInsets.only(
                                                  left: 20, right: 20),
                                              child: Text(
                                                AppLocalizations.of(context)
                                                    .translate(
                                                        'Choose Location'),
                                                style: const TextStyle(
                                                    color: Color(0xffB7B7B7)),
                                              ),
                                            ),
                                            value: hotelsCurrentvalue2,
                                            underline: const SizedBox(),
                                            iconEnabledColor: Colors.black,
                                            items: ContentController.locations
                                                .map((Locations value) {
                                              return DropdownMenuItem<int>(
                                                value: value.id,
                                                child: Container(
                                                    padding:
                                                        const EdgeInsets.only(
                                                            left: 10,
                                                            right: 10),
                                                    child: Text(
                                                      value.name!,
                                                      style: const TextStyle(
                                                          fontSize: 16),
                                                    )),
                                              );
                                            }).toList(),
                                            onChanged: (value) {
                                              stateSetter(() {
                                                hotelsCurrentvalue2 = value;
                                              });
                                            }))
                                    : Container(),
                                const SizedBox(height: 10),
                                Text(
                                  AppLocalizations.of(context)
                                      .translate('Number of Stars'),
                                  style: const TextStyle(fontSize: 13),
                                ),
                                const SizedBox(height: 10),
                                Container(
                                  height: 60,
                                  child: ListView.builder(
                                    shrinkWrap: true,
                                    scrollDirection: Axis.horizontal,
                                    itemCount: 3,
                                    itemBuilder: (BuildContext context, int i) {
                                      return Container(
                                        margin: const EdgeInsets.all(5),
                                        child: InkWell(
                                          onTap: () {
                                            if (hotelsStarts.contains(i + 3)) {
                                              hotelsStarts.remove(i + 3);
                                            } else {
                                              hotelsStarts.add(i + 3);
                                            }
                                            stateSetter(() {});
                                          },
                                          child: Center(
                                            child: Card(
                                              color:
                                                  hotelsStarts.contains(i + 3)
                                                      ? const Color(0xff233549)
                                                      : Colors.white,
                                              child: Padding(
                                                padding:
                                                    const EdgeInsets.all(8.0),
                                                child: Row(
                                                  children: [
                                                    for (var j = 0;
                                                        j < i + 3;
                                                        j++) ...[
                                                      const Icon(
                                                        Icons.star_rate_rounded,
                                                        color: Colors.amber,
                                                        size: 18,
                                                      )
                                                    ]
                                                  ],
                                                ),
                                              ),
                                            ),
                                          ),
                                        ),
                                      );
                                    },
                                  ),
                                ),
                                if (hotelsMinprice < hotelsMaxprice) ...[
                                  const SizedBox(
                                    height: 10,
                                  ),
                                  Row(
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                    children: [
                                      Text(
                                        AppLocalizations.of(context)
                                            .translate('Price Range Per Day'),
                                        style: const TextStyle(fontSize: 13),
                                      ),
                                      Row(
                                        children: [
                                          contentController.minprice != null
                                              ? Text(
                                                  '${contentController.minprice}',
                                                  style: const TextStyle(
                                                      fontSize: 13,
                                                      color: Color(0xff51565B)))
                                              : Container(),
                                          contentController.maxprice != null
                                              ? Text(
                                                  '-${contentController.maxprice}',
                                                  style: const TextStyle(
                                                      fontSize: 13,
                                                      color: Color(0xff51565B)))
                                              : Container(),
                                          Text(
                                              ' ${currencyController.currency}',
                                              style: const TextStyle(
                                                  fontSize: 13,
                                                  color: Color(0xff51565B)))
                                        ],
                                      ),
                                    ],
                                  ),
                                  StatefulBuilder(builder:
                                      (context, StateSetter stateSetter) {
                                    return RangeSlider(
                                      activeColor: const Color(0xFFE2CBA2),
                                      inactiveColor: const Color(0xFF485E77)
                                          .withOpacity(0.9),
                                      values: hotelsCurrentRangeValues!,
                                      min: hotelsMinprice,
                                      max: hotelsMaxprice,
                                      divisions: 50,
                                      labels: RangeLabels(
                                        hotelsCurrentRangeValues!.start
                                            .round()
                                            .toString(),
                                        hotelsCurrentRangeValues!.end
                                            .round()
                                            .toString(),
                                      ),
                                      onChanged: (RangeValues values) {
                                        stateSetter(() {
                                          hotelsCurrentRangeValues = values;
                                        });
                                      },
                                    );
                                  }),
                                  const SizedBox(
                                    height: 10,
                                  ),
                                ],
                                Text(
                                  AppLocalizations.of(context)
                                      .translate('Feature'),
                                  style: const TextStyle(fontSize: 13),
                                ),
                                const SizedBox(
                                  height: 10,
                                ),
                                StatefulBuilder(
                                  builder: (BuildContext context, setStateF) {
                                    return Container(
                                      height: 60,
                                      child: ListView.builder(
                                        shrinkWrap: true,
                                        scrollDirection: Axis.horizontal,
                                        itemCount:
                                            ContentController.features.length,
                                        itemBuilder:
                                            (BuildContext context, int i) {
                                          return Container(
                                              margin: const EdgeInsets.all(5),
                                              child: InkWell(
                                                onTap: () {
                                                  if (hotelsF_id.contains(
                                                      ContentController
                                                          .features[i].id)) {
                                                    hotelsF_id.remove(
                                                        ContentController
                                                            .features[i].id);
                                                  } else {
                                                    hotelsF_id.add(
                                                        ContentController
                                                            .features[i].id!);
                                                  }
                                                  setStateF(() {});
                                                },
                                                child: Card(
                                                  color: hotelsF_id.contains(
                                                          ContentController
                                                              .features[i].id)
                                                      ? const Color(0xff233549)
                                                      : Colors.white,
                                                  child: Center(
                                                    child: Padding(
                                                      padding:
                                                          const EdgeInsets.all(
                                                              8.0),
                                                      child: Text(
                                                        ContentController
                                                            .features[i].name!,
                                                        style: TextStyle(
                                                            color: hotelsF_id.contains(
                                                                    ContentController
                                                                        .features[
                                                                            i]
                                                                        .id)
                                                                ? Colors.white
                                                                : Colors.black),
                                                      ),
                                                    ),
                                                  ),
                                                ),
                                              ));
                                        },
                                      ),
                                    );
                                  },
                                ),
                                const SizedBox(height: 20),
                                Center(
                                    child: GestureDetector(
                                        onTap: onApply,
                                        child: Container(
                                          height: 50,
                                          width:
                                              MediaQuery.of(context).size.width,
                                          decoration: BoxDecoration(
                                              color: const Color(0xFF27b4a8),
                                              borderRadius:
                                                  BorderRadius.circular(10)),
                                          child: Container(
                                              padding: const EdgeInsets.all(10),
                                              child: Center(
                                                  child: Text(
                                                AppLocalizations.of(context)
                                                    .translate('Apply Filter'),
                                                style: const TextStyle(
                                                    color: Colors.white),
                                              ))),
                                        ))),
                                const SizedBox(height: 20),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ],
                  )),
                ));
          }));
}
