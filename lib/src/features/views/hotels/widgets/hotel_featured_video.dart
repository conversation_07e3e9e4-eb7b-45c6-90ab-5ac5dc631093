import 'package:flutter/material.dart';
import 'package:page/src/core/utils/main_cached_image.dart';
import 'package:page/src/features/controllers/currency_controller.dart';
import 'package:page/src/features/models/video_model.dart';
import 'package:provider/provider.dart';

import '../../../../core/localization/app_language.dart';
import '../../../../core/localization/app_localizations.dart';
import '../../ad_details/video_widget.dart';

class HotelFeaturedVideo extends StatelessWidget {
  final VideoModel featuredvideo;
  final CurrencyController currencyController;
  final VoidCallback onAdd;
  final time;
  final date;
  final id;

  const HotelFeaturedVideo({
    Key? key,
    required this.featuredvideo,
    required this.currencyController,
    required this.onAdd,
    required this.time,
    required this.date,
    required this.id,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    var lang =
        Provider.of<AppLanguage>(context, listen: false).appLocal.languageCode;

    final isEnglish = lang == 'en';

    Widget titleWithPriceWidget() {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          featuredvideo.name != null
              ? SizedBox(
                  width: MediaQuery.of(context).size.width * 0.3,
                  child: Text(
                    featuredvideo.name!,
                    style: const TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.w700,
                        fontSize: 12),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                )
              : Container(),
          const SizedBox(height: 5),
          if (isEnglish)
            Text(
              '${AppLocalizations.of(context).translate('Price Range')}\n${featuredvideo.startprice!.toStringAsFixed(0)}-${featuredvideo.endprice!.toStringAsFixed(0)} ${currencyController.currency}',
              style: const TextStyle(color: Colors.white, fontSize: 10),
            )
          else
            Text(
              '${AppLocalizations.of(context).translate('Price Range')}\n${featuredvideo.endprice!.toStringAsFixed(0)}-${featuredvideo.startprice!.toStringAsFixed(0)} ${currencyController.currency}',
              style: const TextStyle(color: Colors.white, fontSize: 10),
            )
        ],
      );
    }

    return GestureDetector(
        onTap: () {
          Navigator.of(context).push(MaterialPageRoute(
              builder: (BuildContext context) => VideoViewWidget(
                  video: featuredvideo, planId: id, date: date, time: time)));
        },
        child: Stack(
          children: [
            Padding(
                padding: const EdgeInsets.only(right: 10),
                child: Stack(children: [
                  ClipRRect(
                    borderRadius: BorderRadius.circular(5),
                    child: featuredvideo.photo != null
                        ? MainCachedImage(
                            featuredvideo.photo!,
                            height: 230,
                            width: MediaQuery.of(context).size.width * 0.35,
                            fit: BoxFit.fitHeight,
                          )
                        : const SizedBox(),
                  ),
                  GestureDetector(
                      onTap: () {
                        Navigator.of(context).push(MaterialPageRoute(
                            builder: (BuildContext context) => VideoViewWidget(
                                video: featuredvideo,
                                planId: id,
                                date: date,
                                time: time)));
                      },
                      child: Container(
                        height: 230,
                        width: MediaQuery.of(context).size.width * 0.35,
                        decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(5),
                            gradient: LinearGradient(
                              end: Alignment.bottomCenter,
                              begin: Alignment.center,
                              colors: <Color>[
                                Colors.transparent,
                                Colors.black.withOpacity(0.7)
                              ],
                            )),
                      )),
                  if (!isEnglish)
                    Positioned(
                        bottom: 12, right: 10, child: titleWithPriceWidget())
                  else
                    Positioned(
                        bottom: 12, left: 7, child: titleWithPriceWidget()),
                  // Positioned(
                  //     top: 10,
                  //     right: 10,
                  //     child: GestureDetector(
                  //       onTap: onAdd,
                  //       child: Container(
                  //           height: 30,
                  //           width: 30,
                  //           decoration: BoxDecoration(
                  //               color: const Color(0xff4d5e72).withOpacity(0.5),
                  //               borderRadius: const BorderRadius.all(
                  //                   Radius.circular(30))),
                  //           child: const Center(
                  //               child: Icon(
                  //             Icons.add,
                  //             color: Colors.white,
                  //           ))),
                  //     ))
                ]))
          ],
        ));
  }
}
