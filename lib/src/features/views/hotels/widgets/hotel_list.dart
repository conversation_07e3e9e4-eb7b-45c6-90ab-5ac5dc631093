import 'package:flutter/material.dart';
import 'package:page/src/features/controllers/currency_controller.dart';
import 'package:provider/provider.dart';

import '../../../../core/localization/app_language.dart';
import '../../../../core/localization/app_localizations.dart';
import '../../../models/video_model.dart';

class HotelCard extends StatelessWidget {
  final VideoModel result;
  final CurrencyController currencyController;
  final VoidCallback onTap;
  final VoidCallback navigate;

  const HotelCard(
      {Key? key,
      required this.result,
      required this.currencyController,
      required this.onTap,
      required this.navigate})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    var lang =
        Provider.of<AppLanguage>(context, listen: false).appLocal.languageCode;

    final isEnglish = lang == 'en';

    Widget titleWithPrice() {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            result.name!,
            style: const TextStyle(
              color: Colors.white,
              fontWeight: FontWeight.w700,
              fontSize: 12,
            ),
          ),
          const SizedBox(height: 5),
          if (isEnglish)
            Text(
              '${AppLocalizations.of(context).translate('Price Range')}\n${result.startprice!.toStringAsFixed(0)}-${result.endprice!.toStringAsFixed(0)} ${currencyController.currency}',
              style: const TextStyle(color: Colors.white, fontSize: 12),
            )
          else
            Text(
              '${AppLocalizations.of(context).translate('Price Range')}\n${result.endprice!.toStringAsFixed(0)}-${result.startprice!.toStringAsFixed(0)} ${currencyController.currency}',
              style: const TextStyle(color: Colors.white, fontSize: 12),
            )
        ],
      );
    }

    return Column(
      children: [
        Stack(children: [
          GestureDetector(
            onTap: () => navigate(),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(5),
              child: Image.network(
                result.images ?? '',
                height: 288,
                width: MediaQuery.of(context).size.width * 0.45,
                fit: BoxFit.fitHeight,
              ),
            ),
          ),
          GestureDetector(
              onTap: () => navigate(),
              child: Container(
                height: 288,
                width: MediaQuery.of(context).size.width * 0.45,
                decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(5),
                    gradient: LinearGradient(
                      end: Alignment.bottomCenter,
                      begin: Alignment.center,
                      colors: <Color>[
                        Colors.transparent,
                        Colors.black.withOpacity(0.7)
                      ],
                    )),
              )),
          // Positioned(
          //     top: 10,
          //     right: 10,
          //     child: GestureDetector(
          //       onTap: onTap,
          //       child: Container(
          //           height: 30,
          //           width: 30,
          //           decoration: BoxDecoration(
          //               color: const Color(0xff4d5e72).withOpacity(0.5),
          //               borderRadius:
          //                   const BorderRadius.all(Radius.circular(30))),
          //           child: const Center(
          //               child: Icon(
          //             Icons.add,
          //             color: Colors.white,
          //           ))),
          //     )),
          if (!isEnglish)
            Positioned(bottom: 12, right: 10, child: titleWithPrice())
          else
            Positioned(bottom: 12, left: 10, child: titleWithPrice()),
        ]),
      ],
    );
  }
}
