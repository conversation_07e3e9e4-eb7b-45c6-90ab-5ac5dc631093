import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get_navigation/src/extension_navigation.dart';
import 'package:page/src/core/config/constants.dart';
import 'package:page/src/core/shared_widgets/main_featured_container.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

import '../../../core/localization/app_localizations.dart';
import '../../../core/services/api.dart';
import '../../../core/shared_widgets/shared_widgets.dart';
import '../../controllers/auth_controller.dart';
import '../../controllers/content_controller.dart';
import '../../controllers/currency_controller.dart';
import '../../controllers/plan_controller.dart';
import '../../models/video_model.dart';
import '../ad_details/video_widget.dart';
import '../home/<USER>/plans/plan_details_bottom_sheet.dart';
import '../home/<USER>/plans/plans_widgets.dart';
import 'widgets/hotel_featured_video.dart';
import 'widgets/hotel_list.dart';
import 'widgets/hotels_filter.dart';

int? hotelsCurrentvalue2;
int? hotelsCurrentvalue4;
List<int> hotelsF_id = [];
List<int> hotelsStarts = [];
double hotelsMinprice = 0;
double hotelsMaxprice = 1000;
RangeValues? hotelsCurrentRangeValues;

// ignore: must_be_immutable
class Hotels extends StatefulWidget {
  int? id;
  var time;
  int? itemid;
  var date;

  Hotels({this.id, this.time, this.itemid, this.date});

  @override
  _Hotels createState() => _Hotels();
}

class _Hotels extends State<Hotels> {
  final TextEditingController searchController = TextEditingController();
  List<VideoModel> results = [];
  List<VideoModel> featuredvideo = [];

  AuthController authController = AuthController();
  final RefreshController _refreshControllerwait =
      RefreshController(initialRefresh: false);
  int pagenumber = 1;

  int pageplan = 0;
  ContentController contentController = ContentController();
  PlanController planController = PlanController();
  CurrencyController currencyController = CurrencyController();

  int code2 = 0;
  String msg2 = 'loading';
  GlobalKey<ScaffoldState> _drawerKey = GlobalKey();

  void _onLoading() async {
    pagenumber = pagenumber + 1;
    getcategory(pagenumber, searchController.text);
    _refreshControllerwait.loadComplete();
  }

  @override
  void initState() {
    super.initState();
    contentController.getlocations();
    contentController.getfeatures(AppConstants.hotelsId.toString());
    contentController.getminprice(AppConstants.hotelsId.toString());
    contentController.getmaxprice(AppConstants.hotelsId.toString());
    currencyController.getcuurentcurrency(context);
    authController.isloggedin();
    getcategory(pagenumber, "");
  }

  @override
  Widget build(BuildContext context) {
    final Widget svg2 = SizedBox(
        width: 20,
        height: 20.0,
        child: SvgPicture.asset(
          'assets/filter.svg',
          semanticsLabel: 'Acme Logo',
          fit: BoxFit.cover,
        ));
    return SafeArea(
        child: Scaffold(
            key: _drawerKey,
            resizeToAvoidBottomInset: false,
            appBar: AppBar(
              backgroundColor: const Color(0xFF27b4a8),
              centerTitle: true,
              title: Text(AppLocalizations.of(context).translate('hotels')),
            ),
            body: SmartRefresher(
              enablePullDown: true,
              enablePullUp: true,
              header: ClassicHeader(
                refreshingIcon: buildLoadingWidget(),
              ),
              footer: CustomFooter(
                builder: (BuildContext context, LoadStatus? mode) {
                  Widget body;
                  if (mode == LoadStatus.loading) {
                    body = const CupertinoActivityIndicator();
                  } else if (mode == LoadStatus.failed) {
                    body = const Text("Load Failed!Click retry!",
                        style: TextStyle(color: Color(0xff233549)));
                  } else if (mode == LoadStatus.canLoading) {
                    body = const Text(
                      "release to load more",
                      style: TextStyle(color: Color(0xff233549)),
                    );
                  } else {
                    body = const Text("No more Data",
                        style: TextStyle(color: Color(0xff233549)));
                  }
                  return Center(child: body);
                },
              ),
              controller: _refreshControllerwait,
              onRefresh: _onRefresh,
              onLoading: _onLoading,
              child: SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    MainFeaturedContainer(
                      label: 'featuredHotels',
                      child: code2 == 0 && msg2 == 'loading'
                          ? buildLoadingWidget()
                          : code2 == 1
                              ? featuredvideo.isNotEmpty
                                  ? Container(
                                      padding: const EdgeInsets.only(
                                          left: 20, right: 20),
                                      height: 230,
                                      width: MediaQuery.of(context).size.width,
                                      child: ListView.builder(
                                        shrinkWrap: true,
                                        physics: const ClampingScrollPhysics(),
                                        scrollDirection: Axis.horizontal,
                                        itemBuilder:
                                            (BuildContext ctxt, int index) {
                                          return HotelFeaturedVideo(
                                              time: widget.time,
                                              date: widget.date,
                                              id: widget.id,
                                              featuredvideo:
                                                  featuredvideo[index],
                                              currencyController:
                                                  currencyController,
                                              onAdd: () {
                                                authController.isLogged == true
                                                    ? widget.time != null &&
                                                            widget.time != ""
                                                        ? planController
                                                            .additemtoplan(
                                                                widget.date,
                                                                'maincategory',
                                                                featuredvideo[
                                                                        index]
                                                                    .id!,
                                                                widget.id!,
                                                                context,
                                                                time:
                                                                    widget.time)
                                                        : widget.id != null
                                                            ? listmyplandetails(
                                                                context,
                                                                widget.id!,
                                                                "maincategory",
                                                                featuredvideo[
                                                                        index]
                                                                    .id!,
                                                                "yes")
                                                            : listmyplan(
                                                                context,
                                                                "maincategory",
                                                                featuredvideo[
                                                                        index]
                                                                    .id!)
                                                    : snackbar(AppLocalizations
                                                            .of(context)
                                                        .translate(
                                                            'Please login first'));
                                              });
                                        },
                                        itemCount: featuredvideo.length,
                                      ))
                                  : nodatafound(AppLocalizations.of(context)
                                      .translate('No featured videos to show'))
                              : buildErrorWidget(msg2),
                    ),
                    const SizedBox(
                      height: 20,
                    ),
                    Padding(
                        padding: const EdgeInsets.only(left: 20, right: 20),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Row(
                              children: [
                                Text(
                                  AppLocalizations.of(context)
                                      .translate('All Hotels'),
                                  style: const TextStyle(
                                      color: Color(0xff51565B),
                                      fontSize: 17,
                                      fontWeight: FontWeight.bold),
                                ),
                              ],
                            ),
                            GestureDetector(
                              onTap: () {
                                contentController.minprice != null
                                    ? hotelsMinprice =
                                        contentController.minprice.toDouble()
                                    : 0;
                                contentController.maxprice != null
                                    ? hotelsMaxprice =
                                        contentController.maxprice.toDouble()
                                    : 0;
                                hotelsCurrentRangeValues =
                                    RangeValues(hotelsMinprice, hotelsMaxprice);

                                filterHotels(
                                  context,
                                  contentController: contentController,
                                  currencyController: currencyController,
                                  onApply: () async {
                                    navigator!.pop(context);
                                    code2 = 0;
                                    msg2 = 'loading';
                                    setState(() {});
                                    await filtermaincategory(
                                        hotelsCurrentvalue2 != null
                                            ? hotelsCurrentvalue2!
                                            : 0,
                                        hotelsStarts,
                                        hotelsCurrentRangeValues!.start,
                                        hotelsCurrentRangeValues!.end,
                                        hotelsF_id,
                                        AppConstants.hotelsId.toString(),
                                        0);
                                    code2 = 1;
                                    msg2 = 'd';
                                  },
                                );
                              },
                              child: svg2,
                            )
                          ],
                        )),
                    const SizedBox(
                      height: 10,
                    ),
                    Padding(
                        padding: const EdgeInsets.only(left: 20, right: 20),
                        child: Container(
                            height: 40,
                            decoration: BoxDecoration(
                                color: const Color(0xffF1F1F1),
                                borderRadius: BorderRadius.circular(5)),
                            child: TextField(
                              controller: searchController,
                              onSubmitted: onSearchSubmitted,
                              decoration: InputDecoration(
                                  prefixIcon: const Icon(
                                    Icons.search,
                                    color: Color(0xff8B959E),
                                  ),
                                  contentPadding: const EdgeInsets.only(
                                      left: 20, right: 20, top: 5),
                                  hintText: AppLocalizations.of(context)
                                      .translate('Search places and locations'),
                                  hintStyle: const TextStyle(
                                      color: Color(0xff8B959E), fontSize: 13),
                                  border: InputBorder.none),
                            ))),
                    const SizedBox(
                      height: 10,
                    ),
                    code2 == 0 && msg2 == 'loading'
                        ? buildLoadingWidget()
                        : code2 == 1
                            ? results.isNotEmpty
                                ? StaggeredGridView.countBuilder(
                                    staggeredTileBuilder: (index) {
                                      return const StaggeredTile.fit(1);
                                    },
                                    shrinkWrap: true,
                                    padding: const EdgeInsets.symmetric(
                                        horizontal: 10.0, vertical: 20.0),
                                    crossAxisCount: 2,
                                    physics:
                                        const NeverScrollableScrollPhysics(),
                                    mainAxisSpacing: 8,
                                    itemCount: results.length,
                                    primary: false,
                                    itemBuilder: (context, index) {
                                      return HotelCard(
                                        navigate: () {
                                          Navigator.of(context).push(
                                              MaterialPageRoute(
                                                  builder: (BuildContext
                                                          context) =>
                                                      VideoViewWidget(
                                                          video: results[index],
                                                          planId: widget.id,
                                                          date: widget.date,
                                                          time: widget.time)));
                                        },
                                        result: results[index],
                                        currencyController: currencyController,
                                        onTap: () {
                                          authController.isLogged == true
                                              ? widget.time != null &&
                                                      widget.time != ""
                                                  ? planController
                                                      .additemtoplan(
                                                          widget.date,
                                                          'maincategory',
                                                          results[index].id!,
                                                          widget.id!,
                                                          context,
                                                          time: widget.time)
                                                  : widget.id != null
                                                      ? listmyplandetails(
                                                          context,
                                                          widget.id!,
                                                          "maincategory",
                                                          results[index].id!,
                                                          "yes")
                                                      : listmyplan(
                                                          context,
                                                          "maincategory",
                                                          results[index].id!)
                                              : snackbar(AppLocalizations.of(
                                                      context)
                                                  .translate(
                                                      'Please login first'));
                                        },
                                      );
                                    })
                                : nodatafound(AppLocalizations.of(context)
                                    .translate('No Hotels to show'))
                            : buildErrorWidget(msg2)
                  ],
                ),
              ),
            )));
  }

  filtermaincategory(int location, List<int> starts, double startprice,
      double endprice, List<int> feature, String category, int type) async {
    results.clear();
    await Api.filtermaincategory(
            location, starts, null, null, feature, category, type)
        .then((value) {
      value != null
          ? setState(() {
              results.addAll(value.category);
            })
          // ignore: unnecessary_statements
          : null;
    });
  }

  void _onRefresh() async {
    await Future.delayed(const Duration(milliseconds: 1000));
    results.clear();
    featuredvideo.clear();
    pagenumber = 1;

    searchController.clear();

    await getcategory(pagenumber, "");

    _refreshControllerwait.refreshCompleted();

    setState(() {});
  }

  void clearResultsAndFeaturedVideos() {
    setState(() {
      results.clear();
      if (searchController.text.isEmpty) {
        featuredvideo.clear();
      }
    });
  }

  void onSearchSubmitted(String text) async {
    clearResultsAndFeaturedVideos();
    if (text.isEmpty) {
      await getcategory(
        1,
        "",
      );
    } else {
      pagenumber = 1;
      await getcategory(
        1,
        text,
      );
    }

    setState(() {});
  }

  getcategory(int page, String key) async {
    await Api.getmainCategory(page, 10, key, AppConstants.hotelsId.toString())
        .then((value) {
      setState(() {
        code2 = value.code;
        msg2 = value.error;
        results.addAll(value.category);
        if (searchController.text.isEmpty) {
          featuredvideo.addAll(value.featuredvideo);
        }
      });
    });
  }
}
