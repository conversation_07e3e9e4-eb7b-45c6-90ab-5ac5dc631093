import 'package:flutter/material.dart';
import 'package:page/src/core/config/constants.dart';

import '../../../core/localization/app_localizations.dart';
import '../../../core/services/api.dart';
import '../../../core/shared_widgets/shared_widgets.dart';

class Termsandcondtion extends StatefulWidget {
  @override
  _Termsandcondtion createState() => _Termsandcondtion();
}

class _Termsandcondtion extends State<Termsandcondtion> {
  String? data;
  int code = 0;

  void initState() {
    super.initState();
    getpolicy();
  }

  void getpolicy() async {
    await Api.getconfiguration().then((value) {
      value != null
          ? setState(() {
              code = value.code;
              data = value.results[AppConstants.policy];
            })
          // ignore: unnecessary_statements
          : null;
    });
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
        child: Scaffold(
            appBar: AppBar(
              backgroundColor: Color(0xff233549),
              centerTitle: true,
              title:
                  Text(AppLocalizations.of(context).translate('LegalPolicy')),
            ),
            body: code == 1
                ? SingleChildScrollView(
                    child: Container(
                    padding: EdgeInsets.all(20),
                    child: data != null ? Text(data ?? '') : Container(),
                  ))
                : buildLoadingWidget()));
  }
}
