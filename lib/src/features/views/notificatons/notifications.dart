import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:page/src/features/views/account/account.dart';

import '../../../core/localization/app_localizations.dart';
import '../../../core/response/notification_response.dart';
import '../../../core/response/profile_response.dart';
import '../../../core/shared_widgets/shared_widgets.dart';
import '../../bloc/auth_blok.dart';
import '../../bloc/notifications_bloc.dart';
import 'widgets/list_notifications.dart';

class Notifications extends StatefulWidget {
  final bool isNotificationTurnedOn;
  const Notifications({super.key, required this.isNotificationTurnedOn});

  @override
  _Notifications createState() => _Notifications();
}

class _Notifications extends State<Notifications> {
  late ProfileResponse profileResponse;
  bool isNotificationTurnedOn = true;

  @override
  void initState() {
    super.initState();
    notificationsBloc.getnotifications(page, 20);

    setState(() {
      isNotificationTurnedOn = widget.isNotificationTurnedOn;
    });
  }

  int page = 0;

  @override
  Widget build(BuildContext context) {
    void afterUpdateNotification(response) {
      if (response['code'] == 1) {
        snackbar2(
            AppLocalizations.of(context).translate('updated successfuly'));
      } else {
        snackbar(AppLocalizations.of(context)
            .translate('Something went wrong, please try again later'));
      }
    }

    return WillPopScope(
      onWillPop: () async {
        Navigator.push(
            context, MaterialPageRoute(builder: (context) => Account()));
        return false;
      },
      child: SafeArea(
          child: Scaffold(
              appBar: AppBar(
                backgroundColor: const Color(0xFF27b4a8),
                centerTitle: true,
                title: Text(
                    AppLocalizations.of(context).translate('Notifications')),
                actions: [
                  //! Turn off notifications switch
                  SizedBox(
                    height: 40,
                    child: Switch(
                      value: isNotificationTurnedOn,
                      onChanged: (value) async {
                        // await FirebaseMessaging.instance.deleteToken();
                        setState(() {
                          isNotificationTurnedOn = value;
                        });
                        if (value) {
                          FirebaseMessaging.instance
                              .getToken()
                              .then((token) async {
                            final response = await bloc.updateFcmToken(token!);

                            afterUpdateNotification(response);
                          });
                        } else {
                          final response = await bloc.updateFcmToken(null);

                          afterUpdateNotification(response);
                        }
                      },
                      activeTrackColor: Colors.green,
                      activeColor: const Color(0xFF27b4a8),
                    ),
                  ),
                ],
              ),
              body: StreamBuilder<NotificationsResponse>(
                stream: notificationsBloc.subject.stream,
                builder:
                    (context, AsyncSnapshot<NotificationsResponse> snapshot) {
                  if (snapshot.hasData) {
                    if (snapshot.data!.error.isNotEmpty) {
                      return buildErrorWidget(snapshot.data!.error);
                    }
                    return listnotifications(snapshot.data!, context);
                  } else if (snapshot.hasError) {
                    return buildErrorWidget(snapshot.error.toString());
                  } else {
                    return buildLoadingWidget();
                  }
                },
              ))),
    );
  }
}
