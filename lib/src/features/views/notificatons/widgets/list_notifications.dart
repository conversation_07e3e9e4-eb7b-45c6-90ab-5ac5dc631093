import 'package:flutter/material.dart';

import '../../../../core/localization/app_localizations.dart';
import '../../../../core/response/notification_response.dart';
import '../../../../core/shared_widgets/shared_widgets.dart';

Widget listnotifications(NotificationsResponse data, BuildContext context) {
  return data.notifications.isNotEmpty
      ? ListView.builder(
          shrinkWrap: true,
          // physics: ClampingScrollPhysics(),
          // scrollDirection: Axis.vertical,
          itemBuilder: (BuildContext ctxt, int index) {
            return GestureDetector(
              onTap: () {},
              child: Container(
                padding: const EdgeInsets.only(
                    top: 20, bottom: 5, right: 20, left: 20),
                child:
                    // padding: EdgeInsets.all(20),
                    Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    Text(data.notifications[index].date!),
                    const SizedBox(
                      height: 10,
                    ),
                    Container(
                      child: Column(
                        children: [
                          for (var i = 0;
                              i < data.notifications[index].items!.length;
                              i++)
                            Container(
                              padding: const EdgeInsets.only(top: 10),
                              child: Container(
                                  padding: const EdgeInsets.all(20),
                                  decoration: BoxDecoration(
                                      borderRadius: BorderRadius.circular(10),
                                      color: const Color(0xffF1F1F1),
                                      border: Border.all(
                                          color: const Color(0xffF1F1F2),
                                          width: 1)),
                                  child: Container(
                                    child: Column(
                                      children: [
                                        Row(
                                          children: [
                                            Container(
                                              height: 5,
                                              width: 5,
                                              decoration: BoxDecoration(
                                                  color: Colors.black,
                                                  borderRadius:
                                                      BorderRadius.circular(
                                                          10)),
                                            ),
                                            const SizedBox(
                                              width: 20,
                                            ),
                                            data.notifications[index].items![i]
                                                        .title !=
                                                    null
                                                ? Text(
                                                    data.notifications[index]
                                                        .items![i].title!,
                                                    style: const TextStyle(
                                                        fontWeight:
                                                            FontWeight.bold),
                                                  )
                                                : Container()
                                          ],
                                        ),
                                        const SizedBox(
                                          height: 10,
                                        ),
                                        Container(
                                          padding: const EdgeInsets.only(
                                              left: 20, right: 20),
                                          child: Text(
                                              data.notifications[index]
                                                  .items![i].message!,
                                              style: const TextStyle(
                                                  color: Color(0xff8B959E),
                                                  fontSize: 12)),
                                        )
                                      ],
                                    ),
                                  )),
                            )
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            );
          },
          itemCount: data.notifications.length,
        )
      : nodatafound(
          AppLocalizations.of(context).translate('No Notifications to show'));
}
