// import 'dart:developer';
//
// import 'package:flutter/material.dart';
// import 'package:flutter_rating_bar/flutter_rating_bar.dart';
// import 'package:get/get.dart';
// import 'package:page/src/features/models/video_model.dart';
// import 'package:page/src/features/views/ad_details/video_widget.dart';
//
// import '../../../../../core/localization/app_localizations.dart';
// import '../../../../../core/shared_widgets/shared_widgets.dart';
// import '../../../car_rent_details/car_rent_details.dart';
// import '../../../holiday_home_details/holiday_home_details.dart';
//
// void detaialsMap(BuildContext context, String type,
//     {required image,
//     required name,
//     required price,
//     required endPrice,
//     required label,
//     holidayType,
//     required rating,
//     required currencyController,
//     required description,
//     required mainid,
//     required VideoDetailsModel videoDetails}) {
//   log('sttssfsfst ${price}');
//   pr!.hide();
//
//   final isRestaurant = label == "restaurant";
//   final isActivity = label == "activity";
//
//   final priceTitle = isRestaurant
//       ? AppLocalizations.of(context).translate('Average Price (Per Person)')
//       : isActivity
//           ? AppLocalizations.of(context).translate('Starting')
//           : type == "holiday home"
//               ? AppLocalizations.of(context).translate('price (Per Night)')
//               // .translate('Price range (Per Night)')
//               : AppLocalizations.of(context).translate('Price Range');
//
//   showModalBottomSheet(
//       context: context,
//       isScrollControlled: true,
//       backgroundColor: Colors.transparent,
//       builder: (context) => Padding(
//           padding:
//               EdgeInsets.only(bottom: MediaQuery.of(context).viewInsets.bottom),
//           child: Container(
//             height: MediaQuery.of(context).size.height * 0.50,
//             decoration: BoxDecoration(
//                 color: Colors.white,
//                 borderRadius: const BorderRadius.only(
//                     topLeft: Radius.circular(25.0),
//                     topRight: Radius.circular(25.0)),
//                 border: Border.all(color: Colors.black, width: 1.0)),
//             child: SingleChildScrollView(
//                 child: Container(
//                     padding: const EdgeInsets.only(left: 20, right: 20),
//                     child: Column(
//                       // crossAxisAlignment: CrossAxisAlignment.start,
//                       children: [
//                         const SizedBox(
//                           height: 10,
//                         ),
//                         Container(
//                             height: 5,
//                             width: 50,
//                             color: const Color(0xffD2D4D6)),
//                         const SizedBox(
//                           height: 20,
//                         ),
//                         Row(
//                           crossAxisAlignment: CrossAxisAlignment.start,
//                           mainAxisAlignment: MainAxisAlignment.start,
//                           children: [
//                             ClipRRect(
//                               borderRadius: BorderRadius.circular(5),
//                               child: image != null
//                                   ? MainCachedImage(
//                                       image!,
//                                       height: 200,
//                                       width: 100,
//                                       fit: BoxFit.cover,
//                                     )
//                                   : Image.asset(
//                                       'assets/Mask Group 2.png',
//                                       height: 200,
//                                       width: 100,
//                                       fit: BoxFit.cover,
//                                     ),
//                             ),
//                             const SizedBox(
//                               width: 20,
//                             ),
//                             Expanded(
//                                 child: Column(
//                               crossAxisAlignment: CrossAxisAlignment.start,
//                               mainAxisAlignment: MainAxisAlignment.start,
//                               children: [
//                                 Row(
//                                   children: [
//                                     Container(
//                                         height: 30,
//                                         width: 100,
//                                         color: const Color(0xffF1F1F1),
//                                         child: Center(
//                                             child: Text(
//                                           label != null
//                                               ? label!
//                                                   .toString()
//                                                   .capitalizeFirst!
//                                               : AppLocalizations.of(context)
//                                                   .translate('holidayhome'),
//                                           style: const TextStyle(
//                                               color: Color(0xff191C1F),
//                                               fontSize: 12),
//                                         ))),
//                                     const SizedBox(
//                                       width: 15,
//                                     ),
//                                     if (holidayType != null)
//                                       Container(
//                                           height: 30,
//                                           width: 100,
//                                           color: const Color(0xffF1F1F1),
//                                           child: Center(
//                                               child: Text(
//                                             holidayType,
//                                             style: const TextStyle(
//                                                 color: Color(0xff191C1F),
//                                                 fontSize: 12),
//                                           ))),
//                                   ],
//                                 ),
//                                 if (label == "hotels") ...[
//                                   const SizedBox(height: 5),
//                                   RatingBar.builder(
//                                     initialRating: rating!.toDouble(),
//                                     onRatingUpdate: (value) {},
//                                     minRating: 1,
//                                     direction: Axis.horizontal,
//                                     allowHalfRating: true,
//                                     itemCount: 5,
//                                     itemSize: 16.0,
//                                     itemPadding: const EdgeInsets.only(left: 2),
//                                     itemBuilder: (context, _) => const Icon(
//                                         Icons.star,
//                                         color: Color(0xffF2BA24),
//                                         size: 12),
//                                     unratedColor: const Color(0xff556477),
//                                     // onRatingUpdate: (rating) {
//                                     //   print(rating);
//                                     // },
//                                   ),
//                                 ],
//                                 const SizedBox(
//                                   height: 20,
//                                 ),
//                                 name != null
//                                     ? Text(name!,
//                                         style: const TextStyle(
//                                             fontWeight: FontWeight.bold,
//                                             color: Color(0xff191C1F),
//                                             fontSize: 17))
//                                     : Container(),
//                                 const SizedBox(
//                                   height: 10,
//                                 ),
//                                 description != null
//                                     ? Text(
//                                         description!,
//                                         softWrap: true,
//                                         maxLines: 2,
//                                         style: const TextStyle(fontSize: 12),
//                                       )
//                                     : Container(),
//                                 const SizedBox(
//                                   height: 10,
//                                 ),
//                                 price != null
//                                     ? Text(
//                                         '$priceTitle: $price ${endPrice != null ? '- $endPrice ' : ''} ${currencyController.currency}',
//                                         style: const TextStyle(fontSize: 12))
//                                     : Container()
//                               ],
//                             ))
//                           ],
//                         ),
//                         const SizedBox(height: 40),
//                         Center(
//                             child: GestureDetector(
//                                 onTap: () async {
//                                   print("ooooooo");
//                                   print(mainid);
//                                   type == "main"
//                                       ? Navigator.of(context).push(
//                                           MaterialPageRoute(
//                                               builder: (BuildContext context) =>
//                                                   VideoViewWidget(
//                                                     video: videoDetails,
//                                                   )))
//                                       : type == "car rent"
//                                           ? Navigator.of(context).push(
//                                               MaterialPageRoute(
//                                                   builder:
//                                                       (BuildContext context) =>
//                                                           CarRentDetails(
//                                                             video: videoDetails,
//                                                           )))
//                                           : type == "holiday home"
//                                               ? Navigator.of(context).push(
//                                                   MaterialPageRoute(
//                                                       builder: (BuildContext
//                                                               context) =>
//                                                           HoldayHomeDetails(
//                                                             video: videoDetails,
//                                                             // ignore: unnecessary_statements
//                                                           )))
//                                               : null;
//                                 },
//                                 child: Container(
//                                   height: 40,
//                                   width: MediaQuery.of(context).size.width,
//                                   decoration: BoxDecoration(
//                                       color: const Color(0xFF27b4a8)
//                                       borderRadius: BorderRadius.circular(5)),
//                                   child: Container(
//                                       padding: const EdgeInsets.all(10),
//                                       child: Center(
//                                           child: Text(
//                                         AppLocalizations.of(context)
//                                             .translate('Watch Video'),
//                                         style: const TextStyle(
//                                             color: Colors.white),
//                                       ))),
//                                 ))),
//                       ],
//                     ))),
//           )));
// }
