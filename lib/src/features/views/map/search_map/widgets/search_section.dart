// import 'package:flutter/material.dart';
// import 'package:flutter_typeahead/flutter_typeahead.dart';
// import 'package:get/get_utils/src/platform/platform.dart';
//
// import '../../../../../core/localization/app_localizations.dart';
//
// class SearchSection extends StatelessWidget {
//   final onSearchTextChanged;
//   final searchcontroller;
//   final searchNames;
//   final onSelected;
//
//   const SearchSection(
//       {Key? key,
//       required this.onSearchTextChanged,
//       required this.searchcontroller,
//       required this.searchNames,
//       required this.onSelected})
//       : super(key: key);
//
//   @override
//   Widget build(BuildContext context) {
//     return Positioned(
//         top: 20,
//         left: 20,
//         right: GetPlatform.isAndroid ? 70 : 10,
//         child: Container(
//           decoration: BoxDecoration(
//               color: Colors.white, borderRadius: BorderRadius.circular(10)),
//           child: Container(
//             decoration: BoxDecoration(
//               borderRadius: const BorderRadius.all(Radius.circular(5)),
//               border: Border.all(color: Colors.black12, width: 1.0),
//             ),
//             child: TypeAheadField(
//                 textFieldConfiguration: TextFieldConfiguration(
//                   controller: searchcontroller,
//                   onChanged: (value) {
//                     if (value.isEmpty) {
//                       onSearchTextChanged(value);
//                       FocusScope.of(context).requestFocus(FocusNode());
//                     }
//                   },
//                   decoration: InputDecoration(
//                     prefixIcon:
//                         const Icon(Icons.search, color: Color(0xff8B959E)),
//                     hintText: AppLocalizations.of(context)
//                         .translate('Search places and locations'),
//                   ),
//                 ),
//                 errorBuilder: (context, error) {
//                   return Padding(
//                     padding: const EdgeInsets.all(8.0),
//                     child: Center(
//                       child: Text("$error"),
//                     ),
//                   );
//                 },
//                 loadingBuilder: (context) {
//                   return const CircularProgressIndicator();
//                 },
//                 suggestionsCallback: (pattern) {
//                   print("pattern ${pattern}");
//                   return searchNames
//                       .where((element) => element.contains(pattern));
//                 },
//                 noItemsFoundBuilder: (context) {
//                   return const Padding(
//                     padding: EdgeInsets.all(8.0),
//                     child: Center(
//                       child: Text("No Item "),
//                     ),
//                   );
//                 },
//                 itemBuilder: (context, suggestion) {
//                   return Padding(
//                     padding: const EdgeInsets.all(8.0),
//                     child: Text(suggestion.toString()),
//                   );
//                 },
//                 onSuggestionSelected: onSelected),
//           ),
//         ));
//   }
// }
