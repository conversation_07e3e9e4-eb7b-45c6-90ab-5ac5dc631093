import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get_utils/src/platform/platform.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:page/src/core/config/constants.dart';
import 'package:provider/provider.dart';

import '../../../../../core/localization/app_language.dart';

class BottomSection extends StatelessWidget {
  final setState;
  final GoogleMapController? myMapController;
  final lat;
  final lng;
  final getmarkers;
  var isload;
  final mainLocation;
  final ValueNotifier<bool>? isActivity;

  BottomSection({
    Key? key,
    required this.setState,
    required this.myMapController,
    required this.lat,
    required this.lng,
    required this.getmarkers,
    required this.isload,
    required this.mainLocation,
    this.isActivity,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    var lang = Provider.of<AppLanguage>(context).appLocal.languageCode;

    return Positioned(
      bottom: 10,
      left: 20,
      right: GetPlatform.isAndroid ? 70 : 10,
      child: SizedBox(
          height: 50,
          width: MediaQuery.of(context).size.width,
          child: ListView.separated(
            shrinkWrap: true,
            physics: const ClampingScrollPhysics(),
            scrollDirection: Axis.horizontal,
            itemBuilder: (BuildContext ctxt, int index) {
              return GestureDetector(
                  onTap: () async {
                    setState(() {
                      isload = false;

                      myMapController!.animateCamera(
                          (CameraUpdate.newLatLngZoom(mainLocation, 10)));
                      // _mainLocation = LatLng(
                      //     25.229279238648324, 55.270265778344175);
                      getmarkers(
                          category: category[index]['value'],
                          lat: lat!,
                          lng: lng!);
                      for (var i = 0; i < category.length; i++) {
                        category[i]['isselected'] = false;
                      }
                      category[index]['isselected'] =
                          !category[index]['isselected'];

                      if (category[index]['value'] ==
                              '${AppConstants.activitiesId}' ||
                          category[index]['value'] ==
                              '${AppConstants.restaurantsId}') {
                        isActivity!.value = true;
                      } else {
                        isActivity!.value = false;
                      }
                    });
                  },
                  child: Container(
                      decoration: BoxDecoration(
                        color: category[index]['isselected'] == true
                            ? const Color(0xff233549)
                            : Colors.white,
                        borderRadius: BorderRadius.circular(10),
                      ),
                      child: Container(
                          padding: const EdgeInsets.all(10),
                          child: Row(
                            children: [
                              Container(
                                  height: 30,
                                  width: 30,
                                  decoration: BoxDecoration(
                                      color:
                                          category[index]['isselected'] == true
                                              ? const Color(0xff233549)
                                              : Colors.grey[100],
                                      borderRadius: BorderRadius.circular(40)),
                                  child: Center(
                                      child: ClipRRect(
                                          borderRadius:
                                              BorderRadius.circular(30),
                                          child: category[index]['isselected'] ==
                                                  false
                                              ? SizedBox(
                                                  width: 20,
                                                  height: 15,
                                                  child:
                                                      index == 5 || index == 6
                                                          ? Image.asset(
                                                              category[index]
                                                                  ['icon'],
                                                              height: 20,
                                                              width: 20,
                                                            )
                                                          : SvgPicture.asset(
                                                              category[index]
                                                                  ['icon'],
                                                              semanticsLabel:
                                                                  'Acme Logo',
                                                              // fit: BoxFit.cover,
                                                            ))
                                              : index == 5 || index == 6
                                                  ? Image.asset(
                                                      category[index]
                                                          ['icon_selected'],
                                                      height: 20,
                                                      width: 20,
                                                      color: Colors.white,
                                                    )
                                                  : SizedBox(
                                                      width: 20,
                                                      height: 15,
                                                      child: SvgPicture.asset(
                                                          category[index]
                                                              ['icon_selected'],
                                                          semanticsLabel:
                                                              'Acme Logo',
                                                          colorFilter:
                                                              const ColorFilter
                                                                      .mode(
                                                                  Colors.white,
                                                                  BlendMode.srcIn)
                                                          // fit: BoxFit.cover,
                                                          ))))),
                              const SizedBox(
                                width: 10,
                              ),
                              lang == "en"
                                  ? Text(
                                      category[index]['name'],
                                      style: TextStyle(
                                          color: category[index]
                                                      ['isselected'] ==
                                                  true
                                              ? Colors.white
                                              : const Color(0xff233549)),
                                    )
                                  : Text(
                                      category[index]['name_ar'],
                                      style: TextStyle(
                                          color: category[index]
                                                      ['isselected'] ==
                                                  true
                                              ? Colors.white
                                              : const Color(0xff233549)),
                                    ),
                            ],
                          ))));
            },
            separatorBuilder: (BuildContext ctxt, int index) {
              return const SizedBox(
                width: 10,
              );
            },
            itemCount: category.length,
          )),
    );
  }
}

List<Map<String, dynamic>> category = [
  {
    'icon': 'assets/Group 6375.svg',
    'icon_selected': 'assets/Group 63799.svg',
    'name': "View all",
    "name_ar": "الكل",
    "isselected": true,
    "value": ""
  },
  {
    'icon': 'assets/hotelb.svg',
    'icon_selected': 'assets/hotelw.svg',
    'name': "Hotels",
    "name_ar": "الفنادق",
    "isselected": false,
    "value": "${AppConstants.hotelsId}"
  },
  {
    'icon': 'assets/restb.svg',
    'icon_selected': 'assets/restw.svg',
    'name': "Restaurants",
    "name_ar": "المطاعم",
    "isselected": false,
    "value": "${AppConstants.restaurantsId}"
  },
  {
    'icon': 'assets/activity.svg',
    'icon_selected': 'assets/activityw.svg',
    'name': "Activities",
    "name_ar": "النشاطات",
    "isselected": false,
    "value": "${AppConstants.activitiesId}"
  },
  {
    'icon': 'assets/shopb.svg',
    'icon_selected': 'assets/icons8-shop.svg',
    'name': "Coffee Shops",
    "name_ar": "كوفي شوب",
    "isselected": false,
    "value": "${AppConstants.coffeeShopsId}"
  },
  {
    'icon': 'assets/holiday_home_active.jpeg',
    'icon_selected': 'assets/home2.png',
    'name': "Holiday Home",
    "name_ar": "بيوت العطلات",
    "isselected": false,
    "value": "${AppConstants.holidayHomesId}"
  },
  {
    'icon': 'assets/icons/destination.png',
    'icon_selected': 'assets/icons/destination.png',
    'name': "Destination",
    "name_ar": "الوجهات السياحية",
    "isselected": false,
    "value": "${AppConstants.destinationsId}"
  },
];
