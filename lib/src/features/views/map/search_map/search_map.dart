// import 'dart:developer';
// import 'dart:typed_data';
//
// import 'package:flutter/material.dart';
// import 'package:flutter_typeahead/flutter_typeahead.dart';
// import 'package:get/get_utils/src/platform/platform.dart';
// import 'package:google_maps_flutter/google_maps_flutter.dart';
// import 'package:page/src/core/config/constants.dart';
// import 'package:page/src/core/extensions/extensions.dart';
// import 'package:page/src/core/localization/app_localizations.dart';
// import 'package:page/src/features/models/search_map.dart';
// import 'package:page/src/features/views/map/search_map/widgets/bottom_section.dart';
// import 'package:page/src/features/views/story/widgets/reel_widgets/reel_details.dart';
//
// import '../../../../core/services/api.dart';
// import '../../../../core/shared_widgets/bottom_navgation_bar.dart';
// import '../../../../core/shared_widgets/shared_widgets.dart';
// import '../../../controllers/currency_controller.dart';
// import '../../ad_details/widgets/ad_details.dart';
// import '../../holiday_home_details/widgets/details.dart';
// import '../../home/<USER>';
//
// // ignore: must_be_immutable
// class SearchMap extends StatefulWidget {
//   final markers;
//   final String? searchKey;
//
//   const SearchMap({Key? key, this.markers, this.searchKey}) : super(key: key);
//
//   @override
//   _SearchMap createState() => _SearchMap();
// }
//
// class _SearchMap extends State<SearchMap> with TickerProviderStateMixin {
//   late Uint8List imageDataRestaurant;
//   late Uint8List imageDataHotel;
//   late Uint8List imageDataShop;
//   late Uint8List imageDataHolidayHome;
//   late Uint8List imageDataActivity;
//   late Uint8List imageDataDestinations;
//   late ByteData byteData;
//   List<String> searchNames = [];
//   final Set<Marker> _markers = {};
//   bool isload = false;
//   int? mainid;
//   int? rating;
//   String? name, description, label, image, type;
//   var price;
//   var endPrice;
//   CurrencyController currencyController = CurrencyController();
//   MarkerId markerId = const MarkerId("YOUR-MARKER-ID");
//   LatLng latLng = const LatLng(33.503447965138115, 36.23894707956065);
//
//   // final Completer<GoogleMapController> myMapController =
//   //     Completer<GoogleMapController>();
//   GoogleMapController? myMapController;
//   final LatLng _mainLocation =
//       const LatLng(25.229279238648324, 55.270265778344175);
//   double zoom = 10.0;
//   bool iscurrent2 = false;
//   List<String>? t;
//   double? lat;
//   double? lng;
//   late TextEditingController searchcontroller;
//
//   Future<void> getmarkers(
//       {required double lat,
//       required double lng,
//       required String category,
//       String? key,
//       bool fromSearch = false}) async {
//     Uint8List? imageData;
//     final res = await Api.getmainCategorySearch(lat, lng, category, key);
//
//     searchResult = res?.results ?? [];
//
//     setState(() {
//       searchNames.clear();
//       _markers.clear();
//     });
//
//     if (!fromSearch) {
//       for (var i = 0; i < searchResult.length; i++) {
//         searchNames.add(searchResult[i].name!);
//
//         searchResult[i].maincategorytype == AppConstants.hotelsId
//             ? imageData = imageDataHotel
//             : searchResult[i].maincategorytype == AppConstants.restaurantsId
//                 ? imageData = imageDataRestaurant
//                 : searchResult[i].maincategorytype == AppConstants.activitiesId
//                     ? imageData = imageDataActivity
//                     : searchResult[i].maincategorytype ==
//                             AppConstants.coffeeShopsId
//                         ? imageData = imageDataShop
//                         : searchResult[i].maincategorytype ==
//                                 AppConstants.destinationsId
//                             ? imageData = imageDataDestinations
//                             // ignore: unnecessary_statements
//                             : null;
//
//         searchResult[i].maincategorytype == AppConstants.holidayHomesId
//             ? imageData = imageDataHolidayHome
//             : "";
//
//         if (searchResult[i].lat != null && searchResult[i].lng != null) {
//           LatLng location = LatLng(searchResult[i].lat!, searchResult[i].lng!);
//
//           setState(() {
//             log('Befoore ${_markers.length}');
//             _markers.add(Marker(
//               onTap: () {
//                 progrsss(context);
//                 pr?.show();
//
//                 searchResult[i].maincategorytype == AppConstants.holidayHomesId
//                     ? gethoildayhomedetails(searchResult[i].id!)
//                     : getmaincategorydetails(searchResult[i].id!);
//
//                 pr!.hide();
//               },
//               markerId: MarkerId(location.toString()),
//               position: location,
//               icon: BitmapDescriptor.fromBytes(imageData!),
//             ));
//
//             log('Afteeer ${_markers.length}');
//           });
//         }
//         setState(() {});
//       }
//     } else {
//       // put one marker for one lat and one long
//       final result = searchResult.firstWhere(
//           (element) => searchcontroller.text == element.name,
//           orElse: () => SearchMapModel());
//
//       log('Seaaarc ${searchcontroller.text} resultssdadsd ${searchResult.map((e) => e.name)}');
//
//       // .indexWhere((element) =>
//       // element.lat == lat && element.lng == lng && element.name == key);
//
//       LatLng location = LatLng(result.lat!, result.lng!);
//
//       result.maincategorytype == AppConstants.hotelsId
//           ? imageData = imageDataHotel
//           : result.maincategorytype == AppConstants.restaurantsId
//               ? imageData = imageDataRestaurant
//               : result.maincategorytype == AppConstants.activitiesId
//                   ? imageData = imageDataActivity
//                   : result.maincategorytype == AppConstants.coffeeShopsId
//                       ? imageData = imageDataShop
//                       : result.maincategorytype == AppConstants.destinationsId
//                           ? imageData = imageDataDestinations
//                           // ignore: unnecessary_statements
//                           : null;
//
//       _markers.add(Marker(
//         onTap: () {
//           progrsss(context);
//           pr?.show();
//
//           result.maincategorytype == AppConstants.holidayHomesId
//               ? gethoildayhomedetails(result.id!)
//               : getmaincategorydetails(result.id!);
//
//           pr!.hide();
//         },
//         markerId: MarkerId(result.id?.toString() ?? location.toString()),
//         position: location,
//         icon: BitmapDescriptor.fromBytes(imageData ?? imageDataHotel),
//       ));
//
//       setState(() {
//         log('_markersasdasfsaLL ${_markers.length}  asdassad ${_markers}');
//       });
//     }
//   }
//
//   ValueNotifier<bool> isActivity = ValueNotifier<bool>(false);
//
//   void getmaincategorydetails(int id) async {
//     await Api.getmainCategorydetails(id).then((value) {
//       Navigator.pop(context);
//
//       value != null
//           ? setState(() {
//               print("Pricce=> ${value.results['price']}");
//               print(value.results['id']);
//               mainid = value.results['id'];
//               print(id);
//               name = value.results['name'];
//               description = value.results['description'];
//               label = value.results['category'] != null
//                   ? value.results['category']['name_en']
//                   : '';
//               image = value.results['images'] != null
//                   ? value.results['images'][0]['url']
//                   : '';
//               rating = value.results['rating'];
//               startprice = value.results['startprice'];
//               price = value.results['price'] ??
//                   value.results['startprice'] ??
//                   value.results['endprice'];
//               endPrice = value.results['endprice'] ??
//                   value.results['price'] ??
//                   value.results['startprice'];
//               type = 'main';
//               isload = true;
//               log('asjdnsaf ${value.results['location'] != null ? value.results['location']['name'] : ''}');
//               adDetails(
//                 context,
//                 video: value.results['video'],
//                 title: name ?? '',
//                 label: label,
//                 category: value.results['category'] != null
//                     ? value.results['category']['name']
//                     : '',
//                 website: value.results['website'],
//                 phone: value.results['phone'],
//                 rating: rating,
//                 currencyController: currencyController,
//                 startprice: startprice,
//                 endPrice: endPrice,
//                 price: price,
//                 location: value.results['location'] != null
//                     ? value.results['location']['name']
//                     : '',
//                 instagram: value.results['instagram'],
//                 description: description,
//                 image: image ?? '',
//                 lat: value.results['latitude'].toString().toDouble(),
//                 lng: value.results['longitude'].toString().toDouble(),
//                 id: mainid,
//               );
//             })
//           // ignore: unnecessary_statements
//           : null;
//       print(name);
//     });
//   }
//
//   void gethoildayhomedetails(int id) async {
//     await Api.getholidayhomedetails(id).then((value) {
//       print("Pricce=> ${value!.results['price']}");
//       Navigator.pop(context);
//
//       final language = Localizations.localeOf(context);
//
//       setState(() {
//         mainid = value.results['id'];
//         name = value.results['name'];
//         description = value.results['description'];
//         label = value.results['label'];
//         image = value.results['images'] != null
//             ? value.results['images'][0]['url']
//             : '';
//         rating = value.results['rating'];
//         price = value.results['startprice'] ?? value.results['price'];
//         type = 'holiday home';
//         isload = true;
//
//         detailsHolidayHome(context,
//             title: value.results['name'],
//             video: value.results['video'],
//             description: description,
//             roomnumber: value.results['rooms'],
//             agentId: value.results['agent']['id'],
//             startprice: price,
//             currencyController: currencyController,
//             location: value.results['location'] != null
//                 ? value.results['location']['name']
//                 : '',
//             type: value.results['type'] != null
//                 ? language.languageCode == 'en'
//                     ? value.results['type']['name']['en']
//                     : value.results['type']['name']['ar']
//                 : '',
//             lat: value.results['latitude'].toString().toDouble(),
//             lng: value.results['longitude'].toString().toDouble(),
//             size: value.results['size'],
//             fromMaps: true,
//             image: image,
//             id: mainid ?? id);
//       });
//     });
//   }
//
//   getMarkerhotels() async {
//     try {
//       byteData =
//           await DefaultAssetBundle.of(context).load("assets/hotel_icon.png");
//     } catch (e) {
//       byteData =
//           await DefaultAssetBundle.of(context).load("assets/hotel_icon.png");
//     }
//     imageDataHotel = byteData.buffer.asUint8List();
//   }
//
//   getMarkerholidayhome() async {
//     try {
//       byteData =
//           await DefaultAssetBundle.of(context).load("assets/holiday_icon.png");
//     } catch (e) {
//       byteData =
//           await DefaultAssetBundle.of(context).load("assets/holiday_icon.png");
//     }
//     imageDataHolidayHome = byteData.buffer.asUint8List();
//   }
//
//   getMarkeractivity() async {
//     try {
//       byteData =
//           await DefaultAssetBundle.of(context).load("assets/activity_icon.png");
//     } catch (e) {
//       byteData =
//           await DefaultAssetBundle.of(context).load("assets/activity_icon.png");
//     }
//     imageDataActivity = byteData.buffer.asUint8List();
//   }
//
//   getMarkershop() async {
//     try {
//       byteData =
//           await DefaultAssetBundle.of(context).load("assets/shop_icon.png");
//     } catch (e) {
//       byteData =
//           await DefaultAssetBundle.of(context).load("assets/shop_icon.png");
//     }
//     imageDataShop = byteData.buffer.asUint8List();
//   }
//
//   getMarkerresturant() async {
//     try {
//       byteData = await DefaultAssetBundle.of(context)
//           .load("assets/resturant_icon.png");
//     } catch (e) {
//       byteData = await DefaultAssetBundle.of(context)
//           .load("assets/resturant_icon.png");
//     }
//     imageDataRestaurant = byteData.buffer.asUint8List();
//   }
//
//   getMarkerDestinations() async {
//     try {
//       byteData = await DefaultAssetBundle.of(context)
//           .load("assets/icons/destination_map.png");
//     } catch (e) {
//       byteData = await DefaultAssetBundle.of(context)
//           .load("assets/icons/destination_map.png");
//     }
//     imageDataDestinations = byteData.buffer.asUint8List();
//   }
//
//   getAllMarkers() async {
//     await getMarkerhotels();
//     await getMarkerholidayhome();
//     await getMarkeractivity();
//     await getMarkershop();
//     await getMarkerresturant();
//     await getMarkerDestinations();
//   }
//
//   List<SearchMapModel> searchResult = [];
//
//   @override
//   void initState() {
//     getAllMarkers();
//     lat = 25.229279238648324;
//     lng = 55.270265778344175;
//     getmarkers(category: "", lat: lat!, lng: lng!);
//
//     currencyController.getcuurentcurrency(context);
//
//     searchcontroller = TextEditingController(text: widget.searchKey ?? "");
//
//     setState(() {});
//     super.initState();
//   }
//
//   void onSearchTextChanged(String text) async {
//     if (text.isEmpty) {
//       getmarkers(category: "", lat: lat!, lng: lng!);
//     } else {
//       log('asfkldlkalsdfn ${text}');
//
//       await getmarkers(
//           category: "", lat: lat!, lng: lng!, key: text, fromSearch: true);
//
//       Navigator.of(context).pushReplacement(MaterialPageRoute(
//           builder: (BuildContext context) =>
//               SearchMap(markers: _markers, searchKey: text)));
//     }
//   }
//
//   @override
//   Widget build(BuildContext context) {
//     zoom = 10;
//
//     final searchField = Positioned(
//         top: 20,
//         left: 20,
//         right: GetPlatform.isAndroid ? 70 : 10,
//         child: Container(
//           decoration: BoxDecoration(
//               color: Colors.white, borderRadius: BorderRadius.circular(10)),
//           child: Container(
//             decoration: BoxDecoration(
//               borderRadius: const BorderRadius.all(Radius.circular(5)),
//               border: Border.all(color: Colors.black12, width: 1.0),
//             ),
//             child: TypeAheadField(
//                 textFieldConfiguration: TextFieldConfiguration(
//                   controller: searchcontroller,
//                   onChanged: (value) {
//                     if (value.isEmpty) {
//                       onSearchTextChanged(value);
//                       FocusScope.of(context).requestFocus(FocusNode());
//                     }
//                   },
//                   decoration: InputDecoration(
//                     prefixIcon:
//                         const Icon(Icons.search, color: Color(0xff8B959E)),
//                     hintText: AppLocalizations.of(context)
//                         .translate('Search places and locations'),
//                   ),
//                 ),
//                 errorBuilder: (context, error) {
//                   return Padding(
//                     padding: const EdgeInsets.all(8.0),
//                     child: Center(
//                       child: Text("$error"),
//                     ),
//                   );
//                 },
//                 loadingBuilder: (context) {
//                   return const CircularProgressIndicator();
//                 },
//                 suggestionsCallback: (pattern) {
//                   print("pattadasdern ${pattern}");
//                   return searchNames
//                       .where((element) => element.contains(pattern));
//                 },
//                 noItemsFoundBuilder: (context) {
//                   return const Padding(
//                     padding: EdgeInsets.all(8.0),
//                     child: Center(
//                       child: Text("No Item "),
//                     ),
//                   );
//                 },
//                 itemBuilder: (context, suggestion) {
//                   return Padding(
//                     padding: const EdgeInsets.all(8.0),
//                     child: Text(suggestion.toString()),
//                   );
//                 },
//                 onSuggestionSelected: (suggestion) async {
//                   onSearchTextChanged(suggestion);
//                   myMapController!.animateCamera(
//                     CameraUpdate.newLatLng(_mainLocation),
//                   );
//                   zoom = 10.0;
//                   searchcontroller.text = suggestion;
//                 }),
//           ),
//         ));
//
//     final clearWidget = Positioned(
//       top: 25,
//       right: 15,
//       child: CircleAvatar(
//         backgroundColor: Colors.white,
//         radius: 19,
//         child: GestureDetector(
//           onTap: () {
//             searchcontroller.clear();
//             Navigator.of(context).pushReplacement(MaterialPageRoute(
//                 builder: (BuildContext context) => const SearchMap()));
//             // onSearchTextChanged("");
//           },
//           child: const Icon(Icons.clear),
//         ),
//       ),
//     );
//
//     return WillPopScope(
//         onWillPop: () {
//           Navigator.of(context).pushAndRemoveUntil(
//               MaterialPageRoute(builder: (context) => const Home()),
//               (Route<dynamic> route) => false);
//
//           return Future.value(false);
//         },
//         child: SafeArea(
//             child: Scaffold(
//           body: Stack(children: [
//             SizedBox(
//               height: MediaQuery.of(context).size.height,
//               width: MediaQuery.of(context).size.width,
//               child: GoogleMap(
//                 padding: const EdgeInsets.symmetric(vertical: 60),
//                 scrollGesturesEnabled: true,
//                 tiltGesturesEnabled: true,
//                 initialCameraPosition: CameraPosition(
//                   target: _mainLocation,
//                   zoom: zoom,
//                 ),
//                 myLocationEnabled: true,
//                 myLocationButtonEnabled: true,
//                 markers: widget.markers ?? _markers,
//                 mapType: MapType.normal,
//                 onMapCreated: (GoogleMapController controller) {
//                   myMapController = controller;
//
//                   setState(() {});
//                 },
//                 onTap: (LatLng latLng) async {},
//               ),
//             ),
//             searchField,
//             if (searchcontroller.text.isNotEmpty) clearWidget,
//             BottomSection(
//                 setState: setState,
//                 myMapController: myMapController,
//                 lat: lat,
//                 lng: lng,
//                 getmarkers: getmarkers,
//                 isload: isload,
//                 isActivity: isActivity,
//                 mainLocation: _mainLocation)
//           ]),
//           bottomNavigationBar: CustomBottomNavgationBar(2),
//         )));
//   }
// }
//
// //import 'dart:developer';
// // import 'dart:typed_data';
// //
// // import 'package:flutter/material.dart';
// // import 'package:flutter_typeahead/flutter_typeahead.dart';
// // import 'package:get/get_utils/src/platform/platform.dart';
// // import 'package:google_maps_flutter/google_maps_flutter.dart';
// // import 'package:page/src/core/config/constants.dart';
// // import 'package:page/src/core/extensions/extensions.dart';
// // import 'package:page/src/core/localization/app_localizations.dart';
// // import 'package:page/src/features/models/search_map.dart';
// // import 'package:page/src/features/views/map/search_map/widgets/bottom_section.dart';
// // import 'package:page/src/features/views/story/widgets/reel_widgets/reel_details.dart';
// //
// // import '../../../../core/services/api.dart';
// // import '../../../../core/shared_widgets/bottom_navgation_bar.dart';
// // import '../../../../core/shared_widgets/shared_widgets.dart';
// // import '../../../controllers/currency_controller.dart';
// // import '../../ad_details/widgets/ad_details.dart';
// // import '../../holiday_home_details/widgets/details.dart';
// // import '../../home/<USER>';
// //
// // // ignore: must_be_immutable
// // class SearchMap extends StatefulWidget {
// //   @override
// //   _SearchMap createState() => _SearchMap();
// // }
// //
// // class _SearchMap extends State<SearchMap> with TickerProviderStateMixin {
// //   late Uint8List imageDataRestaurant;
// //   late Uint8List imageDataHotel;
// //   late Uint8List imageDataShop;
// //   late Uint8List imageDataHolidayHome;
// //   late Uint8List imageDataActivity;
// //   late Uint8List imageDataDestinations;
// //   late ByteData byteData;
// //   List<String> searchNames = [];
// //   final Set<Marker> _markers = {};
// //   bool isload = false;
// //   int? mainid;
// //   int? rating;
// //   String? name, description, label, image, type;
// //   var price;
// //   var endPrice;
// //   CurrencyController currencyController = CurrencyController();
// //   MarkerId markerId = const MarkerId("YOUR-MARKER-ID");
// //   LatLng latLng = const LatLng(33.503447965138115, 36.23894707956065);
// //
// //   // final Completer<GoogleMapController> myMapController =
// //   //     Completer<GoogleMapController>();
// //   GoogleMapController? myMapController;
// //   final LatLng _mainLocation =
// //       const LatLng(25.229279238648324, 55.270265778344175);
// //   double zoom = 10.0;
// //   bool iscurrent2 = false;
// //   List<String>? t;
// //   double? lat;
// //   double? lng;
// //   TextEditingController searchcontroller = TextEditingController();
// //
// //   void getmarkers(
// //       {required double lat,
// //       required double lng,
// //       required String category,
// //       String? key,
// //       bool fromSearch = false}) async {
// //     Uint8List? imageData;
// //     final res = await Api.getmainCategorySearch(lat, lng, category, key);
// //
// //     searchResult = res?.results ?? [];
// //
// //     setState(() {
// //       searchNames.clear();
// //       _markers.clear();
// //     });
// //
// //     if (!fromSearch) {
// //       for (var i = 0; i < searchResult.length; i++) {
// //         searchNames.add(searchResult[i].name!);
// //
// //         searchResult[i].maincategorytype == AppConstants.hotelsId
// //             ? imageData = imageDataHotel
// //             : searchResult[i].maincategorytype == AppConstants.restaurantsId
// //                 ? imageData = imageDataRestaurant
// //                 : searchResult[i].maincategorytype == AppConstants.activitiesId
// //                     ? imageData = imageDataActivity
// //                     : searchResult[i].maincategorytype ==
// //                             AppConstants.coffeeShopsId
// //                         ? imageData = imageDataShop
// //                         : searchResult[i].maincategorytype ==
// //                                 AppConstants.destinationsId
// //                             ? imageData = imageDataDestinations
// //                             // ignore: unnecessary_statements
// //                             : null;
// //
// //         searchResult[i].maincategorytype == AppConstants.holidayHomesId
// //             ? imageData = imageDataHolidayHome
// //             : "";
// //
// //         if (searchResult[i].lat != null && searchResult[i].lng != null) {
// //           LatLng location = LatLng(searchResult[i].lat!, searchResult[i].lng!);
// //
// //           setState(() {
// //             log('Befoore ${_markers.length}');
// //             _markers.add(Marker(
// //               onTap: () {
// //                 progrsss(context);
// //                 pr?.show();
// //
// //                 searchResult[i].maincategorytype == AppConstants.holidayHomesId
// //                     ? gethoildayhomedetails(searchResult[i].id!)
// //                     : getmaincategorydetails(searchResult[i].id!);
// //
// //                 pr!.hide();
// //               },
// //               markerId: MarkerId(location.toString()),
// //               position: location,
// //               icon: BitmapDescriptor.fromBytes(imageData!),
// //             ));
// //
// //             log('Afteeer ${_markers.length}');
// //           });
// //         }
// //         setState(() {});
// //       }
// //     } else {
// //       // put one marker for one lat and one long
// //       final result = searchResult.firstWhere(
// //           (element) => searchcontroller.text == element.name,
// //           orElse: () => SearchMapModel());
// //
// //       log('resultssdadsd ${result.name}');
// //
// //       // .indexWhere((element) =>
// //       // element.lat == lat && element.lng == lng && element.name == key);
// //
// //       LatLng location = LatLng(result.lat!, result.lng!);
// //
// //       result.maincategorytype == AppConstants.hotelsId
// //           ? imageData = imageDataHotel
// //           : result.maincategorytype == AppConstants.restaurantsId
// //               ? imageData = imageDataRestaurant
// //               : result.maincategorytype == AppConstants.activitiesId
// //                   ? imageData = imageDataActivity
// //                   : result.maincategorytype == AppConstants.coffeeShopsId
// //                       ? imageData = imageDataShop
// //                       : result.maincategorytype == AppConstants.destinationsId
// //                           ? imageData = imageDataDestinations
// //                           // ignore: unnecessary_statements
// //                           : null;
// //
// //       _markers.add(Marker(
// //         onTap: () {
// //           progrsss(context);
// //           pr?.show();
// //
// //           result.maincategorytype == AppConstants.holidayHomesId
// //               ? gethoildayhomedetails(result.id!)
// //               : getmaincategorydetails(result.id!);
// //
// //           pr!.hide();
// //         },
// //         markerId: MarkerId(result.id?.toString() ?? location.toString()),
// //         position: location,
// //         icon: BitmapDescriptor.fromBytes(imageData ?? imageDataHotel),
// //       ));
// //
// //       setState(() {
// //         log('_markersasdasfsaLL ${_markers.length}  asdassad ${_markers}');
// //       });
// //     }
// //   }
// //
// //   ValueNotifier<bool> isActivity = ValueNotifier<bool>(false);
// //
// //   void getmaincategorydetails(int id) async {
// //     await Api.getmainCategorydetails(id).then((value) {
// //       Navigator.pop(context);
// //
// //       value != null
// //           ? setState(() {
// //               print("Pricce=> ${value.results['price']}");
// //               print(value.results['id']);
// //               mainid = value.results['id'];
// //               print(id);
// //               name = value.results['name'];
// //               description = value.results['description'];
// //               label = value.results['category'] != null
// //                   ? value.results['category']['name_en']
// //                   : '';
// //               image = value.results['images'] != null
// //                   ? value.results['images'][0]['url']
// //                   : '';
// //               rating = value.results['rating'];
// //               startprice = value.results['startprice'];
// //               price = value.results['price'] ??
// //                   value.results['startprice'] ??
// //                   value.results['endprice'];
// //               endPrice = value.results['endprice'] ??
// //                   value.results['price'] ??
// //                   value.results['startprice'];
// //               type = 'main';
// //               isload = true;
// //               log('asjdnsaf ${value.results['location'] != null ? value.results['location']['name'] : ''}');
// //               adDetails(
// //                 context,
// //                 video: value.results['video'],
// //                 title: name ?? '',
// //                 label: label,
// //                 category: value.results['category'] != null
// //                     ? value.results['category']['name']
// //                     : '',
// //                 website: value.results['website'],
// //                 phone: value.results['phone'],
// //                 rating: rating,
// //                 currencyController: currencyController,
// //                 startprice: startprice,
// //                 endPrice: endPrice,
// //                 price: price,
// //                 location: value.results['location'] != null
// //                     ? value.results['location']['name']
// //                     : '',
// //                 instagram: value.results['instagram'],
// //                 description: description,
// //                 image: image ?? '',
// //                 lat: value.results['latitude'].toString().toDouble(),
// //                 lng: value.results['longitude'].toString().toDouble(),
// //                 id: mainid,
// //               );
// //             })
// //           // ignore: unnecessary_statements
// //           : null;
// //       print(name);
// //     });
// //   }
// //
// //   void gethoildayhomedetails(int id) async {
// //     await Api.getholidayhomedetails(id).then((value) {
// //       print("Pricce=> ${value!.results['price']}");
// //       Navigator.pop(context);
// //
// //       final language = Localizations.localeOf(context);
// //
// //       setState(() {
// //         mainid = value.results['id'];
// //         name = value.results['name'];
// //         description = value.results['description'];
// //         label = value.results['label'];
// //         image = value.results['images'] != null
// //             ? value.results['images'][0]['url']
// //             : '';
// //         rating = value.results['rating'];
// //         price = value.results['startprice'] ?? value.results['price'];
// //         type = 'holiday home';
// //         isload = true;
// //
// //         detailsHolidayHome(context,
// //             title: value.results['name'],
// //             video: value.results['video'],
// //             description: description,
// //             roomnumber: value.results['rooms'],
// //             agentId: value.results['agent']['id'],
// //             startprice: price,
// //             currencyController: currencyController,
// //             location: value.results['location'] != null
// //                 ? value.results['location']['name']
// //                 : '',
// //             type: value.results['type'] != null
// //                 ? language.languageCode == 'en'
// //                     ? value.results['type']['name']['en']
// //                     : value.results['type']['name']['ar']
// //                 : '',
// //             lat: value.results['latitude'].toString().toDouble(),
// //             lng: value.results['longitude'].toString().toDouble(),
// //             size: value.results['size'],
// //             fromMaps: true,
// //             image: image,
// //             id: mainid ?? id);
// //       });
// //     });
// //   }
// //
// //   getMarkerhotels() async {
// //     try {
// //       byteData =
// //           await DefaultAssetBundle.of(context).load("assets/hotel_icon.png");
// //     } catch (e) {
// //       byteData =
// //           await DefaultAssetBundle.of(context).load("assets/hotel_icon.png");
// //     }
// //     imageDataHotel = byteData.buffer.asUint8List();
// //   }
// //
// //   getMarkerholidayhome() async {
// //     try {
// //       byteData =
// //           await DefaultAssetBundle.of(context).load("assets/holiday_icon.png");
// //     } catch (e) {
// //       byteData =
// //           await DefaultAssetBundle.of(context).load("assets/holiday_icon.png");
// //     }
// //     imageDataHolidayHome = byteData.buffer.asUint8List();
// //   }
// //
// //   getMarkeractivity() async {
// //     try {
// //       byteData =
// //           await DefaultAssetBundle.of(context).load("assets/activity_icon.png");
// //     } catch (e) {
// //       byteData =
// //           await DefaultAssetBundle.of(context).load("assets/activity_icon.png");
// //     }
// //     imageDataActivity = byteData.buffer.asUint8List();
// //   }
// //
// //   getMarkershop() async {
// //     try {
// //       byteData =
// //           await DefaultAssetBundle.of(context).load("assets/shop_icon.png");
// //     } catch (e) {
// //       byteData =
// //           await DefaultAssetBundle.of(context).load("assets/shop_icon.png");
// //     }
// //     imageDataShop = byteData.buffer.asUint8List();
// //   }
// //
// //   getMarkerresturant() async {
// //     try {
// //       byteData = await DefaultAssetBundle.of(context)
// //           .load("assets/resturant_icon.png");
// //     } catch (e) {
// //       byteData = await DefaultAssetBundle.of(context)
// //           .load("assets/resturant_icon.png");
// //     }
// //     imageDataRestaurant = byteData.buffer.asUint8List();
// //   }
// //
// //   getMarkerDestinations() async {
// //     try {
// //       byteData = await DefaultAssetBundle.of(context)
// //           .load("assets/icons/destination_map.png");
// //     } catch (e) {
// //       byteData = await DefaultAssetBundle.of(context)
// //           .load("assets/icons/destination_map.png");
// //     }
// //     imageDataDestinations = byteData.buffer.asUint8List();
// //   }
// //
// //   getAllMarkers() async {
// //     await getMarkerhotels();
// //     await getMarkerholidayhome();
// //     await getMarkeractivity();
// //     await getMarkershop();
// //     await getMarkerresturant();
// //     await getMarkerDestinations();
// //   }
// //
// //   List<SearchMapModel> searchResult = [];
// //
// //   @override
// //   void initState() {
// //     getAllMarkers();
// //     lat = 25.229279238648324;
// //     lng = 55.270265778344175;
// //     getmarkers(category: "", lat: lat!, lng: lng!);
// //
// //     setState(() {});
// //     currencyController.getcuurentcurrency(context);
// //
// //     super.initState();
// //   }
// //
// //   void onSearchTextChanged(String text) async {
// //     if (text.isEmpty) {
// //       getmarkers(category: "", lat: lat!, lng: lng!);
// //     } else {
// //       log('asfkldlkalsdfn ${text}');
// //
// //       getmarkers(
// //           category: "", lat: lat!, lng: lng!, key: text, fromSearch: true);
// //
// //       // Navigator.of(context).pushReplacement(
// //       //     MaterialPageRoute(builder: (BuildContext context) => SearchMap()));
// //     }
// //   }
// //
// //   @override
// //   Widget build(BuildContext context) {
// //     zoom = 10;
// //
// //     final searchField = Positioned(
// //         top: 20,
// //         left: 20,
// //         right: GetPlatform.isAndroid ? 70 : 10,
// //         child: Container(
// //           decoration: BoxDecoration(
// //               color: Colors.white, borderRadius: BorderRadius.circular(10)),
// //           child: Container(
// //             decoration: BoxDecoration(
// //               borderRadius: const BorderRadius.all(Radius.circular(5)),
// //               border: Border.all(color: Colors.black12, width: 1.0),
// //             ),
// //             child: TypeAheadField(
// //                 textFieldConfiguration: TextFieldConfiguration(
// //                   controller: searchcontroller,
// //                   onChanged: (value) {
// //                     if (value.isEmpty) {
// //                       onSearchTextChanged(value);
// //                       FocusScope.of(context).requestFocus(FocusNode());
// //                     }
// //                   },
// //                   decoration: InputDecoration(
// //                     prefixIcon:
// //                         const Icon(Icons.search, color: Color(0xff8B959E)),
// //                     hintText: AppLocalizations.of(context)
// //                         .translate('Search places and locations'),
// //                   ),
// //                 ),
// //                 errorBuilder: (context, error) {
// //                   return Padding(
// //                     padding: const EdgeInsets.all(8.0),
// //                     child: Center(
// //                       child: Text("$error"),
// //                     ),
// //                   );
// //                 },
// //                 loadingBuilder: (context) {
// //                   return const CircularProgressIndicator();
// //                 },
// //                 suggestionsCallback: (pattern) {
// //                   print("pattadasdern ${pattern}");
// //                   return searchNames
// //                       .where((element) => element.contains(pattern));
// //                 },
// //                 noItemsFoundBuilder: (context) {
// //                   return const Padding(
// //                     padding: EdgeInsets.all(8.0),
// //                     child: Center(
// //                       child: Text("No Item "),
// //                     ),
// //                   );
// //                 },
// //                 itemBuilder: (context, suggestion) {
// //                   return Padding(
// //                     padding: const EdgeInsets.all(8.0),
// //                     child: Text(suggestion.toString()),
// //                   );
// //                 },
// //                 onSuggestionSelected: (suggestion) async {
// //                   onSearchTextChanged(suggestion);
// //                   myMapController!.animateCamera(
// //                     CameraUpdate.newLatLng(_mainLocation),
// //                   );
// //                   zoom = 10.0;
// //                   searchcontroller.text = suggestion;
// //                 }),
// //           ),
// //         ));
// //
// //     return WillPopScope(
// //         onWillPop: () {
// //           Navigator.of(context).pushAndRemoveUntil(
// //               MaterialPageRoute(builder: (context) => const Home()),
// //               (Route<dynamic> route) => false);
// //
// //           return Future.value(false);
// //         },
// //         child: SafeArea(
// //             child: Scaffold(
// //           body: Stack(children: [
// //             SizedBox(
// //               height: MediaQuery.of(context).size.height,
// //               width: MediaQuery.of(context).size.width,
// //               child: GoogleMap(
// //                 padding: const EdgeInsets.symmetric(vertical: 60),
// //                 scrollGesturesEnabled: true,
// //                 tiltGesturesEnabled: true,
// //                 initialCameraPosition: CameraPosition(
// //                   target: _mainLocation,
// //                   zoom: zoom,
// //                 ),
// //                 myLocationEnabled: true,
// //                 myLocationButtonEnabled: true,
// //                 markers: _markers,
// //                 mapType: MapType.normal,
// //                 onMapCreated: (GoogleMapController controller) {
// //                   myMapController = controller;
// //
// //                   setState(() {});
// //                 },
// //                 onTap: (LatLng latLng) async {},
// //               ),
// //             ),
// //             searchField,
// //             BottomSection(
// //                 setState: setState,
// //                 myMapController: myMapController,
// //                 lat: lat,
// //                 lng: lng,
// //                 getmarkers: getmarkers,
// //                 isload: isload,
// //                 isActivity: isActivity,
// //                 mainLocation: _mainLocation)
// //           ]),
// //           bottomNavigationBar: CustomBottomNavgationBar(2),
// //         )));
// //   }
// // }
