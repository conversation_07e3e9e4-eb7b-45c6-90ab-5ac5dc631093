import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_typeahead/flutter_typeahead.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:page/src/core/config/constants.dart';
import 'package:page/src/core/extensions/extensions.dart';
import 'package:page/src/core/utils/print_services.dart';
import 'package:page/src/features/controllers/content_controller.dart';
import 'package:page/src/features/controllers/currency_controller.dart';
import 'package:page/src/features/models/locations.dart';
import 'package:page/src/features/models/video_model.dart';
import 'package:page/src/features/views/holiday_home_details/widgets/details.dart';
import 'package:page/src/features/views/map/show_on_map/widgets/details.dart';
import 'package:page/src/features/views/splash_screen/services/splash_services.dart';
import 'package:widget_to_marker/widget_to_marker.dart';

import '../../../../core/localization/app_localizations.dart';
import '../../../../core/services/api.dart';
import '../../../../core/shared_widgets/bottom_navgation_bar.dart';
import '../../../../core/shared_widgets/shared_widgets.dart';
import '../../../models/search_map.dart';
import '../../property_details/widgets/property_details.dart';

class PropertiesMapPage extends StatefulWidget {
  const PropertiesMapPage({super.key});

  @override
  _ShowOnMap createState() => _ShowOnMap();
}

class _ShowOnMap extends State<PropertiesMapPage>
    with TickerProviderStateMixin {
  bool isload = false;
  int? mainid;
  String? name, description, label, image, type;
  var price;
  GoogleMapController? myMapController;
  final Set<Marker> _markers = {};
  final LatLng _mainLocation =
      const LatLng(25.1829279238648324, 55.2505); // Adjusted longitude

  final currencyController = CurrencyController();

  double? lat;
  double? lng;
  String? lang;
  TextEditingController searchcontroller = TextEditingController();
  List<String> searchNames = [];
  ContentController contentController = ContentController();

  VideoModel? video;

  double? holidayRoomsValue;
  int? selectedPropertyTypeId;

  LatLng? navigatedLocation;

  // Store all search results for local filtering
  List<SearchMapModel> allSearchResults = [];

  @override
  void initState() {
    super.initState();

    currencyController.getcuurentcurrency(context);

    lat = 25.229279238648324;
    lng = 55.270265778344175;

    getmarkers(
        type: AppConstants.holidayHomesId.toString(), lat: lat!, lng: lng!);

    contentController.getsizesAndRooms();
  }

  Future<void> getmarkers({
    required double lat,
    required double lng,
    required String type,
    String? key,
  }) async {
    final value = await Api.getmainCategorySearch(
      lat,
      lng,
      type,
      key,
      holidayRoomsValue == null ? null : [holidayRoomsValue!],
      null, // Remove location filter from API
    );

    if (value != null) {
      setState(() {
        searchNames.clear();
        _markers.clear();
        allSearchResults = value.results; // Store all results
      });

      // Apply local filtering
      _filterMarkersLocally();
    }
  }

  void _filterMarkersLocally() async {
    setState(() {
      searchNames.clear();
      _markers.clear();
    });

    var filteredResults = allSearchResults.where((result) {
      bool matchesType = selectedPropertyTypeId == null ||
          result.typeId == selectedPropertyTypeId;
      bool matchesBedrooms =
          holidayRoomsValue == null || result.rooms.contains(holidayRoomsValue);
      return matchesType && matchesBedrooms;
    }).toList();

    for (var i = 0; i < filteredResults.length; i++) {
      searchNames.add(filteredResults[i].name!);

      if (filteredResults[i].lat != null && filteredResults[i].lng != null) {
        LatLng location =
            LatLng(filteredResults[i].lat!, filteredResults[i].lng!);
        navigatedLocation = location;

        final marker = Marker(
          onTap: () {
            progrsss(context);
            pr!.show();
            gethoildayhomedetails(filteredResults[i].id!);
          },
          markerId: MarkerId(location.toString()),
          position: location,
          icon: kIsWeb
              ? BitmapDescriptor.defaultMarker
              : await TextPrice(
                  text:
                      '${parsedPrice(filteredResults[i].price)} ${currencyController.currency}',
                  // '${filteredResults[i].price} ${currencyController.currency}',
                ).toBitmapDescriptor(
                  logicalSize: const Size(600, 350),
                  imageSize: const Size(700, 200),

                  //  logicalSize: const Size(200, 180),
                  //                     imageSize: const Size(200, 150),
                ),
        );

        setState(() {
          _markers.add(marker);
        });
      }
    }
    setState(() {});
  }

  void gethoildayhomedetails(int id) async {
    await Api.getholidayhomedetails(id).then((value) {
      video = VideoModel.fromJson(value!.results);

      value != null
          ? setState(() {
              mainid = value.results['id'];
              name = value.results['name'];
              description = value.results['description'];
              label = value.results['label'];
              image = value.results['images'] != null
                  ? value.results['images'][0]['url']
                  : '';
              price = value.results['startprice'];
              type = 'holiday home';
              isload = true;

              final language = Localizations.localeOf(context);

              pr!.hide();

              detailsHolidayHome(context,
                  title: value.results['name'],
                  agentId: value.results['agent']['id'],
                  video: value.results['video'],
                  rmsCategoryId: value.results['rms_category_id'],
                  description: description,
                  roomnumber: value.results['rooms'],
                  startprice: price,
                  currencyController: currencyController,
                  location: value.results['location'] != null
                      ? value.results['location']['name']
                      : '',
                  type: value.results['type'] != null
                      ? language.languageCode == 'en'
                          ? value.results['type']['name']['en']
                          : value.results['type']['name']['ar']
                      : '',
                  lat: value.results['latitude'].toString().toDouble(),
                  lng: value.results['longitude'].toString().toDouble(),
                  size: value.results['size'],
                  fromMaps: true,
                  image: image,
                  id: id);
              // _mapDetails();
            })
          // ignore: unnecessary_statements
          : null;
    });
  }

  Future<Uint8List> getMarker() async {
    ByteData byteData;

    byteData = await DefaultAssetBundle.of(context).load(
        kIsWeb ? "assets/web_holiday_icon.png" : "assets/Group <EMAIL>");

    return byteData.buffer.asUint8List();
  }

  Set<Marker> myMarker() {
    setState(() {
      _markers.add(Marker(
        // This marker id can be anything that uniquely identifies each marker.
        markerId: MarkerId(_mainLocation.toString()),
        position: _mainLocation,
        onTap: () {
          _mapDetails();
        },
        icon: BitmapDescriptor.defaultMarker,
      ));
    });

    return _markers;
  }

  void _mapDetails() => mapDetaials(context,
      name: name,
      description: description,
      image: image,
      price: price.toString(),
      mainid: mainid,
      video: video,
      label: label);

  Future<void> onSearchTextChanged(String text) async {
    if (text.isEmpty) {
      await getmarkers(
          type: AppConstants.holidayHomesId.toString(), lat: lat!, lng: lng!);
    } else {
      await getmarkers(
          type: AppConstants.holidayHomesId.toString(),
          lat: lat!,
          lng: lng!,
          key: text);
    }
  }

  static const double zoomNumber = 11;

  double zoom = zoomNumber;

  @override
  Widget build(BuildContext context) {
    final searchField = Positioned(
        top: 20,
        left: 20,
        right: 10,
        child: Container(
          decoration: BoxDecoration(
              color: Colors.white, borderRadius: BorderRadius.circular(10)),
          child: Container(
            decoration: BoxDecoration(
              borderRadius: const BorderRadius.all(Radius.circular(5)),
              border: Border.all(color: Colors.black12, width: 1.0),
            ),
            child: TypeAheadField(
                textFieldConfiguration: TextFieldConfiguration(
                  controller: searchcontroller,
                  onChanged: (value) {
                    if (value.isEmpty) {
                      onSearchTextChanged(value);
                      FocusScope.of(context).requestFocus(FocusNode());
                    }
                  },
                  decoration: InputDecoration(
                    border: InputBorder.none,
                    contentPadding:
                        EdgeInsets.only(top: isEng(context) ? 10 : 15),
                    prefixIcon:
                        const Icon(Icons.search, color: Color(0xff8B959E)),
                    hintText: AppLocalizations.of(context)
                        .translate('Search places and locations'),
                  ),
                ),
                errorBuilder: (context, error) {
                  return Padding(
                    padding: const EdgeInsets.all(8.0),
                    child: Center(
                      child: Text("$error"),
                    ),
                  );
                },
                loadingBuilder: (context) {
                  return const CircularProgressIndicator();
                },
                suggestionsCallback: (pattern) {
                  print("pattern ${pattern}");
                  return searchNames
                      .where((element) => element.contains(pattern));
                },
                noItemsFoundBuilder: (context) {
                  return const Padding(
                    padding: EdgeInsets.all(8.0),
                    child: Center(
                      child: Text("No Item "),
                    ),
                  );
                },
                itemBuilder: (context, suggestion) {
                  return Padding(
                    padding: const EdgeInsets.all(8.0),
                    child: Text(suggestion.toString()),
                  );
                },
                onSuggestionSelected: (suggestion) async {
                  await onSearchTextChanged(suggestion);

                  myMapController!.animateCamera(
                    CameraUpdate.newLatLng(navigatedLocation ?? _mainLocation),
                    // CameraUpdate.newLatLng(_mainLocation),
                  );

                  zoom = zoomNumber;
                  searchcontroller.text = suggestion;

                  setState(() {});
                }),
          ),
        ));

    void _showPropertyTypeDialog(BuildContext context, StateSetter setStateF) {
      final propertyTypes = allTypes
          .where((element) =>
              element.categoryIds.contains(AppConstants.propertiesId))
          .toList();

      showDialog(
        context: context,
        builder: (BuildContext context) {
          return AlertDialog(
            backgroundColor: Colors.white,
            surfaceTintColor: Colors.white,
            title: Text(AppLocalizations.of(context).translate('Choose Type')),
            content: Container(
              padding: const EdgeInsets.symmetric(
                horizontal: 12,
              ),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(10),
                color: Colors.white,
                boxShadow: [
                  BoxShadow(
                    color: Colors.grey.withOpacity(0.5),
                    spreadRadius: 1,
                    blurRadius: 7,
                    offset: const Offset(0, 3),
                  ),
                ],
              ),
              child: DropdownButton<int>(
                isExpanded: true,
                underline: const SizedBox(),
                borderRadius: BorderRadius.circular(12),
                dropdownColor: Colors.white,
                hint: Text(
                  AppLocalizations.of(context).translate('Choose Type'),
                  style: const TextStyle(color: Color(0xffB7B7B7)),
                ),
                value: selectedPropertyTypeId,
                items: propertyTypes.map((type) {
                  return DropdownMenuItem<int>(
                    value: type.id,
                    child: Text(type.name ?? ''),
                  );
                }).toList(),
                onChanged: (value) {
                  setStateF(() {
                    selectedPropertyTypeId = value;
                    // Filter markers locally instead of API call
                    _filterMarkersLocally();
                  });
                  Navigator.of(context).pop();
                },
              ),
            ),
            actions: [
              TextButton(
                onPressed: () {
                  Navigator.of(context).pop();
                },
                child: Text(AppLocalizations.of(context).translate('Close'),
                    style: const TextStyle(color: Colors.black)),
              ),
            ],
          );
        },
      );
    }

    void _showBedroomsDialog(BuildContext context, StateSetter setStateF) {
      showDialog(
        context: context,
        builder: (BuildContext context) {
          return AlertDialog(
            backgroundColor: Colors.white,
            surfaceTintColor: Colors.white,
            title: Text(
                AppLocalizations.of(context).translate('Number of Bedrooms')),
            content: SizedBox(
              width: 200,
              child: Wrap(
                spacing: 10.0,
                runSpacing: 10.0,
                children: ContentController.holidayRoomSizes.map((roomSize) {
                  return GestureDetector(
                    onTap: () {
                      setStateF(() {
                        holidayRoomsValue = roomSize.size;
                        // Filter markers locally instead of API call
                        _filterMarkersLocally();
                      });
                      Navigator.of(context).pop();
                    },
                    child: Card(
                      color: holidayRoomsValue == roomSize.size
                          ? const Color(0xff233549)
                          : Colors.white,
                      child: Padding(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 14, vertical: 10),
                        child: Text(
                          roomSize.size!.toInt().toString(),
                          style: TextStyle(
                            color: holidayRoomsValue == roomSize.size
                                ? Colors.white
                                : Colors.black,
                          ),
                        ),
                      ),
                    ),
                  );
                }).toList(),
              ),
            ),
            actions: [
              TextButton(
                onPressed: () {
                  Navigator.of(context).pop();
                },
                child: Text(AppLocalizations.of(context).translate('Close'),
                    style: const TextStyle(color: Colors.black)),
              ),
            ],
          );
        },
      );
    }

    final bottomBedroomsFilter = StatefulBuilder(
      builder: (BuildContext context, StateSetter setStateF) {
        final propertyTypeWidget = Expanded(
          child: GestureDetector(
            onTap: () => _showPropertyTypeDialog(context, setStateF),
            child: Container(
              height: 50,
              margin: const EdgeInsets.only(left: 10),
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(10),
                color: Colors.white,
                boxShadow: [
                  BoxShadow(
                    color: Colors.grey.withOpacity(0.5),
                    spreadRadius: 1,
                    blurRadius: 7,
                    offset: const Offset(0, 3),
                  ),
                ],
              ),
              child: Center(
                child: Text(
                  selectedPropertyTypeId == null
                      ? AppLocalizations.of(context).translate('Choose Type')
                      : allTypes
                              .firstWhere(
                                  (type) => type.id == selectedPropertyTypeId)
                              .name ??
                          '',
                  style: const TextStyle(fontSize: 13),
                ),
              ),
            ),
          ),
        );
        final numOfBedRoomsWidget = Expanded(
          child: GestureDetector(
            onTap: () => _showBedroomsDialog(context, setStateF),
            child: Container(
              height: 50,
              margin: const EdgeInsets.only(left: 10),
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(10),
                color: Colors.white,
                boxShadow: [
                  BoxShadow(
                    color: Colors.grey.withOpacity(0.5),
                    spreadRadius: 1,
                    blurRadius: 7,
                    offset: const Offset(0, 3),
                  ),
                ],
              ),
              child: Center(
                child: Text(
                  holidayRoomsValue == null
                      ? AppLocalizations.of(context)
                          .translate('Number of Bedrooms')
                      : holidayRoomsValue!.toInt().toString(),
                  style: const TextStyle(fontSize: 13),
                ),
              ),
            ),
          ),
        );
        return Padding(
          padding: const EdgeInsets.all(8.0),
          child: Row(
            textDirection: TextDirection.ltr,
            children: [
              if (isEng(context)) ...[
                propertyTypeWidget,
                numOfBedRoomsWidget,
              ] else ...[
                numOfBedRoomsWidget,
                propertyTypeWidget,
              ],
              const SizedBox(width: 60),
            ],
          ),
        );
      },
    );

    return SafeArea(
        child: Scaffold(
      appBar: AppBar(
        automaticallyImplyLeading: false,
        backgroundColor: const Color(0xFF27b4a8),
        centerTitle: true,
        title: Text(
          AppLocalizations.of(context).translate('holidayHomes'),
          style: TextStyle(
            fontFamily: isEng(context) ? 'Roboto' : 'Tajawal',
          ),
        ),
      ),
      body: Stack(children: [
        SizedBox(
          height: MediaQuery.of(context).size.height,
          width: MediaQuery.of(context).size.width,
          child: GoogleMap(
              initialCameraPosition: CameraPosition(
                target: _mainLocation,
                zoom: zoom,
              ),
              markers: Set<Marker>.of(_markers),
              mapType: MapType.normal,
              onMapCreated: (controller) {
                setState(() {
                  myMapController = controller;
                });
              },
              onTap: (LatLng latLng) async {}),
        ),
        searchField,
        if (ContentController.holidayRoomSizes.isNotEmpty)
          Positioned(
            bottom: 0,
            left: 0,
            right: 10,
            child: bottomBedroomsFilter,
          ),
      ]),
      bottomNavigationBar: CustomBottomNavgationBar(2),
    ));
  }
}

class TextPrice extends StatelessWidget {
  const TextPrice({
    super.key,
    required this.text,
  });

  final String text;

  @override
  Widget build(BuildContext context) {
    return FittedBox(
      fit: BoxFit.scaleDown,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(100),
          boxShadow: [
            BoxShadow(
              color: Colors.grey.withOpacity(0.5),
              spreadRadius: 1,
              blurRadius: 7,
              offset: const Offset(0, 3),
            ),
          ],
        ),
        child: Padding(
          padding: const EdgeInsets.only(top: 8.0),
          child: Text(
            text,
            style: const TextStyle(
              color: Colors.black,
              fontWeight: FontWeight.bold,
              fontSize: 30,
              fontFamily: 'Tajawal',
            ),
            textAlign: TextAlign.center,
          ),
        ),
      ),
    );
  }
}

//import 'package:flutter/foundation.dart';
// import 'package:flutter/material.dart';
// import 'package:flutter_typeahead/flutter_typeahead.dart';
// import 'package:google_maps_flutter/google_maps_flutter.dart';
// import 'package:page/src/core/config/constants.dart';
// import 'package:page/src/core/extensions/extensions.dart';
// import 'package:page/src/core/utils/print_services.dart';
// import 'package:page/src/features/controllers/content_controller.dart';
// import 'package:page/src/features/controllers/currency_controller.dart';
// import 'package:page/src/features/models/locations.dart';
// import 'package:page/src/features/models/video_model.dart';
// import 'package:page/src/features/views/holiday_home_details/widgets/details.dart';
// import 'package:page/src/features/views/map/show_on_map/widgets/details.dart';
// import 'package:page/src/features/views/splash_screen/services/splash_services.dart';
// import 'package:widget_to_marker/widget_to_marker.dart';
//
// import '../../../../core/localization/app_localizations.dart';
// import '../../../../core/services/api.dart';
// import '../../../../core/shared_widgets/bottom_navgation_bar.dart';
// import '../../../../core/shared_widgets/shared_widgets.dart';
// import '../../property_details/widgets/property_details.dart';
//
// class PropertiesMapPage extends StatefulWidget {
//   const PropertiesMapPage({super.key});
//
//   @override
//   _ShowOnMap createState() => _ShowOnMap();
// }
//
// class _ShowOnMap extends State<PropertiesMapPage>
//     with TickerProviderStateMixin {
//   bool isload = false;
//   int? mainid;
//   String? name, description, label, image, type;
//   var price;
//   GoogleMapController? myMapController;
//   final Set<Marker> _markers = {};
//   final LatLng _mainLocation =
//       const LatLng(25.1829279238648324, 55.2505); // Adjusted longitude
//
//   final currencyController = CurrencyController();
//
//   double? lat;
//   double? lng;
//   String? lang;
//   TextEditingController searchcontroller = TextEditingController();
//   List<String> searchNames = [];
//   ContentController contentController = ContentController();
//
//   VideoModel? video;
//
//   double? holidayRoomsValue;
//   int? selectedPropertyTypeId;
//
//   LatLng? navigatedLocation;
//
//   // Store all search results for local filtering
//   List<VideoModel> allSearchResults = [];
//
//   @override
//   void initState() {
//     super.initState();
//
//     currencyController.getcuurentcurrency(context);
//
//     lat = 25.229279238648324;
//     lng = 55.270265778344175;
//
//     getmarkers(
//         type: AppConstants.holidayHomesId.toString(), lat: lat!, lng: lng!);
//
//     contentController.getsizesAndRooms();
//   }
//
//   Future<void> getmarkers({
//     required double lat,
//     required double lng,
//     required String type,
//     String? key,
//   }) async {
//     if (properties.isNotEmpty) {
//       setState(() {
//         searchNames.clear();
//         _markers.clear();
//         allSearchResults = properties; // Store all results
//       });
//
//       // Apply local filtering
//       _filterMarkersLocally();
//     } else {
//       // final value = await Api.getmainCategorySearch(
//       //   lat,
//       //   lng,
//       //   type,
//       //   key,
//       //   holidayRoomsValue == null ? null : [holidayRoomsValue!],
//       //   null, // Remove location filter from API
//       // );
//       // setState(() {
//       //   searchNames.clear();
//       //   _markers.clear();
//       //   allSearchResults = value?.results ?? [];
//       // });
//       //
//       // // Apply local filtering
//       // _filterMarkersLocally();
//     }
//   }
//
//   void _filterMarkersLocally() async {
//     setState(() {
//       searchNames.clear();
//       _markers.clear();
//     });
//
//     var filteredResults = allSearchResults.where((result) {
//       bool matchesType = selectedPropertyTypeId == null ||
//           result.typeId == selectedPropertyTypeId;
//       bool matchesBedrooms = holidayRoomsValue == null ||
//           result.rooms == holidayRoomsValue.toString();
//       return matchesType && matchesBedrooms;
//     }).toList();
//
//     for (var i = 0; i < filteredResults.length; i++) {
//       searchNames.add(filteredResults[i].name!);
//
//       if (filteredResults[i].latitude != null &&
//           filteredResults[i].longitude != null) {
//         LatLng location =
//             LatLng(filteredResults[i].latitude!, filteredResults[i].longitude!);
//         navigatedLocation = location;
//
//         final marker = Marker(
//           onTap: () {
//             progrsss(context);
//             pr!.show();
//             gethoildayhomedetails(filteredResults[i].id!);
//           },
//           markerId: MarkerId(location.toString()),
//           position: location,
//           icon: kIsWeb
//               ? BitmapDescriptor.defaultMarker
//               : await TextPrice(
//                   text:
//                       '${parsedPrice(filteredResults[i].price)} ${currencyController.currency}',
//                   // '${filteredResults[i].price} ${currencyController.currency}',
//                 ).toBitmapDescriptor(
//                   logicalSize: const Size(600, 350),
//                   imageSize: const Size(700, 200),
//
//                   //  logicalSize: const Size(200, 180),
//                   //                     imageSize: const Size(200, 150),
//                 ),
//         );
//
//         setState(() {
//           _markers.add(marker);
//         });
//       }
//     }
//     setState(() {});
//   }
//
//   void gethoildayhomedetails(int id) async {
//     await Api.getholidayhomedetails(id).then((value) {
//       video = VideoModel.fromJson(value!.results);
//
//       value != null
//           ? setState(() {
//               mainid = value.results['id'];
//               name = value.results['name'];
//               description = value.results['description'];
//               label = value.results['label'];
//               image = value.results['images'] != null
//                   ? value.results['images'][0]['url']
//                   : '';
//               price = value.results['startprice'];
//               type = 'holiday home';
//               isload = true;
//
//               final language = Localizations.localeOf(context);
//
//               pr!.hide();
//
//               detailsHolidayHome(context,
//                   title: value.results['name'],
//                   agentId: value.results['agent']['id'],
//                   video: value.results['video'],
//                   rmsCategoryId: value.results['rms_category_id'],
//                   description: description,
//                   roomnumber: value.results['rooms'],
//                   startprice: price,
//                   currencyController: currencyController,
//                   location: value.results['location'] != null
//                       ? value.results['location']['name']
//                       : '',
//                   type: value.results['type'] != null
//                       ? language.languageCode == 'en'
//                           ? value.results['type']['name']['en']
//                           : value.results['type']['name']['ar']
//                       : '',
//                   lat: value.results['latitude'].toString().toDouble(),
//                   lng: value.results['longitude'].toString().toDouble(),
//                   size: value.results['size'],
//                   fromMaps: true,
//                   image: image,
//                   id: id);
//               // _mapDetails();
//             })
//           // ignore: unnecessary_statements
//           : null;
//     });
//   }
//
//   Future<Uint8List> getMarker() async {
//     ByteData byteData;
//
//     byteData = await DefaultAssetBundle.of(context).load(
//         kIsWeb ? "assets/web_holiday_icon.png" : "assets/Group <EMAIL>");
//
//     return byteData.buffer.asUint8List();
//   }
//
//   Set<Marker> myMarker() {
//     setState(() {
//       _markers.add(Marker(
//         // This marker id can be anything that uniquely identifies each marker.
//         markerId: MarkerId(_mainLocation.toString()),
//         position: _mainLocation,
//         onTap: () {
//           _mapDetails();
//         },
//         icon: BitmapDescriptor.defaultMarker,
//       ));
//     });
//
//     return _markers;
//   }
//
//   void _mapDetails() => mapDetaials(context,
//       name: name,
//       description: description,
//       image: image,
//       price: price.toString(),
//       mainid: mainid,
//       video: video,
//       label: label);
//
//   Future<void> onSearchTextChanged(String text) async {
//     if (text.isEmpty) {
//       await getmarkers(
//           type: AppConstants.holidayHomesId.toString(), lat: lat!, lng: lng!);
//     } else {
//       await getmarkers(
//           type: AppConstants.holidayHomesId.toString(),
//           lat: lat!,
//           lng: lng!,
//           key: text);
//     }
//   }
//
//   static const double zoomNumber = 11;
//
//   double zoom = zoomNumber;
//
//   @override
//   Widget build(BuildContext context) {
//     final searchField = Positioned(
//         top: 20,
//         left: 20,
//         right: 10,
//         child: Container(
//           decoration: BoxDecoration(
//               color: Colors.white, borderRadius: BorderRadius.circular(10)),
//           child: Container(
//             decoration: BoxDecoration(
//               borderRadius: const BorderRadius.all(Radius.circular(5)),
//               border: Border.all(color: Colors.black12, width: 1.0),
//             ),
//             child: TypeAheadField(
//                 textFieldConfiguration: TextFieldConfiguration(
//                   controller: searchcontroller,
//                   onChanged: (value) {
//                     if (value.isEmpty) {
//                       onSearchTextChanged(value);
//                       FocusScope.of(context).requestFocus(FocusNode());
//                     }
//                   },
//                   decoration: InputDecoration(
//                     border: InputBorder.none,
//                     contentPadding:
//                         EdgeInsets.only(top: isEng(context) ? 10 : 15),
//                     prefixIcon:
//                         const Icon(Icons.search, color: Color(0xff8B959E)),
//                     hintText: AppLocalizations.of(context)
//                         .translate('Search places and locations'),
//                   ),
//                 ),
//                 errorBuilder: (context, error) {
//                   return Padding(
//                     padding: const EdgeInsets.all(8.0),
//                     child: Center(
//                       child: Text("$error"),
//                     ),
//                   );
//                 },
//                 loadingBuilder: (context) {
//                   return const CircularProgressIndicator();
//                 },
//                 suggestionsCallback: (pattern) {
//                   print("pattern ${pattern}");
//                   return searchNames
//                       .where((element) => element.contains(pattern));
//                 },
//                 noItemsFoundBuilder: (context) {
//                   return const Padding(
//                     padding: EdgeInsets.all(8.0),
//                     child: Center(
//                       child: Text("No Item "),
//                     ),
//                   );
//                 },
//                 itemBuilder: (context, suggestion) {
//                   return Padding(
//                     padding: const EdgeInsets.all(8.0),
//                     child: Text(suggestion.toString()),
//                   );
//                 },
//                 onSuggestionSelected: (suggestion) async {
//                   await onSearchTextChanged(suggestion);
//
//                   myMapController!.animateCamera(
//                     CameraUpdate.newLatLng(navigatedLocation ?? _mainLocation),
//                     // CameraUpdate.newLatLng(_mainLocation),
//                   );
//
//                   zoom = zoomNumber;
//                   searchcontroller.text = suggestion;
//
//                   setState(() {});
//                 }),
//           ),
//         ));
//
//     void _showPropertyTypeDialog(BuildContext context, StateSetter setStateF) {
//       final propertyTypes = allTypes
//           .where((element) =>
//               element.categoryIds.contains(AppConstants.propertiesId))
//           .toList();
//
//       showDialog(
//         context: context,
//         builder: (BuildContext context) {
//           return AlertDialog(
//             backgroundColor: Colors.white,
//             surfaceTintColor: Colors.white,
//             title: Text(AppLocalizations.of(context).translate('Choose Type')),
//             content: Container(
//               padding: const EdgeInsets.symmetric(
//                 horizontal: 12,
//               ),
//               decoration: BoxDecoration(
//                 borderRadius: BorderRadius.circular(10),
//                 color: Colors.white,
//                 boxShadow: [
//                   BoxShadow(
//                     color: Colors.grey.withOpacity(0.5),
//                     spreadRadius: 1,
//                     blurRadius: 7,
//                     offset: const Offset(0, 3),
//                   ),
//                 ],
//               ),
//               child: DropdownButton<int>(
//                 isExpanded: true,
//                 underline: const SizedBox(),
//                 borderRadius: BorderRadius.circular(12),
//                 dropdownColor: Colors.white,
//                 hint: Text(
//                   AppLocalizations.of(context).translate('Choose Type'),
//                   style: const TextStyle(color: Color(0xffB7B7B7)),
//                 ),
//                 value: selectedPropertyTypeId,
//                 items: propertyTypes.map((type) {
//                   return DropdownMenuItem<int>(
//                     value: type.id,
//                     child: Text(type.name ?? ''),
//                   );
//                 }).toList(),
//                 onChanged: (value) {
//                   setStateF(() {
//                     selectedPropertyTypeId = value;
//                     // Filter markers locally instead of API call
//                     _filterMarkersLocally();
//                   });
//                   Navigator.of(context).pop();
//                 },
//               ),
//             ),
//             actions: [
//               TextButton(
//                 onPressed: () {
//                   Navigator.of(context).pop();
//                 },
//                 child: Text(AppLocalizations.of(context).translate('Close'),
//                     style: const TextStyle(color: Colors.black)),
//               ),
//             ],
//           );
//         },
//       );
//     }
//
//     void _showBedroomsDialog(BuildContext context, StateSetter setStateF) {
//       showDialog(
//         context: context,
//         builder: (BuildContext context) {
//           return AlertDialog(
//             backgroundColor: Colors.white,
//             surfaceTintColor: Colors.white,
//             title: Text(
//                 AppLocalizations.of(context).translate('Number of Bedrooms')),
//             content: SizedBox(
//               width: 200,
//               child: Wrap(
//                 spacing: 10.0,
//                 runSpacing: 10.0,
//                 children: ContentController.holidayRoomSizes.map((roomSize) {
//                   return GestureDetector(
//                     onTap: () {
//                       setStateF(() {
//                         holidayRoomsValue = roomSize.size;
//                         // Filter markers locally instead of API call
//                         _filterMarkersLocally();
//                       });
//                       Navigator.of(context).pop();
//                     },
//                     child: Card(
//                       color: holidayRoomsValue == roomSize.size
//                           ? const Color(0xff233549)
//                           : Colors.white,
//                       child: Padding(
//                         padding: const EdgeInsets.symmetric(
//                             horizontal: 14, vertical: 10),
//                         child: Text(
//                           roomSize.size!.toInt().toString(),
//                           style: TextStyle(
//                             color: holidayRoomsValue == roomSize.size
//                                 ? Colors.white
//                                 : Colors.black,
//                           ),
//                         ),
//                       ),
//                     ),
//                   );
//                 }).toList(),
//               ),
//             ),
//             actions: [
//               TextButton(
//                 onPressed: () {
//                   Navigator.of(context).pop();
//                 },
//                 child: Text(AppLocalizations.of(context).translate('Close'),
//                     style: const TextStyle(color: Colors.black)),
//               ),
//             ],
//           );
//         },
//       );
//     }
//
//     final bottomBedroomsFilter = StatefulBuilder(
//       builder: (BuildContext context, StateSetter setStateF) {
//         final propertyTypeWidget = Expanded(
//           child: GestureDetector(
//             onTap: () => _showPropertyTypeDialog(context, setStateF),
//             child: Container(
//               height: 50,
//               margin: const EdgeInsets.only(left: 10),
//               padding: const EdgeInsets.all(8),
//               decoration: BoxDecoration(
//                 borderRadius: BorderRadius.circular(10),
//                 color: Colors.white,
//                 boxShadow: [
//                   BoxShadow(
//                     color: Colors.grey.withOpacity(0.5),
//                     spreadRadius: 1,
//                     blurRadius: 7,
//                     offset: const Offset(0, 3),
//                   ),
//                 ],
//               ),
//               child: Center(
//                 child: Text(
//                   selectedPropertyTypeId == null
//                       ? AppLocalizations.of(context).translate('Choose Type')
//                       : allTypes
//                               .firstWhere(
//                                   (type) => type.id == selectedPropertyTypeId)
//                               .name ??
//                           '',
//                   style: const TextStyle(fontSize: 13),
//                 ),
//               ),
//             ),
//           ),
//         );
//         final numOfBedRoomsWidget = Expanded(
//           child: GestureDetector(
//             onTap: () => _showBedroomsDialog(context, setStateF),
//             child: Container(
//               height: 50,
//               margin: const EdgeInsets.only(left: 10),
//               padding: const EdgeInsets.all(8),
//               decoration: BoxDecoration(
//                 borderRadius: BorderRadius.circular(10),
//                 color: Colors.white,
//                 boxShadow: [
//                   BoxShadow(
//                     color: Colors.grey.withOpacity(0.5),
//                     spreadRadius: 1,
//                     blurRadius: 7,
//                     offset: const Offset(0, 3),
//                   ),
//                 ],
//               ),
//               child: Center(
//                 child: Text(
//                   holidayRoomsValue == null
//                       ? AppLocalizations.of(context)
//                           .translate('Number of Bedrooms')
//                       : holidayRoomsValue!.toInt().toString(),
//                   style: const TextStyle(fontSize: 13),
//                 ),
//               ),
//             ),
//           ),
//         );
//         return Padding(
//           padding: const EdgeInsets.all(8.0),
//           child: Row(
//             textDirection: TextDirection.ltr,
//             children: [
//               if (isEng(context)) ...[
//                 propertyTypeWidget,
//                 numOfBedRoomsWidget,
//               ] else ...[
//                 numOfBedRoomsWidget,
//                 propertyTypeWidget,
//               ],
//               const SizedBox(width: 60),
//             ],
//           ),
//         );
//       },
//     );
//
//     return SafeArea(
//         child: Scaffold(
//       appBar: AppBar(
//         automaticallyImplyLeading: false,
//         backgroundColor: const Color(0xFF27b4a8),
//         centerTitle: true,
//         title: Text(
//           AppLocalizations.of(context).translate('holidayHomes'),
//           style: TextStyle(
//             fontFamily: isEng(context) ? 'Roboto' : 'Tajawal',
//           ),
//         ),
//       ),
//       body: Stack(children: [
//         SizedBox(
//           height: MediaQuery.of(context).size.height,
//           width: MediaQuery.of(context).size.width,
//           child: GoogleMap(
//               initialCameraPosition: CameraPosition(
//                 target: _mainLocation,
//                 zoom: zoom,
//               ),
//               markers: Set<Marker>.of(_markers),
//               mapType: MapType.normal,
//               onMapCreated: (controller) {
//                 setState(() {
//                   myMapController = controller;
//                 });
//               },
//               onTap: (LatLng latLng) async {}),
//         ),
//         searchField,
//         if (ContentController.holidayRoomSizes.isNotEmpty)
//           Positioned(
//             bottom: 0,
//             left: 0,
//             right: 10,
//             child: bottomBedroomsFilter,
//           ),
//       ]),
//       bottomNavigationBar: CustomBottomNavgationBar(2),
//     ));
//   }
// }
//
// class TextPrice extends StatelessWidget {
//   const TextPrice({
//     super.key,
//     required this.text,
//   });
//
//   final String text;
//
//   @override
//   Widget build(BuildContext context) {
//     return FittedBox(
//       fit: BoxFit.scaleDown,
//       child: Container(
//         padding: const EdgeInsets.all(16),
//         decoration: BoxDecoration(
//           color: Colors.white,
//           borderRadius: BorderRadius.circular(100),
//           boxShadow: [
//             BoxShadow(
//               color: Colors.grey.withOpacity(0.5),
//               spreadRadius: 1,
//               blurRadius: 7,
//               offset: const Offset(0, 3),
//             ),
//           ],
//         ),
//         child: Padding(
//           padding: const EdgeInsets.only(top: 8.0),
//           child: Text(
//             text,
//             style: const TextStyle(
//               color: Colors.black,
//               fontWeight: FontWeight.bold,
//               fontSize: 30,
//               fontFamily: 'Tajawal',
//             ),
//             textAlign: TextAlign.center,
//           ),
//         ),
//       ),
//     );
//   }
// }
