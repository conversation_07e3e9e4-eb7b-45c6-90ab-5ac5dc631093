import 'package:flutter/material.dart';
import 'package:page/src/core/utils/main_cached_image.dart';
import 'package:page/src/features/controllers/currency_controller.dart';
import 'package:page/src/features/models/video_model.dart';

import '../../../../../core/localization/app_localizations.dart';
import '../../../../../core/shared_widgets/shared_widgets.dart';
import '../../../holiday_home_details/holiday_home_details.dart';

final CurrencyController _currencyController = CurrencyController();

void mapDetaials(
  BuildContext context, {
  required String? name,
  required String? description,
  required String? image,
  required String? price,
  required String? label,
  required mainid,
  required VideoModel? video,
}) {
  pr!.hide();

  _currencyController.getcuurentcurrency(context);

  showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Padding(
          padding:
              EdgeInsets.only(bottom: MediaQuery.of(context).viewInsets.bottom),
          child: Container(
            height: MediaQuery.of(context).size.height * 0.50,
            decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(25.0),
                    topRight: Radius.circular(25.0)),
                border: Border.all(color: Colors.black, width: 1.0)),
            child: SingleChildScrollView(
                child: Container(
                    padding: const EdgeInsets.only(left: 20, right: 20),
                    child: Column(
                      // crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const SizedBox(
                          height: 10,
                        ),
                        Container(
                            height: 5,
                            width: 50,
                            color: const Color(0xffD2D4D6)),
                        const SizedBox(
                          height: 20,
                        ),
                        Row(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisAlignment: MainAxisAlignment.start,
                          children: [
                            SizedBox(
                              height: 150,
                              child: ClipRRect(
                                borderRadius: BorderRadius.circular(5),
                                child: image != null
                                    ? MainCachedImage(
                                        image,
                                        height: 150,
                                        width: 100,
                                        fit: BoxFit.cover,
                                      )
                                    : Image.asset(
                                        'assets/Mask Group 2.png',
                                        height: 200,
                                        width: 100,
                                        fit: BoxFit.cover,
                                      ),
                              ),
                            ),
                            const SizedBox(
                              width: 20,
                            ),
                            Expanded(
                                child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              mainAxisAlignment: MainAxisAlignment.start,
                              children: [
                                Row(
                                  children: [
                                    Container(
                                        height: 30,
                                        width: 100,
                                        color: const Color(0xffF1F1F1),
                                        child: Center(
                                            child: Text(
                                          label ??
                                              AppLocalizations.of(context)
                                                  .translate('holidayhome'),
                                          style: const TextStyle(
                                              color: Color(0xff191C1F),
                                              fontSize: 12),
                                        ))),
                                    const SizedBox(
                                      width: 20,
                                    ),
                                  ],
                                ),
                                const SizedBox(
                                  height: 20,
                                ),
                                name != null
                                    ? Text(name,
                                        style: const TextStyle(
                                            fontWeight: FontWeight.bold,
                                            color: Color(0xff191C1F),
                                            fontSize: 17))
                                    : Container(),
                                const SizedBox(
                                  height: 10,
                                ),
                                description != null
                                    ? Text(
                                        description,
                                        softWrap: true,
                                        maxLines: 2,
                                        style: const TextStyle(fontSize: 12),
                                      )
                                    : Container(),
                                const SizedBox(
                                  height: 10,
                                ),
                                price != null
                                    ? Text(
                                        '${AppLocalizations.of(context).translate('price')}: $price ${_currencyController.currency}',
                                        style: const TextStyle(fontSize: 12))
                                    : Container()
                              ],
                            ))
                          ],
                        ),
                        const SizedBox(height: 20),
                        Center(
                            child: GestureDetector(
                                onTap: () async {
                                  Navigator.of(context).push(MaterialPageRoute(
                                      builder: (BuildContext context) =>
                                          HoldayHomeDetails(
                                            video: video,
                                          )));
                                },
                                child: Container(
                                  height: 40,
                                  width: MediaQuery.of(context).size.width,
                                  decoration: BoxDecoration(
                                      color: const Color(0xFF27b4a8),
                                      borderRadius: BorderRadius.circular(5)),
                                  child: Container(
                                      padding: const EdgeInsets.all(10),
                                      child: Center(
                                          child: Text(
                                        AppLocalizations.of(context)
                                            .translate('CheckDetails'),
                                        style: const TextStyle(
                                            color: Colors.white),
                                      ))),
                                ))),
                      ],
                    ))),
          )));
}
