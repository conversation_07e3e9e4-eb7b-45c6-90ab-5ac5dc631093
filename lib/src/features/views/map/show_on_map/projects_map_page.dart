import 'dart:developer';

import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_typeahead/flutter_typeahead.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:get/get.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:page/src/core/config/constants.dart';
import 'package:page/src/core/extensions/extensions.dart';
import 'package:page/src/core/utils/main_cached_image.dart';
import 'package:page/src/core/utils/print_services.dart';
import 'package:page/src/features/controllers/content_controller.dart';
import 'package:page/src/features/controllers/currency_controller.dart';
import 'package:page/src/features/models/locations.dart';
import 'package:page/src/features/models/video_model.dart';
import 'package:page/src/features/views/splash_screen/services/splash_services.dart';
import 'package:widget_to_marker/widget_to_marker.dart';

import '../../../../core/localization/app_localizations.dart';
import '../../../../core/services/api.dart';
import '../../../../core/shared_widgets/bottom_navgation_bar.dart';
import '../../../controllers/language_controller.dart';
import '../../projects/project_details_page.dart';

class ProjectsMapPage extends StatefulWidget {
  final double? initialLat;
  final double? initialLng;

  const ProjectsMapPage({
    super.key,
    this.initialLat,
    this.initialLng,
  });

  @override
  _ProjectsMapPageState createState() => _ProjectsMapPageState();
}

class _ProjectsMapPageState extends State<ProjectsMapPage>
    with TickerProviderStateMixin {
  bool isload = false;
  int? mainid;
  String? name, description, label, image, type;
  var price;
  GoogleMapController? myMapController;
  final Set<Marker> _markers = {};
  late LatLng _mainLocation;

  final currencyController = CurrencyController();

  double? lat;
  double? lng;
  String? lang;
  TextEditingController searchcontroller = TextEditingController();
  List<String> searchNames = [];
  ContentController contentController = ContentController();

  VideoModel? video;

  int? projectLocationValue;
  int? projectPropertyStatusValue;
  String? projectPaymentMethodValue;
  int? selectedProjectTypeId;

  LatLng? navigatedLocation;

  // Store all project results for local filtering
  List<VideoModel> allProjectResults = [];

  @override
  void initState() {
    super.initState();

    currencyController.getcuurentcurrency(context);

    // Use provided coordinates or default to Dubai
    lat = widget.initialLat ?? 25.089921601164132;
    lng = widget.initialLng ?? 55.16847029328346;
    // lat = widget.initialLat ?? 25.229279238648324;
    // lng = widget.initialLng ?? (55.270265778344175);
    _mainLocation = LatLng(lat!, lng!);

    getmarkers(type: AppConstants.projectsId.toString(), lat: lat!, lng: lng!);

    contentController.getlocations();
    contentController.getPropertyStatuses();
  }

  Future<void> getmarkers({
    required double lat,
    required double lng,
    required String type,
    String? key,
  }) async {
    final value = await Api.getmainCategory(
      1,
      100,
      key ?? '',
      AppConstants.projectsId.toString(),
    );

    // final value = await Api.getmainCategorySearch(
    //   lat,
    //   lng,
    //   '10',
    //   key,
    //   null, // Projects don't use bedroom filters
    //   projectLocationValue,
    // );

    if (value.category.isNotEmpty) {
      setState(() {
        searchNames.clear();
        _markers.clear();
        allProjectResults = value.category; // Store all results
      });

      // Apply local filtering
      _filterMarkersLocally();
    }
  }

  void _filterMarkersLocally() async {
    setState(() {
      searchNames.clear();
      _markers.clear();
    });

    // If specific coordinates are provided, show only that project
    bool showSpecificProject =
        widget.initialLat != null && widget.initialLng != null;

    var filteredResults = allProjectResults.where((project) {
      bool matchesType = selectedProjectTypeId == null ||
          project.typeId == selectedProjectTypeId;
      bool matchesLocation = projectLocationValue == null ||
          project.locationId == projectLocationValue;
      return matchesType && matchesLocation;
    }).toList();

    for (var i = 0; i < filteredResults.length; i++) {
      if (filteredResults[i].latitude != null &&
          filteredResults[i].longitude != null) {
        LatLng location =
            LatLng(filteredResults[i].latitude!, filteredResults[i].longitude!);

        // If showing specific project, only add marker if coordinates match
        if (showSpecificProject) {
          double latDiff = (location.latitude - widget.initialLat!).abs();
          double lngDiff = (location.longitude - widget.initialLng!).abs();
          // Allow small tolerance for coordinate matching (0.001 degrees ≈ 100m)
          if (latDiff > 0.001 || lngDiff > 0.001) {
            continue; // Skip this project if coordinates don't match
          }
        }

        searchNames.add(filteredResults[i].name!);
        navigatedLocation = location;

        final marker = Marker(
          onTap: () {
            _showProjectPopup(context, filteredResults[i]);
          },
          markerId: MarkerId(location.toString()),
          position: location,
          icon: kIsWeb
              ? BitmapDescriptor.defaultMarker
              : await const ProjectIcon().toBitmapDescriptor(
                  logicalSize: const Size(100, 100),
                  imageSize: const Size(100, 100),
                ),
        );

        setState(() {
          _markers.add(marker);
        });
      }
    }
    setState(() {});
  }

  void _showProjectPopup(BuildContext context, VideoModel project) {
    final firstPlan = project.projectPlans?.isNotEmpty == true
        ? project.projectPlans!.first
        : null;

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return Dialog(
          backgroundColor: Colors.white,
          surfaceTintColor: Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          child: Container(
            width: MediaQuery.of(context).size.width * 0.9,
            // constraints: const BoxConstraints(maxHeight: 400),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Image section
                Stack(
                  alignment: isEnglish(context)
                      ? Alignment.topRight
                      : Alignment.topLeft,
                  children: [
                    ClipRRect(
                      borderRadius: const BorderRadius.only(
                        topLeft: Radius.circular(16),
                        topRight: Radius.circular(16),
                      ),
                      child: SizedBox(
                        height: 250,
                        width: double.infinity,
                        child: project.images?.isNotEmpty == true
                            ? MainCachedImage(
                                project.images!,
                                fit: BoxFit.cover,
                              )
                            : Container(
                                color: Colors.grey[300],
                                child: const Icon(
                                  Icons.image,
                                  size: 50,
                                  color: Colors.grey,
                                ),
                              ),
                      ),
                    ),
                    Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: GestureDetector(
                        onTap: () => Navigator.pop(context),
                        child: const CircleAvatar(
                          backgroundColor: Colors.white,
                          radius: 19,
                          child: Icon(
                            Icons.close,
                            color: Colors.black,
                            size: 22,
                          ),
                        ),
                      ),
                    )
                  ],
                ),
                // Content section
                Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        project.name ?? '',
                        style: const TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: Colors.black87,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                      const SizedBox(height: 8),
                      Row(
                        children: [
                          Icon(
                            Icons.location_on,
                            size: 16,
                            color: primaryColor,
                          ),
                          const SizedBox(width: 4),
                          Expanded(
                            child: Text(
                              project.locationName ?? '',
                              style: TextStyle(
                                fontSize: 14,
                                color: Colors.grey[600],
                              ),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),

                      Row(
                        children: [
                          Icon(
                            FontAwesomeIcons.moneyBillWave,
                            size: 14,
                            color: primaryColor,
                          ),
                          const SizedBox(width: 4),
                          Expanded(
                            child: Text(
                              firstPlan?.priceFrom != 0
                                  ? '${AppLocalizations.of(context).translate('Price From')} ${sortPrice(firstPlan?.priceFrom ?? 0)} ${currencyController.currency} (${AppLocalizations.of(context).translate(project.paymentMethod?.capitalize ?? 'Cash')})'
                                  : '-',
                              style: TextStyle(
                                fontSize: 12,
                                color: Colors.grey[600],
                              ),
                              maxLines: 3,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                        ],
                      ),

                      const SizedBox(height: 8),

                      // Three icons with values
                      Row(
                        children: [
                          Icon(
                            Icons.bed,
                            size: 16,
                            color: primaryColor,
                          ),
                          const SizedBox(width: 4),
                          Expanded(
                            child: Text(
                              project.projectPlans?.map((plan) {
                                    return isEnglish(context)
                                        ? plan.bedroomsEn ??
                                            plan.bedroomsAr ??
                                            ''
                                        : plan.bedroomsAr ??
                                            plan.bedroomsEn ??
                                            '';
                                  }).join(', ') ??
                                  '-',
                              style: TextStyle(
                                fontSize: 12,
                                color: Colors.grey[600],
                              ),
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 16),
                      SizedBox(
                        width: double.infinity,
                        child: ElevatedButton(
                          onPressed: () {
                            Navigator.of(context).pop();
                            Navigator.of(context).push(MaterialPageRoute(
                              builder: (BuildContext context) =>
                                  ProjectDetailsPage(
                                project: project,
                              ),
                            ));
                          },
                          style: ElevatedButton.styleFrom(
                            backgroundColor: primaryColor,
                            foregroundColor: Colors.white,
                            padding: const EdgeInsets.symmetric(vertical: 12),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                          ),
                          child: Text(
                            AppLocalizations.of(context)
                                .translate('Project Details'),
                            style: const TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Future<void> onSearchTextChanged(String text) async {
    if (text.isEmpty) {
      await getmarkers(
          type: AppConstants.projectsId.toString(), lat: lat!, lng: lng!);
    } else {
      await getmarkers(
          type: AppConstants.projectsId.toString(),
          lat: lat!,
          lng: lng!,
          key: text);
    }
  }

  void _showProjectTypeDialog(BuildContext context, StateSetter setStateF) {
    final projectTypes = allTypes
        .where(
            (element) => element.categoryIds.contains(AppConstants.projectsId))
        .toList();

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          backgroundColor: Colors.white,
          surfaceTintColor: Colors.white,
          title: Text(
              AppLocalizations.of(context).translate('Choose Project Type')),
          content: Container(
            padding: const EdgeInsets.symmetric(horizontal: 12),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(10),
              color: Colors.white,
              boxShadow: [
                BoxShadow(
                  color: Colors.grey.withOpacity(0.5),
                  spreadRadius: 1,
                  blurRadius: 7,
                  offset: const Offset(0, 3),
                ),
              ],
            ),
            child: DropdownButton<int>(
              isExpanded: true,
              dropdownColor: Colors.white,
              underline: const SizedBox(),
              borderRadius: BorderRadius.circular(12),
              hint: Text(
                AppLocalizations.of(context).translate('Choose Project Type'),
                style: const TextStyle(color: Color(0xffB7B7B7)),
              ),
              value: selectedProjectTypeId,
              items: projectTypes.map((type) {
                return DropdownMenuItem<int>(
                  value: type.id,
                  child: Text(type.name ?? ''),
                );
              }).toList(),
              onChanged: (value) {
                setStateF(() {
                  selectedProjectTypeId = value;
                  _filterMarkersLocally();
                });
                Navigator.of(context).pop();
              },
            ),
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: Text(AppLocalizations.of(context).translate('Close'),
                  style: const TextStyle(color: Colors.black)),
            ),
          ],
        );
      },
    );
  }

  void _showProjectLocationDialog(BuildContext context, StateSetter setStateF) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          backgroundColor: Colors.white,
          surfaceTintColor: Colors.white,
          title: Text(AppLocalizations.of(context)
              .translate('Choose Project Location')),
          content: Container(
            padding: const EdgeInsets.symmetric(horizontal: 12),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(10),
              color: Colors.white,
              boxShadow: [
                BoxShadow(
                  color: Colors.grey.withOpacity(0.5),
                  spreadRadius: 1,
                  blurRadius: 7,
                  offset: const Offset(0, 3),
                ),
              ],
            ),
            child: DropdownButton<int>(
              isExpanded: true,
              dropdownColor: Colors.white,
              borderRadius: BorderRadius.circular(12),
              underline: const SizedBox(),
              hint: Text(
                AppLocalizations.of(context)
                    .translate('Choose Project Location'),
                style: const TextStyle(color: Color(0xffB7B7B7)),
              ),
              value: projectLocationValue,
              items: ContentController.locations.map((Locations value) {
                return DropdownMenuItem<int>(
                  value: value.id,
                  child: Text(value.name!),
                );
              }).toList(),
              onChanged: (value) {
                setStateF(() {
                  projectLocationValue = value;
                  _filterMarkersLocally();
                });
                Navigator.of(context).pop();
              },
            ),
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: Text(AppLocalizations.of(context).translate('Close'),
                  style: const TextStyle(color: Colors.black)),
            ),
          ],
        );
      },
    );
  }

  static const double zoomNumber = 10;
  double zoom = zoomNumber;

  @override
  Widget build(BuildContext context) {
    final searchField = widget.initialLat != null && widget.initialLng != null
        ? const SizedBox.shrink()
        : Positioned(
            top: 20,
            left: 20,
            right: 20,
            child: Container(
              decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(12),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.1),
                      blurRadius: 10,
                      offset: const Offset(0, 2),
                    ),
                  ]),
              child: TypeAheadField(
                  textFieldConfiguration: TextFieldConfiguration(
                    controller: searchcontroller,
                    onChanged: (value) {
                      if (value.isEmpty) {
                        onSearchTextChanged(value);
                        FocusScope.of(context).requestFocus(FocusNode());
                      }
                    },
                    decoration: InputDecoration(
                      border: InputBorder.none,
                      contentPadding: EdgeInsets.only(
                        top: isEng(context) ? 15 : 20,
                        left: 16,
                        right: 16,
                      ),
                      prefixIcon: const Icon(
                        Icons.search,
                        color: Color(0xff8B959E),
                        size: 20,
                      ),
                      hintText: AppLocalizations.of(context)
                          .translate('Search projects'),
                      hintStyle: const TextStyle(
                        color: Color(0xff8B959E),
                        fontSize: 14,
                      ),
                    ),
                  ),
                  errorBuilder: (context, error) {
                    return Padding(
                      padding: const EdgeInsets.all(8.0),
                      child: Center(
                        child: Text("$error"),
                      ),
                    );
                  },
                  loadingBuilder: (context) {
                    return const Padding(
                      padding: EdgeInsets.all(16.0),
                      child: Center(child: CircularProgressIndicator()),
                    );
                  },
                  suggestionsCallback: (pattern) {
                    return searchNames.where((element) =>
                        element.toLowerCase().contains(pattern.toLowerCase()));
                  },
                  noItemsFoundBuilder: (context) {
                    return Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Center(
                        child: Text(
                          AppLocalizations.of(context)
                              .translate('No projects found'),
                          style: const TextStyle(color: Colors.grey),
                        ),
                      ),
                    );
                  },
                  itemBuilder: (context, suggestion) {
                    return Padding(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 16, vertical: 12),
                      child: Text(
                        suggestion.toString(),
                        style: const TextStyle(fontSize: 14),
                      ),
                    );
                  },
                  onSuggestionSelected: (suggestion) async {
                    await onSearchTextChanged(suggestion);

                    myMapController!.animateCamera(
                      CameraUpdate.newLatLng(
                          navigatedLocation ?? _mainLocation),
                    );

                    zoom = zoomNumber;
                    searchcontroller.text = suggestion;

                    setState(() {});
                  }),
            ));

    return SafeArea(
        child: Scaffold(
      appBar: AppBar(
        automaticallyImplyLeading: false,
        backgroundColor: primaryColor,
        centerTitle: true,
        elevation: 0,
        title: Text(
          AppLocalizations.of(context).translate('Projects Map'),
          style: TextStyle(
            fontFamily: isEng(context) ? 'Roboto' : 'Tajawal',
            color: Colors.white,
            fontWeight: FontWeight.w600,
          ),
        ),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => Navigator.of(context).pop(),
        ),
      ),
      body: Stack(children: [
        SizedBox(
          height: MediaQuery.of(context).size.height,
          width: MediaQuery.of(context).size.width,
          child: GoogleMap(
              initialCameraPosition: CameraPosition(
                target: _mainLocation,
                bearing: -20,
                zoom: zoom,
              ),
              markers: Set<Marker>.of(_markers),
              mapType: MapType.normal,
              onCameraMove: (CameraPosition position) {
                log('LAAT ${position.target.latitude} LL ${position.target.longitude}');
              },
              onMapCreated: (controller) {
                setState(() {
                  myMapController = controller;
                });
              },
              onTap: (LatLng latLng) async {}),
        ),
        searchField,
        // Add bottom filters only if not showing specific project
        if (widget.initialLat == null && widget.initialLng == null)
          _buildBottomFilters(),
      ]),
      bottomNavigationBar: CustomBottomNavgationBar(2),
    ));
  }

  Widget _buildBottomFilters() {
    return StatefulBuilder(
      builder: (BuildContext context, StateSetter setStateF) {
        final projectTypeWidget = Expanded(
          child: GestureDetector(
            onTap: () => _showProjectTypeDialog(context, setStateF),
            child: Container(
              height: 50,
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(10),
                color: Colors.white,
                boxShadow: [
                  BoxShadow(
                    color: Colors.grey.withOpacity(0.5),
                    spreadRadius: 1,
                    blurRadius: 7,
                    offset: const Offset(0, 3),
                  ),
                ],
              ),
              child: Center(
                child: Text(
                  selectedProjectTypeId == null
                      ? AppLocalizations.of(context).translate('Project Type')
                      : allTypes
                              .firstWhere(
                                  (type) => type.id == selectedProjectTypeId)
                              .name ??
                          '',
                  style: const TextStyle(fontSize: 13),
                ),
              ),
            ),
          ),
        );

        final projectLocationWidget = Expanded(
          child: GestureDetector(
            onTap: () => _showProjectLocationDialog(context, setStateF),
            child: Container(
              height: 50,
              margin: const EdgeInsets.only(left: 10),
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(10),
                color: Colors.white,
                boxShadow: [
                  BoxShadow(
                    color: Colors.grey.withOpacity(0.5),
                    spreadRadius: 1,
                    blurRadius: 7,
                    offset: const Offset(0, 3),
                  ),
                ],
              ),
              child: Center(
                child: Text(
                  projectLocationValue == null
                      ? AppLocalizations.of(context)
                          .translate('Project Location')
                      : ContentController.locations
                          .firstWhere((loc) => loc.id == projectLocationValue)
                          .name!,
                  style: const TextStyle(fontSize: 13),
                ),
              ),
            ),
          ),
        );

        return Positioned(
          bottom: 0,
          left: 0,
          right: 10,
          child: Padding(
            padding: const EdgeInsets.all(8.0),
            child: Row(
              textDirection: TextDirection.ltr,
              children: [
                if (isEng(context)) ...[
                  projectTypeWidget,
                  projectLocationWidget,
                ] else ...[
                  projectLocationWidget,
                  projectTypeWidget,
                ],
                const SizedBox(width: 60),
              ],
            ),
          ),
        );
      },
    );
  }
}

class ProjectIcon extends StatelessWidget {
  const ProjectIcon({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 100,
      height: 100,
      decoration: BoxDecoration(
        color: Colors.white,
        shape: BoxShape.circle,
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.5),
            spreadRadius: 1,
            blurRadius: 7,
            offset: const Offset(0, 3),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(30),
        child: Icon(
          CupertinoIcons.home,
          color: primaryColor,
          size: 60,
        ),
        // Image.asset(
        //   kIsWeb ? 'assets/web_holiday_icon.png' : 'assets/holiday_icon.png',
        //   width: 70,
        //   height: 70,
        //   fit: BoxFit.contain,
        // ),
      ),
    );
  }
}
