import 'package:flutter/material.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';

class OpenMap extends StatefulWidget {
  final double lat;
  final double lng;
  const OpenMap(this.lat, this.lng, {super.key});

  @override
  _OpenMap createState() => _OpenMap();
}

class _OpenMap extends State<OpenMap> with TickerProviderStateMixin {
  final markers = <Marker>{};
  GoogleMapController? myMapController;
  final Set<Marker> _markers = {};
  LatLng _mainLocation = const LatLng(25.69893, 32.6421);

  Set<Marker> myMarker() {
    setState(() {
      _markers.add(Marker(
        // This marker id can be anything that uniquely identifies each marker.
        markerId: MarkerId(_mainLocation.toString()),
        position: _mainLocation,

        icon: BitmapDescriptor.defaultMarker,
      ));
    });

    return _markers;
  }

  @override
  void initState() {
    setState(() {
      _mainLocation = LatLng(widget.lat, widget.lng);
    });

    super.initState();
    _markers.add(Marker(
      // This marker id can be anything that uniquely identifies each marker.
      markerId: MarkerId(_mainLocation.toString()),
      position: _mainLocation,

      icon: BitmapDescriptor.defaultMarker,
    ));
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
        onWillPop: () async {
          Navigator.pop(context, 'ANY RETURN VALUE');
          return Future(() => false);
        },
        child: SafeArea(
            child: Stack(children: [
          Container(
              padding: const EdgeInsets.only(left: 10, right: 10),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Container(height: 50, width: 50, color: Colors.red),
                ],
              )),
          Container(
            height: MediaQuery.of(context).size.height,
            width: MediaQuery.of(context).size.width,
            child: GoogleMap(
              initialCameraPosition: CameraPosition(
                target: _mainLocation,
                zoom: 10.0,
              ),
              myLocationEnabled: true,
              myLocationButtonEnabled: true,
              markers: Set<Marker>.of(_markers),
              mapType: MapType.normal,
              onMapCreated: (controller) {
                setState(() {
                  myMapController = controller;
                });
              },
            ),
          ),
          Positioned(
            right: 20,
            top: 10,
            child: GestureDetector(
                onTap: () {
                  Navigator.pop(context);
                },
                child: Container(
                    height: 30,
                    width: 30,
                    decoration: const BoxDecoration(
                      borderRadius: BorderRadius.all(Radius.circular(30)),
                      color: Color(0xff233549),
                    ),
                    child: const Center(
                      child: Icon(
                        Icons.keyboard_arrow_right,
                        color: Colors.white,
                      ),
                    ))),
          ),
        ])));
  }
}
