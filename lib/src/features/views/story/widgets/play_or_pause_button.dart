// import 'dart:developer';
//
// import 'package:flutter/material.dart';
//
// class PlayOrPauseButton extends StatelessWidget {
//   final bool isPlaying;
//   final VoidCallback onPressed;
//   const PlayOrPauseButton(
//       {Key? key, required this.isPlaying, required this.onPressed})
//       : super(key: key);
//
//   @override
//   Widget build(BuildContext context) {
//     log('PlayOrPauseButton $isPlaying');
//     return CircleAvatar(
//       backgroundColor: Colors.black.withOpacity(0.5),
//       child: IconButton(
//         icon: Icon(isPlaying ? Icons.pause : Icons.play_arrow),
//         onPressed: onPressed,
//       ),
//     );
//   }
// }
