import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:page/src/core/extensions/extensions.dart';
import 'package:page/src/features/controllers/language_controller.dart';
import 'package:video_player/video_player.dart';

import '../../../../../core/services/api.dart';
import '../../../../../core/shared_widgets/shared_widgets.dart';
import '../../../car_rent_details/widgets/car_rental_details.dart';
import 'reel_details.dart';

void getcarrentdetails(BuildContext context, int id,
    VideoPlayerController videoPlayerController) async {
  progrsss(context);
  pr?.show();

  final LanguageController language = LanguageController();
  await language.getcuurentlanguage();

  await Api.getmainCategorydetails(id).then((value) {
    video = value!.results['video'];
    name = value.results['name'];
    description = value.results['description'];
    location = value.results['location'] != null
        ? value.results['location']['name']
        : '';
    startprice = value.results['price'];
    privateDriverPrice = value.results['private_driver_price'];
    lat = value.results['latitude'].toString().toDouble();
    lng = value.results['longitude'].toString().toDouble();
    brand = value.results['brand_car'] != null
        ? language.languagecode == 'en'
            ? value.results['brand_car']['name']['en']
            : value.results['brand_car']['name']['ar']
        : '';
    type = value.results['type'] != null
        ? language.languagecode == 'en'
            ? value.results['type']['name']['en']
            : value.results['type']['name']['ar']
        : '';

    year = value.results['year_car'] != null
        ? value.results['year_car']['year']
        : '';
    feature = value.results['features'] != null &&
            value.results['features'].isNotEmpty
        ? language.languagecode == 'en'
            ? value.results['features'][0]['name']['en']
            : value.results['features'][0]['name']['ar']
        : '';
    phone = value.results['phone'];

    var featureList = value.results['features'] != null &&
            value.results['features'].length > 0
        ? List<String>.from(value.results['features'].map((e) =>
            language.languagecode == 'en' ? e['name']['en'] : e['name']['ar']))
        : [];

    log('sdjadnasldnslk ${value.results}');

    pr?.hide();

    Future<void> future = carrentaldetaialsWidget(context,
        id: id,
        phone: phone,
        agentId: value.results['agent'] != null
            ? value.results['agent']['id']
            : null,
        name: name,
        description: description,
        startprice: startprice,
        privateDriverPrice: privateDriverPrice,
        currencyController: currencyController,
        type: type,
        brand: brand,
        feature: feature,
        featureList: featureList,
        year: year);

    future.then((value) {
      closeModal(videoPlayerController);
    });
  });
}

// void carrentaldetaials(
//     BuildContext context, VideoPlayerController videoPlayerController) {
//   Future<void> future = showModalBottomSheet(
//       context: context,
//       isScrollControlled: true,
//       backgroundColor: Colors.transparent,
//       builder: (context) => Padding(
//           padding:
//               EdgeInsets.only(bottom: MediaQuery.of(context).viewInsets.bottom),
//           child: Container(
//             height: MediaQuery.of(context).size.height * 0.50,
//             decoration: BoxDecoration(
//                 color: const Color(0xffF5F6F7),
//                 borderRadius: const BorderRadius.only(
//                     topLeft: Radius.circular(25.0),
//                     topRight: Radius.circular(25.0)),
//                 border: Border.all(color: Colors.black, width: 1.0)),
//             child: SingleChildScrollView(
//                 child: Column(
//               children: [
//                 const SizedBox(
//                   height: 10,
//                 ),
//                 Container(height: 5, width: 50, color: const Color(0xffD2D4D6)),
//                 const SizedBox(
//                   height: 20,
//                 ),
//                 Center(
//                     child: Text(
//                   AppLocalizations.of(context).translate('More Details'),
//                   style: const TextStyle(fontWeight: FontWeight.bold),
//                 )),
//                 Container(
//                   padding: const EdgeInsets.all(15),
//                   child: Container(
//                     decoration: BoxDecoration(
//                         color: Colors.white,
//                         borderRadius: BorderRadius.circular(10)),
//                     child: Container(
//                       padding: const EdgeInsets.all(15),
//                       child: Column(
//                         crossAxisAlignment: CrossAxisAlignment.start,
//                         children: [
//                           Row(
//                             children: [
//                               Container(
//                                   height: 30,
//                                   width: 70,
//                                   color: const Color(0xffF1F1F1),
//                                   child: Center(
//                                       child: FittedBox(
//                                     fit: BoxFit.scaleDown,
//                                     child: Text(
//                                       AppLocalizations.of(context)
//                                           .translate('carrental'),
//                                       style: const TextStyle(
//                                           fontWeight: FontWeight.bold),
//                                       maxLines: 1,
//                                     ),
//                                   ))),
//                             ],
//                           ),
//                           const SizedBox(
//                             height: 10,
//                           ),
//                           Row(
//                             children: [
//                               Text(
//                                   '${AppLocalizations.of(context).translate('Price Range Per Day')}: '),
//                               const SizedBox(
//                                 width: 10,
//                               ),
//                               startprice != null
//                                   ? Text(
//                                       '$startprice ${currencyController.currency}',
//                                       style: const TextStyle(
//                                           fontWeight: FontWeight.bold),
//                                     )
//                                   : Container(),
//                             ],
//                           ),
//                           const SizedBox(
//                             height: 10,
//                           ),
//                           Row(
//                             children: [
//                               Text(
//                                   '${AppLocalizations.of(context).translate('brand')}:'),
//                               const SizedBox(
//                                 width: 10,
//                               ),
//                               brand != null
//                                   ? Text(
//                                       brand!,
//                                       style: const TextStyle(
//                                           fontWeight: FontWeight.bold),
//                                     )
//                                   : Container(),
//                             ],
//                           ),
//                           const SizedBox(
//                             height: 10,
//                           ),
//                           Row(
//                             children: [
//                               Text(
//                                   '${AppLocalizations.of(context).translate('type')}:'),
//                               const SizedBox(
//                                 width: 10,
//                               ),
//                               type != null
//                                   ? Text(
//                                       type!,
//                                       style: const TextStyle(
//                                           fontWeight: FontWeight.bold),
//                                     )
//                                   : Container(),
//                             ],
//                           ),
//                           const SizedBox(
//                             height: 10,
//                           ),
//                           Row(
//                             children: [
//                               Text(
//                                   '${AppLocalizations.of(context).translate('Year')}:'),
//                               const SizedBox(
//                                 width: 10,
//                               ),
//                               year != null
//                                   ? Text(
//                                       year.toString(),
//                                       style: const TextStyle(
//                                           fontWeight: FontWeight.bold),
//                                     )
//                                   : Container(),
//                             ],
//                           ),
//                           const SizedBox(
//                             height: 10,
//                           ),
//                           Divider(
//                             color: Colors.grey[100],
//                           ),
//                           const SizedBox(
//                             height: 10,
//                           ),
//                           description != null
//                               ? Text(
//                                   description!,
//                                   softWrap: true,
//                                 )
//                               : Container(),
//                           const SizedBox(
//                             height: 10,
//                           ),
//                         ],
//                       ),
//                     ),
//                   ),
//                 ),
//               ],
//             )),
//           )));
//   future.then((void value) => closeModal(videoPlayerController));
// }
