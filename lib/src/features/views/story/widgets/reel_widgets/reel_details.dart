import 'package:video_player/video_player.dart';

import '../../../../controllers/currency_controller.dart';

String? video;
String? name;
var rating;
String? description,
    location,
    phone,
    website,
    instagram,
    greviewlink,
    whatsapp,
    greviewName,
    brand,
    type;
var startprice,
    endprice,
    privateDriverPrice,
    year,
    size,
    roomnumber,
    startsize,
    endsize,
    feature;
double? lat, lng;
CurrencyController currencyController = CurrencyController();

void closeModal(VideoPlayerController videoPlayerController) {
  videoPlayerController.play();
}
