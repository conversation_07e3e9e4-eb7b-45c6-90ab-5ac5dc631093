import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:page/src/core/config/constants.dart';
import 'package:page/src/core/extensions/extensions.dart';
import 'package:page/src/features/models/video_model.dart';
import 'package:page/src/features/views/car_rent_details/widgets/car_rental_details.dart';
import 'package:page/src/features/views/holiday_home_details/widgets/details.dart';
import 'package:page/src/features/views/property_details/widgets/property_details.dart';
import 'package:video_player/video_player.dart';

import '../../../../../core/services/api.dart';
import '../../../../../core/shared_widgets/shared_widgets.dart';
import '../../../ad_details/widgets/ad_details.dart';
import 'reel_details.dart';

void getmaincategorydetails(BuildContext context, int id, String type,
    VideoPlayerController videoPlayerController) async {
  progrsss(context);
  pr?.show();
  final language = Localizations.localeOf(context);

  final isEng = language.languageCode == 'en';

  await Api.getmainCategorydetails(id).then((value) {
    final videoModel = VideoModel.fromJson(value!.results);

    currencyController.getcuurentcurrency(context);
    video = value!.results['video'];
    name = value.results['name'];
    description = value.results['description'];
    location = value.results['location'] != null
        ? value.results['location']['name']
        : '';
    startprice =
        value.results['startprice'] == null || value.results['startprice'] == 0
            ? value.results['price']
            : value.results['startprice'];
    endprice = value.results['endprice'];
    privateDriverPrice = value.results['private_driver_price'];
    lat = value.results['latitude'].toString().toDouble();
    lng = value.results['longitude'].toString().toDouble();

    website = value.results['website'];
    instagram = value.results['instagram'];

    // website = value.results['website'] != null &&
    //         value.results['website'].startsWith('http')
    //     ? value.results['website']
    //     : null;
    //
    //
    // instagram = value.results['instagram'] != null &&
    //         value.results['instagram'].startsWith('http')
    //     ? value.results['instagram']
    //     : null;
    phone = value.results['phone'];
    rating = value.results['numberofstarts'] ?? 0;
    greviewlink = value.results['greviewlink'];
    lat = value.results['latitude'].toString().toDouble();
    lng = value.results['longitude'].toString().toDouble();
    brand = value.results['brand_car'] != null
        ? isEng
            ? value.results['brand_car']['name']['en']
            : value.results['brand_car']['name']['ar']
        : '';
    year = value.results['year_car'] != null
        ? value.results['year_car']['year']
        : '';
    feature = value.results['features'] != null &&
            value.results['features'].isNotEmpty
        ? isEng
            ? value.results['features'][0]['name']['en']
            : value.results['features'][0]['name']['ar']
        : '';
    size = value.results['size'];
    whatsapp = value.results['whatsapp'];

    startsize = value.results['start_size'];
    endsize = value.results['end_size'];

    roomnumber = value.results['rooms'];
    type = value.results['type'] != null
        ? isEng
            ? value.results['type']['name']['en']
            : value.results['type']['name']['ar']
        : '';

    var featureList = value.results['features'] != null &&
            value.results['features'].length > 0
        ? List<String>.from(value.results['features']
            .map((e) => isEng ? e['name']['en'] : e['name']['ar']))
        : [];

    pr?.hide();

    log('TTTdfdfdfdype $lat $lng');

    final catId = value.results['category']['id'];
    final isCarRent = catId == AppConstants.carRentalsId;

    final isHolidayHome = catId == AppConstants.holidayHomesId;

    final isProperty = catId == AppConstants.propertiesId;

    late Future<void> future;

    if (isCarRent) {
      future = carrentaldetaialsWidget(context,
          id: id,
          video: videoModel,
          agentId: value.results['agent']['id'],
          description: description,
          fromReels: true,
          startprice: value.results['price'],
          phone: phone,
          privateDriverPrice: privateDriverPrice,
          currencyController: currencyController,
          name: value.results['name'],
          type: value.results['type'] != null
              ? isEng
                  ? value.results['type']['name']['en']
                  : value.results['type']['name']['ar']
              : '',
          brand: brand,
          year: year,
          featureList: featureList,
          feature: feature);
    } else if (isHolidayHome) {
      future = detailsHolidayHome(context,
          id: id,
          video: value.results['video'],
          rmsCategoryId: value.results['rms_category_id'],
          agentId: value.results['agent']['id'],
          fromReels: true,
          title: value.results['name'],
          description: description,
          image: value.results['images'] != null
              ? value.results['images'][0]['url']
              : '',
          roomnumber: roomnumber,
          startprice: startprice,
          currencyController: currencyController,
          location: location,
          type: value.results['type'] != null
              ? isEng
                  ? value.results['type']['name']['en']
                  : value.results['type']['name']['ar']
              : '',
          lat: lat,
          lng: lng,
          size: size);
    } else if (isProperty) {
      future = propertyDetails(context,
          currencyController: currencyController,
          location: location,
          startsize: startsize,
          endsize: endsize,
          description: description,
          roomnumber: roomnumber,
          startprice: startprice,
          type: type,
          lat: lat,
          whatsapp: whatsapp,
          phone: phone,
          website: website,
          instagram: instagram,
          lng: lng);
    } else {
      future = adDetails(
        context,
        id: id,
        video: value.results['video'],
        title: value.results['name'],
        label: value.results['category']['name_en'],
        category: value.results['category']['name'],
        website: value.results['website'],
        phone: value.results['phone'],
        rating: rating,
        currencyController: currencyController,
        startprice: startprice,
        endPrice: endprice,
        price: value.results['price'],
        location: value.results['location'] != null
            ? value.results['location']['name']
            : '',
        instagram: value.results['instagram'],
        description: description,
        lat: lat,
        lng: lng,
        fromVibes: true,
      );
    }

    future.then((value) {
      closeModal(videoPlayerController);
    });
  });
}
