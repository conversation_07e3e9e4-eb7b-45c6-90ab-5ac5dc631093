import 'package:flutter/material.dart';
import 'package:page/src/core/extensions/extensions.dart';
import 'package:page/src/features/controllers/language_controller.dart';
import 'package:video_player/video_player.dart';

import '../../../../../core/services/api.dart';
import '../../../../../core/shared_widgets/shared_widgets.dart';
import '../../../holiday_home_details/widgets/details.dart';
import 'reel_details.dart';

void getholidayhomedetails(
    BuildContext context,
    int id,
    VideoPlayerController videoPlayerController,
    LanguageController language) async {
  progrsss(context);
  pr?.show();
  await Api.getmainCategorydetails(id).then((value) {
    video = value!.results['video'];
    name = value.results['name'];
    description = value.results['description'];
    location = value.results['location'] != null
        ? value.results['location']['name']
        : '';
    startprice = value.results['startprice'];
    endprice = value.results['endprice'];
    lat = value.results['latitude'].toString().toDouble();
    lng = value.results['longitude'].toString().toDouble();

    size = value.results['size'];
    roomnumber = value.results['rooms'];
    type = value.results['type'] != null
        ? language.languagecode == 'en'
            ? value.results['type']['name']['en']
            : value.results['type']['name']['ar']
        : '';
    phone = value.results['phone'];
    // holidayhomedetaials(context, videoPlayerController);

    pr?.hide();

    Future<void> future = detailsHolidayHome(context,
        title: value.results['name'],
        rmsCategoryId: value.results['rms_category_id'],
        video: video,
        agentId: value.results['agent']['id'],
        description: description,
        roomnumber: roomnumber,
        startprice: startprice,
        currencyController: currencyController,
        location: location,
        type: type,
        lat: lat,
        lng: lng,
        id: id,
        size: size,
    );

    future.then((value) {
      closeModal(videoPlayerController);
    });
  });
}
