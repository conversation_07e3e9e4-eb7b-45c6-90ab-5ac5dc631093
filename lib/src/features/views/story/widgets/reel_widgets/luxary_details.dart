import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:page/src/core/extensions/extensions.dart';
import 'package:page/src/core/localization/app_language.dart';
import 'package:page/src/features/views/holiday_home_details/widgets/details.dart';
import 'package:provider/provider.dart';
import 'package:url_launcher/url_launcher_string.dart';
import 'package:video_player/video_player.dart';

import '../../../../../core/localization/app_localizations.dart';
import '../../../../../core/services/api.dart';
import '../../../property_details/widgets/property_details.dart';
import 'reel_details.dart';

void getpropertydetails(BuildContext context, int id,
    VideoPlayerController videoPlayerControllerasync) async {
  log('asfsafafaf');
  await Api.getmainCategorydetails(id).then((value) {
    video = value?.results['video'];
    name = value?.results['name'];
    description = value?.results['description'];
    location = value?.results['location'] != null
        ? value?.results['location']['name']
        : '';
    startprice = value?.results['price'];

    phone = value?.results['phone'] ?? '';

    lat = value?.results['latitude'].toString().toDouble();
    lng = value?.results['longitude'].toString().toDouble();

    type = value?.results['type'];
    whatsapp = value?.results['whatsapp'];
    roomnumber = value?.results['rooms'];
    startsize = value?.results['startsize'];
    endsize = value?.results['endsize'];

    propertyDetails(context,
        currencyController: currencyController,
        location: location,
        startsize: startsize,
        endsize: endsize,
        description: description,
        whatsapp: whatsapp,
        roomnumber: roomnumber,
        startprice: startprice,
        type: type,
        lat: lat,
        phone: phone,
        website: website,
        instagram: instagram,
        lng: lng);

    // luxurydetails(context, videoPlayerControllerasync);
  });
}

void luxurydetails(
    BuildContext context, VideoPlayerController videoPlayerController) {
  var lang =
      Provider.of<AppLanguage>(context, listen: false).appLocal.languageCode;
  final Widget svg = SizedBox(
      width: 25,
      height: 25,
      child: SvgPicture.asset(
        'assets/icons8-website.svg',
        semanticsLabel: 'Acme Logo',
        fit: BoxFit.cover,
      ));
  Future<void> future = showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Padding(
          padding:
              EdgeInsets.only(bottom: MediaQuery.of(context).viewInsets.bottom),
          child: Container(
            height: MediaQuery.of(context).size.height * 0.70,
            decoration: BoxDecoration(
                color: const Color(0xffF5F6F7),
                borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(25.0),
                    topRight: Radius.circular(25.0)),
                border: Border.all(color: Colors.black, width: 1.0)),
            child: SingleChildScrollView(
                child: Column(
              children: [
                const SizedBox(
                  height: 10,
                ),
                Container(height: 5, width: 50, color: const Color(0xffD2D4D6)),
                const SizedBox(
                  height: 20,
                ),
                Center(
                    child: Text(
                  AppLocalizations.of(context).translate('More Details'),
                  style: const TextStyle(fontWeight: FontWeight.bold),
                )),
                Container(
                  padding: const EdgeInsets.all(15),
                  child: Container(
                    decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(10)),
                    child: Container(
                      padding: const EdgeInsets.all(15),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Container(
                                  height: 30,
                                  width: 70,
                                  color: const Color(0xffF1F1F1),
                                  child: Center(
                                      child: FittedBox(
                                    fit: BoxFit.scaleDown,
                                    child: Text(
                                      AppLocalizations.of(context)
                                          .translate('Properties'),
                                      style: const TextStyle(
                                          fontWeight: FontWeight.bold),
                                      maxLines: 1,
                                    ),
                                  ))),
                            ],
                          ),
                          const SizedBox(
                            height: 10,
                          ),
                          Row(
                            children: [
                              Text(
                                  '${AppLocalizations.of(context).translate('location')}:'),
                              const SizedBox(
                                width: 10,
                              ),
                              location != null
                                  ? Text(
                                      location!,
                                      style: const TextStyle(
                                          fontWeight: FontWeight.bold),
                                    )
                                  : Container(),
                            ],
                          ),
                          const SizedBox(
                            height: 10,
                          ),
                          Row(
                            children: [
                              Text(
                                  '${AppLocalizations.of(context).translate('type')}:'),
                              const SizedBox(
                                width: 10,
                              ),
                              type != null
                                  ? Text(
                                      type!,
                                      style: const TextStyle(
                                          fontWeight: FontWeight.bold),
                                    )
                                  : Container(),
                            ],
                          ),
                          const SizedBox(
                            height: 10,
                          ),
                          Row(
                            children: [
                              Text(
                                  '${AppLocalizations.of(context).translate('price')}:'),
                              const SizedBox(
                                width: 10,
                              ),
                              startprice != null
                                  ? Text(
                                      '${AppLocalizations.of(context).translate('Start from')} $startprice ${currencyController.currency}',
                                      style: const TextStyle(
                                          fontWeight: FontWeight.bold),
                                    )
                                  : Container(),
                            ],
                          ),
                          const SizedBox(
                            height: 10,
                          ),
                          Row(
                            children: [
                              Text(
                                  '${AppLocalizations.of(context).translate('size')}:'),
                              const SizedBox(
                                width: 10,
                              ),
                              startsize != null
                                  ? Text(
                                      '$startsize',
                                      style: const TextStyle(
                                          fontWeight: FontWeight.bold),
                                    )
                                  : Container(),
                              endsize != null
                                  ? Text(
                                      '- $endsize',
                                      style: const TextStyle(
                                          fontWeight: FontWeight.bold),
                                    )
                                  : Container(),
                              Text(
                                AppLocalizations.of(context).translate('sqrf'),
                                style: const TextStyle(
                                    fontWeight: FontWeight.bold),
                              )
                            ],
                          ),
                          const SizedBox(
                            height: 10,
                          ),
                          Row(
                            children: [
                              Text(
                                  '${AppLocalizations.of(context).translate('Availability')}:'),
                              const SizedBox(
                                width: 10,
                              ),
                              roomnumber != null
                                  ? Flexible(
                                      child: Text(
                                      '${(roomnumber as List).join(' ${AppLocalizations.of(context).translate('Bedrooms')} / ')} ${AppLocalizations.of(context).translate('Bedrooms')}',
                                      style: const TextStyle(
                                          fontWeight: FontWeight.bold),
                                    ))
                                  : Container(),
                            ],
                          ),
                          const SizedBox(
                            height: 10,
                          ),
                          Divider(
                            color: Colors.grey[100],
                          ),
                          const SizedBox(
                            height: 10,
                          ),
                          description != null
                              ? Text(
                                  description!,
                                  softWrap: true,
                                  maxLines: 2,
                                )
                              : Container(),
                          const SizedBox(
                            height: 10,
                          ),
                          InkWell(
                              onTap: () async {
                                // ignore: unnecessary_statements
                                website != null
                                    ? launchUrlString(website!.contains('https')
                                        ? website ?? ''
                                        : "https://$website")
                                    : null;
                              },
                              child: Container(
                                  // padding: EdgeInsets.only(left: 20, right: 20),
                                  decoration: BoxDecoration(
                                      color: Colors.white,
                                      border: Border.all(color: Colors.black12),
                                      borderRadius: BorderRadius.circular(5)),
                                  child: Container(
                                    padding: const EdgeInsets.all(10),
                                    child: Column(
                                      children: [
                                        Row(
                                          children: [
                                            svg,
                                            const SizedBox(width: 10),
                                            website != null
                                                ? Text(
                                                    AppLocalizations.of(context)
                                                        .translate('website'),
                                                    style: const TextStyle(
                                                        fontWeight:
                                                            FontWeight.bold,
                                                        fontSize: 12),
                                                  )
                                                : Text(AppLocalizations.of(
                                                        context)
                                                    .translate(
                                                        'no website found')),
                                            const SizedBox(
                                              height: 10,
                                            ),
                                            const Spacer(),
                                            lang == 'en'
                                                ? const Icon(
                                                    Icons.keyboard_arrow_right,
                                                    color: Colors.black26)
                                                : const Icon(
                                                    Icons.keyboard_arrow_left,
                                                    color: Colors.black26)
                                          ],
                                        ),
                                      ],
                                    ),
                                  ))),
                          const SizedBox(
                            height: 10,
                          ),
                          InkWell(
                              onTap: () async {
                                instagram != null
                                    ? launchUrlString(
                                        instagram!.contains('https')
                                            ? instagram ?? ''
                                            : "https://$instagram")
                                    : null;
                              },
                              child: Container(
                                  // padding: EdgeInsets.only(left: 10, right: 10),
                                  decoration: BoxDecoration(
                                      color: Colors.white,
                                      border: Border.all(color: Colors.black12),
                                      borderRadius: BorderRadius.circular(5)),
                                  child: Container(
                                    padding: const EdgeInsets.all(10),
                                    child: Column(
                                      children: [
                                        Row(
                                          // mainAxisAlignment: MainAxisAlignment.center,
                                          // crossAxisAlignment: CrossAxisAlignment.center,
                                          children: [
                                            Image.asset(
                                              'assets/instagram.jpg',
                                              height: 20,
                                              width: 20,
                                            ),
                                            const SizedBox(width: 10),
                                            instagram != null
                                                ? Text(
                                                    AppLocalizations.of(context)
                                                        .translate(
                                                            'instagram page'),
                                                    style: const TextStyle(
                                                        fontWeight:
                                                            FontWeight.bold,
                                                        fontSize: 12),
                                                  )
                                                : Text(AppLocalizations.of(
                                                        context)
                                                    .translate(
                                                        'no instagram page found')),
                                            const SizedBox(
                                              height: 10,
                                            ),
                                            const Spacer(),
                                            lang == 'en'
                                                ? const Icon(
                                                    Icons.keyboard_arrow_right,
                                                    color: Colors.black26)
                                                : const Icon(
                                                    Icons.keyboard_arrow_left,
                                                    color: Colors.black26)
                                          ],
                                        ),
                                      ],
                                    ),
                                  ))),
                          const SizedBox(
                            height: 10,
                          ),
                          Center(
                              child: GestureDetector(
                                  onTap: () async {
                                    if (lat != null && lng != null) {
                                      navigateToMap(context,
                                          lat: lat!, lng: lng!);
                                    }
                                    // lng != null
                                    //     ? Navigator.of(context).push(
                                    //         MaterialPageRoute(
                                    //             builder:
                                    //                 (BuildContext context) =>
                                    //                     OpenMap(lat!, lng!)))
                                    //     : snackbar(AppLocalizations.of(context)
                                    //         .translate('no location found'));
                                  },
                                  child: Container(
                                    height: 40,
                                    width: MediaQuery.of(context).size.width,
                                    decoration: BoxDecoration(
                                        color: const Color(0xffF3F7FB),
                                        borderRadius: BorderRadius.circular(5)),
                                    child: Container(
                                        padding: const EdgeInsets.all(5),
                                        child: Center(
                                            child: Text(
                                          AppLocalizations.of(context).translate(
                                              'View Location on Google maps'),
                                          style: const TextStyle(
                                              color: Color(0xff0852AB)),
                                        ))),
                                  ))),
                          const SizedBox(
                            height: 10,
                          ),
                          Center(
                              child: GestureDetector(
                                  onTap: () async {
                                    // try {
                                    //   log('Call Phone $phone');
                                    //   launchUrlString("tel://:$phone");
                                    // } catch (e) {
                                    //   log('Cannot open $e');
                                    // }
                                    try {
                                      final String? phoneNumber = phone
                                          ?.replaceAll(RegExp(r'[^0-9]'), '');

                                      log('Call Phone $phoneNumber');

                                      await callNumber(phoneNumber!);
                                      // await launchUrlString("tel:$phoneNumber");
                                    } catch (e) {
                                      log('Cannot open $e');
                                    }
                                  },
                                  child: Container(
                                    height: 40,
                                    width: MediaQuery.of(context).size.width,
                                    decoration: BoxDecoration(
                                        color: const Color(0xffF3F7FB),
                                        borderRadius: BorderRadius.circular(5)),
                                    child: Container(
                                        padding: const EdgeInsets.all(5),
                                        child: Center(
                                            child: Text(
                                          AppLocalizations.of(context)
                                              .translate('call property'),
                                          style: const TextStyle(
                                              color: Color(0xff0852AB)),
                                        ))),
                                  ))),
                          // Center(
                          //     child: GestureDetector(
                          //         onTap: () async {
                          //           lat != null && lng != null
                          //               ? Navigator.of(context).push(
                          //                   MaterialPageRoute(
                          //                       builder:
                          //                           (BuildContext context) =>
                          //                               OpenMap(lat!, lng!)))
                          //               : snackbar(AppLocalizations.of(context)
                          //                   .translate('no location found'));
                          //         },
                          //         child: Container(
                          //           height: 40,
                          //           width: MediaQuery.of(context).size.width,
                          //           decoration: BoxDecoration(
                          //               color: const Color(0xffF3F7FB),
                          //               borderRadius: BorderRadius.circular(5)),
                          //           child: Container(
                          //               padding: const EdgeInsets.all(5),
                          //               child: Center(
                          //                   child: Text(
                          //                 AppLocalizations.of(context).translate(
                          //                     'View Location on Google maps'),
                          //                 style: const TextStyle(
                          //                     color: Color(0xff0852AB)),
                          //               ))),
                          //         ))),
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            )),
          )));
  future.then((void value) => closeModal(videoPlayerController));
}
