import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter_rating_bar/flutter_rating_bar.dart';
import 'package:page/src/features/views/story/widgets/reel_actions/discussion_current.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../../../../core/localization/app_localizations.dart';
import '../../../../../core/services/api.dart';
import '../../../../../core/shared_widgets/shared_widgets.dart';

getreviews(
    BuildContext context, int id, String greviewlink, greviewName) async {
  reviews.clear();
  progrsss(context);
  pr!.show();
  await Api.getreviews(id, 'main_category').then((value) {
    log('Dataaa ${value?.reviews}');
    reviews.addAll(value?.reviews ?? []);
    code = value?.code;
    msg = value?.msg ?? '';

    // isload = true;
    pr!.hide();

    review(context, greviewlink, greviewName);
  });
}

void review(BuildContext context, String greviewlink, greviewName) {
  showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder:
          (context) =>
              StatefulBuilder(builder: (context, StateSetter stateSetter) {
                return Padding(
                    padding: EdgeInsets.only(
                        bottom: MediaQuery.of(context).viewInsets.bottom),
                    child: Container(
                      height: MediaQuery.of(context).size.height * 0.70,
                      decoration: BoxDecoration(
                          color: const Color(0xffF5F6F7),
                          borderRadius: const BorderRadius.only(
                              topLeft: Radius.circular(25.0),
                              topRight: Radius.circular(25.0)),
                          border: Border.all(color: Colors.black, width: 1.0)),
                      child: SingleChildScrollView(
                          child: Column(
                        children: [
                          const SizedBox(
                            height: 10,
                          ),
                          Container(
                              height: 5,
                              width: 50,
                              color: const Color(0xffD2D4D6)),
                          const SizedBox(
                            height: 20,
                          ),
                          Center(
                              child: Text(
                            AppLocalizations.of(context).translate('Reviews'),
                            style: const TextStyle(fontWeight: FontWeight.bold),
                          )),
                          Container(
                            padding: const EdgeInsets.all(15),
                            child: Container(
                              decoration: BoxDecoration(
                                  color: Colors.white,
                                  borderRadius: BorderRadius.circular(10)),
                              child: Container(
                                padding: const EdgeInsets.all(15),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    code == 0 && msg == 'Loading'
                                        ? buildLoadingWidget()
                                        : code == 1
                                            ? reviews.length > 0
                                                ? Container(
                                                    height:
                                                        MediaQuery.of(context)
                                                                .size
                                                                .height *
                                                            0.5,
                                                    child: ListView.builder(
                                                        shrinkWrap: true,
                                                        physics:
                                                            const ScrollPhysics(),
                                                        scrollDirection:
                                                            Axis.vertical,
                                                        itemBuilder:
                                                            (BuildContext ctxt,
                                                                int index) {
                                                          return Column(
                                                              crossAxisAlignment:
                                                                  CrossAxisAlignment
                                                                      .start,
                                                              children: [
                                                                Row(
                                                                  children: [
                                                                    Container(
                                                                        height:
                                                                            50,
                                                                        width:
                                                                            50,
                                                                        decoration:
                                                                            BoxDecoration(
                                                                          borderRadius:
                                                                              BorderRadius.circular(50),
                                                                          color:
                                                                              const Color(0xffE4E4E4),
                                                                        ),
                                                                        child: Center(
                                                                            child: reviews[index].username!.substring(0, 2) != null
                                                                                ? Text(
                                                                                    reviews[index].username!.substring(0, 2),
                                                                                    style: const TextStyle(color: Color(0xffB7B7B7)),
                                                                                  )
                                                                                : Container())),
                                                                    const SizedBox(
                                                                      width: 20,
                                                                    ),
                                                                    Column(
                                                                      crossAxisAlignment:
                                                                          CrossAxisAlignment
                                                                              .start,
                                                                      children: [
                                                                        Text(
                                                                            reviews[index]
                                                                                .username!,
                                                                            style:
                                                                                const TextStyle(fontWeight: FontWeight.bold)),
                                                                        const SizedBox(
                                                                          height:
                                                                              10,
                                                                        ),
                                                                        Row(
                                                                          children: [
                                                                            RatingBar.builder(
                                                                              onRatingUpdate: (value) {},
                                                                              initialRating: 0,

                                                                              minRating: reviews[index].rating!.toDouble(),
                                                                              direction: Axis.horizontal,
                                                                              allowHalfRating: true,
                                                                              itemCount: 5,
                                                                              itemSize: 16.0,
                                                                              itemPadding: const EdgeInsets.only(left: 2),
                                                                              itemBuilder: (context, _) => const Icon(Icons.star, color: Color(0xffF2BA24), size: 12),
                                                                              unratedColor: const Color(0xff556477),
                                                                              // onRatingUpdate: (rating) {
                                                                              //   print(rating);
                                                                              // },
                                                                            ),
                                                                            reviews[index].time != null
                                                                                ? Text(
                                                                                    reviews[index].time!,
                                                                                    style: const TextStyle(color: Color(0xff51565B)),
                                                                                  )
                                                                                : Container()
                                                                          ],
                                                                        )
                                                                      ],
                                                                    ),
                                                                    const Spacer(),
                                                                    // svg3
                                                                  ],
                                                                ),
                                                                const SizedBox(
                                                                  height: 10,
                                                                ),
                                                                Text(
                                                                  reviews[index]
                                                                      .text!,
                                                                  softWrap:
                                                                      true,
                                                                ),
                                                                Container(
                                                                    padding: const EdgeInsets
                                                                            .only(
                                                                        left:
                                                                            10,
                                                                        right:
                                                                            10),
                                                                    child:
                                                                        Divider(
                                                                      color: Colors
                                                                              .grey[
                                                                          100],
                                                                    )),
                                                                const SizedBox(
                                                                  height: 10,
                                                                ),
                                                              ]);
                                                        },
                                                        itemCount:
                                                            reviews.length))
                                                : nodatafound(AppLocalizations
                                                        .of(context)
                                                    .translate(
                                                        'No Reviews to show'))
                                            : buildErrorWidget(msg),
                                    // SizedBox(
                                    //   height: 20,
                                    // ),
                                  ],
                                ),
                              ),
                            ),
                          ),
                          //    SizedBox(
                          //   height: 20,
                          // ),
                          InkWell(
                              onTap: () async {
                                if (greviewName != null) {
                                  final googleReviewLink =
                                      "https://www.google.com/maps/search/?api=1&query=$greviewName";

                                  // final googleReviewLink =
                                  // "https://www.google.com/maps/place/$greviewlink";

                                  final uri = Uri.parse(googleReviewLink);

                                  if (await canLaunchUrl(uri)) {
                                    await launchUrl(uri,
                                        mode: LaunchMode.inAppWebView);
                                  } else {
                                    snackbar(AppLocalizations.of(context).translate(
                                        'no link found to review please go to google map'));
                                  }
                                } else {
                                  snackbar(AppLocalizations.of(context).translate(
                                      'no link found to review please go to google map'));
                                }
                                // greviewlink != null &&
                                //         greviewlink.startsWith("http")
                                //     ? launchUrlString(greviewlink)
                                //     : snackbar(AppLocalizations.of(context)
                                //         .translate(
                                //             'no link found to review please go to google map'));
                              },
                              child: Center(
                                  child: Text(
                                AppLocalizations.of(context)
                                    .translate('See All Reviews'),
                                style:
                                    const TextStyle(color: Color(0xff0852AB)),
                              )))
                        ],
                      )),
                    ));
              }));
}
