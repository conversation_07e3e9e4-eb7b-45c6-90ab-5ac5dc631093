import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:google_cloud_translation/google_cloud_translation.dart';
import 'package:lottie/lottie.dart';

import '../../../../../core/localization/app_localizations.dart';
import '../../../../../core/response/generalResponse.dart';
import '../../../../../core/services/api.dart';
import '../../../../../core/shared_widgets/shared_widgets.dart';
import '../../../../controllers/auth_controller.dart';
import '../../../../controllers/language_controller.dart';
import '../../../../models/discussions.dart';
import '../../../../models/reviews.dart';

bool isactions = false;
List<Reviews> reviews = [];
List<Discussions> dis = [];
TranslationModel? reelsTranslated;
String? text;
int index2 = -1;
LanguageController language = LanguageController();
TextEditingController discussioncontroller = TextEditingController();
AuthController authController = AuthController();
bool isdiscussion = false;

int pagenumber = 1;
int? code;
String msg = 'Loading';

getcarrentdiscussions(BuildContext context, int id) async {
  progrsss(context);
  pr!.show();
  dis.clear();
  await Api.getcarrentdiscussions(pagenumber, 20, id).then((value) {
    log('asdagggggsd ${value!.dscussions.length}');

    dis.addAll(value!.dscussions);
    code = value.code;
    msg = value.msg;

    // isload = true;

    discussioncarrent(context, id);
  });
}

void discussioncarrent(BuildContext context, int id) {
  pr!.hide();
  Translation _translation = Translation(
    apiKey: 'AIzaSyCXOO147BdbuceLIl8Z8D5Jxru2Vjhqd4Q',
  );
  language.getcuurentlanguage();
  authController.isloggedin();
  final Widget svg3 = SizedBox(
      width: 20,
      height: 20,
      child: SvgPicture.asset(
        'assets/Group 6310.svg',
        semanticsLabel: 'Acme Logo',
        fit: BoxFit.cover,
      ));
  log('asasdasfa ${dis.length}');
  showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder:
          (context) =>
              StatefulBuilder(builder: (context, StateSetter stateSetter) {
                return Padding(
                    padding: EdgeInsets.only(
                        bottom: MediaQuery.of(context).viewInsets.bottom),
                    child: Container(
                      height: MediaQuery.of(context).size.height * 0.70,
                      decoration: BoxDecoration(
                          color: const Color(0xffF5F6F7),
                          borderRadius: const BorderRadius.only(
                              topLeft: Radius.circular(25.0),
                              topRight: Radius.circular(25.0)),
                          border: Border.all(color: Colors.black, width: 1.0)),
                      child: SingleChildScrollView(
                          child: Column(
                        children: [
                          const SizedBox(
                            height: 10,
                          ),
                          Container(
                              height: 5,
                              width: 50,
                              color: const Color(0xffD2D4D6)),
                          const SizedBox(
                            height: 20,
                          ),
                          Center(
                              child: Text(
                            AppLocalizations.of(context)
                                .translate('Discussion'),
                            style: const TextStyle(fontWeight: FontWeight.bold),
                          )),
                          Container(
                            padding: const EdgeInsets.all(15),
                            child: Container(
                              decoration: BoxDecoration(
                                  color: Colors.white,
                                  borderRadius: BorderRadius.circular(10)),
                              child: Container(
                                padding: const EdgeInsets.all(15),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    code == 0 && msg == 'Loading'
                                        ? buildLoadingWidget()
                                        : code == 1
                                            ? dis.isNotEmpty
                                                ? Container(
                                                    height:
                                                        MediaQuery.of(context)
                                                                .size
                                                                .height *
                                                            0.5,
                                                    child: ListView.builder(
                                                        shrinkWrap: true,
                                                        physics:
                                                            const ScrollPhysics(),
                                                        scrollDirection:
                                                            Axis.vertical,
                                                        itemBuilder:
                                                            (BuildContext ctxt,
                                                                int index) {
                                                          reelsTranslated =
                                                              TranslationModel(
                                                                  translatedText:
                                                                      dis[index]
                                                                          .comment!,
                                                                  detectedSourceLanguage:
                                                                      '');
                                                          return Column(
                                                              crossAxisAlignment:
                                                                  CrossAxisAlignment
                                                                      .start,
                                                              children: [
                                                                Row(
                                                                  children: [
                                                                    dis[index].fullname !=
                                                                            null
                                                                        ? Container(
                                                                            height:
                                                                                50,
                                                                            width:
                                                                                50,
                                                                            decoration:
                                                                                BoxDecoration(
                                                                              borderRadius: BorderRadius.circular(50),
                                                                              color: const Color(0xffE4E4E4),
                                                                            ),
                                                                            child: Center(
                                                                                child: dis[index].fullname!.length > 1
                                                                                    ? Text(
                                                                                        dis[index].fullname!.substring(0, 2),
                                                                                        style: const TextStyle(color: Color(0xffB7B7B7)),
                                                                                      )
                                                                                    : Text(
                                                                                        dis[index].fullname!,
                                                                                        style: const TextStyle(color: Color(0xffB7B7B7)),
                                                                                      )))
                                                                        : Container(),
                                                                    const SizedBox(
                                                                      width: 20,
                                                                    ),
                                                                    Column(
                                                                      crossAxisAlignment:
                                                                          CrossAxisAlignment
                                                                              .start,
                                                                      children: [
                                                                        dis[index].fullname !=
                                                                                null
                                                                            ? Text(dis[index].fullname!,
                                                                                style: const TextStyle(fontWeight: FontWeight.bold))
                                                                            : Container(),
                                                                      ],
                                                                    ),
                                                                    const Spacer(),
                                                                    InkWell(
                                                                        onTap:
                                                                            () async {
                                                                          reelsTranslated = await _translation.translate(
                                                                              text: dis[index].comment!,
                                                                              to: language.languagecode);
                                                                          stateSetter(
                                                                              () {
                                                                            text =
                                                                                reelsTranslated!.translatedText;
                                                                            index2 =
                                                                                index;
                                                                          });
                                                                          if (text ==
                                                                              dis[index].comment) {
                                                                            snackbar2(language.languagecode == 'ar'
                                                                                ? '${AppLocalizations.of(context).translate('your language is')} العربية'
                                                                                : '${AppLocalizations.of(context).translate('your language is')} English');
                                                                          }
                                                                        },
                                                                        child:
                                                                            svg3)
                                                                  ],
                                                                ),
                                                                const SizedBox(
                                                                  height: 10,
                                                                ),
                                                                // Text(
                                                                // dis[index].comment
                                                                // ),

                                                                index == index2
                                                                    ? text !=
                                                                            null
                                                                        ? text !=
                                                                                reelsTranslated!.translatedText
                                                                            ? Text(text!)
                                                                            : Text(text!)
                                                                        : Text(
                                                                            reelsTranslated!.translatedText,
                                                                            softWrap:
                                                                                true,
                                                                          )
                                                                    : Text(
                                                                        reelsTranslated!
                                                                            .translatedText,
                                                                        softWrap:
                                                                            true,
                                                                      ),
                                                                Container(
                                                                    padding: const EdgeInsets
                                                                        .only(
                                                                        left:
                                                                            10,
                                                                        right:
                                                                            10),
                                                                    child:
                                                                        Divider(
                                                                      color: Colors
                                                                              .grey[
                                                                          100],
                                                                    )),
                                                              ]);
                                                        },
                                                        itemCount: dis.length))
                                                : nodatafound(AppLocalizations
                                                        .of(context)
                                                    .translate(
                                                        'No discussions to show'))
                                            : buildErrorWidget(msg),
                                    const SizedBox(
                                      height: 20,
                                    ),
                                    Container(
                                        height: 50,
                                        decoration: BoxDecoration(
                                            color: Colors.grey[100],
                                            borderRadius:
                                                BorderRadius.circular(3)),
                                        child: Container(
                                            decoration: BoxDecoration(
                                                borderRadius:
                                                    const BorderRadius.all(
                                                        Radius.circular(5)),
                                                border: Border.all(
                                                    color: Colors.black12,
                                                    width: 1.0)),
                                            child: TextFormField(
                                              controller: discussioncontroller,
                                              decoration: InputDecoration(
                                                  suffixIcon: Padding(
                                                      padding:
                                                          const EdgeInsets.only(
                                                              top: 10,
                                                              bottom: 10,
                                                              right: 10,
                                                              left: 10),
                                                      child: authController
                                                              .isLogged
                                                          ? InkWell(
                                                              onTap: () async {
                                                                stateSetter(() {
                                                                  isdiscussion =
                                                                      true;
                                                                });
                                                                print(
                                                                    "ttttttttt");
                                                                print(
                                                                    isdiscussion);
                                                                GeneralResponse
                                                                    sucessinformation =
                                                                    await Api.sendcarrentdiscussion(
                                                                        id,
                                                                        discussioncontroller
                                                                            .text);
                                                                print(
                                                                    sucessinformation
                                                                        .code);
                                                                if (sucessinformation
                                                                        .code ==
                                                                    "1") {
                                                                  snackbar2(AppLocalizations.of(
                                                                          context)
                                                                      .translate(
                                                                          'add discussion successfuly'));
                                                                  stateSetter(
                                                                      () {
                                                                    dis = [];
                                                                    discussioncontroller
                                                                        .text = "";
                                                                    getcarrentdiscussions(
                                                                        context,
                                                                        id);
                                                                    discussioncontroller
                                                                        .text = "";
                                                                    // isfav = true;
                                                                  });
                                                                } else {
                                                                  snackbar(AppLocalizations.of(
                                                                          context)
                                                                      .translate(
                                                                          'Something went wrong, please try again later'));
                                                                }
                                                                discussioncontroller
                                                                    .text = "";
                                                                stateSetter(() {
                                                                  isdiscussion =
                                                                      false;
                                                                });
                                                                print(
                                                                    "fssfsfstteeteteee");
                                                                print(
                                                                    isdiscussion);
                                                              },
                                                              child: Container(
                                                                  height: 20,
                                                                  width: 30,
                                                                  decoration: BoxDecoration(
                                                                      color: const Color(
                                                                          0xffF1F1F1),
                                                                      borderRadius:
                                                                          BorderRadius.circular(
                                                                              20)),
                                                                  child: !isdiscussion
                                                                      ? const Center(
                                                                          child: Icon(Icons.send,
                                                                              size:
                                                                                  18))
                                                                      : Center(
                                                                          child: Lottie.asset(
                                                                              'assets/59218-progress-indicator.json',
                                                                              height: 20,
                                                                              width: 20))),
                                                            )
                                                          : InkWell(
                                                              onTap: () async {
                                                                snackbar(AppLocalizations.of(
                                                                        context)
                                                                    .translate(
                                                                        'Please login first'));
                                                              },
                                                              child: Container(
                                                                  height: 20,
                                                                  width: 30,
                                                                  decoration: BoxDecoration(
                                                                      color: const Color(
                                                                          0xffF1F1F1),
                                                                      borderRadius:
                                                                          BorderRadius.circular(
                                                                              20)),
                                                                  child: const Center(
                                                                      child: Icon(Icons.send,
                                                                          size:
                                                                              18))))),
                                                  contentPadding:
                                                      const EdgeInsets.only(
                                                          left: 20,
                                                          right: 20,
                                                          top: 10),
                                                  hintText:
                                                      AppLocalizations.of(context)
                                                          .translate('Joindiscussion'),
                                                  hintStyle: const TextStyle(color: Colors.grey, fontSize: 12),
                                                  border: InputBorder.none),
                                            )))
                                  ],
                                ),
                              ),
                            ),
                          ),
                        ],
                      )),
                    ));
              }));
}
