import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:page/src/features/views/holiday_home_details/widgets/details.dart';
import 'package:page/src/features/views/story/widgets/reel_actions/discussion_current.dart';
import 'package:video_player/video_player.dart';

import '../../../../../core/config/constants.dart';
import '../../../../../core/localization/app_localizations.dart';
import 'review.dart';

moreaction(
    BuildContext context,
    String type,
    String name,
    var lat,
    var lng,
    int id,
    String greviewlink,
    greviewName,
    VideoPlayerController videoPlayerController) {
  return StatefulBuilder(builder: (context, StateSetter stateSetter) {
    final isMainCategory = type != AppConstants.holidayHomesId.toString() &&
        type != AppConstants.carRentalsId.toString() &&
        type != AppConstants.propertiesId.toString();

    log('isMainCasfsafategory: $isMainCategory');
    return isactions
        ? Container(
            height: MediaQuery.of(context).size.height,
            width: MediaQuery.of(context).size.width,
            color: Colors.grey.withOpacity(0.4),
            child: Container(
                padding: const EdgeInsets.only(
                  left: 10,
                  right: 10,
                  bottom: 20,
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    Container(
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(20),
                        color: Colors.grey[100],
                      ),
                      width: MediaQuery.of(context).size.width,
                      child: Column(
                        children: [
                          const SizedBox(
                            height: 20,
                          ),
                          isMainCategory
                              ? InkWell(
                                  onTap: () {
                                    getreviews(
                                        context, id, greviewlink, greviewName);
                                  },
                                  child: Container(
                                    width: MediaQuery.of(context).size.width,
                                    child: Center(
                                        child: Text(
                                      AppLocalizations.of(context)
                                          .translate('Reviews'),
                                      style: const TextStyle(
                                          color: Color(0xff007AFF),
                                          fontSize: 18),
                                    )),
                                  ))
                              : Container(),
                          isMainCategory
                              ? const SizedBox(
                                  height: 10,
                                )
                              : Container(),
                          type == AppConstants.holidayHomesId.toString()
                              ? Divider(
                                  color: Colors.green[100],
                                )
                              : Container(),
                          type != AppConstants.holidayHomesId.toString() &&
                                  type != AppConstants.carRentalsId.toString()
                              ? const SizedBox(
                                  height: 10,
                                )
                              : Container(),
                          // InkWell(
                          //   onTap: () {
                          //     isMainCategory
                          //         ? getmaincategorydiscussions(context, id)
                          //         : type == AppConstants.carRentalsId.toString()
                          //             ? getcarrentdiscussions(context, id)
                          //             : type ==
                          //                     AppConstants.holidayHomesId
                          //                         .toString()
                          //                 ? getholidayhomediscussions(
                          //                     context, id)
                          //                 : type ==
                          //                         AppConstants.propertiesId
                          //                             .toString()
                          //                     ? getluxurydiscussions(
                          //                         context, id)
                          //                     // ignore: unnecessary_statements
                          //                     : "";
                          //   },
                          //   child: Container(
                          //       width: MediaQuery.of(context).size.width,
                          //       child: Center(
                          //           child: Text(
                          //         AppLocalizations.of(context)
                          //             .translate('Joindiscussion'),
                          //         style: const TextStyle(
                          //             color: Color(0xff007AFF), fontSize: 18),
                          //       ))),
                          // ),
                          // const SizedBox(
                          //   height: 10,
                          // ),
                          // Divider(
                          //   color: Colors.green[100],
                          // ),
                          const SizedBox(
                            height: 10,
                          ),
                          InkWell(
                            onTap: () {
                              if (lat != null && lng != null) {
                                navigateToMap(context, lat: lat, lng: lng);
                              }
                              // lat != null && lng != null
                              //     ? Navigator.of(context).push(
                              //         MaterialPageRoute(
                              //             builder: (BuildContext context) =>
                              //                 OpenMap(lat, lng)))
                              //     : snackbar(AppLocalizations.of(context)
                              //         .translate('no location found'));
                            },
                            child: Container(
                                width: MediaQuery.of(context).size.width,
                                child: Center(
                                  child: Text(
                                      AppLocalizations.of(context).translate(
                                          'Viewlocationongooglemaps'),
                                      style: const TextStyle(
                                          color: Color(0xff007AFF),
                                          fontSize: 18)),
                                )),
                          ),
                          const SizedBox(
                            height: 10,
                          ),
                          Divider(
                            color: Colors.green[100],
                          ),
                          const SizedBox(
                            height: 10,
                          ),
                          // InkWell(
                          //     // onTap: () async {
                          //     //   final String linkPathData =
                          //     //       '?id=${story.reelid}&type=${'reels'}';
                          //     //   final dynamicLink =
                          //     //       await DynamicLinkHandler.createDynamicLink(
                          //     //     linkPathData,
                          //     //   );
                          //     //
                          //     //   log('sdsfsf ${dynamicLink.toString()}');
                          //     //   log('sdsfsf222323 ${story.reelid}}');
                          //     //
                          //     //   Share.share(dynamicLink.toString());
                          //     // },
                          //     //     () {
                          //     //   Share.share(
                          //     //       '$name    https://play.google.com/');
                          //     // },
                          //     child: Container(
                          //         width: MediaQuery.of(context).size.width,
                          //         child: Center(
                          //             child: Text(
                          //           AppLocalizations.of(context)
                          //               .translate('Share'),
                          //           style: const TextStyle(
                          //               color: Color(0xff007AFF), fontSize: 18),
                          //         )))),
                          const SizedBox(
                            height: 20,
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(
                      height: 10,
                    ),
                    InkWell(
                        onTap: () {
                          stateSetter(() {
                            isactions = !isactions;
                            videoPlayerController.play();
                          });
                        },
                        child: Container(
                            child: Container(
                          height: 50,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(20),
                            color: Colors.white,
                          ),
                          width: MediaQuery.of(context).size.width,
                          child: Center(
                            child: Text(
                              AppLocalizations.of(context).translate('Cancel'),
                              style: const TextStyle(
                                  color: Color(0xff007AFF), fontSize: 18),
                            ),
                          ),
                        )))
                  ],
                )),
          )
        : Container();
  });
}
