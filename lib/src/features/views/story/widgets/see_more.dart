import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../../../core/localization/app_language.dart';
import '../../../../core/localization/app_localizations.dart';
import '../../../models/reels.dart';

const int maxChars = 27;

class StoryDetailWithSeeMore extends StatelessWidget {
  final Reels story;
  final onSeeMore;

  const StoryDetailWithSeeMore({
    Key? key,
    required this.story,
    required this.onSeeMore,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    var lang =
        Provider.of<AppLanguage>(context, listen: false).appLocal.languageCode;

    final isEnglish = lang == 'en';

    final rowWidget = SizedBox(
      width: MediaQuery.of(context).size.width * 0.75,
      child: Row(
        children: [
          Flexible(
            child: FittedBox(
              fit: BoxFit.scaleDown,
              child: Text.rich(
                TextSpan(
                  style: const TextStyle(
                    color: Colors.white,
                  ),
                  children: [
                    TextSpan(
                      text: story.description != null
                          ? story.description!.length > maxChars
                              ? story.description?.substring(0, maxChars)
                              : story.description
                          : '',
                      style: const TextStyle(
                        fontWeight: FontWeight.normal,
                      ),
                    ),
                    TextSpan(
                      text: AppLocalizations.of(context).translate('seemore'),
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                      ),
                      recognizer: TapGestureRecognizer()..onTap = onSeeMore,
                    ),
                  ],
                ),
                maxLines: 1,
              ),
            ),
          ),
        ],
      ),
    );

    if (isEnglish) {
      return Positioned(bottom: 30, left: 16, child: rowWidget);
    } else {
      return Positioned(bottom: 30, right: 16, child: rowWidget);
    }
  }
}
