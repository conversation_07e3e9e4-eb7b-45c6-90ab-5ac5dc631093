// import 'dart:developer';
//
// import 'package:chewie/chewie.dart';
// import 'package:flutter/material.dart';
// import 'package:flutter/services.dart';
// // import 'package:flutter_ffmpeg/flutter_ffmpeg.dart';
//
// import 'package:flutter_svg/svg.dart';
// import 'package:flutter_swiper_plus/flutter_swiper_plus.dart';
// import 'package:flutter_video_info/flutter_video_info.dart';
// import 'package:page/src/core/config/constants.dart';
// import 'package:page/src/core/services/api.dart';
// import 'package:page/src/features/controllers/language_controller.dart';
// import 'package:page/src/features/views/story/widgets/reel_widgets/current_details.dart';
// import 'package:page/src/features/views/story/widgets/reel_widgets/details.dart';
// import 'package:page/src/features/views/story/widgets/reel_widgets/holiday_details.dart';
// import 'package:page/src/features/views/story/widgets/reel_widgets/property_details.dart';
// import 'package:preload_page_view/preload_page_view.dart';
// import 'package:video_player/video_player.dart';
//
// import '../../../core/shared_widgets/bottom_navgation_bar.dart';
// import '../../../core/shared_widgets/shared_widgets.dart';
// import '../../controllers/auth_controller.dart';
// import '../../models/reels.dart';
// import '../ad_details/video_widget.dart';
// import 'widgets/reel_actions/more_actions.dart';
// import 'widgets/reel_widgets/reel_details.dart';
// import 'widgets/see_more.dart';
// import 'widgets/video_actions.dart';
//
// // ignore: must_be_immutable
// class ReelsScreen extends StatefulWidget {
//   const ReelsScreen({super.key});
//
//   @override
//   _ReelsScreenState createState() => _ReelsScreenState();
// }
//
// List<Reels> stories = [];
//
// class _ReelsScreenState extends State<ReelsScreen>
//     with SingleTickerProviderStateMixin {
//   int code = 0, code2 = 0;
//   String msg = 'loading';
//   AuthController authController = AuthController();
//
//   // List<VideoPlayerController>? _videoPlayerController;
//
//   ChewieController? _chewieController;
//   bool isLoaded = false;
//   final SwiperController _controller = SwiperController();
//   int? id;
//
//   // final List<String> videos = [];
//
//   final videoInfo = FlutterVideoInfo();
//
//   final languageController = LanguageController();
//
//   Future<void> getReels(int page, int size) async {
//     try {
//       if (stories.isEmpty) {
//         final value = await Api.getreels(page, size);
//
//         if (value != null) {
//           code = value.code;
//
//           stories.addAll(value.category);
//
//           log('213qrwqasfasfasf ${stories.length}');
//
//           // initializePlayers(_videoControllers);
//
//           isLoaded = true;
//
//           setState(() {});
//         }
//       } else {
//         setState(() {
//           isLoaded = true;
//         });
//       }
//     } catch (e) {
//       log('Error: $e');
//     }
//   }
//
//   // Future<void> initializePlayers(
//   //     Map<int?, VideoPlayerController> videoControllers) async {
//   //   final storiesToInitialize =
//   //       stories.where((story) => story.video != null).toList();
//   //
//   //   videoControllers.removeWhere(
//   //       (key, value) => !storiesToInitialize.any((story) => story.id == key));
//   //
//   //   for (int i = 0; i < storiesToInitialize.length; i++) {
//   //     final story = storiesToInitialize[i];
//   //
//   //     if (videoControllers[story.id] == null) {
//   //       videoControllers[story.id] =
//   //           VideoPlayerController.networkUrl(Uri.parse(story.video!));
//   //       if (i == 0) {
//   //         await videoControllers[story.id]!.initialize();
//   //       } else {
//   //         videoControllers[story.id]!.initialize();
//   //       }
//   //     }
//   //   }
//   //
//   //   storiesToInitialize[0].video != null
//   //       ? videoControllers[storiesToInitialize[0].id!]!.play()
//   //       : null;
//   //
//   //   setState(() {
//   //     isLoaded = true;
//   //   });
//   // }
//
//   // Future<void> initializePlayers(
//   //     Map<int?, VideoPlayerController> videoControllers) async {
//   //   final storiesToInitialize =
//   //       stories.where((story) => story.video != null).toList();
//   //
//   //   videoControllers.removeWhere(
//   //       (key, value) => !storiesToInitialize.any((story) => story.id == key));
//   //
//   //   for (int i = 0; i < storiesToInitialize.length; i++) {
//   //     final story = storiesToInitialize[i];
//   //
//   //     if (videoControllers[story.id] == null) {
//   //       videoControllers[story.id] =
//   //           VideoPlayerController.networkUrl(Uri.parse(story.video!));
//   //       if (i == 0) {
//   //         await videoControllers[story.id]!.initialize();
//   //       } else {
//   //         videoControllers[story.id]!.initialize();
//   //       }
//   //     }
//   //   }
//   //
//   //   storiesToInitialize[0].video != null
//   //       ? videoControllers[storiesToInitialize[0].id!]!.play()
//   //       : null;
//   //
//   //   // Preload next 2 videos
//   //   for (int i = 1; i <= 2 && i < storiesToInitialize.length; i++) {
//   //     final story = storiesToInitialize[i];
//   //     if (videoControllers[story.id] != null) {
//   //       await videoControllers[story.id]!.initialize();
//   //     }
//   //   }
//   //
//   //   setState(() {
//   //     isLoaded = true;
//   //   });
//   // }
//
//   Future<void> initializePlayers() async {
//     log('aasdfasfasf ${stories.length}');
//
//     if (stories.isEmpty) await getReels(0, 20);
//     final storiesToInitialize =
//         stories.where((story) => story.video != null).toList();
//
//     for (int i = 0; i < storiesToInitialize.length; i++) {
//       final story = storiesToInitialize[i];
//
//       if (_videoControllers[story.id] == null) {
//         _videoControllers[story.id] =
//             VideoPlayerController.networkUrl(Uri.parse(story.video!));
//         if (i == 0) {
//           await _videoControllers[story.id]!.initialize();
//         }
//         // else {
//         //   videoControllers[story.id]!.initialize();
//         // }
//       }
//     }
//
//     storiesToInitialize[0].video != null
//         ? _videoControllers[storiesToInitialize[0].id!]!.play()
//         : null;
//
//     setState(() {
//       isLoaded = true;
//     });
//   }
//
//   @override
//   void initState() {
//     super.initState();
//     // getReels(0, 20);
//     initializePlayers();
//     languageController.getcuurentlanguage();
//     authController.isloggedin();
//
//     // if (_videoControllers.isNotEmpty) {
//     //   _videoControllers[stories[0].id]!.play();
//     // }
//     // if (stories.length > 2) {
//     //   for (int i = 1; i < 3; i++) {
//     //     _videoControllers[stories[i].id]!.initialize();
//     //   }
//     // }
//   }
//
//   @override
//   void dispose() async {
//     super.dispose();
//
//     disposeVideos();
//
//     SystemChrome.setPreferredOrientations([
//       DeviceOrientation.portraitUp,
//     ]);
//   }
//
//   void disposeVideos() {
//     _videoControllers.forEach((key, value) {
//       value.pause();
//       value.dispose();
//     });
//
//     _controller.dispose();
//
//     stories.clear();
//   }
//
//   bool isPlaying = true;
//
//   final Map<int?, VideoPlayerController> _videoControllers = {};
//
//   @override
//   Widget build(BuildContext context) {
//     SizedBox(
//         width: 30,
//         height: 25,
//         child: SvgPicture.asset(
//           'assets/media_icon.svg',
//           semanticsLabel: 'Acme Logo',
//           width: 13,
//           height: 13,
//           fit: BoxFit.fill,
//           color: Colors.white,
//         ));
//
//     void pauseAllOtherVideos(int index) {
//       _videoControllers.entries
//           .where((element) => element.key != stories[index].id)
//           .forEach((element) {
//         element.value.pause();
//       });
//
//       _videoControllers[stories[index].id]!.play();
//
//       setState(() {
//         isPlaying = true;
//       });
//     }
//
//     return WillPopScope(
//       onWillPop: () async {
//         disposeVideos();
//
//         setState(() {
//           isactions = false;
//         });
//         return Future.value(true);
//       },
//       child: Scaffold(
//         backgroundColor: Colors.black,
//         body: code == 1
//             ? Stack(
//                 children: <Widget>[
//                   PreloadPageView.builder(
//                     itemCount: stories.length,
//                     scrollDirection: Axis.vertical,
//                     onPageChanged: (index) {
//                       pauseAllOtherVideos(index);
//
//                       if (index + 1 < stories.length) {
//                         _videoControllers[stories[index + 1].id]!.initialize();
//                       }
//
//                       // Preload next 2 videos
//                       // if (index + 1 < stories.length) {
//                       //   _videoControllers[stories[index + 1].id]!.initialize();
//                       // }
//                       //
//                       // if (index + 2 < stories.length) {
//                       //   _videoControllers[stories[index + 2].id]!.initialize();
//                       // }
//
//                       // if (index + 1 < stories.length) {
//                       //   _videoControllers[stories[index + 1].id]!.initialize();
//                       // }
//                     },
//                     controller: PreloadPageController(),
//                     itemBuilder: (BuildContext context, int index) {
//                       final Reels story = stories[index];
//
//                       void playOrPause() {
//                         _videoControllers[story.id]!.value.isPlaying
//                             ? _videoControllers[story.id]!.pause()
//                             : _videoControllers[story.id]!.play();
//
//                         setState(() {
//                           isPlaying = !isPlaying;
//                         });
//                       }
//
//                       id = story.id;
//                       name = story.name;
//                       lat = story.latitude;
//                       lng = story.longitude;
//                       greviewlink = story.greviewlink;
//                       greviewName = story.greviewName;
//
//                       log('Reviiew ${story.greviewlink}}');
//                       type = story.categorytype;
//
//                       return GestureDetector(
//                         onTap: () {
//                           playOrPause();
//                         },
//                         child: Stack(children: [
//                           // isLoaded
//                           //     ?
//                           SizedBox(
//                             height: MediaQuery.of(context).size.height,
//                             width: MediaQuery.of(context).size.width,
//                             child: VideoPlayer(
//                               _videoControllers[story.id]!,
//                             ),
//                             // child: Chewie(
//                             //   controller: _chewieController!,
//                             // ),
//                           ),
//                           // : Center(child: buildLoadingWidget()),
//                           isLoaded
//                               ? StoryDetailWithSeeMore(
//                                   story: story,
//                                   onSeeMore: () {
//                                     setState(() {
//                                       _videoControllers[story.id]!.pause();
//                                     });
//
//                                     log('sddadasd ${story.categorytype} NNNN ${story.name} dsassaf ${story.id}');
//
//                                     story.categorytype ==
//                                             AppConstants.carRentalsId.toString()
//                                         ? getcarrentdetails(context, story.id!,
//                                             _videoControllers[story.id]!)
//                                         : story.categorytype ==
//                                                 AppConstants.holidayHomesId
//                                                     .toString()
//                                             ? getholidayhomedetails(
//                                                 context,
//                                                 story.id!,
//                                                 _videoControllers[story.id]!,
//                                                 languageController)
//                                             : story.categorytype ==
//                                                     AppConstants.propertiesId
//                                                         .toString()
//                                                 ? getpropertydetails(
//                                                     context,
//                                                     story.id!,
//                                                     _videoControllers[
//                                                         story.id]!)
//                                                 : getmaincategorydetails(
//                                                     context,
//                                                     story.id!,
//                                                     story.label!,
//                                                     _videoControllers[
//                                                         story.id]!);
//                                   },
//                                 )
//                               : const SizedBox(),
//                           isLoaded
//                               ? VideoActions(
//                                   story: story,
//                                   setState: setState,
//                                   videoController: _videoControllers[story.id]!,
//                                 )
//                               : const SizedBox(),
//                           if (!isPlaying)
//                             Center(
//                               child: InkWell(
//                                 onTap: () {
//                                   playOrPause();
//                                 },
//                                 child: CircleAvatar(
//                                   backgroundColor:
//                                       Colors.black.withOpacity(0.5),
//                                   maxRadius: 30,
//                                   child: const Icon(
//                                     Icons.play_arrow,
//                                     color: Colors.white,
//                                     size: 50,
//                                   ),
//                                 ),
//                               ),
//                             ),
//                         ]),
//                       );
//                     },
//                   ),
//                   isactions
//                       ? moreaction(
//                           context,
//                           type!,
//                           name!,
//                           lat,
//                           lng,
//                           id!,
//                           greviewlink!,
//                           greviewName,
//                           _videoControllers[stories[_controller.index].id]!,
//                         )
//                       : const SizedBox(),
//                 ],
//               )
//             : buildLoadingWidget(),
//         bottomNavigationBar: CustomBottomNavgationBar(
//           1,
//         ),
//       ),
//     );
//   }
// }

// PreloadPageView.builder(
//   itemCount: stories.length,
//   scrollDirection: Axis.vertical,
//   onPageChanged: (index) {
//     print('DDDDone ${videos[index]}');
//
//     _videoControllers[stories[index].id]!.play();
//
//     // _videoControllers[videos[stories[index]]]!
//     //     .play();
//
//     // if (value == 0) {
//     //   _videoControllers[videos[0]]!.play();
//     // } else {
//     //   _videoControllers[videos[value - 1]]!.pause();
//     // }
//
//     // initializePlayer(videos[value]);
//   },
//   preloadPagesCount: 3,
//   controller: PreloadPageController(),
//   itemBuilder: (BuildContext context, int index) {
//     final Reels story = stories[index];
//
//     id = story.id;
//     name = story.name;
//     lat = story.latitude;
//     lng = story.longitude;
//     greviewlink = story.greviewlink;
//     greviewName = story.greviewName;
//
//     log('Reviiew ${story.greviewlink}}');
//     type = story.categorytype;
//
//     return GestureDetector(
//       onTap: () {
//         _videoControllers[story.id]!.value.isPlaying
//             ? _videoControllers[story.id]!.pause()
//             : _videoControllers[story.id]!.play();
//       },
//       child: Stack(children: [
//         isLoaded
//             ? SizedBox(
//           height: MediaQuery.of(context).size.height,
//           width: MediaQuery.of(context).size.width,
//           child: VideoPlayer(
//             _videoControllers[story.id]!,
//           ),
//           // child: Chewie(
//           //   controller: _chewieController!,
//           // ),
//         )
//             : Center(child: buildLoadingWidget()),
//         isLoaded
//             ? StoryDetailWithSeeMore(
//           story: story,
//           onSeeMore: () {
//             setState(() {
//               _videoControllers![story.id]!.pause();
//             });
//
//             story.categorytype ==
//                 AppConstants.carRentalsId
//                     .toString()
//                 ? getcarrentdetails(context, id!,
//                 _videoControllers![story.id]!)
//                 : story.categorytype ==
//                 AppConstants.holidayHomesId
//                     .toString()
//                 ? getholidayhomedetails(
//                 context,
//                 id!,
//                 _videoControllers![story.id]!,
//                 languageController)
//                 : story.categorytype ==
//                 AppConstants.propertiesId
//                     .toString()
//                 ? getpropertydetails(
//                 context,
//                 id!,
//                 _videoControllers![
//                 story.id]!)
//                 : getmaincategorydetails(
//                 context,
//                 id!,
//                 story.label!,
//                 _videoControllers![
//                 story.id]!);
//           },
//         )
//             : const SizedBox(),
//         isLoaded
//             ? VideoActions(
//           story: story,
//           setState: setState,
//           videoController:
//           _videoControllers[story.id]!,
//         )
//             : const SizedBox()
//       ]),
//     );
//   },
// ),

//class ReelsScreen extends StatefulWidget {
//   List<Reels> stories = [];
//
//   ReelsScreen({super.key});
//
//   @override
//   _ReelsScreenState createState() => _ReelsScreenState();
// }
//
// class _ReelsScreenState extends State<ReelsScreen>
//     with SingleTickerProviderStateMixin {
//   PageController? _pageController;
//   AnimationController? _animController;
//   int code = 0, code2 = 0;
//   String msg = 'loading';
//   AuthController authController = AuthController();
//
//   // List<VideoPlayerController>? _videoPlayerController;
//
//   ChewieController? _chewieController;
//   bool isLoaded = false;
//   final SwiperController _controller = SwiperController();
//   int? id;
//   final List<String> videos = [];
//
//   final videoInfo = FlutterVideoInfo();
//
//   final languageController = LanguageController();
//
//   getreels(int page, int size) async {
//     await Api.getreels(0, 100).then((value) {
//       value != null
//           ? setState(() {
//               code = value.code;
//
//               stories.addAll(value.category);
//
//               videos.addAll(value.category.map((e) => e.video!));
//
//               initializePlayers();
//             })
//           // ignore: unnecessary_statements
//           : null;
//     });
//   }
//
//   @override
//   void initState() {
//     super.initState();
//     getreels(0, 20);
//     languageController.getcuurentlanguage();
//     authController.isloggedin();
//   }
//
//   @override
//   void dispose() async {
//     super.dispose();
//
//     _videoControllers.forEach((key, value) {
//       value.pause();
//     });
//
//     _controller.dispose();
//
//     SystemChrome.setPreferredOrientations([
//       DeviceOrientation.portraitUp,
//     ]);
//   }
//
//   void disposeVideos() {
//     _videoControllers.forEach((key, value) {
//       value.pause();
//     });
//
//     _controller.dispose();
//   }
//
//   bool isPlaying = true;
//
//
//   final Map<int?, VideoPlayerController> _videoControllers = {};
//
//   Future<void> initializePlayers() async {
//     final storiesToInitialize =
//         stories.where((story) => story.video != null).toList();
//
//
//     _videoControllers.removeWhere(
//         (key, value) => !storiesToInitialize.any((story) => story.id == key));
//
//     for (var story in storiesToInitialize) {
//
//       if (_videoControllers[story.id] == null) {
//         _videoControllers[story.id] =
//             VideoPlayerController.network(story.video!);
//         await _videoControllers[story.id]!.initialize();
//       }
//     }
//
//     storiesToInitialize[0].video != null
//         ? _videoControllers[storiesToInitialize[0].id!]!.play()
//         : null;
//
//
//     setState(() {
//       isLoaded = true;
//     });
//
//   }
//
//
//
//   @override
//   Widget build(BuildContext context) {
//     SizedBox(
//         width: 30,
//         height: 25,
//         child: SvgPicture.asset(
//           'assets/media_icon.svg',
//           semanticsLabel: 'Acme Logo',
//           width: 13,
//           height: 13,
//           fit: BoxFit.fill,
//           color: Colors.white,
//         ));
//     return WillPopScope(
//       onWillPop: () async {
//         // _videoPlayerController!.value.isPlaying
//         // for (var controller in _videoControllers!) {
//         //   controller.pause();
//         //   controller.dispose();
//         // }
//         // if (_videoPlayerController!.value.isPlaying) {
//         //   _videoPlayerController!.pause();
//         //   _videoPlayerController = null;
//         //   _controller.dispose();
//         // }
//
//         disposeVideos();
//
//         setState(() {
//           isactions = false;
//         });
//         return Future.value(true);
//       },
//       child: Scaffold(
//         backgroundColor: Colors.black,
//         body: code == 1
//             ? Stack(
//                 children: <Widget>[
//                   Swiper(
//                     itemCount: stories.length,
//                     scrollDirection: Axis.vertical,
//                     onIndexChanged: (index) {
//                       log(videos[index]);
//
//                       _videoControllers.entries
//                           .where((element) =>
//                               element.key != stories[index].id)
//                           .forEach((element) {
//                         element.value.pause();
//                       });
//
//                       _videoControllers[stories[index].id]!.play();
//
//                       setState(() {
//                         isPlaying = true;
//                       });
//                     },
//                     controller: _controller,
//                     // onPageChanged: (index) {
//                     //   print('DDDDone ${videos[index]}');
//                     //
//                     //   _videoControllers[stories[index].id]!.play();
//                     //
//                     //   // _videoControllers[videos[stories[index]]]!
//                     //   //     .play();
//                     //
//                     //   // if (value == 0) {
//                     //   //   _videoControllers[videos[0]]!.play();
//                     //   // } else {
//                     //   //   _videoControllers[videos[value - 1]]!.pause();
//                     //   // }
//                     //
//                     //   // initializePlayer(videos[value]);
//                     // },
//                     // preloadPagesCount: 3,
//                     // controller: PreloadPageController(),
//                     itemBuilder: (BuildContext context, int index) {
//                       final Reels story = stories[index];
//
//                       void playOrPause() {
//                         _videoControllers[story.id]!.value.isPlaying
//                             ? _videoControllers[story.id]!.pause()
//                             : _videoControllers[story.id]!.play();
//
//                         setState(() {
//                           isPlaying = !isPlaying;
//                         });
//                       }
//
//                       id = story.id;
//                       name = story.name;
//                       lat = story.latitude;
//                       lng = story.longitude;
//                       greviewlink = story.greviewlink;
//                       greviewName = story.greviewName;
//
//                       log('Reviiew ${story.greviewlink}}');
//                       type = story.categorytype;
//
//                       return GestureDetector(
//                         onTap: () {
//                           playOrPause();
//                         },
//                         child: Stack(children: [
//                           isLoaded
//                               ? SizedBox(
//                                   height: MediaQuery.of(context).size.height,
//                                   width: MediaQuery.of(context).size.width,
//                                   child: VideoPlayer(
//                                     _videoControllers[story.id]!,
//                                   ),
//                                   // child: Chewie(
//                                   //   controller: _chewieController!,
//                                   // ),
//                                 )
//                               : Center(child: buildLoadingWidget()),
//                           isLoaded
//                               ? StoryDetailWithSeeMore(
//                                   story: story,
//                                   onSeeMore: () {
//                                     setState(() {
//                                       _videoControllers![story.id]!.pause();
//                                     });
//
//                                     story.categorytype ==
//                                             AppConstants.carRentalsId.toString()
//                                         ? getcarrentdetails(context, id!,
//                                             _videoControllers![story.id]!)
//                                         : story.categorytype ==
//                                                 AppConstants.holidayHomesId
//                                                     .toString()
//                                             ? getholidayhomedetails(
//                                                 context,
//                                                 id!,
//                                                 _videoControllers![story.id]!,
//                                                 languageController)
//                                             : story.categorytype ==
//                                                     AppConstants.propertiesId
//                                                         .toString()
//                                                 ? getpropertydetails(
//                                                     context,
//                                                     id!,
//                                                     _videoControllers![
//                                                         story.id]!)
//                                                 : getmaincategorydetails(
//                                                     context,
//                                                     id!,
//                                                     story.label!,
//                                                     _videoControllers![
//                                                         story.id]!);
//                                   },
//                                 )
//                               : const SizedBox(),
//                           isLoaded
//                               ? VideoActions(
//                                   story: story,
//                                   setState: setState,
//                                   videoController: _videoControllers[story.id]!,
//                                 )
//                               : const SizedBox(),
//                           if (!isPlaying)
//                             Center(
//                               child: InkWell(
//                                 onTap: () {
//                                   playOrPause();
//                                 },
//                                 child: CircleAvatar(
//                                   backgroundColor:
//                                       Colors.black.withOpacity(0.5),
//                                   maxRadius: 30,
//                                   child: const Icon(
//                                     Icons.play_arrow,
//                                     color: Colors.white,
//                                     size: 50,
//                                   ),
//                                 ),
//                               ),
//                             ),
//                         ]),
//                       );
//                     },
//                   ),
//                   isactions
//                       ? moreaction(
//                           context,
//                           type!,
//                           name!,
//                           lat,
//                           lng,
//                           id!,
//                           greviewlink!,
//                           greviewName,
//                           _videoControllers[
//                               stories[_controller.index].id]!,
//                         )
//                       : const SizedBox(),
//                 ],
//               )
//             : buildLoadingWidget(),
//         bottomNavigationBar: CustomBottomNavgationBar(
//           1,
//         ),
//       ),
//     );
//   }
// }
