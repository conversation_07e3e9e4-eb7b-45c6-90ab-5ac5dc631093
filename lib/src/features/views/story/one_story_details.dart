import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
// import 'package:flutter_ffmpeg/flutter_ffmpeg.dart';

import 'package:flutter_svg/svg.dart';
import 'package:flutter_swiper_plus/flutter_swiper_plus.dart';
// import 'package:flutter_video_info/flutter_video_info.dart';
import 'package:page/src/core/config/constants.dart';
import 'package:page/src/features/controllers/language_controller.dart';
import 'package:video_player/video_player.dart';

import '../../../core/localization/app_localizations.dart';
import '../../../core/services/api.dart';
import '../../../core/shared_widgets/shared_widgets.dart';
import '../../controllers/auth_controller.dart';
import '../../models/reels.dart';
import '../ad_details/video_widget.dart';
import 'widgets/reel_widgets/current_details.dart';
import 'widgets/reel_widgets/details.dart';
import 'widgets/reel_widgets/holiday_details.dart';
import 'widgets/reel_widgets/luxary_details.dart';
import 'widgets/reel_widgets/reel_details.dart';
import 'widgets/see_more.dart';
import 'widgets/video_actions.dart';

// ignore: must_be_immutable
class OneStoryScreen extends StatefulWidget {
  final int? id;

  const OneStoryScreen({super.key, required this.id});

  @override
  _OneStoryScreenState createState() => _OneStoryScreenState();
}

class _OneStoryScreenState extends State<OneStoryScreen>
    with SingleTickerProviderStateMixin {
  PageController? _pageController;
  AnimationController? _animController;
  VideoPlayerController? _videoController;
  int code = 0, code2 = 0;
  String msg = 'loading';
  AuthController authController = AuthController();
  VideoPlayerController? _videoPlayerController;
  bool isloading = false;
  final SwiperController _controller = SwiperController();
  int? id;
  final List<String> videos = [];

  //   'http://www.exit109.com/~dnn/clips/RW20seconds_1.mp4',
  //   'http://www.exit109.com/~dnn/clips/RW20seconds_2.mp4',
  //   'https://assets.mixkit.co/videos/preview/mixkit-young-mother-with-her-little-daughter-decorating-a-christmas-tree-39745-large.mp4',
  //   'https://assets.mixkit.co/videos/preview/mixkit-mother-with-her-little-daughter-eating-a-marshmallow-in-nature-39764-large.mp4',
  //   'https://assets.mixkit.co/videos/preview/mixkit-girl-in-neon-sign-1232-large.mp4',
  //   'https://assets.mixkit.co/videos/preview/mixkit-taking-photos-from-different-angles-of-a-model-34421-large.mp4',
  //   'https://assets.mixkit.co/videos/preview/mixkit-winter-fashion-cold-looking-woman-concept-video-39874-large.mp4',
  //   'https://assets.mixkit.co/videos/preview/mixkit-womans-feet-splashing-in-the-pool-1261-large.mp4',
  //   'https://assets.mixkit.co/videos/preview/mixkit-a-girl-blowing-a-bubble-gum-at-an-amusement-park-1226-large.mp4'
  // ];
  // final videoInfo = FlutterVideoInfo();

  Reels? story;

  getReel() async {
    await Api.getOneReel(widget.id!).then((value) {
      try {
        value != null
            ? setState(() {
                code = value.code;

                story = value.category;

                videos.add(value.category!.video!);

                initializePlayer(value.category!.video!);
              })
            // ignore: unnecessary_statements
            : null;

        log("sdkdsnwnd ${videos}");
      } catch (e) {
        Navigator.pop(context);
        snackbar(AppLocalizations.of(context).translate('error occured'));
        print(e);
      }
    });
  }

  final languageController = LanguageController();

  @override
  void initState() {
    super.initState();
    getReel();
    languageController.getcuurentlanguage();

    authController.isloggedin();
  }

  @override
  void dispose() async {
    super.dispose();
    print('dispose stories');
    _pageController!.dispose();
    _animController!.dispose();
    _videoController?.dispose();
    _videoPlayerController!.dispose();
    _videoPlayerController?.pause();
    _videoPlayerController!.dispose();
    // _chewieController!.dispose();
    if (_videoPlayerController!.value.isPlaying) {
      _videoPlayerController!.dispose();
    }
    // if (_videoPlayerController.value.isPlaying) _videoPlayerController.pause();

    _videoPlayerController = null;

    await _videoPlayerController!.seekTo(Duration.zero);
    SystemChrome.setPreferredOrientations([
      DeviceOrientation.portraitUp,
    ]);
  }

  bool isPlaying = false;

  Future initializePlayer(String url) async {
    setState(() {
      isloading = false;
    });
    if (_videoPlayerController != null) {
      _videoPlayerController!.dispose();
    }
    _videoPlayerController =
        VideoPlayerController.network(Uri.parse(url).toString());

    await Future.wait([_videoPlayerController!.initialize()]);
    if (!mounted) return;
    // _chewieController = ChewieController(
    //   videoPlayerController: _videoPlayerController!,
    //   autoPlay: true,
    //   showControls: false,
    //   looping: true,
    //   aspectRatio: 16.25 / 32,
    // );
    setState(() {
      isloading = true;
    });
    _videoPlayerController!.addListener(() {
      setState(() {
        isPlaying = _videoPlayerController!.value.isPlaying;
      });

      if (_videoPlayerController!.value.position.inSeconds ==
          _videoPlayerController!.value.duration.inSeconds) {
        _controller.next();
        _videoPlayerController!.removeListener(() {});
      }
    });

    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    SizedBox(
        width: 30,
        height: 25,
        child: SvgPicture.asset(
          'assets/media_icon.svg',
          semanticsLabel: 'Acme Logo',
          width: 13,
          height: 13,
          fit: BoxFit.fill,
          color: Colors.white,
        ));
    return WillPopScope(
      onWillPop: () async {
        if (_videoPlayerController!.value.isPlaying) {
          _videoPlayerController!.pause();
          _videoPlayerController = null;
          _controller.dispose();
        }
        setState(() {
          isactions = false;
        });
        return Future.value(true);
      },
      child: GestureDetector(
        onTap: () {
          _videoPlayerController!.value.isPlaying
              ? _videoPlayerController!.pause()
              : _videoPlayerController!.play();
        },
        child: Scaffold(
          backgroundColor: Colors.black,
          body: code == 1
              ? Builder(builder: (context) {
                  id = story?.id;
                  name = story?.name;
                  lat = story?.latitude;
                  lng = story?.longitude;
                  greviewlink = story?.greviewlink;

                  log('Reviiew ${story?.greviewlink}}');
                  type = story?.categorytype;

                  return Stack(children: [
                    isloading
                        ? SizedBox(
                            height: MediaQuery.of(context).size.height,
                            width: MediaQuery.of(context).size.width,
                            child: VideoPlayer(
                              _videoPlayerController!,
                              // looping: true,
                              // autoplay: true,
                            ),
                            // Chewie(
                            //   controller: _chewieController!,
                            // ),
                          )
                        : Center(child: buildLoadingWidget()),
                    isloading
                        ? StoryDetailWithSeeMore(
                            story: story!,
                            onSeeMore: () {
                              setState(() {
                                _videoPlayerController?.pause();
                              });
                              story?.categorytype ==
                                      AppConstants.carRentalsId.toString()
                                  ? getcarrentdetails(
                                      context, id!, _videoPlayerController!)
                                  : story?.categorytype ==
                                          AppConstants.holidayHomesId.toString()
                                      ? getholidayhomedetails(
                                          context,
                                          id!,
                                          _videoPlayerController!,
                                          languageController)
                                      : story?.categorytype ==
                                              AppConstants.propertiesId
                                                  .toString()
                                          ? getpropertydetails(context, id!,
                                              _videoPlayerController!)
                                          : getmaincategorydetails(
                                              context,
                                              id!,
                                              story!.label!,
                                              _videoPlayerController!);
                              // story?.categorytype == 'main_category'
                              //     ? getmaincategorydetails(context, id!,
                              //         story!.label!, _videoPlayerController!)
                              //     : story?.categorytype == 'car_rent'
                              //         ? getcarrentdetails(
                              //             context, id!, _videoPlayerController!)
                              //         : story?.categorytype == 'holiday_home'
                              //             ? getholidayhomedetails(context, id!,
                              //                 _videoPlayerController!)
                              //             : story?.categorytype == 'luxury'
                              //                 ? getpropertydetails(context, id!,
                              //                     _videoPlayerController!)
                              //                 : null;
                            },
                          )
                        : const SizedBox(),
                    isloading
                        ? VideoActions(
                            story: story!,
                            setState: setState,
                            videoController: _videoPlayerController!,
                          )
                        : const SizedBox()
                  ]);
                })
              : buildLoadingWidget(),
        ),
      ),
    );
  }
}
