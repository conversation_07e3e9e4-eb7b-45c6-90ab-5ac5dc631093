// import 'dart:developer';
//
// import 'package:flutter/material.dart';
// import 'package:flutter/services.dart';
// // import 'package:flutter_ffmpeg/flutter_ffmpeg.dart';
//
// import 'package:flutter_svg/svg.dart';
// import 'package:flutter_video_info/flutter_video_info.dart';
// import 'package:page/src/core/config/constants.dart';
// import 'package:page/src/core/services/api.dart';
// import 'package:page/src/features/controllers/language_controller.dart';
// import 'package:page/src/features/views/home/<USER>';
// import 'package:page/src/features/views/splash_screen/services/splash_services.dart';
// import 'package:page/src/features/views/story/widgets/reel_widgets/current_details.dart';
// import 'package:page/src/features/views/story/widgets/reel_widgets/details.dart';
// import 'package:page/src/features/views/story/widgets/reel_widgets/holiday_details.dart';
// import 'package:page/src/features/views/story/widgets/reel_widgets/property_details.dart';
// import 'package:preload_page_view/preload_page_view.dart';
// import 'package:video_player/video_player.dart';
//
// import '../../../core/shared_widgets/bottom_navgation_bar.dart';
// import '../../../core/shared_widgets/shared_widgets.dart';
// import '../../controllers/auth_controller.dart';
// import '../../models/reels.dart';
// import 'widgets/reel_widgets/reel_details.dart';
// import 'widgets/see_more.dart';
// import 'widgets/video_actions.dart';
//
// class ReelsScreen extends StatefulWidget {
//   const ReelsScreen({super.key});
//
//   @override
//   _ReelsScreenState createState() => _ReelsScreenState();
// }
//
// List<Reels> stories = [];
//
// class _ReelsScreenState extends State<ReelsScreen>
//     with SingleTickerProviderStateMixin {
//   int code = 0;
//   AuthController authController = AuthController();
//
//   bool isLoaded = false;
//
//   int? id;
//
//   final videoInfo = FlutterVideoInfo();
//
//   final languageController = LanguageController();
//
//   Future<void> getReels(int page, int size) async {
//     try {
//       if (stories.isEmpty) {
//         final value = await Api.getreels(page, size);
//
//         if (value != null) {
//           code = value.code;
//
//           stories.addAll(value.category);
//
//           isLoaded = true;
//
//           setState(() {});
//         }
//       } else {
//         setState(() {
//           isLoaded = true;
//         });
//       }
//     } catch (e) {
//       log('Error: $e');
//     }
//   }
//
//   Future<void> initializePlayers() async {
//     setState(() {
//       isLoaded = false;
//     });
//     if (stories.isEmpty) await getReels(0, 20);
//     final storiesToInitialize =
//         stories.where((story) => story.video != null).toList();
//
//     for (int i = 0; i < storiesToInitialize.length; i++) {
//       final story = storiesToInitialize[i];
//
//       if (_videoControllers[story.id] == null) {
//         _videoControllers[story.id] =
//             VideoPlayerController.networkUrl(Uri.parse(story.video!));
//         if (i == 0) {
//           await _videoControllers[story.id]!.initialize();
//         } else {
//           _videoControllers[story.id]!.initialize();
//         }
//       }
//     }
//
//     storiesToInitialize[0].video != null
//         ? _videoControllers[storiesToInitialize[0].id!]!.play()
//         : null;
//
//     setState(() {
//       isLoaded = true;
//     });
//   }
//
//   @override
//   void initState() {
//     super.initState();
//     splashServices.disposeHomeVideoControllers();
//
//     //! Init Reels Videos ====================================
//     initializePlayers();
//
//     languageController.getcuurentlanguage();
//     authController.isloggedin();
//   }
//
//   final splashServices = SplashServices();
//
//   @override
//   void dispose() async {
//     disposeVideos();
//
//     SystemChrome.setPreferredOrientations([
//       DeviceOrientation.portraitUp,
//     ]);
//
//     splashServices.initControllersForHomeData();
//
//     super.dispose();
//   }
//
//   void disposeVideos() {
//     _videoControllers.forEach((key, value) {
//       value?.pause();
//       value = null;
//
//       // value.dispose();
//     });
//     // stories.clear();
//   }
//
//   // get chewieController => ChewieController(
//   //   videoPlayerController: videoPlayerController[id!]!,
//   //   autoPlay: true,
//   //   showControls: false,
//   //   looping: true,
//   //   // aspectRatio: 20 / 23,
//   //   aspectRatio: 5 / 8,
//   //   // aspectRatio: 16.25 / 32,
//   // );
//
//   bool isPlaying = true;
//
//   final Map<int?, VideoPlayerController?> _videoControllers =
//       reelsVideoController;
//
//   @override
//   Widget build(BuildContext context) {
//     SizedBox(
//         width: 30,
//         height: 25,
//         child: SvgPicture.asset(
//           'assets/media_icon.svg',
//           semanticsLabel: 'Acme Logo',
//           width: 13,
//           height: 13,
//           fit: BoxFit.fill,
//           color: Colors.white,
//         ));
//
//     void pauseAllOtherVideos(int index) {
//       _videoControllers.entries
//           .where((element) => element.key != stories[index].id)
//           .forEach((element) {
//         element.value?.pause();
//       });
//
//       _videoControllers[stories[index].id]!.play();
//
//       setState(() {
//         isPlaying = true;
//       });
//     }
//
//     return WillPopScope(
//       onWillPop: () async {
//         disposeVideos();
//
//         return Future.value(true);
//       },
//       child: Scaffold(
//         backgroundColor: Colors.black,
//         body: isLoaded == true
//             ? PreloadPageView.builder(
//                 itemCount: stories.length,
//                 scrollDirection: Axis.vertical,
//                 onPageChanged: (index) {
//                   pauseAllOtherVideos(index);
//
//                   // check if initialized or not
//                   log('DDSDDASDAS ${stories[index].id} ViideoUrl ${stories[index].video}');
//
//                   if (!_videoControllers[stories[index].id]!
//                       .value
//                       .isInitialized) {
//                     try {
//                       _videoControllers[stories[index].id]!
//                           .initialize()
//                           .then((_) {
//                         _videoControllers[stories[index].id]!.play();
//                       });
//                     } catch (e) {
//                       log('ErrorRRRRRRRR: $e');
//                     }
//                   }
//                 },
//                 controller: PreloadPageController(),
//                 itemBuilder: (BuildContext context, int index) {
//                   final Reels story = stories[index];
//
//                   final videoActions = VideoActions(
//                     story: story,
//                     setState: setState,
//                     videoController: _videoControllers[story.id]!,
//                   );
//
//                   void playOrPause() {
//                     if (mounted) {
//                       _videoControllers[story.id]!.value.isPlaying
//                           ? _videoControllers[story.id]!.pause()
//                           : _videoControllers[story.id]!.play();
//
//                       setState(() {
//                         isPlaying = !isPlaying;
//                       });
//                     }
//                   }
//
//                   id = story.id;
//                   name = story.name;
//                   lat = story.latitude;
//                   lng = story.longitude;
//                   greviewlink = story.greviewlink;
//                   greviewName = story.greviewName;
//
//                   type = story.categorytype;
//
//                   return GestureDetector(
//                     onTap: () {
//                       playOrPause();
//                     },
//                     child: Stack(children: [
//                       // isLoaded
//                       //     ?
//                       SizedBox(
//                         height: MediaQuery.sizeOf(context).height,
//                         width: MediaQuery.sizeOf(context).width,
//                         child: VideoPlayer(
//                           _videoControllers[story.id]!,
//                         ),
//                       ),
//
//                       //! See More Widget
//                       seeMoreWidget(story: story),
//
//                       //! Video Actions (Like, Share)
//                       videoActions,
//
//                       if (!isPlaying)
//                         Center(
//                           child: InkWell(
//                             onTap: () {
//                               playOrPause();
//                             },
//                             child: CircleAvatar(
//                               backgroundColor: Colors.black.withOpacity(0.5),
//                               maxRadius: 30,
//                               child: const Icon(
//                                 Icons.play_arrow,
//                                 color: Colors.white,
//                                 size: 50,
//                               ),
//                             ),
//                           ),
//                         ),
//                     ]),
//                   );
//                 },
//               )
//             : buildLoadingWidget(),
//         bottomNavigationBar: CustomBottomNavgationBar(
//           1,
//         ),
//       ),
//     );
//   }
//
//   Widget seeMoreWidget({
//     required Reels story,
//   }) {
//     return isLoaded
//         ? StoryDetailWithSeeMore(
//             story: story,
//             onSeeMore: () {
//               setState(() {
//                 _videoControllers[story.id]!.pause();
//               });
//
//               story.categorytype == AppConstants.carRentalsId.toString()
//                   ? getcarrentdetails(
//                       context, story.id!, _videoControllers[story.id]!)
//                   : story.categorytype == AppConstants.holidayHomesId.toString()
//                       ? getholidayhomedetails(context, story.id!,
//                           _videoControllers[story.id]!, languageController)
//                       : story.categorytype ==
//                               AppConstants.propertiesId.toString()
//                           ? getpropertydetails(
//                               context, story.id!, _videoControllers[story.id]!)
//                           : getmaincategorydetails(context, story.id!,
//                               story.label!, _videoControllers[story.id]!);
//             },
//           )
//         : const SizedBox();
//   }
// }
