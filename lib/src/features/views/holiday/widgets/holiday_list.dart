import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:page/src/core/config/constants.dart';
import 'package:page/src/core/services/api.dart';
import 'package:page/src/features/controllers/auth_controller.dart';
import 'package:page/src/features/models/video_model.dart';
import 'package:page/src/features/views/holiday/widgets/holiday_filter.dart';
import 'package:provider/provider.dart';

import '../../../../core/localization/app_language.dart';
import '../../../../core/localization/app_localizations.dart';
import '../../holiday_home_details/holiday_home_details.dart';
import '../../property_details/widgets/property_details.dart';

class HolidayList extends StatelessWidget {
  final List<VideoModel> results;
  final AuthController authController;
  final date;
  final time;
  final id;

  // final featuredvideo;
  final currencyController;

  const HolidayList(
    this.results,
    this.authController,
    this.date,
    this.time,
    this.id,
    // this.featuredvideo,
    this.currencyController, {
    Key? key,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    var lang =
        Provider.of<AppLanguage>(context, listen: false).appLocal.languageCode;

    final isEnglish = lang == 'en';

    Widget body(int index) {
      Widget titleWithPriceWidget() {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            results[index].name != null
                ? SizedBox(
                    width: kIsWeb
                        ? baseWebWidth
                        : MediaQuery.of(context).size.width * 0.3,
                    child: Text(
                      results[index].name!,
                      style: const TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.w700,
                          fontSize: 12),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  )
                : Container(),
            const SizedBox(height: 5),
            if (results.isNotEmpty && results[index].startprice != null)
              SizedBox(
                width: kIsWeb
                    ? baseWebWidth
                    : MediaQuery.of(context).size.width * 0.3,
                child: Text(
                  '${parsedPrice(results[index].startprice!)} ${currencyController.currency}',
                  // '${num.tryParse(results[index].startprice!.toString())!.toStringAsFixed(0)} ${currencyController.currency}',
                  style: const TextStyle(color: Colors.white, fontSize: 12),
                ),
              )
          ],
        );
      }

      return Column(
        children: [
          Stack(children: [
            GestureDetector(
                onTap: () {
                  Navigator.of(context).push(MaterialPageRoute(
                      builder: (BuildContext context) =>
                          HoldayHomeDetails(video: results[index])));
                },
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(5),
                  child: Image.network(
                    results[index].images!,
                    height: 288,
                    width: kIsWeb
                        ? baseWebWidth2
                        : MediaQuery.of(context).size.width * 0.45,
                    fit: BoxFit.fitHeight,
                  ),
                )),
            GestureDetector(
                onTap: () {
                  Navigator.of(context).push(MaterialPageRoute(
                      builder: (BuildContext context) =>
                          HoldayHomeDetails(video: results[index])));
                },
                child: Container(
                  height: 288,
                  width: kIsWeb
                      ? baseWebWidth2
                      : MediaQuery.of(context).size.width * 0.45,
                  decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(5),
                      gradient: LinearGradient(
                        end: Alignment.bottomCenter,
                        begin: Alignment.center,
                        colors: <Color>[
                          Colors.transparent,
                          //  Colors.white.withOpacity(0.5),
                          Colors.black.withOpacity(0.7)
                        ],
                      )),
                )),
            GestureDetector(
                onTap: () {
                  Navigator.of(context).push(MaterialPageRoute(
                      builder: (BuildContext context) =>
                          HoldayHomeDetails(video: results[index])));
                },
                child: Container(
                  height: 230,
                  width: kIsWeb
                      ? baseWebWidth2
                      : MediaQuery.of(context).size.width * 0.45,
                  decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(5),
                      gradient: LinearGradient(
                        end: Alignment.topCenter,
                        begin: Alignment.center,
                        colors: <Color>[
                          Colors.transparent,
                          //  Colors.white.withOpacity(0.5),
                          Colors.black.withOpacity(0.4)
                        ],
                      )),
                )),
            Positioned.fill(
              top: 5,
              left: 0,
              child: Align(
                alignment: Alignment.topLeft,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Flexible(
                      child: Builder(builder: (context) {
                        final videoClosedDates =
                            closedVideoDates[results[index].rmsCategoryId];

                        if (startDateNotifier.value == null ||
                            endDateNotifier.value == null) {
                          return const SizedBox.shrink();
                        }

                        if (videoClosedDates == null ||
                            videoClosedDates.isEmpty) {
                          return Container(
                            padding: const EdgeInsets.all(5),
                            margin: const EdgeInsets.all(5),
                            decoration: BoxDecoration(
                                color: Colors.green,
                                borderRadius: BorderRadius.circular(5)),
                            child: Text(
                              AppLocalizations.of(context)
                                  .translate('Available'),
                              style: const TextStyle(
                                  color: Colors.white, fontSize: 12),
                            ),
                          );
                        }

                        final closedDates = videoClosedDates?.map((e) {
                              final dateDay = e.split('-')[2];
                              final dateMonth = e.split('-')[1];

                              return '$dateDay/$dateMonth';
                            }).join(' - ') ??
                            '';

                        final isClosedDatesMoreThan3 =
                            videoClosedDates.length > 3;

                        if (isClosedDatesMoreThan3) {
                          return GestureDetector(
                            onTap: () {
                              showDialog(
                                context: context,
                                builder: (BuildContext context) {
                                  return AlertDialog(
                                    backgroundColor: Colors.white,
                                    title: Text(AppLocalizations.of(context)
                                        .translate('Unavailable Dates')),
                                    content: Text(closedDates),
                                    actions: [
                                      TextButton(
                                        child: Text(AppLocalizations.of(context)
                                            .translate('Close')),
                                        onPressed: () {
                                          Navigator.of(context).pop();
                                        },
                                      ),
                                    ],
                                  );
                                },
                              );
                            },
                            child: Container(
                              padding: const EdgeInsets.all(5),
                              margin: const EdgeInsets.all(5),
                              decoration: BoxDecoration(
                                  color: Colors.red,
                                  borderRadius: BorderRadius.circular(5)),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  Text(
                                    AppLocalizations.of(context)
                                        .translate('Unavailable'),
                                    style: const TextStyle(
                                      color: Colors.white,
                                      fontSize: 12,
                                    ),
                                  ),
                                  Text(
                                    AppLocalizations.of(context)
                                        .translate('on selected dates'),
                                    style: TextStyle(
                                      fontFamily: isEnglish
                                          ? null
                                          : GoogleFonts.tajawal().fontFamily,
                                      color: Colors.white,
                                      fontSize: 12,
                                      decoration: TextDecoration.underline,
                                      decorationColor: Colors.white,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          );
                        }
                        return Container(
                          padding: const EdgeInsets.all(5),
                          margin: const EdgeInsets.all(5),
                          decoration: BoxDecoration(
                              color: Colors.red,
                              borderRadius: BorderRadius.circular(5)),
                          child: Text(
                            '${AppLocalizations.of(context).translate('Unavailable on')}\n$closedDates',
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 12,
                            ),
                          ),
                        );
                      }),
                    ),
                  ],
                ),
              ),
            ),
            if (!isEnglish)
              Positioned(bottom: 12, right: 10, child: titleWithPriceWidget())
            else
              Positioned(bottom: 12, left: 10, child: titleWithPriceWidget()),
            Positioned(
                top: 17,
                left: lang == 'ar' ? null : 10,
                right: lang == 'ar' ? 10 : null,
                child: SizedBox(
                  width: MediaQuery.of(context).size.width * 0.25,
                  child: Text(
                    results[index].locationName!,
                    style: const TextStyle(color: Colors.white, fontSize: 13),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ))
          ]),
        ],
      );
    }

    if (kIsWeb) {
      return Padding(
        padding: const EdgeInsets.symmetric(horizontal: 10.0, vertical: 20.0),
        child: Wrap(
          children: results.indexed.map(
            (e) {
              return Padding(
                padding:
                    const EdgeInsets.symmetric(horizontal: 10.0, vertical: 12),
                child: body(e.$1),
              );
            },
          ).toList(),
        ),
      );
    }
    return StaggeredGridView.countBuilder(
        staggeredTileBuilder: (index) {
          return const StaggeredTile.fit(1);
        },
        shrinkWrap: true,
        padding: const EdgeInsets.symmetric(horizontal: 10.0, vertical: 20.0),
        crossAxisCount: 2,
        physics: const NeverScrollableScrollPhysics(),
        mainAxisSpacing: 8,
        itemCount: results.length,
        primary: false,
        itemBuilder: (context, index) {
          return body(index);
        });
  }
}
