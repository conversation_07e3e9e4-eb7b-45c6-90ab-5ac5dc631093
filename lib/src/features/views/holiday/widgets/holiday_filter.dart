import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:page/src/core/services/api.dart';
import 'package:page/src/features/controllers/content_controller.dart';
import 'package:page/src/features/controllers/currency_controller.dart';

import '../../../../core/localization/app_localizations.dart';
import '../../../models/content.dart';
import '../../../models/locations.dart';
import '../holiday.dart';

void holidayFilter(
  BuildContext context, {
  required VoidCallback onApply,
  // required VoidCallback onReset,
  required ContentController contentController,
  required CurrencyController currencyController,
  required format2,
  required Types? type,
}) {
  f_id.clear();

  showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => HolidayHomeFilter(
            onApply: onApply,
            type: type,
            // onReset: onReset,
            contentController: contentController,
            currencyController: currencyController,
            format2: format2,
          ));
}

void resetHolidayFilter() {
  holidayCurrentvalue2 = null;
  holidayCurrentvalue3 = [];
  holidayCurrentvalue4 = null;
  f_id.clear();
  startDateNotifier.value = null;
  endDateNotifier.value = null;
  closedVideoDates.clear();
}

ValueNotifier<DateTime?> startDateNotifier = ValueNotifier(null);
ValueNotifier<DateTime?> endDateNotifier = ValueNotifier(null);
List<int> f_id = [];

class HolidayHomeFilter extends StatefulWidget {
  final VoidCallback onApply;

  // final VoidCallback onReset;
  final ContentController contentController;
  final CurrencyController currencyController;
  final format2;
  final bool fromSearch;
  final Types? type;

  const HolidayHomeFilter(
      {super.key,
      required this.onApply,
      required this.contentController,
      required this.currencyController,
      this.format2,
      required this.type,
      this.fromSearch = false});

  @override
  State<HolidayHomeFilter> createState() => _HolidayHomeFilterState();
}

class _HolidayHomeFilterState extends State<HolidayHomeFilter> {
  Future<void> _selectDate(
      BuildContext context, ValueNotifier<DateTime?> dateNotifier) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: dateNotifier.value ?? DateTime.now(),
      firstDate: DateTime(2000),
      lastDate: DateTime(2101),
    );
    if (picked != null && picked != dateNotifier.value) {
      dateNotifier.value = picked;
    }
  }

  @override
  Widget build(BuildContext context) {
    log('afafsaf ${widget.type}');
    return Padding(
        padding:
            EdgeInsets.only(bottom: MediaQuery.of(context).viewInsets.bottom),
        child: Container(
          height: MediaQuery.of(context).size.height * 0.90,
          decoration: widget.fromSearch
              ? null
              : BoxDecoration(
                  color: const Color(0xffF5F6F7),
                  borderRadius: const BorderRadius.only(
                      topLeft: Radius.circular(25.0),
                      topRight: Radius.circular(25.0)),
                  border: Border.all(color: Colors.black, width: 1.0)),
          child: SingleChildScrollView(
              child: Column(
            children: [
              if (!widget.fromSearch) ...[
                const SizedBox(
                  height: 10,
                ),
                Container(height: 5, width: 30, color: const Color(0xffD2D4D6)),
                const SizedBox(
                  height: 20,
                ),
              ],
              Container(
                  padding: const EdgeInsets.only(left: 20, right: 20),
                  child: Stack(
                    children: [
                      Align(
                        alignment: AppLocalizations.of(context).locale ==
                                const Locale('ar')
                            ? Alignment.centerLeft
                            : Alignment.centerRight,
                        child: GestureDetector(
                            onTap: () {
                              setState(() {
                                resetHolidayFilter();
                              });
                            },
                            child: Text(
                              AppLocalizations.of(context).translate('reset'),
                              style: const TextStyle(color: Color(0xff51565B)),
                            )),
                      ),
                      if (!widget.fromSearch)
                        Center(
                          child: Text(
                            AppLocalizations.of(context).translate('filter'),
                            style: const TextStyle(fontWeight: FontWeight.bold),
                            textAlign: TextAlign.center,
                          ),
                        )
                    ],
                  )),
              // ],
              Container(
                padding: EdgeInsets.all(widget.fromSearch ? 0 : 15),
                child: Container(
                  decoration: BoxDecoration(
                      color:
                          widget.fromSearch ? Colors.transparent : Colors.white,
                      borderRadius: BorderRadius.circular(10)),
                  child: Container(
                    padding: const EdgeInsets.all(15),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          AppLocalizations.of(context).translate('location'),
                          style: const TextStyle(fontSize: 13),
                        ),
                        const SizedBox(
                          height: 10,
                        ),
                        ContentController.locations.isNotEmpty
                            ? Container(
                                // width: 120,
                                height: 50,
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(5),
                                  border: Border.all(
                                      color: Colors.black12, width: 1.0),
                                ),
                                child: DropdownButton<int>(
                                    isExpanded: true,
                                    hint: Padding(
                                      padding: const EdgeInsets.only(
                                          left: 20, right: 20),
                                      child: Text(
                                        AppLocalizations.of(context)
                                            .translate('Choose Location'),
                                        style: const TextStyle(
                                            color: Color(0xffB7B7B7)),
                                      ),
                                    ),
                                    value: holidayCurrentvalue2,
                                    underline: const SizedBox(),
                                    iconEnabledColor: Colors.black,
                                    items: ContentController.locations
                                        .map((Locations value) {
                                      return DropdownMenuItem<int>(
                                        value: value.id,
                                        child: Container(
                                            padding: const EdgeInsets.only(
                                                left: 10, right: 10),
                                            child: Text(
                                              value.name!,
                                              style:
                                                  const TextStyle(fontSize: 16),
                                            )),
                                      );
                                    }).toList(),
                                    onChanged: (value) {
                                      setState(() {
                                        holidayCurrentvalue2 = value;
                                      });
                                    }))
                            : Text(AppLocalizations.of(context)
                                .translate('No Loction to show')),
                        if (widget.type == null) ...[
                          const SizedBox(height: 20),
                          Text(
                            AppLocalizations.of(context)
                                .translate('Property Type'),
                            style: const TextStyle(fontSize: 13),
                          ),
                          const SizedBox(
                            height: 10,
                          ),
                          ContentController.types.isNotEmpty
                              ? Container(
                                  height: 50,
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(5),
                                    border: Border.all(
                                        color: Colors.black12, width: 1.0),
                                  ),
                                  child: DropdownButton<int>(
                                      isExpanded: true,
                                      hint: Padding(
                                        padding: const EdgeInsets.only(
                                            left: 20, right: 20),
                                        child: Text(
                                          AppLocalizations.of(context)
                                              .translate(
                                                  'Choose Property Type'),
                                          style: const TextStyle(
                                              color: Color(0xffB7B7B7)),
                                        ),
                                      ),
                                      value: holidayCurrentvalue4,
                                      underline: const SizedBox(),
                                      iconEnabledColor: Colors.black,
                                      items: ContentController.types
                                          .map((Types value) {
                                        return DropdownMenuItem<int>(
                                          value: value.id,
                                          child: Container(
                                              padding: const EdgeInsets.only(
                                                  left: 10, right: 10),
                                              child: Text(
                                                value.name!,
                                                style: const TextStyle(
                                                    fontSize: 16),
                                              )),
                                        );
                                      }).toList(),
                                      onChanged: (value) {
                                        setState(() {
                                          holidayCurrentvalue4 = value;
                                        });
                                      }))
                              : Center(
                                  child: Text(AppLocalizations.of(context)
                                      .translate('No types to show'))),
                        ],
                        const SizedBox(height: 20),
                        Text(
                          AppLocalizations.of(context).translate('Feature'),
                          style: const TextStyle(fontSize: 13),
                        ),
                        const SizedBox(
                          height: 10,
                        ),
                        StatefulBuilder(
                          builder: (BuildContext context, setStateF) {
                            return SizedBox(
                              height: 60,
                              child: ListView.builder(
                                shrinkWrap: true,
                                scrollDirection: Axis.horizontal,
                                itemCount: ContentController.features.length,
                                itemBuilder: (BuildContext context, int i) {
                                  return Container(
                                      margin: const EdgeInsets.all(5),
                                      child: InkWell(
                                        onTap: () {
                                          if (f_id.contains(ContentController
                                              .features[i].id)) {
                                            f_id.remove(ContentController
                                                .features[i].id);
                                          } else {
                                            f_id.add(ContentController
                                                .features[i].id!);
                                          }
                                          setStateF(() {});
                                        },
                                        child: Card(
                                          color: f_id.contains(ContentController
                                                  .features[i].id)
                                              ? const Color(0xff233549)
                                              : Colors.white,
                                          child: Center(
                                            child: Padding(
                                              padding:
                                                  const EdgeInsets.all(8.0),
                                              child: Text(
                                                ContentController
                                                    .features[i].name!,
                                                style: TextStyle(
                                                    color: f_id.contains(
                                                            ContentController
                                                                .features[i].id)
                                                        ? Colors.white
                                                        : Colors.black),
                                              ),
                                            ),
                                          ),
                                        ),
                                      ));
                                },
                              ),
                            );
                          },
                        ),
                        const SizedBox(
                          height: 20,
                        ),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              AppLocalizations.of(context).translate('size'),
                              style: const TextStyle(fontSize: 13),
                            ),
                            Row(
                              children: [
                                Text('${holidayMinSize?.toInt() ?? ''}',
                                    style: const TextStyle(
                                        fontSize: 13,
                                        color: Color(0xff51565B))),
                                Text(
                                    ' - ${holidayMaxSize?.toInt() ?? ''} ${AppLocalizations.of(context).translate('sqrf')}',
                                    style: const TextStyle(
                                        fontSize: 13,
                                        color: Color(0xff51565B))),
                              ],
                            ),
                          ],
                        ),
                        const SizedBox(
                          height: 10,
                        ),
                        StatefulBuilder(builder: (context, setState) {
                          return RangeSlider(
                            activeColor: const Color(0xFFE2CBA2),
                            inactiveColor:
                                const Color(0xFF485E77).withOpacity(0.9),
                            values: holidaySizeRangeValues!,
                            min: holidayMinSize ?? 0,
                            max: holidayMaxSize ?? 0,
                            divisions: 50,
                            labels: RangeLabels(
                              holidaySizeRangeValues!.start.round().toString(),
                              holidaySizeRangeValues!.end.round().toString(),
                            ),
                            onChanged: (RangeValues values) {
                              setState(() {
                                holidaySizeRangeValues = values;
                              });
                            },
                          );
                        }),
                        const SizedBox(
                          height: 10,
                        ),
                        Text(
                          AppLocalizations.of(context)
                              .translate('Number of Bedrooms'),
                          style: const TextStyle(fontSize: 13),
                        ),
                        const SizedBox(
                          height: 10,
                        ),
                        ContentController.holidayRoomSizes.isNotEmpty
                            ? StatefulBuilder(
                                builder: (BuildContext context, setStateF) {
                                  return SizedBox(
                                    height: 60,
                                    child: ListView.builder(
                                      shrinkWrap: true,
                                      scrollDirection: Axis.horizontal,
                                      itemCount: ContentController
                                          .holidayRoomSizes.length,
                                      itemBuilder:
                                          (BuildContext context, int i) {
                                        return Container(
                                            margin: const EdgeInsets.all(5),
                                            child: InkWell(
                                              onTap: () {
                                                if (holidayCurrentvalue3!
                                                    .contains(ContentController
                                                        .holidayRoomSizes[i]
                                                        .size)) {
                                                  holidayCurrentvalue3!.remove(
                                                      ContentController
                                                          .holidayRoomSizes[i]
                                                          .size);
                                                } else {
                                                  holidayCurrentvalue3!.add(
                                                      ContentController
                                                          .holidayRoomSizes[i]
                                                          .size!);
                                                }
                                                setStateF(() {});
                                              },
                                              child: Card(
                                                color: holidayCurrentvalue3!
                                                        .contains(
                                                            ContentController
                                                                .holidayRoomSizes[
                                                                    i]
                                                                .size)
                                                    ? const Color(0xff233549)
                                                    : Colors.white,
                                                child: Center(
                                                  child: Padding(
                                                    padding:
                                                        const EdgeInsets.all(
                                                            8.0),
                                                    child: Text(
                                                      ContentController
                                                          .holidayRoomSizes[i]
                                                          .size!
                                                          .toStringAsFixed(0),
                                                      style: TextStyle(
                                                          color: holidayCurrentvalue3!
                                                                  .contains(
                                                                      ContentController
                                                                          .holidayRoomSizes[
                                                                              i]
                                                                          .size)
                                                              ? Colors.white
                                                              : Colors.black),
                                                    ),
                                                  ),
                                                ),
                                              ),
                                            ));
                                      },
                                    ),
                                  );
                                },
                              )
                            : Center(
                                child: Text(AppLocalizations.of(context)
                                    .translate('no room numbers to show')),
                              ),
                        // const SizedBox(height: 20),
                        // Text(
                        //   AppLocalizations.of(context)
                        //       .translate('Company Name'),
                        //   style: const TextStyle(fontSize: 13),
                        // ),
                        // const SizedBox(
                        //   height: 10,
                        // ),
                        // ContentController.agents.isNotEmpty
                        //     ? Container(
                        //         // width: 120,
                        //         height: 50,
                        //         decoration: BoxDecoration(
                        //           borderRadius: BorderRadius.circular(5),
                        //           border: Border.all(
                        //               color: Colors.black12, width: 1.0),
                        //         ),
                        //         child: DropdownButton<int>(
                        //             isExpanded: true,
                        //             hint: Padding(
                        //               padding: const EdgeInsets.only(
                        //                   left: 20, right: 20),
                        //               child: Text(
                        //                 AppLocalizations.of(context)
                        //                     .translate('Choose Agent'),
                        //                 style: const TextStyle(
                        //                     color: Color(0xffB7B7B7)),
                        //               ),
                        //             ),
                        //             value: holidayCurrentvalue6,
                        //             underline: const SizedBox(),
                        //             iconEnabledColor: Colors.black,
                        //             items: ContentController.agents
                        //                 .map((Agents value) {
                        //               return DropdownMenuItem<int>(
                        //                 value: value.id,
                        //                 child: Container(
                        //                     padding: const EdgeInsets.only(
                        //                         left: 10, right: 10),
                        //                     child: Text(
                        //                       value.name!,
                        //                       style:
                        //                           const TextStyle(fontSize: 16),
                        //                     )),
                        //               );
                        //             }).toList(),
                        //             onChanged: (value) {
                        //               setState(() {
                        //                 holidayCurrentvalue6 = value;
                        //               });
                        //             }))
                        //     : Center(
                        //         child: Text(AppLocalizations.of(context)
                        //             .translate('MoreRelatedReels'))),
                        const SizedBox(
                          height: 20,
                        ),
                        Center(
                            child: GestureDetector(
                                onTap: widget.onApply,
                                child: Container(
                                  height: 50,
                                  width: MediaQuery.of(context).size.width,
                                  decoration: BoxDecoration(
                                      color: const Color(0xFF27b4a8),
                                      borderRadius: BorderRadius.circular(10)),
                                  child: Container(
                                      padding: const EdgeInsets.all(10),
                                      child: Center(
                                          child: Text(
                                        widget.fromSearch
                                            ? AppLocalizations.of(context)
                                                .translate('Search')
                                            : AppLocalizations.of(context)
                                                .translate('Apply Filter'),
                                        style: const TextStyle(
                                            color: Colors.white),
                                      ))),
                                ))),
                        SizedBox(height: widget.fromSearch ? 70 : 20),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          )),
        ));
  }
}
