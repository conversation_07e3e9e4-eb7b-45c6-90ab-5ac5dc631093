import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:page/src/core/localization/app_localizations.dart';

import 'package:flutter/material.dart';

class SortingDialogWidget extends StatefulWidget {
  final Function(String) onSort;

  const SortingDialogWidget({Key? key, required this.onSort}) : super(key: key);

  @override
  _SortingDialogWidgetState createState() => _SortingDialogWidgetState();
}

class _SortingDialogWidgetState extends State<SortingDialogWidget> {
  String selectedSorting = 'None'; // The selected sorting value

  // Sorting options
  final List<String> sortingOptions = [
    'None',
    'Price Low to High',
    'Price High to Low',
    'Newest First',
    'Oldest First',
  ];

  void showSortingDialog(BuildContext context) {
    // translated sorting options
    final List<String> translatedOptions = sortingOptions
        .map((option) => AppLocalizations.of(context).translate(option))
        .toList();

    showGeneralDialog(
      context: context,
      barrierLabel: "Sorting Dialog",
      barrierDismissible: true,
      barrierColor: Colors.black.withOpacity(0.5),
      transitionDuration: const Duration(milliseconds: 300),
      pageBuilder: (context, anim1, anim2) {
        return Align(
          alignment: Alignment.center,
          child: Material(
            borderRadius: BorderRadius.circular(15),
            child: Container(
              padding: const EdgeInsets.all(20),
              // height: 500,
              width: 300,
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(15),
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    AppLocalizations.of(context).translate('Sort By'),
                    style: const TextStyle(
                        fontSize: 18, fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 20),
                  ...translatedOptions.map((option) => RadioListTile<String>(
                        title: Text(option),
                        value: option,
                        groupValue: selectedSorting == 'None'
                            ? AppLocalizations.of(context)
                                .translate(selectedSorting)
                            : AppLocalizations.of(context)
                                .translate(selectedSorting),
                        onChanged: (value) {
                          setState(() {
                            selectedSorting = value!;
                          });
                          widget.onSort(value!); // Call the onSort function
                          Navigator.pop(
                              context); // Close dialog after selection
                        },
                      )),
                ],
              ),
            ),
          ),
        );
      },
      transitionBuilder: (context, anim1, anim2, child) {
        return SlideTransition(
          position: Tween(begin: const Offset(0, 1), end: const Offset(0, 0))
              .animate(anim1),
          child: child,
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => showSortingDialog(context),
      child: Row(
        children: [
          if (selectedSorting !=
                  AppLocalizations.of(context).translate('None') &&
              selectedSorting != 'None') ...[
            Text(
              selectedSorting,
              style: const TextStyle(fontSize: 16),
            ),
            const SizedBox(width: 10),
          ],
          const Icon(CupertinoIcons.sort_down),
        ],
      ),
    );
  }
}
