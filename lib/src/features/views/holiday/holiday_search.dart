import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:page/src/core/shared_widgets/bottom_navgation_bar.dart';
import 'package:page/src/core/utils/print_services.dart';
import 'package:page/src/features/views/holiday/holiday.dart';

import '../../../core/localization/app_localizations.dart';
import '../../controllers/auth_controller.dart';
import '../../controllers/content_controller.dart';
import '../../controllers/currency_controller.dart';
import '../../models/video_model.dart';

class HolidaySearchPage extends StatefulWidget {
  const HolidaySearchPage({super.key});

  @override
  State<HolidaySearchPage> createState() => _HolidaySearchPageState();
}

class _HolidaySearchPageState extends State<HolidaySearchPage> {
  List<int> f_id = [];
  final format2 = DateFormat("dd/MM/yyyy");
  TextEditingController startdateController = TextEditingController();
  TextEditingController enddateController = TextEditingController();
  int pagenumber = 1;
  int code = 0, code2 = 0;
  String msg = 'loading', msg2 = 'loading';
  ContentController contentController = ContentController();
  AuthController authController = AuthController();
  CurrencyController currencyController = CurrencyController();
  List<VideoModel> results = [];
  List<VideoModel> featuredvideo = [];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      bottomNavigationBar: CustomBottomNavgationBar(1),
      appBar: AppBar(
          automaticallyImplyLeading: false,
          title: Text(
            AppLocalizations.of(context).translate('Search Holiday Homes'),
            style: TextStyle(
              fontFamily: isEng(context) ? 'Roboto' : 'Tajawal',
            ),
          ),
          centerTitle: true,
          backgroundColor: const Color(0xFF27b4a8)),
      body: const Padding(
        padding: EdgeInsets.only(
          top: 20,
        ),
        child: HolidayHomeWidgetSection(
          fromSearch: true,
        ),
      ),
    );
  }
}
