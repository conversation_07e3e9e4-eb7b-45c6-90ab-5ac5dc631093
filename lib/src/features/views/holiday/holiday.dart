import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:intl/intl.dart';
import 'package:page/src/core/config/constants.dart';
import 'package:page/src/core/shared_widgets/main_featured_container.dart';
import 'package:page/src/core/utils/show_toast.dart';
import 'package:page/src/features/views/holiday/widgets/holiday_filter.dart';
import 'package:page/src/features/views/holiday/widgets/holiday_list.dart';
import 'package:page/src/features/views/holiday/widgets/holiday_sorting.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

import '../../../core/localization/app_localizations.dart';
import '../../../core/services/api.dart';
import '../../../core/shared_widgets/shared_widgets.dart';
import '../../../core/utils/print_services.dart';
import '../../controllers/auth_controller.dart';
import '../../controllers/content_controller.dart';
import '../../controllers/currency_controller.dart';
import '../../models/content.dart';
import '../../models/video_model.dart';
import 'widgets/holiday_featured_video.dart';

int? holidayCurrentvalue2;
int? holidayCurrentvalue4;
int? holidayCurrentvalue5;
int? holidayCurrentvalue6;
List<double>? holidayCurrentvalue3 = [];
// List<double>? holidayCurrentvalue7 = [];

double? holidayMinSize = 500;
double? holidayMaxSize = 50000;

RangeValues? holidaySizeRangeValues = const RangeValues(500, 50000);

class HolidayHomePage extends StatefulWidget {
  final int? id;
  final time;
  final int? itemid;
  final date;
  final Types? type;

  const HolidayHomePage(
      {this.id, this.time, this.itemid, this.date, this.type});

  @override
  _HolidayName createState() => _HolidayName();
}

class _HolidayName extends State<HolidayHomePage> {
  TextEditingController searchController = TextEditingController();

  List<int> f_id = [];
  final format2 = DateFormat("dd/MM/yyyy");
  TextEditingController startdateController = TextEditingController();
  TextEditingController enddateController = TextEditingController();
  int pagenumber = 1;
  int code = 0, code2 = 0;
  String msg = 'loading', msg2 = 'loading';
  ContentController contentController = ContentController();
  AuthController authController = AuthController();
  CurrencyController currencyController = CurrencyController();
  List<VideoModel> results = [];
  List<VideoModel> featuredvideo = [];
  final RefreshController _refreshControllerwait =
      RefreshController(initialRefresh: false);

  @override
  void initState() {
    super.initState();
    getcategory(pagenumber, "");
    currencyController.getcuurentcurrency(context);
    resetHolidayFilter();
  }

  @override
  void dispose() {
    super.dispose();
    resetHolidayFilter();
  }

  bool isLoading = false;

  @override
  Widget build(BuildContext context) {
    return SafeArea(
        child: Scaffold(
      appBar: AppBar(
        backgroundColor: const Color(0xFF27b4a8),
        centerTitle: true,
        title: Text(
          widget.type != null
              ? widget.type!.name!
              : AppLocalizations.of(context).translate('holidayhome'),
          style: TextStyle(
            fontFamily: isEng(context) ? 'Roboto' : 'Tajawal',
          ),
        ),
      ),
      body: SmartRefresher(
        enablePullDown: true,
        enablePullUp: true,
        header: ClassicHeader(
          refreshingIcon: buildLoadingWidget(),
        ),
        footer: CustomFooter(
          builder: (BuildContext context, LoadStatus? mode) {
            Widget body;
            if (mode == LoadStatus.loading) {
              body = const CupertinoActivityIndicator();
            } else if (mode == LoadStatus.failed) {
              body = const Text("Load Failed!Click retry!",
                  style: TextStyle(color: Color(0xff233549)));
            } else if (mode == LoadStatus.canLoading) {
              body = const Text("release to load more",
                  style: TextStyle(color: Color(0xff233549)));
            } else {
              body = const Text("No more Data",
                  style: TextStyle(color: Color(0xff233549)));
            }
            return Center(child: body);
          },
        ),
        controller: _refreshControllerwait,
        onRefresh: _onRefresh,
        onLoading: _onLoading,
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              isLoading
                  ? buildLoadingWidget()
                  : featuredvideo.isNotEmpty
                      ? MainFeaturedContainer(
                          label: 'featuredHolidayVideos',
                          child: HolidayFeaturedVideo(
                              featuredvideo: featuredvideo,
                              id: widget.id,
                              date: widget.date,
                              time: widget.time,
                              currencyController: currencyController),
                        )
                      : nodatafound(AppLocalizations.of(context)
                          .translate('No featured videos to show')),
              const SizedBox(
                height: 20,
              ),
              HolidayHomeWidgetSection(
                id: widget.id,
                date: widget.date,
                time: widget.time,
                itemid: widget.itemid,
                type: widget.type,
              ),
            ],
          ),
        ),
      ),
    ));
  }

  getcategory(int page, String key, {bool onlyFeatured = false}) async {
    isLoading = true;
    await Api.getmainCategory(
            page, 10, key, AppConstants.propertiesId.toString(),
            typeId: widget.type?.id.toString())
        .then((value) {
      setState(() {
        code2 = value.code;
        msg2 = value.error;
        isLoading = false;
        if (onlyFeatured) {
          featuredvideo.addAll(value.featuredvideo);
          return;
        }
        results.addAll(value.category);
        if (searchController.text.isEmpty) {
          featuredvideo.addAll(value.featuredvideo);
        }
      });
    });
  }

  bool isRefresh = false;

  void _onRefresh() async {
    await Future.delayed(const Duration(milliseconds: 1000));
    Navigator.pushReplacement(context, MaterialPageRoute(builder: (context) {
      return HolidayHomePage(
        type: widget.type,
      );
    }));
    // results.clear();
    // featuredvideo.clear();
    // pagenumber = 1;
    //
    // searchController.clear();
    //
    // await getcategory(pagenumber, "");
    //
    // _refreshControllerwait.refreshCompleted();
  }

  void _onLoading() async {
    pagenumber = pagenumber + 1;
    getcategory(pagenumber, "");
    _refreshControllerwait.loadComplete();
  }
}

class HolidayHomeWidgetSection extends StatefulWidget {
  final int? id;
  final time;
  final int? itemid;
  final date;
  final bool fromSearch;
  final bool fromHome;
  final Types? type;
  final List<VideoModel> propertiesVideos;

  const HolidayHomeWidgetSection(
      {super.key,
      this.id,
      this.time,
      this.itemid,
      this.date,
      this.type,
      this.propertiesVideos = const [],
      this.fromSearch = false,
      this.fromHome = false});

  @override
  State<HolidayHomeWidgetSection> createState() =>
      _HolidayHomeWidgetSectionState();
}

class _HolidayHomeWidgetSectionState extends State<HolidayHomeWidgetSection> {
  TextEditingController searchController = TextEditingController();
  final format2 = DateFormat("dd/MM/yyyy");
  int pagenumber = 1;
  int code2 = 0;
  String msg2 = 'loading';
  ContentController contentController = ContentController();
  CurrencyController currencyController = CurrencyController();
  List<VideoModel> results = [];
  List<VideoModel> featuredvideo = [];
  int code = 0;
  String msg = 'loading';
  AuthController authController = AuthController();

  bool isFilterVisible = true;
  bool isLoading = false;

  // Sorting variables
  String selectedSorting = 'None'; // Track current sorting selection

  void clearResultsAndFeaturedVideos() {
    setState(() {
      results.clear();
      if (searchController.text.isEmpty) {
        featuredvideo.clear();
      }
    });
  }

  Future<void> getcategory(int page, String key) async {
    if (widget.propertiesVideos.isEmpty) {
      await Api.getmainCategory(
        page,
        10,
        key,
        AppConstants.holidayHomesId.toString(),
        typeId: widget.type?.id.toString(),
      ).then((value) {
        setState(() {
          code2 = value.code;
          msg2 = value.error;
          results.addAll(value.category);
          if (searchController.text.isEmpty) {
            featuredvideo.addAll(value.featuredvideo);
          }
        });
      });
    } else {
      setState(() {
        results.addAll(widget.propertiesVideos);
        isLoading = false;
        code2 = 1;
      });
    }
  }

  void onSearchSubmitted(String text) async {
    clearResultsAndFeaturedVideos();
    isLoading = true;
    if (text.isEmpty) {
      await getcategory(1, "");
    } else {
      pagenumber = 1;
      await getcategory(1, text);
    }

    setState(() {
      isLoading = false;
    });
  }

  @override
  void initState() {
    super.initState();
    isFilterVisible = widget.fromSearch;

    Future.wait([
      getcategory(pagenumber, ""),
      if (ContentController.locations.isEmpty) contentController.getlocations(),
      if (ContentController.features.isEmpty)
        contentController.getfeatures(AppConstants.holidayHomesId.toString()),
      // if (ContentController.types.isEmpty)
      contentController.gettypes(AppConstants.holidayHomesId.toString()),
      if (ContentController.sizes.isEmpty) contentController.getsizes(),
      if (ContentController.holidayRoomSizes.isEmpty)
        contentController.getsizesAndRooms(),
      authController.isloggedin(),
      currencyController.getcuurentcurrency(context),
    ]);
  }

  void sortResults(String criteria) {
    setState(() {
      if (criteria == 'Price Low to High') {
        results.sort((a, b) => a.startprice!.compareTo(b.startprice!));
      } else if (criteria == 'Price High to Low') {
        results.sort((a, b) => b.startprice!.compareTo(a.startprice!));
      } else if (criteria == 'Oldest First') {
        results.sort((a, b) => a.createdAt!.compareTo(b.createdAt!));
      } else if (criteria == 'Newest First') {
        results.sort((a, b) => b.createdAt!.compareTo(a.createdAt!));
      } else {
        results.sort((a, b) => a.id!.compareTo(b.id!));
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final Widget svg2 = SizedBox(
        width: 20,
        height: 20.0,
        child: SvgPicture.asset(
          'assets/filter.svg',
          semanticsLabel: 'Filter Icon',
          fit: BoxFit.cover,
        ));

    final body = [
      if (widget.fromSearch && isFilterVisible)
        AnimatedContainer(
          duration: const Duration(milliseconds: 300),
          child: HolidayHomeFilter(
            fromSearch: true,
            type: widget.type,
            onApply: () async {
              final startDate = startDateNotifier.value;
              final endDate = endDateNotifier.value;

              if (startDate != null) {
                if (endDate == null) {
                  showToast(
                    AppLocalizations.of(context)
                        .translate('Please select end date'),
                  );
                  return;
                }
                if (endDate.isBefore(startDate)) {
                  showToast(
                    AppLocalizations.of(context)
                        .translate('End date should be after start date'),
                  );
                  return;
                }
              }

              filtermaincategory(
                holidayCurrentvalue2 != null ? holidayCurrentvalue2! : 0,
                f_id,
                holidayCurrentvalue4 != null ? holidayCurrentvalue4! : 0,
              );
              if (widget.fromSearch) {
                setState(() {
                  isFilterVisible = false;
                });
              } else {
                Navigator.pop(context);
              }
            },
            contentController: contentController,
            currencyController: currencyController,
            format2: format2,
          ),
        ),
      if (widget.fromSearch && !isFilterVisible)
        GestureDetector(
          onTap: () {
            setState(() {
              isFilterVisible = true;
            });
          },
          child: Container(
            padding: const EdgeInsets.only(left: 20, right: 20),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  AppLocalizations.of(context).translate('Filter'),
                  style: const TextStyle(
                      color: Color(0xff51565B), fontWeight: FontWeight.bold),
                ),
                Row(
                  children: [
                    SortingDialogWidget(
                      onSort: (value) {
                        setState(() {
                          selectedSorting = value;
                        });
                        sortResults(value);
                      },
                    ),
                    const SizedBox(width: 10),
                    svg2,
                  ],
                ),
              ],
            ),
          ),
        ),
      if (!widget.fromSearch) ...[
        Container(
          padding: const EdgeInsets.only(left: 20, right: 20),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                child: Text(
                  AppLocalizations.of(context).translate('All Properties'),
                  style: widget.fromHome
                      ? const TextStyle(
                          color: Color(0xff51565B),
                          fontSize: 17,
                          fontWeight: FontWeight.bold)
                      : const TextStyle(
                          color: Color(0xff51565B),
                          fontWeight: FontWeight.bold),
                ),
              ),

              // Filter Button
              Row(
                children: [
                  // Sorting Button
                  SortingDialogWidget(
                    onSort: (value) {
                      setState(() {
                        selectedSorting = value;
                      });
                      sortResults(value);
                    },
                  ),
                  const SizedBox(width: 10),
                  GestureDetector(
                    onTap: () {
                      holidayFilter(
                        context,
                        contentController: contentController,
                        currencyController: currencyController,
                        format2: format2,
                        type: widget.type,
                        onApply: () async {
                          final startDate = startDateNotifier.value;
                          final endDate = endDateNotifier.value;

                          if (startDate != null) {
                            if (endDate == null) {
                              showToast(
                                AppLocalizations.of(context)
                                    .translate('Please select end date'),
                              );
                              return;
                            }
                            if (endDate.isBefore(startDate)) {
                              showToast(
                                AppLocalizations.of(context).translate(
                                    'End date should be after start date'),
                              );
                              return;
                            }
                          }
                          Navigator.pop(context);
                          setState(() {
                            isLoading = true;
                          });

                          await filtermaincategory(
                            holidayCurrentvalue2 != null
                                ? holidayCurrentvalue2!
                                : 0,
                            f_id,
                            holidayCurrentvalue4 != null
                                ? holidayCurrentvalue4!
                                : 0,
                          );

                          setState(() {
                            isLoading = false;
                          });
                        },
                      );
                    },
                    child: svg2,
                  ),
                ],
              ),
            ],
          ),
        ),
      ],
      const SizedBox(height: 10),
      if (!isFilterVisible) ...[
        if (results.isEmpty && widget.fromSearch && !isLoading)
          nodatafound(AppLocalizations.of(context)
              .translate('No Holiday Homes to show'))
        else if (isLoading || code2 == 0 && msg2 == 'loading')
          buildLoadingWidget()
        else if (code2 == 1)
          results.isNotEmpty
              ? HolidayList(results, authController, widget.date, widget.time,
                  widget.id, currencyController)
              : nodatafound(AppLocalizations.of(context)
                  .translate('No Holiday Homes to show'))
        else
          buildErrorWidget(msg2),
      ]
    ];

    return widget.fromSearch
        ? ListView(children: body)
        : Column(crossAxisAlignment: CrossAxisAlignment.start, children: body);
  }

  filtermaincategory(
    int location,
    List<int> feature,
    int type,
  ) async {
    results.clear();

    final startSize = holidaySizeRangeValues!.start;

    final endSize = holidaySizeRangeValues!.end;

    isLoading = true;
    await Api.filterholidayhome(location, holidayCurrentvalue3!, null, null,
            feature, type, startSize, endSize)
        .then((value) {
      value != null
          ? setState(() {
              isLoading = false;
              results.addAll(value.category);
            })
          // ignore: unnecessary_statements
          : null;
    });
  }
}
