import 'package:flutter/material.dart';
import 'package:page/src/core/utils/main_cached_image.dart';
import 'package:page/src/features/models/video_model.dart';
import 'package:provider/provider.dart';

import '../../../../core/localization/app_language.dart';
import '../../../../core/localization/app_localizations.dart';
import '../../../controllers/auth_controller.dart';
import '../../../controllers/currency_controller.dart';
import '../../car_rent_details/car_rent_details.dart';

class CarRentalFeaturedVideo extends StatelessWidget {
  final List<VideoModel> featuredvideo;
  final int? id;
  final String? date;
  final String? time;
  final CurrencyController currencyController;

  const CarRentalFeaturedVideo({
    Key? key,
    required this.featuredvideo,
    required this.id,
    required this.date,
    required this.time,
    required this.currencyController,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    AuthController authController = AuthController();
    var lang =
        Provider.of<AppLanguage>(context, listen: false).appLocal.languageCode;

    final isEnglish = lang == 'en';

    return SizedBox(
        height: 230,
        width: MediaQuery.of(context).size.width,
        child: ListView.builder(
          shrinkWrap: true,
          padding: const EdgeInsets.symmetric(horizontal: 20),
          physics: const ClampingScrollPhysics(),
          scrollDirection: Axis.horizontal,
          itemBuilder: (BuildContext ctxt, int index) {
            Widget titleWithPriceWidget() {
              return Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  featuredvideo[index].name != null
                      ? SizedBox(
                          width: MediaQuery.of(context).size.width * 0.3,
                          child: Text(
                            featuredvideo[index].name!,
                            style: const TextStyle(
                                color: Colors.white,
                                fontWeight: FontWeight.w700,
                                fontSize: 12),
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                          ),
                        )
                      : const SizedBox(),
                  const SizedBox(height: 5),
                  featuredvideo[index].startprice != null
                      ? SizedBox(
                          width: MediaQuery.of(context).size.width * 0.3,
                          child: Text(
                            AppLocalizations.of(context)
                                    .translate('Day Price') +
                                " " +
                                featuredvideo[index]
                                    .startprice!
                                    .toStringAsFixed(0) +
                                ' ' +
                                currencyController.currency,
                            style: const TextStyle(
                                color: Colors.white, fontSize: 12),
                          ),
                        )
                      : const SizedBox()
                ],
              );
            }

            return GestureDetector(
                onTap: () {
                  Navigator.of(context).push(MaterialPageRoute(
                      builder: (BuildContext context) =>
                          CarRentDetailsVideoWidget(
                            video: featuredvideo[index],
                          )));
                },
                child: Padding(
                    padding: const EdgeInsets.only(right: 10),
                    child: Stack(children: [
                      ClipRRect(
                        borderRadius: BorderRadius.circular(5),
                        child: featuredvideo[index].photo != null
                            ? MainCachedImage(
                                featuredvideo[index].photo!,
                                height: 230,
                                width: MediaQuery.of(context).size.width * 0.35,
                                fit: BoxFit.fitHeight,
                              )
                            : const SizedBox(),
                      ),
                      InkWell(
                          onTap: () {
                            Navigator.of(context).push(MaterialPageRoute(
                                builder: (BuildContext context) =>
                                    CarRentDetailsVideoWidget(
                                      video: featuredvideo[index],
                                    )));
                          },
                          child: Container(
                            height: 230,
                            width: MediaQuery.of(context).size.width * 0.35,
                            decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(5),
                                gradient: LinearGradient(
                                  end: Alignment.bottomCenter,
                                  begin: Alignment.center,
                                  colors: <Color>[
                                    Colors.transparent,
                                    Colors.black.withOpacity(0.7)
                                  ],
                                )),
                          )),
                      if (!isEnglish)
                        Positioned(
                            bottom: 17, right: 7, child: titleWithPriceWidget())
                      else
                        Positioned(
                            bottom: 17, left: 7, child: titleWithPriceWidget()),
                      Positioned(
                          top: 10,
                          right: 10,
                          left: 10,
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              // if (featuredvideo[index].year != null &&
                              //     featuredvideo[index].year!.isNotEmpty)
                              //   Container(
                              //       height: 20,
                              //       width: 40,
                              //       decoration: const BoxDecoration(
                              //           color: Colors.white,
                              //           borderRadius: BorderRadius.all(
                              //               Radius.circular(5))),
                              //       child: Center(
                              //           child: Text(
                              //         featuredvideo[index].year.toString(),
                              //         maxLines: 1,
                              //       )))
                              // else
                              //   const Spacer(),
                              // GestureDetector(
                              //     onTap: () {
                              //       authController.isLogged == true
                              //           ? time != null && time != ""
                              //               ? planController.additemtoplan(
                              //                   date,
                              //                   'car_rent',
                              //                   featuredvideo[index].id!,
                              //                   id!,
                              //                   context,
                              //                   time: time)
                              //               : id != null
                              //                   ? listmyplandetails(
                              //                       context,
                              //                       id!,
                              //                       "car_rent",
                              //                       featuredvideo[index].id!,
                              //                       "yes")
                              //                   : listmyplan(
                              //                       context,
                              //                       "car_rent",
                              //                       featuredvideo[index].id!)
                              //           : snackbar(AppLocalizations.of(context)
                              //               .translate('Please login first'));
                              //     },
                              //     child: Container(
                              //         height: 30,
                              //         width: 30,
                              //         decoration: const BoxDecoration(
                              //             color: Colors.grey,
                              //             borderRadius: BorderRadius.all(
                              //                 Radius.circular(30))),
                              //         child: const Center(
                              //             child: Icon(
                              //           Icons.add,
                              //           color: Colors.white,
                              //         )))),
                            ],
                          )),
                    ])));
          },
          itemCount: featuredvideo.length,
        ));
  }
}
