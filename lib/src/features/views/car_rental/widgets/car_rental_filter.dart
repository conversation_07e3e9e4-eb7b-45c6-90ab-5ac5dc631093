import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:page/src/features/controllers/content_controller.dart';
import 'package:page/src/features/controllers/currency_controller.dart';

import '../../../../core/localization/app_localizations.dart';
import '../../../models/content.dart';
import '../../../models/features.dart';
import '../car_rental.dart';

void carRentalFilter(
  BuildContext context, {
  required VoidCallback onApply,
  // required VoidCallback onReset,
  required ContentController contentController,
  required CurrencyController currencyController,
}) {
  SizedBox(
      width: 20,
      height: 20,
      child: SvgPicture.asset(
        'assets/icons8-calendar.svg',
        semanticsLabel: 'Acme Logo',
        fit: BoxFit.cover,
      ));
  SizedBox(
      width: 23,
      height: 23,
      child: SvgPicture.asset(
        'assets/Rectangle 1067.svg',
        semanticsLabel: 'Acme Logo',
        fit: BoxFit.cover,
      ));
  showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) =>
          StatefulBuilder(builder: (context, StateSetter stateSetter) {
            return Padding(
                padding: EdgeInsets.only(
                    bottom: MediaQuery.of(context).viewInsets.bottom),
                child: Container(
                  height: MediaQuery.of(context).size.height * 0.90,
                  decoration: BoxDecoration(
                      color: const Color(0xffF5F6F7),
                      borderRadius: const BorderRadius.only(
                          topLeft: Radius.circular(25.0),
                          topRight: Radius.circular(25.0)),
                      border: Border.all(color: Colors.black, width: 1.0)),
                  child: SingleChildScrollView(
                      child: Column(
                    children: [
                      const SizedBox(
                        height: 10,
                      ),
                      Container(
                          height: 5, width: 30, color: const Color(0xffD2D4D6)),
                      const SizedBox(
                        height: 20,
                      ),
                      Padding(
                          padding: const EdgeInsets.only(left: 20, right: 20),
                          child: Stack(
                            children: [
                              Align(
                                alignment:
                                    AppLocalizations.of(context).locale ==
                                            const Locale('ar')
                                        ? Alignment.centerLeft
                                        : Alignment.centerRight,
                                child: GestureDetector(
                                    onTap: () {
                                      stateSetter(() {
                                        carRentalCurrentvalue2 = null;
                                        carRentalCurrentvalue3 = null;
                                        carRentalCurrentvalue4 = null;
                                        carRentalCurrentvalue5 = null;
                                        carRentalCurrentvalue6 = null;
                                        carRentalCurrentvalue7 = null;
                                      });
                                    },
                                    child: Text(
                                      AppLocalizations.of(context)
                                          .translate('reset'),
                                      style: const TextStyle(
                                          color: Color(0xff51565B)),
                                    )),
                              ),
                              Center(
                                child: Text(
                                  AppLocalizations.of(context)
                                      .translate('filter'),
                                  style: const TextStyle(
                                      fontWeight: FontWeight.bold),
                                  textAlign: TextAlign.center,
                                ),
                              )
                            ],
                          )),
                      Container(
                        padding: const EdgeInsets.all(15),
                        child: Container(
                          decoration: BoxDecoration(
                              color: Colors.white,
                              borderRadius: BorderRadius.circular(10)),
                          child: Container(
                            padding: const EdgeInsets.all(15),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  AppLocalizations.of(context)
                                      .translate('brand'),
                                  style: const TextStyle(fontSize: 13),
                                ),
                                const SizedBox(
                                  height: 10,
                                ),
                                ContentController.brands.isNotEmpty
                                    ? Container(
                                        // width: 120,
                                        height: 50,
                                        decoration: BoxDecoration(
                                          borderRadius:
                                              BorderRadius.circular(5),
                                          border: Border.all(
                                              color: Colors.black12,
                                              width: 1.0),
                                        ),
                                        child: DropdownButton<int>(
                                            isExpanded: true,
                                            hint: Padding(
                                              padding: const EdgeInsets.only(
                                                  left: 20, right: 20),
                                              child: Text(
                                                AppLocalizations.of(context)
                                                    .translate('Choose Brand'),
                                                style: const TextStyle(
                                                    color: Color(0xffB7B7B7)),
                                              ),
                                            ),
                                            value: carRentalCurrentvalue2,
                                            underline: const SizedBox(),
                                            iconEnabledColor: Colors.black,
                                            items: ContentController.brands
                                                .map((Brands value) {
                                              return DropdownMenuItem<int>(
                                                value: value.id,
                                                child: Container(
                                                    padding:
                                                        const EdgeInsets.only(
                                                            left: 10,
                                                            right: 10),
                                                    child: Text(
                                                      value.name!,
                                                      style: const TextStyle(
                                                          fontSize: 16),
                                                    )),
                                              );
                                            }).toList(),
                                            onChanged: (value) {
                                              stateSetter(() {
                                                carRentalCurrentvalue2 = value;
                                              });
                                            }))
                                    : Center(
                                        child: Text(AppLocalizations.of(context)
                                            .translate('no brands to show')),
                                      ),
                                const SizedBox(height: 20),
                                Text(
                                  AppLocalizations.of(context)
                                      .translate('type'),
                                  style: const TextStyle(fontSize: 13),
                                ),
                                const SizedBox(
                                  height: 10,
                                ),
                                ContentController.types.isNotEmpty
                                    ? Container(
                                        child: Container(
                                            // width: 120,
                                            height: 50,
                                            decoration: BoxDecoration(
                                              borderRadius:
                                                  BorderRadius.circular(5),
                                              border: Border.all(
                                                  color: Colors.black12,
                                                  width: 1.0),
                                            ),
                                            child: DropdownButton<int>(
                                                isExpanded: true,
                                                hint: Padding(
                                                  padding:
                                                      const EdgeInsets.only(
                                                          left: 20, right: 20),
                                                  child: Text(
                                                    AppLocalizations.of(context)
                                                        .translate('type'),
                                                    style: const TextStyle(
                                                        color:
                                                            Color(0xffB7B7B7)),
                                                  ),
                                                ),
                                                value: carRentalCurrentvalue3,
                                                underline: const SizedBox(),
                                                iconEnabledColor: Colors.black,
                                                items: ContentController.types
                                                    .map((Types value) {
                                                  return DropdownMenuItem<int>(
                                                    value: value.id,
                                                    child: Container(
                                                        padding:
                                                            const EdgeInsets
                                                                .only(
                                                                left: 10,
                                                                right: 10),
                                                        child: Text(
                                                          value.name!,
                                                          style:
                                                              const TextStyle(
                                                                  fontSize: 16),
                                                        )),
                                                  );
                                                }).toList(),
                                                onChanged: (value) {
                                                  stateSetter(() {
                                                    carRentalCurrentvalue3 =
                                                        value;
                                                  });
                                                })))
                                    : Center(
                                        child: Text(AppLocalizations.of(context)
                                            .translate('No types to show'))),
                                const SizedBox(height: 20),
                                Text(
                                  AppLocalizations.of(context)
                                      .translate('Year'),
                                  style: const TextStyle(fontSize: 13),
                                ),
                                const SizedBox(
                                  height: 10,
                                ),
                                ContentController.years.isNotEmpty
                                    ? Container(
                                        child: Container(
                                            // width: 120,
                                            height: 50,
                                            decoration: BoxDecoration(
                                              borderRadius:
                                                  BorderRadius.circular(5),
                                              border: Border.all(
                                                  color: Colors.black12,
                                                  width: 1.0),
                                            ),
                                            child: DropdownButton<int>(
                                                isExpanded: true,
                                                hint: Padding(
                                                  padding:
                                                      const EdgeInsets.only(
                                                          left: 20, right: 20),
                                                  child: Text(
                                                    AppLocalizations.of(context)
                                                        .translate(
                                                            'Choose Year'),
                                                    style: const TextStyle(
                                                        color:
                                                            Color(0xffB7B7B7)),
                                                  ),
                                                ),
                                                value: carRentalCurrentvalue4,
                                                underline: const SizedBox(),
                                                iconEnabledColor: Colors.black,
                                                items: ContentController.years
                                                    .map((Years value) {
                                                  return DropdownMenuItem<int>(
                                                    value: value.id,
                                                    child: Container(
                                                        padding:
                                                            const EdgeInsets
                                                                .only(
                                                                left: 10,
                                                                right: 10),
                                                        child: Text(
                                                          value.year.toString(),
                                                          style:
                                                              const TextStyle(
                                                                  fontSize: 16),
                                                        )),
                                                  );
                                                }).toList(),
                                                onChanged: (value) {
                                                  stateSetter(() {
                                                    carRentalCurrentvalue4 =
                                                        value;
                                                  });
                                                })))
                                    : Center(
                                        child: Text(AppLocalizations.of(context)
                                            .translate('no years to show'))),
                                const SizedBox(height: 20),
                                Text(
                                  AppLocalizations.of(context)
                                      .translate('Feature'),
                                  style: const TextStyle(fontSize: 13),
                                ),
                                const SizedBox(
                                  height: 10,
                                ),
                                if (ContentController.features.isNotEmpty)
                                  Container(
                                      // width: 120,
                                      height: 50,
                                      decoration: BoxDecoration(
                                        borderRadius: BorderRadius.circular(5),
                                        border: Border.all(
                                            color: Colors.black12, width: 1.0),
                                      ),
                                      child: DropdownButton<int>(
                                          isExpanded: true,
                                          hint: Padding(
                                            padding: const EdgeInsets.only(
                                                left: 20, right: 20),
                                            child: Text(
                                              AppLocalizations.of(context)
                                                  .translate('Feature'),
                                              style: const TextStyle(
                                                  color: Color(0xffB7B7B7)),
                                            ),
                                          ),
                                          value: carRentalCurrentvalue5,
                                          underline: const SizedBox(),
                                          iconEnabledColor: Colors.black,
                                          items: ContentController.features
                                              .map((Features value) {
                                            return DropdownMenuItem<int>(
                                              value: value.id,
                                              child: Container(
                                                  padding:
                                                      const EdgeInsets.only(
                                                          left: 10, right: 10),
                                                  child: Text(
                                                    value.name ?? '',
                                                    style: const TextStyle(
                                                        fontSize: 16),
                                                  )),
                                            );
                                          }).toList(),
                                          onChanged: (value) {
                                            stateSetter(() {
                                              carRentalCurrentvalue5 = value;
                                            });
                                          }))
                                else
                                  Center(
                                      child: Text(AppLocalizations.of(context)
                                          .translate('No features to show'))),
                                const SizedBox(
                                  height: 20,
                                ),
                                Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    Text(
                                      AppLocalizations.of(context)
                                          .translate('Day Price'),
                                      style: const TextStyle(fontSize: 13),
                                    ),
                                    Row(
                                      children: [
                                        Text('${currencyController.currency} -',
                                            style: const TextStyle(
                                                fontSize: 13,
                                                color: Color(0xff51565B))),
                                        contentController.maxprice != null
                                            ? Text(
                                                contentController.maxprice
                                                    .toString(),
                                                style: const TextStyle(
                                                    fontSize: 13,
                                                    color: Color(0xff51565B)))
                                            : Container(),
                                      ],
                                    ),
                                  ],
                                ),
                                StatefulBuilder(builder:
                                    (context, StateSetter stateSetter) {
                                  return RangeSlider(
                                    activeColor: const Color(0xFFE2CBA2),
                                    inactiveColor: const Color(0xFF485E77)
                                        .withOpacity(0.9),
                                    values: carRentalCurrentRangeValues!,
                                    min: 0,
                                    max: maxprice,
                                    divisions: 50,
                                    labels: RangeLabels(
                                      carRentalCurrentRangeValues!.start
                                          .round()
                                          .toString(),
                                      carRentalCurrentRangeValues!.end
                                          .round()
                                          .toString(),
                                    ),
                                    onChanged: (RangeValues values) {
                                      stateSetter(() {
                                        carRentalCurrentRangeValues = values;
                                      });
                                    },
                                  );
                                }),
                                const SizedBox(height: 20),
                                Text(
                                  AppLocalizations.of(context)
                                      .translate('Agent Name'),
                                  style: const TextStyle(fontSize: 13),
                                ),
                                const SizedBox(
                                  height: 10,
                                ),
                                ContentController.agents.isNotEmpty
                                    ? Container(
                                        child: Container(
                                            // width: 120,
                                            height: 50,
                                            decoration: BoxDecoration(
                                              borderRadius:
                                                  BorderRadius.circular(5),
                                              border: Border.all(
                                                  color: Colors.black12,
                                                  width: 1.0),
                                            ),
                                            child: DropdownButton<int>(
                                                isExpanded: true,
                                                hint: Padding(
                                                  padding:
                                                      const EdgeInsets.only(
                                                          left: 20, right: 20),
                                                  child: Text(
                                                    AppLocalizations.of(context)
                                                        .translate(
                                                            'Choose Agent'),
                                                    style: const TextStyle(
                                                        color:
                                                            Color(0xffB7B7B7)),
                                                  ),
                                                ),
                                                value: carRentalCurrentvalue6,
                                                underline: const SizedBox(),
                                                iconEnabledColor: Colors.black,
                                                items: ContentController.agents
                                                    .map((Agents value) {
                                                  return DropdownMenuItem<int>(
                                                    value: value.id,
                                                    child: Container(
                                                        padding:
                                                            const EdgeInsets
                                                                .only(
                                                                left: 10,
                                                                right: 10),
                                                        child: Text(
                                                          value.name!,
                                                          style:
                                                              const TextStyle(
                                                                  fontSize: 16),
                                                        )),
                                                  );
                                                }).toList(),
                                                onChanged: (value) {
                                                  stateSetter(() {
                                                    carRentalCurrentvalue6 =
                                                        value;
                                                  });
                                                })))
                                    : Center(
                                        child: Text(AppLocalizations.of(context)
                                            .translate('no agents to show'))),
                                const SizedBox(
                                  height: 20,
                                ),
                                Center(
                                    child: Container(
                                        // padding: EdgeInsets.only(right: 20, left: 20),
                                        child: GestureDetector(
                                            onTap: onApply,
                                            child: Container(
                                              height: 50,
                                              width: MediaQuery.of(context)
                                                  .size
                                                  .width,
                                              decoration: BoxDecoration(
                                                  color:
                                                      const Color(0xFF27b4a8),
                                                  borderRadius:
                                                      BorderRadius.circular(
                                                          10)),
                                              child: Container(
                                                  padding:
                                                      const EdgeInsets.all(10),
                                                  child: Center(
                                                      child: Text(
                                                    AppLocalizations.of(context)
                                                        .translate(
                                                            'Apply Filter'),
                                                    style: const TextStyle(
                                                        color: Colors.white),
                                                  ))),
                                            )))),
                                const SizedBox(height: 20),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ],
                  )),
                ));
          }));
}
