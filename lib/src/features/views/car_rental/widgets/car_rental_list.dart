import 'package:flutter/material.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:page/src/features/models/video_model.dart';
import 'package:provider/provider.dart';

import '../../../../core/localization/app_language.dart';
import '../../../../core/localization/app_localizations.dart';
import '../../car_rent_details/car_rent_details.dart';

class CarRentalList extends StatelessWidget {
  final List<VideoModel> results;
  final authController;
  final date;
  final time;
  final id;
  final currencyController;

  const CarRentalList(
    this.results,
    this.authController,
    this.date,
    this.time,
    this.id,
    this.currencyController, {
    Key? key,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    var lang =
        Provider.of<AppLanguage>(context, listen: false).appLocal.languageCode;

    final isEnglish = lang == 'en';

    return StaggeredGridView.countBuilder(
        staggeredTileBuilder: (index) {
          return const StaggeredTile.fit(1);
        },
        shrinkWrap: true,
        padding: const EdgeInsets.symmetric(horizontal: 10.0, vertical: 20.0),
        crossAxisCount: 2,
        mainAxisSpacing: 8,
        itemCount: results.length,
        primary: false,
        itemBuilder: (context, index) {
          Widget titleWithPriceWidget() {
            return Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                results[index].name != null
                    ? SizedBox(
                        width: MediaQuery.of(context).size.width * 0.3,
                        child: Text(
                          results[index].name!,
                          style: const TextStyle(
                              color: Colors.white,
                              fontWeight: FontWeight.w700,
                              fontSize: 12),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                      )
                    : Container(),
                const SizedBox(height: 5),
                Text(
                  '${AppLocalizations.of(context).translate('Day Price')} ${results[index].startprice?.toStringAsFixed(0)} ${currencyController.currency}',
                  style: const TextStyle(color: Colors.white, fontSize: 12),
                )
              ],
            );
          }

          return Column(children: [
            const SizedBox(
              width: 5,
            ),
            Stack(children: [
              ClipRRect(
                borderRadius: BorderRadius.circular(5),
                child: Image.network(
                  results[index].images!,
                  height: 288,
                  width: MediaQuery.of(context).size.width * 0.45,
                  fit: BoxFit.fitHeight,
                ),
              ),
              InkWell(
                  onTap: () {
                    Navigator.of(context).push(MaterialPageRoute(
                        builder: (BuildContext context) =>
                            CarRentDetailsVideoWidget(
                              video: results[index],
                            )));
                  },
                  child: Container(
                    height: 288,
                    width: MediaQuery.of(context).size.width * 0.45,
                    decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(5),
                        gradient: LinearGradient(
                          end: Alignment.bottomCenter,
                          begin: Alignment.center,
                          colors: <Color>[
                            Colors.transparent,
                            //  Colors.white.withOpacity(0.5),
                            Colors.black.withOpacity(0.7)
                          ],
                        )),
                  )),
              if (!isEnglish)
                Positioned(bottom: 12, right: 10, child: titleWithPriceWidget())
              else
                Positioned(bottom: 12, left: 10, child: titleWithPriceWidget()),
              Positioned(
                  top: 10,
                  right: 10,
                  left: 10,
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      if (results[index].year != null &&
                          results[index].year!.isNotEmpty)
                        Container(
                            height: 20,
                            width: 40,
                            decoration: const BoxDecoration(
                                color: Colors.white,
                                borderRadius:
                                    BorderRadius.all(Radius.circular(5))),
                            child: Center(
                                child: Text(results[index].year.toString())))
                      else
                        const Spacer(),
                      // GestureDetector(
                      //     onTap: () {
                      //       authController.isLogged == true
                      //           ? time != null && time != ""
                      //               ? planController.additemtoplan(
                      //                   date,
                      //                   'car_rent',
                      //                   results[index].id!,
                      //                   id!,
                      //                   context,
                      //                   time: time)
                      //               : id != null
                      //                   ? listmyplandetails(
                      //                       context,
                      //                       id!,
                      //                       "car_rent",
                      //                       results[index].id!,
                      //                       "yes")
                      //                   : listmyplan(context, "car_rent",
                      //                       results[index].id!)
                      //           : snackbar(AppLocalizations.of(context)
                      //               .translate('Please login first'));
                      //     },
                      //     child: Container(
                      //         height: 30,
                      //         width: 30,
                      //         decoration: const BoxDecoration(
                      //             color: Colors.grey,
                      //             borderRadius:
                      //                 BorderRadius.all(Radius.circular(30))),
                      //         child: const Center(
                      //             child: Icon(
                      //           Icons.add,
                      //           color: Colors.white,
                      //         )))),
                    ],
                  )),
            ])
          ]);
        });
  }
}
