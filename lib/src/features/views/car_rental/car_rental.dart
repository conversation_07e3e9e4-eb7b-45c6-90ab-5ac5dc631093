import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:intl/intl.dart';
import 'package:page/src/core/config/constants.dart';
import 'package:page/src/core/shared_widgets/main_featured_container.dart';
import 'package:page/src/features/views/car_rental/widgets/car_rental_featured_video.dart';
import 'package:page/src/features/views/car_rental/widgets/car_rental_filter.dart';
import 'package:page/src/features/views/car_rental/widgets/car_rental_list.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

import '../../../core/localization/app_localizations.dart';
import '../../../core/services/api.dart';
import '../../../core/shared_widgets/shared_widgets.dart';
import '../../controllers/auth_controller.dart';
import '../../controllers/content_controller.dart';
import '../../controllers/currency_controller.dart';
import '../../models/video_model.dart';

int? carRentalCurrentvalue2;
int? carRentalCurrentvalue3;
int? carRentalCurrentvalue4;
int? carRentalCurrentvalue5;
int? carRentalCurrentvalue6;
int? carRentalCurrentvalue7;
double minprice = 0;
double maxprice = 5000;
RangeValues? carRentalCurrentRangeValues;

// ignore: must_be_immutable
class CarRental extends StatefulWidget {
  final int? id;
  final time;
  final int? itemid;
  final date;

  const CarRental({super.key, this.id, this.time, this.itemid, this.date});

  @override
  _CarRental createState() => _CarRental();
}

class _CarRental extends State<CarRental> {
  ContentController contentController = ContentController();

  AuthController authController = AuthController();
  CurrencyController currencyController = CurrencyController();
  TextEditingController searchController = TextEditingController();

  final format2 = DateFormat("dd/MM/yyyy");
  TextEditingController startdateController = TextEditingController();
  TextEditingController enddateController = TextEditingController();
  List<VideoModel> results = [];
  List<VideoModel> featuredvideo = [];
  int pagenumber = 1;
  int code2 = 0;
  String msg2 = 'loading';

  @override
  void initState() {
    super.initState();
    authController.isloggedin();
    contentController.getfeatures(AppConstants.carRentalsId.toString());
    contentController.gettypes(AppConstants.carRentalsId.toString());
    contentController.getmaxpricecarrent();
    contentController.getagentsbycategory(AppConstants.carRentalsId.toString());
    contentController.getbrands();
    contentController.getyears();

    currencyController.getcuurentcurrency(context);
    getcategory(pagenumber, "");
  }

  filtercarrent(int brand, var year, double price, int feature, int type,
      int agent) async {
    results.clear();
    await Api.filtercarrent(brand, year, price, feature, type, agent)
        .then((value) {
      value != null
          ? setState(() {
              results.addAll(value.category);
            })
          : null;
    });
  }

  void clearResultsAndFeaturedVideos() {
    setState(() {
      results.clear();
      if (searchController.text.isEmpty) {
        featuredvideo.clear();
      }
    });
  }

  void _onLoading() async {
    pagenumber = pagenumber + 1;
    getcategory(pagenumber, "");
    _refreshControllerwait.loadComplete();
  }

  final RefreshController _refreshControllerwait =
      RefreshController(initialRefresh: false);

  void _onRefresh() async {
    await Future.delayed(const Duration(milliseconds: 1000));
    results.clear();
    featuredvideo.clear();
    pagenumber = 1;

    searchController.clear();

    await getcategory(pagenumber, "");

    _refreshControllerwait.refreshCompleted();

    setState(() {});
  }

  void onSearchSubmitted(String text) async {
    clearResultsAndFeaturedVideos();
    if (text.isEmpty) {
      await getcategory(
        1,
        "",
      );
    } else {
      pagenumber = 1;
      await getcategory(
        1,
        text,
      );
    }

    setState(() {});
  }

  getcategory(int page, String key) async {
    await Api.getmainCategory(
            page, 10, key, AppConstants.carRentalsId.toString())
        .then((value) {
      setState(() {
        code2 = value.code;
        msg2 = value.error;
        results.addAll(value.category);
        if (searchController.text.isEmpty) {
          featuredvideo..addAll(value.featuredvideo);
        }
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    final Widget svg2 = SizedBox(
        width: 20,
        height: 20,
        child: SvgPicture.asset(
          'assets/filter.svg',
          semanticsLabel: 'Acme Logo',
          fit: BoxFit.cover,
        ));

    return SafeArea(
        child: Scaffold(
      appBar: AppBar(
        backgroundColor: const Color(0xFF27b4a8),
        title: Text(AppLocalizations.of(context).translate('carrental')),
        centerTitle: true,
      ),
      body: SmartRefresher(
        controller: _refreshControllerwait,
        enablePullDown: true,
        enablePullUp: true,
        header: ClassicHeader(
          refreshingIcon: buildLoadingWidget(),
        ),
        footer: CustomFooter(
          builder: (BuildContext context, LoadStatus? mode) {
            Widget body;
            if (mode == LoadStatus.loading) {
              body = const CupertinoActivityIndicator();
            } else if (mode == LoadStatus.failed) {
              body = const Text("Load Failed!Click retry!",
                  style: TextStyle(color: Color(0xff233549)));
            } else if (mode == LoadStatus.canLoading) {
              body = const Text("release to load more",
                  style: TextStyle(color: Color(0xff233549)));
            } else {
              body = const Text("No more Data",
                  style: TextStyle(color: Color(0xff233549)));
            }
            return Center(child: body);
          },
        ),
        onRefresh: _onRefresh,
        onLoading: _onLoading,
        child: SingleChildScrollView(
          child:
              Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                code2 == 0 && msg2 == 'loading'
                    ? buildLoadingWidget()
                    : code2 == 1
                        ? featuredvideo.isNotEmpty
                            ? MainFeaturedContainer(
                                label: 'featuredCarRentals',
                                child: CarRentalFeaturedVideo(
                                    featuredvideo: featuredvideo,
                                    id: widget.id,
                                    date: widget.date,
                                    time: widget.time,
                                    currencyController: currencyController),
                              )
                            : nodatafound(AppLocalizations.of(context)
                                .translate('No featured videos to show'))
                        : buildErrorWidget(msg2),
              ],
            ),
            const SizedBox(
              height: 20,
            ),
            Padding(
                padding: const EdgeInsets.only(left: 20, right: 20),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      AppLocalizations.of(context).translate('Allrentalcars'),
                      style: const TextStyle(
                          color: Color(0xff51565B),
                          fontWeight: FontWeight.bold),
                    ),
                    GestureDetector(
                      onTap: () {
                        contentController.maxprice != null
                            ? maxprice = contentController.maxprice.toDouble()
                            : 0;

                        carRentalCurrentRangeValues =
                            RangeValues(minprice, maxprice);
                        carRentalFilter(
                          context,
                          onApply: () async {
                            filtercarrent(
                                carRentalCurrentvalue2 != null
                                    ? carRentalCurrentvalue2!
                                    : 0,
                                carRentalCurrentvalue4 ?? 0,
                                carRentalCurrentRangeValues!.end,
                                carRentalCurrentvalue5 != null
                                    ? carRentalCurrentvalue5!
                                    : 0,
                                carRentalCurrentvalue3 != null
                                    ? carRentalCurrentvalue3!
                                    : 0,
                                carRentalCurrentvalue6 != null
                                    ? carRentalCurrentvalue6!
                                    : 0);
                            Navigator.pop(context);
                          },
                          contentController: contentController,
                          currencyController: currencyController,
                        );
                      },
                      child: svg2,
                    )
                  ],
                )),
            const SizedBox(
              height: 10,
            ),
            Padding(
                padding: const EdgeInsets.only(left: 20, right: 20),
                child: Container(
                    height: 40,
                    decoration: BoxDecoration(
                        color: const Color(0xffF1F1F1),
                        borderRadius: BorderRadius.circular(5)),
                    child: TextField(
                      onSubmitted: onSearchSubmitted,
                      controller: searchController,
                      decoration: InputDecoration(
                          prefixIcon: const Icon(
                            Icons.search,
                            color: Color(0xff8B959E),
                          ),
                          contentPadding: const EdgeInsets.only(
                              left: 20, right: 20, top: 5),
                          hintText: AppLocalizations.of(context)
                              .translate('Search places and locations'),
                          hintStyle: const TextStyle(
                              color: Color(0xff8B959E), fontSize: 13),
                          border: InputBorder.none),
                    ))),
            const SizedBox(
              height: 20,
            ),
            code2 == 0 && msg2 == 'loading'
                ? buildLoadingWidget()
                : code2 == 1
                    ? results.isNotEmpty
                        ? CarRentalList(results, authController, widget.date,
                            widget.time, widget.id, currencyController)
                        : nodatafound(AppLocalizations.of(context)
                            .translate('No car rental to show'))
                    : buildErrorWidget(msg2),
            const SizedBox(
              height: 20,
            )
          ]),
        ),
      ),
    ));
  }
}
