import 'dart:developer';

import 'package:country_pickers/country.dart';
import 'package:country_pickers/utils/utils.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:page/src/features/bloc/auth_blok.dart';
import 'package:page/src/features/controllers/auth_controller.dart';

import '../../../core/localization/app_localizations.dart';
import '../../../core/shared_widgets/shared_widgets.dart';
import '../../repository/notification_controller.dart';
import 'widgets/register_buttons.dart';
import 'widgets/register_fields.dart';
import 'widgets/verify_code/verfiy_code.dart';

class FirstRegister extends StatefulWidget {
  @override
  _FirstRegister createState() => _FirstRegister();
}

bool isLoadingRegister = false;

class _FirstRegister extends State<FirstRegister> {
  @override
  void initState() {
    isLoadingRegister = false;
    FirebaseMessaging _firebaseMessaging = FirebaseMessaging.instance;
    _firebaseMessaging.getToken().then((value) {
      setState(() {
        token = value;
      });
      print("dfffd");
      print(value);
    });
    super.initState();
  }

  bool _isChecked = false;
  final GlobalKey<ScaffoldState> scaffoldKey = GlobalKey<ScaffoldState>();
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();

  String? token;
  TextEditingController passwordController = TextEditingController();
  TextEditingController emailController = TextEditingController();
  TextEditingController nameController = TextEditingController();
  TextEditingController phoneController = TextEditingController();
  NotificationController? notificationController;

  // Country _selectedCountry = CountryPickerUtils.getCountryByIsoCode('EG');
  Country _selectedCountry = CountryPickerUtils.getCountryByIsoCode('AE');

  @override
  Widget build(BuildContext context) {
    return SafeArea(
        child: Scaffold(
            body: Container(
                padding: const EdgeInsets.only(left: 20, right: 20),
                child: SingleChildScrollView(
                    child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          GestureDetector(
                            onTap: () {
                              Navigator.pop(context);
                            },
                            child: Container(
                              padding:
                                  const EdgeInsets.only(bottom: 20, top: 20),
                              child: const Icon(Icons.arrow_back),
                            ),
                          ),
                          Text(
                            AppLocalizations.of(context)
                                .translate('Registration'),
                            style: const TextStyle(
                                fontSize: 20, fontWeight: FontWeight.bold),
                          ),
                          Container(
                            height: 10,
                            color: Colors.transparent,
                          )
                        ],
                      ),
                      const SizedBox(
                        height: 30,
                      ),
                      Form(
                          key: _formKey,
                          child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                //! Register Fields
                                RegisterFields(
                                  onCountryChanged: (Country country) {
                                    setState(() {
                                      _selectedCountry = country;
                                    });
                                  },
                                  selectedCountry: _selectedCountry,
                                  emailController: emailController,
                                  passwordController: passwordController,
                                  nameController: nameController,
                                  phoneController: phoneController,
                                ),

                                20.verticalSpace,

                                //! Register Button
                                RegisterButtons(
                                  onRegisterPressed: () {
                                    submit(
                                        emailController.text,
                                        passwordController.text,
                                        nameController.text,
                                        phoneController.text,
                                        '+${_selectedCountry.phoneCode.toString()}');
                                  },
                                  isChecked: _isChecked,
                                  onCheckboxChanged: (bool? isChecked) {
                                    setState(() {
                                      _isChecked = isChecked!;
                                      print(_isChecked);
                                    });
                                  },
                                ),
                              ])),
                      20.verticalSpace,
                    ])))));
  }

  void submit(String email, String password, String name, String phone,
      String phonecode) async {
    if (emailController.text.isEmpty) {
      snackbar(AppLocalizations.of(context)
          .translate('Please enter the email field'));

      return;
    }

    emailController.text.trim();
    bool emailValid = RegExp(r'^.+@[a-zA-Z]+\.{1}[a-zA-Z]+(\.{0,1}[a-zA-Z]+)$')
        .hasMatch(emailController.text.trim());

    if (emailValid == false) {
      snackbar(
          AppLocalizations.of(context).translate('Please enter a vaild Email'));
      return;
    }
    if (phoneController.text.isEmpty) {
      snackbar(AppLocalizations.of(context)
          .translate('Please enter the  phone field'));

      return;
    }
    // if (phoneController.text.length < 9) {
    //   snackbar(AppLocalizations.of(context)
    //       .translate('Phone must be 9 number or more'));
    //
    //   return;
    // }
    if (passwordController.text.isEmpty) {
      snackbar(AppLocalizations.of(context)
          .translate('Please enter the password field'));

      return;
    }
    if (passwordController.text.length < 6) {
      snackbar(AppLocalizations.of(context)
          .translate('The password must be six characters or more'));

      return;
    }

    if (_isChecked == false) {
      snackbar(AppLocalizations.of(context)
          .translate('Please agree to the terms and conditions'));

      return;
    } else {
      setState(() {
        isLoadingRegister = true;
      });

      try {
        final Map<String, dynamic> successInformation = await bloc.register(
            phone, password, name, email, token!, phonecode);

        log('PhoneIs $phonecode$phone');

        if (successInformation['code'] == 1) {
          await AuthController().sendOtp(phoneNumber: '$phonecode$phone');

          Navigator.of(context).push(MaterialPageRoute(
              builder: (context) => VerfiyCode(phonecode, phone, name)));
          // (Route<dynamic> route) => false);
        } else {
          print('adksndklsnd ${successInformation}');

          if (successInformation['email_msg'] != null) {
            snackbar(AppLocalizations.of(context)
                .translate('The email has already been taken'));
          } else if (successInformation['phone_msg'] != null) {
            snackbar(AppLocalizations.of(context)
                .translate('The phone has already been taken'));
          } else {
            if (successInformation['msg'] == null) {
              snackbar(AppLocalizations.of(context)
                  .translate('Something went wrong, please try again later'));
            } else {
              snackbar(successInformation['msg']);
            }
          }
        }
      } catch (error) {
        print(error);
        snackbar(AppLocalizations.of(context)
            .translate('Something went wrong, please try again later'));
      }
    }

    setState(() {
      isLoadingRegister = false;
    });
  }
}
