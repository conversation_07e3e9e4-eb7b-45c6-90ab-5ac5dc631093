import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:lottie/lottie.dart';

import '../../../../core/localization/app_localizations.dart';
import '../../login/login.dart';
import '../../terms_and_conditions/terms_condtion.dart';
import '../register.dart';

class RegisterButtons extends StatelessWidget {
  final bool isChecked;
  final Function(bool?) onCheckboxChanged;
  final Function() onRegisterPressed;

  const RegisterButtons({
    Key? key,
    required this.isChecked,
    required this.onCheckboxChanged,
    required this.onRegisterPressed,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final Widget svg2 = SizedBox(
        width: 30,
        height: 10,
        child: SvgPicture.asset(
          'assets/right-arrow.svg',
          semanticsLabel: 'Acme Logo',
          // fit: BoxFit.cover,
        ));
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: <Widget>[
        Row(
          children: [
            SizedBox(
                height: 24.0,
                width: 24.0,
                child: Checkbox(
                  value: isChecked,
                  checkColor: const Color(0xff4D63A0),
                  tristate: false,
                  activeColor: Colors.white,
                  materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                  onChanged: onCheckboxChanged,
                )),
            const SizedBox(
              width: 10,
            ),
            Row(
              children: [
                Text(
                  AppLocalizations.of(context).translate('IAgree'),
                ),
                InkWell(
                  onTap: () {
                    Navigator.of(context).push(MaterialPageRoute(
                        builder: (BuildContext context) => Termsandcondtion()));
                  },
                  child: Text(
                    AppLocalizations.of(context)
                        .translate('IAgreeToTermsandConditions'),
                    style: const TextStyle(color: Color(0xff0852AB)),
                  ),
                )
              ],
            )
          ],
        ),
        const SizedBox(
          height: 40,
        ),
        !isLoadingRegister
            ? Container(
                height: 50,
                decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(3)),
                child: GestureDetector(
                    onTap: onRegisterPressed,
                    child: Container(
                        padding: const EdgeInsets.only(left: 20, right: 20),
                        decoration: BoxDecoration(
                            color: const Color(0xFF27b4a8),
                            borderRadius: BorderRadius.circular(10),
                            border:
                                Border.all(color: Colors.black12, width: 1.0)),
                        child: Center(
                            child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              AppLocalizations.of(context)
                                  .translate('Register'),
                              style: const TextStyle(
                                  color: Colors.white, fontSize: 16),
                            ),
                            const SizedBox(
                              width: 15,
                            ),
                            svg2
                          ],
                        )))))
            : Center(
                child: Lottie.asset('assets/59218-progress-indicator.json',
                    height: 50, width: 50)),
        const SizedBox(
          height: 30,
        ),
        Center(
          child: Container(
              child: Text(
            AppLocalizations.of(context).translate('Alreadhaveanaccount'),
            style: const TextStyle(fontSize: 16),
          )),
        ),
        InkWell(
            onTap: () {
              Navigator.of(context).pushReplacement(MaterialPageRoute(
                  builder: (BuildContext context) => const Login()));
            },
            child: Center(
              child: Text(
                AppLocalizations.of(context).translate('LoginNow'),
                style: const TextStyle(fontSize: 16, color: Color(0xff4D63A0)),
              ),
            )),
      ],
    );
  }
}
