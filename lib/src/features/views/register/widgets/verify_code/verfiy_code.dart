import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:page/src/features/controllers/auth_controller.dart';
import 'package:pin_code_fields/pin_code_fields.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../../../../core/localization/app_localizations.dart';
import '../../../../../core/response/auth_response.dart';
import '../../../../../core/services/api.dart';
import '../../../../../core/shared_widgets/shared_widgets.dart';
import '../../../account/account.dart';
import 'verify_button.dart';

class VerfiyCode extends StatefulWidget {
  final String phonecode, phone, username;
  final bool isUpdateProfile;

  const VerfiyCode(this.phonecode, this.phone, this.username,
      {super.key, this.isUpdateProfile = false});

  @override
  _VerfiyCode createState() => _VerfiyCode();
}

class _VerfiyCode extends State<VerfiyCode> {
  bool isLoading = false;
  TextEditingController codeController = TextEditingController();

  @override
  void initState() {
    super.initState();
  }

  //AuthApiProvider provider = AuthApiProvider();
  @override
  Widget build(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    return SafeArea(
        child: Scaffold(
            body: Container(
                padding: const EdgeInsets.only(left: 20, right: 20),
                child: SingleChildScrollView(
                    child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          GestureDetector(
                            onTap: () {
                              Navigator.pop(context);
                            },
                            child: Container(
                              padding:
                                  const EdgeInsets.only(bottom: 20, top: 20),
                              child: const Icon(Icons.arrow_back),
                            ),
                          ),
                          const Text(
                            'Phone Verification',
                            style: TextStyle(
                                fontSize: 20, fontWeight: FontWeight.bold),
                          ),
                          Container(
                            height: 10,
                            color: Colors.transparent,
                          )
                        ],
                      ),
                      const SizedBox(
                        height: 30,
                      ),
                      Container(
                        width: width,
                        color: const Color(0xffF8F8F8),
                        padding: const EdgeInsets.all(30),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            Center(
                                child: Image.asset(
                              'assets/d89ef476-d915-410c-b3b3-9c5f543bc4f7.png',
                              width: 44,
                              height: 44,
                              fit: BoxFit.cover,
                            )),
                            Text(
                              '${widget.username}, you are one step away!',
                              style: const TextStyle(
                                  fontWeight: FontWeight.bold, fontSize: 12),
                            ),
                            const Text(
                              'we need to verify your phone number',
                              style: TextStyle(fontSize: 15),
                            ),
                            Text(
                              widget.isUpdateProfile
                                  ? 'to update your profile.'
                                  : 'to complete your registration.',
                              style: const TextStyle(fontSize: 12),
                            )
                          ],
                        ),
                      ),
                      const SizedBox(
                        height: 30,
                      ),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          const Text(
                            'Code sent to',
                            style: TextStyle(fontSize: 15),
                          ),
                          const SizedBox(
                            width: 10,
                          ),
                          Text(
                            '+${widget.phonecode.replaceAll('+', '')}${widget.phone}',
                            style: const TextStyle(
                                fontSize: 15, color: Color(0xff00579F)),
                          ),
                          const SizedBox(
                            width: 10,
                          ),
                          // Text(
                          //   '(Edit)',
                          //   style: TextStyle(
                          //       fontSize: 15, color: Color(0xff00579F)),
                          // ),
                        ],
                      ),
                      const SizedBox(
                        height: 20,
                      ),
                      Container(
                        padding: const EdgeInsets.only(left: 60, right: 60),
                        height: 50,
                        child: Directionality(
                          textDirection: TextDirection.ltr,
                          child: PinCodeTextField(
                            keyboardType: TextInputType.number,
                            autoFocus: true,
                            length: 6,
                            obscureText: false,
                            animationType: AnimationType.fade,
                            pinTheme: PinTheme(
                                shape: PinCodeFieldShape.box,
                                activeFillColor: const Color(0xffF1F1F1),
                                activeColor: const Color(0xffF1F1F1),
                                inactiveColor: const Color(0xffF1F1F1),
                                inactiveFillColor: const Color(0xffF1F1F1),
                                selectedFillColor: Colors.white,
                                selectedColor: Colors.white,
                                fieldHeight: 50,
                                fieldWidth: 35,
                                // disabledColor:
                                //     Color(0xff009695),
                                borderWidth: 1),
                            cursorColor: const Color(0xff00579F),
                            animationDuration:
                                const Duration(milliseconds: 300),
                            enableActiveFill: true,
                            controller: codeController,
                            onCompleted: (_) => null,
                            appContext: context,
                            onChanged: (value) => () {},
                          ),
                        ),
                      ),
                      const SizedBox(
                        height: 20,
                      ),
                      //! Verify Button
                      VerifyButton(
                          isLoading: isLoading,
                          // false,
                          onTapVerify: () async {
                            if (codeController.text.isEmpty) {
                              snackbar(AppLocalizations.of(context)
                                  .translate('please insert code'));

                              return;
                            } else {
                              setState(() {
                                isLoading = true;
                              });

                              // pr.show();
                              try {
                                if (!kDebugMode) {
                                  await AuthController().verifyOTP(
                                    otp: codeController.text,
                                  );
                                }

                                AuthResponse response =
                                    await Api.verfiycoderegister(
                                  widget.phone,
                                  codeController.text,
                                );

                                if (!widget.isUpdateProfile) {
                                  SharedPreferences prefs =
                                      await SharedPreferences.getInstance();

                                  String token = response.token;
                                  prefs.setInt(
                                      'user_id', response.results['id']);
                                  prefs.setString('token', token);
                                  prefs.setBool('is_logged', true);
                                }
                                Navigator.of(context).pushAndRemoveUntil(
                                    MaterialPageRoute(
                                        builder: (context) => const Account()),
                                    (Route<dynamic> route) => false);
                              } catch (e) {
                                snackbar(AppLocalizations.of(context).translate(
                                    'Something went wrong, please try again later'));
                              }

                              // print('rfsdfdddd ${response.token}');
                              // pr.hide();
                              // if (response.code == 1) {
                              // } else {
                              //   snackbar(response.msg);
                              // }
                            }
                            setState(() {
                              isLoading = false;
                            });
                          },
                          onTapResendCode: () async {
                            // AuthResponse response = await Api.resendcode(
                            //   widget.phone,
                            // );
                            try {
                              await AuthController().resendCode(
                                phoneNumber:
                                    '${widget.phonecode}${widget.phone}',
                              );

                              snackbar2(AppLocalizations.of(context)
                                  .translate('code send successfuly'));
                            } catch (e) {
                              snackbar(AppLocalizations.of(context).translate(
                                  'Something went wrong, please try again later'));
                            }
                            // print(response);
                            // pr.hide();
                            // if (response.code == 1) {
                            //   snackbar2(AppLocalizations.of(context)
                            //       .translate('code send successfuly'));
                            // } else {
                            //   snackbar(response.msg);
                            // }
                          }),
                    ])))));
  }
}
