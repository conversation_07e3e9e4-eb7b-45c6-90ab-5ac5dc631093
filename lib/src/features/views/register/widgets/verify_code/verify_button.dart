import 'package:flutter/material.dart';
import 'package:lottie/lottie.dart';

import '../../../../../core/localization/app_localizations.dart';

class VerifyButton extends StatelessWidget {
  final bool isLoading;
  final VoidCallback onTapResendCode;
  final VoidCallback onTapVerify;

  const VerifyButton(
      {Key? key,
      required this.isLoading,
      required this.onTapResendCode,
      required this.onTapVerify})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        !isLoading
            ? Center(
                child: Container(
                    height: 50,
                    width: MediaQuery.of(context).size.width * 0.5,
                    decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(3)),
                    child: GestureDetector(
                        onTap: onTapVerify,
                        child: Container(
                            padding: const EdgeInsets.only(left: 20, right: 20),
                            decoration: BoxDecoration(
                                color: const Color(0xFF27b4a8),
                                borderRadius: BorderRadius.circular(10),
                                border: Border.all(
                                    color: Colors.black12, width: 1.0)),
                            child: Center(
                                child: Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Center(
                                    child: Text(
                                  AppLocalizations.of(context)
                                      .translate('verify'),
                                  style: const TextStyle(
                                      color: Colors.white, fontSize: 16),
                                )),
                                const SizedBox(
                                  width: 15,
                                ),
                              ],
                            ))))))
            : Center(
                child: Lottie.asset('assets/59218-progress-indicator.json',
                    height: 50, width: 50)),
        const SizedBox(
          height: 30,
        ),
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            InkWell(
                onTap: onTapResendCode,
                child: Text(
                  AppLocalizations.of(context)
                      .translate('did not receive the code'),
                  // 'Didn’t receive a code?',
                  style: TextStyle(fontSize: 12),
                )),
            const SizedBox(
              width: 10,
            ),
          ],
        ),
        const SizedBox(
          height: 10,
        ),
      ],
    );
  }
}
