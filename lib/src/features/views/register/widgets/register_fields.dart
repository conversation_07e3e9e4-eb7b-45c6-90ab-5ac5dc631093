import 'package:country_pickers/country.dart';
import 'package:flutter/material.dart';

import '../../../../core/localization/app_localizations.dart';
import '../../../../core/shared_widgets/country_picker.dart';

class RegisterFields extends StatefulWidget {
  final Function(Country country) onCountryChanged;
  final TextEditingController nameController;
  final TextEditingController emailController;
  final TextEditingController phoneController;
  final TextEditingController passwordController;
  final Country selectedCountry;

  const RegisterFields(
      {Key? key,
      required this.onCountryChanged,
      required this.nameController,
      required this.emailController,
      required this.phoneController,
      required this.passwordController,
      required this.selectedCountry})
      : super(key: key);

  @override
  State<RegisterFields> createState() => _RegisterFieldsState();
}

class _RegisterFieldsState extends State<RegisterFields> {
  bool _validatephone = false;
  bool isvisible = false;
  bool isphone = false;

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: <Widget>[
        Text(
          AppLocalizations.of(context).translate('FullName'),
          style: const TextStyle(
            fontSize: 16,
          ),
        ),
        const SizedBox(
          height: 10,
        ),
        Container(
            height: 50,
            decoration: BoxDecoration(
                color: Colors.white, borderRadius: BorderRadius.circular(3)),
            child: Container(
                decoration: BoxDecoration(
                    borderRadius: const BorderRadius.all(Radius.circular(5)),
                    border: Border.all(color: Colors.black12, width: 1.0)),
                child: TextFormField(
                  controller: widget.nameController,
                  decoration: InputDecoration(
                      contentPadding:
                          const EdgeInsets.only(left: 20, right: 20, top: 5),
                      hintText:
                          AppLocalizations.of(context).translate('FullName'),
                      hintStyle:
                          const TextStyle(color: Colors.grey, fontSize: 16),
                      border: InputBorder.none),
                ))),
        const SizedBox(
          height: 20,
        ),
        Text(
          AppLocalizations.of(context).translate('EmailAdress'),
          style: const TextStyle(
            fontSize: 16,
          ),
        ),
        const SizedBox(
          height: 10,
        ),
        Container(
            height: 50,
            decoration: BoxDecoration(
                color: Colors.white, borderRadius: BorderRadius.circular(3)),
            child: Container(
                decoration: BoxDecoration(
                    borderRadius: const BorderRadius.all(Radius.circular(5)),
                    border: Border.all(color: Colors.black12, width: 1.0)),
                child: TextFormField(
                  controller: widget.emailController,
                  // textAlignVertical : TextAlignVertical.center,
                  decoration: InputDecoration(
                      contentPadding:
                          const EdgeInsets.only(left: 20, right: 20, top: 5),
                      hintText:
                          AppLocalizations.of(context).translate('EmailAdress'),
                      hintStyle:
                          const TextStyle(color: Colors.grey, fontSize: 16),
                      border: InputBorder.none),
                ))),
        const SizedBox(
          height: 20,
        ),
        Text(AppLocalizations.of(context).translate('PhoneNumber')),
        const SizedBox(
          height: 10,
        ),
        Container(
            width: MediaQuery.of(context).size.width,
            height: 50,
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(5),
              border: Border.all(color: Colors.black12, width: 1.0),
            ),
            child: Container(
                // width: MediaQuery.of(context).size.width * 0.4,
                decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(3)),
                child: TextFormField(
                  onChanged: (value) {
                    if (value.length > 9) {
                      setState(() {
                        isphone = !isphone;
                      });
                    }
                  },
                  keyboardType: TextInputType.number,
                  controller: widget.phoneController,
                  decoration: InputDecoration(
                      prefixIcon: GestureDetector(
                        onTap: () {
                          showCountryPickerDialog(context, (Country country) {
                            widget.onCountryChanged(country);
                          });
                        },
                        child: countryCodeField(
                          context,
                          widget.selectedCountry,
                        ),
                      ),
                      contentPadding: const EdgeInsets.only(top: 15),
                      errorText:
                          _validatephone ? 'Please insert phone number' : null,
                      hintText:
                          AppLocalizations.of(context).translate('PhoneNumber'),
                      hintStyle:
                          const TextStyle(color: Colors.grey, fontSize: 16),
                      border: InputBorder.none),
                ))),
        const SizedBox(
          height: 10,
        ),
        (widget.selectedCountry.phoneCode) != '971'
            ? Container(
                color: const Color(0xffFAF5EC),
                padding: const EdgeInsets.all(20),
                child: Text(AppLocalizations.of(context).translate(
                    'If youare not a UAE citizen please provide us with a phone number that has a WhatsApp account')),
              )
            : Container(),
        const SizedBox(
          height: 20,
        ),
        Text(
          AppLocalizations.of(context).translate('Password'),
          style: const TextStyle(
            fontSize: 16,
          ),
        ),
        const SizedBox(
          height: 10,
        ),
        Container(
            height: 50,
            decoration: BoxDecoration(
                color: Colors.white, borderRadius: BorderRadius.circular(3)),
            child: Container(
                decoration: BoxDecoration(
                    borderRadius: const BorderRadius.all(Radius.circular(5)),
                    border: Border.all(color: Colors.black12, width: 1.0)),
                child: TextFormField(
                  obscureText: !isvisible ? true : false,
                  controller: widget.passwordController,
                  decoration: InputDecoration(
                      suffixIconConstraints: const BoxConstraints(
                        // minWidth:
                        // 50,
                        minHeight: 50,
                      ),
                      contentPadding:
                          const EdgeInsets.only(left: 20, right: 20, top: 15),
                      hintText:
                          AppLocalizations.of(context).translate('Password'),
                      suffixIcon: Container(
                          padding: const EdgeInsets.only(
                              left: 10, right: 10, top: 5),
                          child: GestureDetector(
                              onTap: () {
                                setState(() {
                                  isvisible = !isvisible;
                                });
                              },
                              child: const Icon(Icons.remove_red_eye))),
                      hintStyle:
                          const TextStyle(color: Colors.grey, fontSize: 16),
                      border: InputBorder.none),
                ))),
      ],
    );
  }
}
