import 'package:flutter/material.dart';

class SendRequestCarRental extends StatelessWidget {
  int id;
  int agentId;
  num? startPrice;
  num? privateDriverPrice;

  SendRequestCarRental(
    this.id, {
    super.key,
    required this.agentId,
    required this.startPrice,
    required this.privateDriverPrice,
  });

  @override
  Widget build(BuildContext context) {
    return const Placeholder();
  }
}

// import 'dart:developer';
//
// import 'package:flutter/material.dart';
// import 'package:intl/intl.dart';
// import 'package:lottie/lottie.dart';
// import 'package:page/src/features/models/booked_model.dart';
// import 'package:page/src/features/views/send_request_car/widgets/car_rent_price_details_section.dart';
// import 'package:page/src/features/views/send_request_car/widgets/view_schedule_days.dart';
//
// import '../../../core/localization/app_localizations.dart';
// import '../../../core/response/generalResponse.dart';
// import '../../../core/services/api.dart';
// import '../../../core/shared_widgets/book_calendar.dart';
// import '../../../core/shared_widgets/shared_widgets.dart';
// import '../../controllers/currency_controller.dart';
//
// // ignore: must_be_immutable
// class SendRequestCarRental extends StatefulWidget {
//   int id;
//   int agentId;
//   num? startPrice;
//   num? privateDriverPrice;
//
//   SendRequestCarRental(
//     this.id, {
//     super.key,
//     required this.agentId,
//     required this.startPrice,
//     required this.privateDriverPrice,
//   });
//
//   @override
//   _SendRequestCarRental createState() => _SendRequestCarRental();
// }
//
// class _SendRequestCarRental extends State<SendRequestCarRental> {
//   CurrencyController currencyController = CurrencyController();
//
//   bool isLoading = false;
//
//   List<BookedModel> bookedDays = [];
//
//   void getBookedDays() async {
//     setState(() {
//       isLoading = true;
//     });
//
//     bookedDays = await Api.getBookedDays(widget.id);
//
//     setState(() {
//       isLoading = false;
//     });
//   }
//
//   @override
//   void initState() {
//     currencyController.getcuurentcurrency(context);
//
//     getBookedDays();
//     super.initState();
//   }
//
//   final format2 = DateFormat("yyyy-MM-dd");
//
//   // TextEditingController startdateController = TextEditingController();
//   // TextEditingController enddateController = TextEditingController();
//   TextEditingController promocode = TextEditingController();
//   TextEditingController locationCtrl = TextEditingController();
//   TextEditingController noteController = TextEditingController();
//   bool isselected = false;
//   bool isPrivateCar = false;
//   String? currentvalue3;
//   bool isload = false;
//   bool isfilstartdate = false;
//   bool isfilenddate = false;
//   int pickup = 0;
//   int? code;
//   String? msg;
//   Map<String, dynamic>? pricerequest;
//   var totalVal = 0.0;
//   int? promocodeid;
//   int usepromocode = 0;
//
//   // DateTime? calculatedDateTime;
//
//   ValueNotifier<List<DateTime?>> selectedDates =
//       ValueNotifier<List<DateTime?>>([]);
//
//   void getpricerequest(int id, int isPrivate) async {
//     // final startDate = DateTime.parse(startdateController.text);
//     // calculatedDateTime = startDate.add(const Duration(days: 1));
//
//     if (selectedDates.value.isEmpty) {
//       return;
//     }
//
//     final startDate = selectedDates.value.first!.add(const Duration(days: 1));
//
//     final endDate = format2.format(selectedDates.value.last!);
//
//     final date = format2.format(startDate);
//
//     log('Dateee $date');
//
//     await Api.getcarrentprice(id, date, endDate, promocodeid, isPrivate)
//         .then((value) {
//       value != null
//           ? setState(() {
//               code = value.code;
//               msg = value.error;
//               pricerequest = value.results;
//
//               totalVal = double.tryParse(
//                   pricerequest?['total_amount'].toString() ?? '0')!;
//             })
//           // ignore: unnecessary_statements
//           : null;
//     });
//   }
//
//   @override
//   Widget build(BuildContext context) {
//     log('asdassa ${pricerequest?['num_days']}');
//
//     return SafeArea(
//         child: Scaffold(
//       appBar: AppBar(
//         centerTitle: true,
//         backgroundColor: const Color(0xFF27b4a8),
//         title: Text(AppLocalizations.of(context).translate('Send Request')),
//       ),
//       body: isLoading
//           ? buildLoadingWidget()
//           : SingleChildScrollView(
//               child: Container(
//                 padding: const EdgeInsets.only(left: 20, right: 20),
//                 child: Column(
//                   crossAxisAlignment: CrossAxisAlignment.start,
//                   children: [
//                     // const SizedBox(
//                     //   height: 20,
//                     // ),
//                     // Row(
//                     //   children: [
//                     //     Expanded(
//                     //       // width:
//                     //       //     MediaQuery.of(context).size.width *
//                     //       //         0.4,
//                     //       child: Text(
//                     //         AppLocalizations.of(context).translate('Fromdate'),
//                     //         style: const TextStyle(fontSize: 13),
//                     //       ),
//                     //     ),
//                     //     Expanded(
//                     //       // width:
//                     //       //     MediaQuery.of(context).size.width *
//                     //       //         0.4,
//                     //       child: Text(
//                     //         AppLocalizations.of(context).translate('Todate'),
//                     //         style: const TextStyle(fontSize: 13),
//                     //       ),
//                     //     ),
//                     //   ],
//                     // ),
//                     // const SizedBox(
//                     //   height: 10,
//                     // ),
//
//                     // Row(
//                     //   children: [
//                     //     Expanded(
//                     //         child: Container(
//                     //             height: 50,
//                     //             decoration: BoxDecoration(
//                     //                 color: Colors.white,
//                     //                 borderRadius: BorderRadius.circular(3)),
//                     //             child: Container(
//                     //                 decoration: BoxDecoration(
//                     //                     borderRadius: const BorderRadius.all(
//                     //                         Radius.circular(5)),
//                     //                     border: Border.all(
//                     //                         color: Colors.black12, width: 1.0)),
//                     //                 child: Container(
//                     //                     // width: 150,
//                     //                     child: DateTimeField(
//                     //                   controller: startdateController,
//                     //                   format: format2,
//                     //                   initialValue: DateTime.now(),
//                     //                   onChanged: (value) {
//                     //                     final startDate = DateTime.parse(
//                     //                         startdateController.text);
//                     //
//                     //                     calculatedDateTime = startDate
//                     //                         .add(const Duration(days: 1));
//                     //
//                     //                     if (isfilenddate == true) {
//                     //                       getpricerequest(
//                     //                           widget.id,
//                     //                           startdateController.text,
//                     //                           enddateController.text,
//                     //                           isPrivateCar == true ? 1 : 0);
//                     //                     }
//                     //
//                     //                     setState(() {});
//                     //                   },
//                     //                   decoration: const InputDecoration(
//                     //                       hintText: 'DD/MM/YY',
//                     //                       hintStyle: TextStyle(
//                     //                           color: Colors.grey, fontSize: 12),
//                     //                       contentPadding: EdgeInsets.only(
//                     //                           left: 5,
//                     //                           right: 5,
//                     //                           top: 10,
//                     //                           bottom: 15),
//                     //                       prefixIconConstraints: BoxConstraints(
//                     //                         minWidth: 15,
//                     //                         minHeight: 15,
//                     //                       ),
//                     //                       border: InputBorder.none),
//                     //                   onShowPicker: (context, currentValue) {
//                     //                     setState(() {
//                     //                       isfilstartdate = true;
//                     //                     });
//                     //                     return showDatePicker(
//                     //                         context: context,
//                     //                         firstDate: DateTime(1900),
//                     //                         initialDate:
//                     //                             currentValue ?? DateTime.now(),
//                     //                         lastDate: DateTime(2100));
//                     //                   },
//                     //                 ))))),
//                     //     const SizedBox(
//                     //       width: 10,
//                     //     ),
//                     //     Expanded(
//                     //       // width:
//                     //       //     MediaQuery.of(context).size.width *
//                     //       //         0.4,
//                     //       child: Container(
//                     //           height: 50,
//                     //           decoration: BoxDecoration(
//                     //               color: Colors.white,
//                     //               borderRadius: BorderRadius.circular(3)),
//                     //           child: Container(
//                     //               decoration: BoxDecoration(
//                     //                   borderRadius: const BorderRadius.all(
//                     //                       Radius.circular(5)),
//                     //                   border: Border.all(
//                     //                       color: Colors.black12, width: 1.0)),
//                     //               child: DateTimeField(
//                     //                 controller: enddateController,
//                     //                 format: format2,
//                     //                 initialValue: DateTime.now(),
//                     //                 decoration: const InputDecoration(
//                     //                     hintText: 'DD/MM/YY',
//                     //                     hintStyle: TextStyle(
//                     //                         color: Colors.grey, fontSize: 12),
//                     //                     contentPadding: EdgeInsets.only(
//                     //                         left: 5,
//                     //                         right: 5,
//                     //                         top: 10,
//                     //                         bottom: 15),
//                     //                     prefixIconConstraints: BoxConstraints(
//                     //                       minWidth: 15,
//                     //                       minHeight: 15,
//                     //                     ),
//                     //                     border: InputBorder.none),
//                     //                 onChanged: (value) {
//                     //                   if (isfilstartdate == true &&
//                     //                       isfilenddate == true) {
//                     //                     print("fdfddfdfdf");
//                     //                     print(enddateController.text);
//                     //                     getpricerequest(
//                     //                         widget.id,
//                     //                         startdateController.text,
//                     //                         enddateController.text,
//                     //                         isPrivateCar == true ? 1 : 0);
//                     //                   }
//                     //                 },
//                     //                 onShowPicker: (context, currentValue) {
//                     //                   if (isfilstartdate == true) {
//                     //                     setState(() {
//                     //                       isfilenddate = true;
//                     //                     });
//                     //                   }
//                     //                   return showDatePicker(
//                     //                       context: context,
//                     //                       firstDate: DateTime(1900),
//                     //                       initialDate:
//                     //                           currentValue ?? DateTime.now(),
//                     //                       lastDate: DateTime(2100));
//                     //                 },
//                     //               ))),
//                     //     ),
//                     //   ],
//                     // ),
//
//                     // BookCalendar(
//                     //   // reservedInDays: reservedInDays(bookedDays: bookedDays),
//                     //   selectedDates: selectedDates,
//                     //   onDateChanged: (dates) {
//                     //     if (dates.isNotEmpty) {
//                     //       if (dates.length > 1 &&
//                     //           !dateValidation(dates, bookedDays: bookedDays)) {
//                     //         setState(() {
//                     //           selectedDates.value = [dates.last];
//                     //         });
//                     //
//                     //         return;
//                     //       }
//                     //
//                     //       getpricerequest(widget.id, isPrivateCar ? 1 : 0);
//                     //
//                     //       setState(() {});
//                     //     }
//                     //   },
//                     // ),
//                     const SizedBox(
//                       height: 20,
//                     ),
//                     Row(
//                       children: [
//                         Expanded(
//                           child: GestureDetector(
//                             onTap: () {
//                               setState(() {
//                                 pickup = 0;
//                                 isselected = !isselected;
//                                 // pickup = 1;
//                               });
//                             },
//                             child: Container(
//                                 height: 50,
//                                 decoration: BoxDecoration(
//                                     color: isselected == false
//                                         ? const Color(0xff263547)
//                                         : Colors.grey[50],
//                                     borderRadius: BorderRadius.circular(3)),
//                                 child: Container(
//                                     decoration: BoxDecoration(
//                                         borderRadius: const BorderRadius.all(
//                                             Radius.circular(5)),
//                                         border: Border.all(
//                                             color: Colors.black12, width: 1.0)),
//                                     child: Center(
//                                       child: Text(
//                                         AppLocalizations.of(context)
//                                             .translate('Pick Up'),
//                                         style: TextStyle(
//                                             color: isselected == false
//                                                 ? Colors.white
//                                                 : Colors.black),
//                                       ),
//                                     ))),
//                           ),
//                         ),
//                         const SizedBox(
//                           width: 10,
//                         ),
//                         Expanded(
//                           // width:
//                           //     MediaQuery.of(context).size.width *
//                           //         0.4,
//                           child: GestureDetector(
//                             onTap: () {
//                               setState(() {
//                                 pickup = 0;
//                                 isselected = !isselected;
//                                 pickup = 1;
//                               });
//                             },
//                             child: Container(
//                                 height: 50,
//                                 decoration: BoxDecoration(
//                                     color: isselected == true
//                                         ? const Color(0xff263547)
//                                         : Colors.grey[50],
//                                     borderRadius: BorderRadius.circular(3)),
//                                 child: Container(
//                                     decoration: BoxDecoration(
//                                         borderRadius: const BorderRadius.all(
//                                             Radius.circular(5)),
//                                         border: Border.all(
//                                             color: Colors.black12, width: 1.0)),
//                                     child: Center(
//                                         child: Text(
//                                       AppLocalizations.of(context)
//                                           .translate('Drop Off'),
//                                       style: TextStyle(
//                                         color: isselected == true
//                                             ? Colors.white
//                                             : Colors.black,
//                                       ),
//                                     )))),
//                           ),
//                         ),
//                       ],
//                     ),
//                     isselected == true
//                         ? const SizedBox(
//                             height: 20,
//                           )
//                         : Container(),
//                     isselected == true
//                         ? Text(
//                             AppLocalizations.of(context).translate('location'),
//                             style: const TextStyle(fontSize: 13),
//                           )
//                         : Container(),
//                     const SizedBox(
//                       height: 10,
//                     ),
//                     isselected == true
//                         ? Container(
//                             height: 50,
//                             decoration: BoxDecoration(
//                                 color: Colors.white,
//                                 borderRadius: BorderRadius.circular(3)),
//                             child: Container(
//                                 decoration: BoxDecoration(
//                                     borderRadius: const BorderRadius.all(
//                                         Radius.circular(5)),
//                                     border: Border.all(
//                                         color: Colors.black12, width: 1.0)),
//                                 child: TextFormField(
//                                   controller: locationCtrl,
//                                   decoration: InputDecoration(
//                                       contentPadding: const EdgeInsets.only(
//                                           left: 20, right: 20, top: 10),
//                                       hintText: AppLocalizations.of(context)
//                                           .translate('location'),
//                                       hintStyle: const TextStyle(
//                                           color: Colors.grey, fontSize: 12),
//                                       border: InputBorder.none),
//                                 )))
//                         : Container(),
//                     const SizedBox(
//                       height: 20,
//                     ),
//                     Row(
//                       mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                       children: [
//                         Text(AppLocalizations.of(context)
//                             .translate('Private Driver')),
//                         Switch(
//                           inactiveTrackColor: Colors.grey[200],
//                           inactiveThumbColor: Colors.grey,
//                           activeColor: Colors.white,
//                           activeTrackColor: Colors.green,
//                           onChanged: (index2) {
//                             setState(() {
//                               isPrivateCar = !isPrivateCar;
//                               getpricerequest(widget.id, isPrivateCar ? 1 : 0);
//                             });
//                           },
//                           value: isPrivateCar,
//                         )
//                       ],
//                     ),
//                     const SizedBox(
//                       height: 20,
//                     ),
//                     Text(
//                       AppLocalizations.of(context).translate('note'),
//                       style: const TextStyle(fontSize: 13),
//                     ),
//                     const SizedBox(
//                       height: 10,
//                     ),
//                     Container(
//                         height: 100,
//                         decoration: BoxDecoration(
//                             color: Colors.white,
//                             borderRadius: BorderRadius.circular(3)),
//                         child: Container(
//                             decoration: BoxDecoration(
//                                 borderRadius:
//                                     const BorderRadius.all(Radius.circular(5)),
//                                 border: Border.all(
//                                     color: Colors.black12, width: 1.0)),
//                             child: TextFormField(
//                               controller: noteController,
//                               decoration: InputDecoration(
//                                   contentPadding: const EdgeInsets.only(
//                                       left: 20, right: 20, top: 10),
//                                   hintText: AppLocalizations.of(context)
//                                       .translate('Write down your note'),
//                                   hintStyle: const TextStyle(
//                                       color: Colors.grey, fontSize: 12),
//                                   border: InputBorder.none),
//                             ))),
//                     const SizedBox(
//                       height: 20,
//                     ),
//                     Text(
//                       AppLocalizations.of(context).translate('Promo Code'),
//                     ),
//                     const SizedBox(
//                       height: 10,
//                     ),
//                     Container(
//                         height: 60,
//                         decoration: BoxDecoration(
//                             color: Colors.white,
//                             borderRadius: BorderRadius.circular(3)),
//                         child: Container(
//                             decoration: BoxDecoration(
//                                 borderRadius:
//                                     const BorderRadius.all(Radius.circular(5)),
//                                 border: Border.all(
//                                     color: Colors.black12, width: 1.0)),
//                             child: TextFormField(
//                               controller: promocode,
//                               decoration: InputDecoration(
//                                   contentPadding: const EdgeInsets.only(
//                                       left: 20, right: 20, top: 20),
//                                   suffixIcon: Container(
//                                       padding: const EdgeInsets.only(
//                                           left: 5, right: 5, top: 10),
//                                       child: InkWell(
//                                           onTap: () async {
//                                             if (!dateValidation(
//                                                 selectedDates.value,
//                                                 bookedDays: bookedDays)) {
//                                               snackbar(AppLocalizations.of(
//                                                       context)
//                                                   .translate(
//                                                       'please select correct range'));
//                                               return;
//                                             }
//
//                                             setState(() {
//                                               isload = !isload;
//                                             });
//
//                                             if (usepromocode > 0) {
//                                               snackbar(AppLocalizations.of(
//                                                       context)
//                                                   .translate(
//                                                       'this promo code you use'));
//                                             } else {
//                                               GeneralResponseWithData
//                                                   sucessinformation =
//                                                   await Api.applypromocode(
//                                                       promocode.text,
//                                                       widget.agentId);
//
//                                               print(sucessinformation.code);
//                                               if (sucessinformation.code ==
//                                                   "1") {
//                                                 promocodeid =
//                                                     sucessinformation.data;
//
//                                                 setState(() {
//                                                   getpricerequest(
//                                                       widget.id,
//                                                       isPrivateCar == true
//                                                           ? 1
//                                                           : 0);
//                                                 });
//                                                 snackbar2(AppLocalizations.of(
//                                                         context)
//                                                     .translate(
//                                                         'Discount code will be used'));
//
//                                                 // Navigator.pop(context);
//                                               } else {
//                                                 snackbar(
//                                                     sucessinformation.msg!);
//                                               }
//                                               setState(() {
//                                                 isload = !isload;
//                                               });
//                                             }
//                                           },
//                                           child: Container(
//                                               height: 20,
//                                               width: 100,
//                                               color: Colors.grey[200],
//                                               child: Center(
//                                                 child: Text(AppLocalizations.of(
//                                                         context)
//                                                     .translate('Apply Promo')),
//                                               )))),
//                                   hintText: AppLocalizations.of(context)
//                                       .translate('Apply Promo'),
//                                   hintStyle: const TextStyle(
//                                       color: Colors.grey, fontSize: 16),
//                                   border: InputBorder.none),
//                             ))),
//                     const SizedBox(
//                       height: 20,
//                     ),
//                     if (pricerequest != null &&
//                         pricerequest?['scheduling_periods'] != null &&
//                         pricerequest?['scheduling_periods'].isNotEmpty) ...[
//                       Center(
//                         child: InkWell(
//                           onTap: () => showModalBottomSheet(
//                               context: context,
//                               builder: (_) {
//                                 return ViewScheduleDays(
//                                   scheduleDays:
//                                       pricerequest!['scheduling_periods'],
//                                   currencyController: currencyController,
//                                   startPrice: widget.startPrice,
//                                   privateDriverPrice: widget.privateDriverPrice,
//                                 );
//                               }),
//                           child: Text(
//                             AppLocalizations.of(context)
//                                 .translate('Show Schedule Prices'),
//                             style: const TextStyle(
//                                 fontSize: 13,
//                                 fontWeight: FontWeight.bold,
//                                 decoration: TextDecoration.underline,
//                                 color: Color(0xff233549)),
//                           ),
//                         ),
//                       ),
//                       const SizedBox(
//                         height: 20,
//                       ),
//                     ],
//
//                     pricerequest != null &&
//                             dateValidation(selectedDates.value,
//                                 bookedDays: bookedDays)
//                         ? CarRentPriceDetailsSection(
//                             pricerequest: pricerequest!,
//                             isPrivateCar: isPrivateCar,
//                             currencyController: currencyController)
//                         : Container(),
//                     const SizedBox(
//                       height: 20,
//                     ),
//                     !isload
//                         ? Center(
//                             child: GestureDetector(
//                                 onTap: () async {
//                                   if (!checkReversedDays(context,
//                                       selectedDates: selectedDates,
//                                       bookedDays: bookedDays)) return;
//
//                                   if (!dateValidation(selectedDates.value,
//                                       bookedDays: bookedDays)) {
//                                     snackbar(AppLocalizations.of(context)
//                                         .translate(
//                                             'please select correct range'));
//                                     return;
//                                   }
//
//                                   setState(() {
//                                     isload = !isload;
//                                   });
//
//                                   int isprivatedrop = 0;
//                                   if (isPrivateCar == true) {
//                                     isprivatedrop = 1;
//                                   }
//
//                                   final periods =
//                                       List<Map?>.from(pricerequest!['periods'])
//                                           .map((e) => {
//                                                 'start_date':
//                                                     e?['commonDays'].first,
//                                                 'end_date':
//                                                     e?['commonDays'].last,
//                                                 'price_day': e?['price_day'],
//                                                 "num_days": e?['num_days'],
//                                                 'price_driver':
//                                                     e?['price_driver'],
//                                               })
//                                           .toList();
//
//                                   Map<String, dynamic> data = {
//                                     "start_date": format2
//                                         .format(selectedDates.value.first!),
//                                     "end_date": format2
//                                         .format(selectedDates.value.last!),
//                                     "num_days": pricerequest!['num_days'],
//                                     "request_date":
//                                         DateTime.now().toIso8601String(),
//                                     "video_id": widget.id,
//                                     "promo_code": promocodeid,
//                                     'discount': pricerequest!['discount'],
//                                     'price_driver':
//                                         pricerequest!['car_private_driver'],
//                                     "private_drop": isprivatedrop,
//                                     "drop_off": pickup,
//                                     "location": locationCtrl.text,
//                                     "user_note": noteController.text,
//                                     "agent_id": widget.agentId,
//                                     'vat': pricerequest!['vat'],
//                                     'subtotal': pricerequest!['subtotal'],
//                                     'total': pricerequest!['total_amount'],
//                                     "periods": periods,
//                                     "num_not_common_days":
//                                         pricerequest!['num_not_common_days'],
//                                     "normal_price":
//                                         pricerequest!['normal_price'],
//                                     "normal_driver_price":
//                                         pricerequest!['normal_driver_price'],
//                                   };
//                                   print('asfasfasasfa $data');
//                                   GeneralResponse sucessinformation =
//                                       await Api.sendrequestcarrent(data);
//
//                                   print(sucessinformation.code);
//                                   if (sucessinformation.code == "1") {
//                                     snackbar2(AppLocalizations.of(context)
//                                         .translate('Request send successfuly'));
//                                     Navigator.pop(context);
//                                   } else {
//                                     snackbar(sucessinformation.msg!);
//                                   }
//                                   setState(() {
//                                     isload = !isload;
//                                   });
//                                 },
//                                 child: Container(
//                                   height: 50,
//                                   width: MediaQuery.of(context).size.width,
//                                   decoration: BoxDecoration(
//                                       color: const Color(0xFF27b4a8),
//                                       borderRadius: BorderRadius.circular(10)),
//                                   child: Container(
//                                       padding: const EdgeInsets.all(10),
//                                       child: Center(
//                                           child: Text(
//                                         AppLocalizations.of(context)
//                                             .translate('Send Request'),
//                                         style: const TextStyle(
//                                             color: Colors.white),
//                                       ))),
//                                 )))
//                         : Center(
//                             child: Lottie.asset(
//                                 'assets/59218-progress-indicator.json',
//                                 height: 50,
//                                 width: 50)),
//                     const SizedBox(
//                       height: 20,
//                     ),
//                   ],
//                 ),
//               ),
//             ),
//     ));
//   }
// }
