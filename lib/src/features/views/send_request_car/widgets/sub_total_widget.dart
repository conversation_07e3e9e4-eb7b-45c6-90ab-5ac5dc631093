import 'package:flutter/material.dart';

import '../../../../core/localization/app_localizations.dart';

class SubTotalWidget extends StatelessWidget {
  final currencyController;
  final pricerequest;
  const SubTotalWidget(
      {Key? key, required this.currencyController, required this.pricerequest})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: const BorderRadius.all(Radius.circular(5)),
            border: Border.all(color: Colors.black12, width: 1.0)),
        child: Column(
          children: [
            Container(
                decoration: BoxDecoration(
                    color: Colors.grey[200],
                    borderRadius: const BorderRadius.all(Radius.circular(5)),
                    border: Border.all(color: Colors.black12, width: 1.0)),
                height: 50,
                child: Center(
                    child: Text(
                  AppLocalizations.of(context).translate('Price Breakdown'),
                ))),
            const SizedBox(
              height: 20,
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  AppLocalizations.of(context).translate('No Of days'),
                  style:
                      const TextStyle(color: Color(0xff51565B), fontSize: 13),
                ),
                pricerequest!['days'] != null
                    ? Text(
                        pricerequest!['days'].toString() +
                            AppLocalizations.of(context).translate('days'),
                        style: const TextStyle(
                            color: Color(0xff51565B), fontSize: 13),
                      )
                    : Container(),
              ],
            ),
            const SizedBox(
              height: 20,
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  AppLocalizations.of(context).translate('Sub Total'),
                  style:
                      const TextStyle(color: Color(0xff51565B), fontSize: 13),
                ),
                Row(
                  children: [
                    pricerequest!['subtotal'] != null
                        ? Text(
                            pricerequest!['subtotal']
                                    .round()
                                    .toStringAsFixed(2) +
                                ' ' +
                                '${currencyController.currency}',
                            style: const TextStyle(
                                color: Color(0xff51565B), fontSize: 13),
                          )
                        : Container(),
                  ],
                )
              ],
            ),
            const SizedBox(
              height: 20,
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  AppLocalizations.of(context).translate('Promo Subtotal'),
                  style:
                      const TextStyle(color: Color(0xff51565B), fontSize: 13),
                ),
                pricerequest!['discount'] != null
                    ? Text(
                        '${pricerequest!['discount']} ${currencyController.currency}',
                        style: const TextStyle(
                            color: Color(0xff51565B), fontSize: 13),
                      )
                    : Container(),
              ],
            ),
            const SizedBox(
              height: 20,
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  AppLocalizations.of(context).translate('VAT'),
                  style:
                      const TextStyle(color: Color(0xff51565B), fontSize: 13),
                ),
                pricerequest!['vat'] != null
                    ? Text(
                        pricerequest!['vat'].round().toStringAsFixed(2) +
                            ' ' +
                            '${currencyController.currency}',
                        style: const TextStyle(
                            color: Color(0xff51565B), fontSize: 13),
                      )
                    : Container(),
              ],
            ),
            const SizedBox(
              height: 20,
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  '${AppLocalizations.of(context).translate('Tourism Fee')}',
                  // ( ${pricerequest!['days']}${AppLocalizations.of(context).translate('nights')} )
                  style:
                      const TextStyle(color: Color(0xff51565B), fontSize: 13),
                ),
                pricerequest!['tourismfees'] != null
                    ? Text(
                        pricerequest!['tourismfees']
                                .round()
                                .toStringAsFixed(2) +
                            ' ' +
                            '${currencyController.currency}',
                        style: const TextStyle(
                            color: Color(0xff51565B), fontSize: 13),
                      )
                    : Container(),
              ],
            ),
            const SizedBox(
              height: 20,
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  AppLocalizations.of(context).translate('total'),
                  style: const TextStyle(
                      color: Colors.black,
                      fontSize: 13,
                      fontWeight: FontWeight.bold),
                ),
                pricerequest!['total'] != null
                    ? Text(
                        pricerequest!['total'].round().toStringAsFixed(2) +
                            ' ' +
                            '${currencyController.currency}',
                        style: const TextStyle(
                            color: Color(0xff51565B),
                            fontSize: 13,
                            fontWeight: FontWeight.bold),
                      )
                    : Container(),
              ],
            )
          ],
        ));
  }
}
