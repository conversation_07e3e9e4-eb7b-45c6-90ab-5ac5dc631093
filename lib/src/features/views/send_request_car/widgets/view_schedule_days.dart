import 'package:flutter/material.dart';
import 'package:page/src/core/localization/app_localizations.dart';

class ViewScheduleDays extends StatelessWidget {
  final List<dynamic> scheduleDays;
  final currencyController;
  final num? startPrice;
  final num? privateDriverPrice;

  const ViewScheduleDays({
    super.key,
    required this.scheduleDays,
    required this.currencyController,
    required this.startPrice,
    this.privateDriverPrice,
  });

  @override
  Widget build(BuildContext context) {
    //[{id: 9, start_date: 2023-08-07, end_date: 2023-08-10, start_price: 0, end_price: 0, price_driver: 155, price_day: 123, type: normal, schedule_type: carRent, video_id: 42, created_at: 2023-08-07T19:41:16.000000Z, updated_at: 2023-08-07T19:41:16.000000Z}]
    return SizedBox(
      height: 600,
      child: ListView(
        children: [
          const SizedBox(
            height: 20,
          ),
          //? Default Prices
          Center(
            child: Text(
              AppLocalizations.of(context).translate('Normal Prices'),
              style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  decoration: TextDecoration.underline),
            ),
          ),
          const SizedBox(
            height: 10,
          ),
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 12.0, horizontal: 8),
            child: RowPrices(
              label: startPrice?.toStringAsFixed(2) ?? '',
              label2: privateDriverPrice?.toStringAsFixed(2),
              currency: currencyController.currency,
            ),
          ),
          //? Default Prices
          Center(
            child: Text(
              AppLocalizations.of(context).translate('Schedule Days Prices'),
              style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  decoration: TextDecoration.underline),
            ),
          ),
          const SizedBox(
            height: 10,
          ),

          SizedBox(
            height: 350,
            child: ListView.separated(
              padding: const EdgeInsets.all(12),
              shrinkWrap: true,
              itemCount: scheduleDays.length,
              itemBuilder: (context, index) {
                // show date for each schedule and price
                return Padding(
                  padding: const EdgeInsets.symmetric(vertical: 8.0),
                  child: ListTile(
                    title: Container(
                      padding: const EdgeInsets.all(10),
                      decoration: BoxDecoration(
                          color: Colors.grey[200],
                          borderRadius: BorderRadius.circular(8),
                          border:
                              Border.all(color: Colors.black12, width: 1.0)),
                      alignment: Alignment.center,
                      child: Text(
                        scheduleDays[index]['start_date'] +
                            ' - ' +
                            scheduleDays[index]['end_date'],
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                    subtitle: Padding(
                      padding: const EdgeInsets.symmetric(vertical: 12.0),
                      child: RowPrices(
                        label: scheduleDays[index]['price_day'].toString(),
                        label2: scheduleDays[index]['price_driver'].toString(),
                        currency: currencyController.currency,
                      ),
                    ),
                  ),
                );
              },
              separatorBuilder: (context, index) {
                return const Divider(
                  color: Colors.black12,
                  height: 1,
                );
              },
            ),
          ),
        ],
      ),
    );
  }
}

class RowPrices extends StatelessWidget {
  final String label;
  final String? label2;
  final String? currency;

  const RowPrices(
      {super.key, required this.label, this.label2, required this.currency});

  @override
  Widget build(BuildContext context) {
    final isHolidayRequest = label2 == null;

    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Flexible(
          child: FittedBox(
            fit: BoxFit.scaleDown,
            child: Text(
              '${AppLocalizations.of(context).translate(isHolidayRequest ? 'Night Price' : 'Day Price')}: $label $currency',
              style: const TextStyle(
                fontSize: 18,
              ),
            ),
          ),
        ),
        if (label2 != null) ...[
          const Text(
            '/',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          Flexible(
            child: FittedBox(
              fit: BoxFit.scaleDown,
              child: Text(
                '${AppLocalizations.of(context).translate('Private Driver')}: $label2 $currency',
                style: const TextStyle(
                  fontSize: 18,
                ),
                maxLines: 1,
              ),
            ),
          ),
        ],
      ],
    );
  }
}
