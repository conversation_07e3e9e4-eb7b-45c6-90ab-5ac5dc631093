import 'package:flutter/material.dart';
import 'package:page/src/core/localization/app_localizations.dart';
import 'package:page/src/core/response/generalResponse.dart';
import 'package:page/src/core/services/api.dart';
import 'package:page/src/core/shared_widgets/shared_widgets.dart';
import 'package:page/src/features/bloc/plan_bloc.dart';

bool isload = false;

void deleteplan(
  int id, {
  required BuildContext context,
}) {
  showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      enableDrag: true,
      backgroundColor: Colors.transparent,
      builder: (context) =>
          StatefulBuilder(builder: (context, StateSetter stateSetter) {
            return Padding(
              padding: EdgeInsets.only(
                  bottom: MediaQuery.of(context).viewInsets.bottom),
              child: Container(
                  height: MediaQuery.of(context).size.height * 0.3,
                  decoration: BoxDecoration(
                      color: const Color(0xffF5F6F7),
                      borderRadius: const BorderRadius.only(
                          topLeft: Radius.circular(25.0),
                          topRight: Radius.circular(25.0)),
                      border: Border.all(color: Colors.black, width: 1.0)),
                  child: SingleChildScrollView(
                      child: Column(
                    children: [
                      const SizedBox(
                        height: 10,
                      ),
                      Container(
                          height: 5, width: 50, color: const Color(0xffD2D4D6)),
                      const SizedBox(
                        height: 20,
                      ),
                      Center(
                          child: Text(
                        AppLocalizations.of(context).translate('Delete plan'),
                        style: const TextStyle(fontWeight: FontWeight.bold),
                      )),
                      Container(
                        padding: const EdgeInsets.all(15),
                        child: Container(
                          decoration: BoxDecoration(
                              color: Colors.white,
                              borderRadius: BorderRadius.circular(10)),
                          child: Container(
                            padding: const EdgeInsets.all(15),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                const SizedBox(
                                  height: 20,
                                ),
                                Text(AppLocalizations.of(context).translate(
                                    'Are you sure you want to delete Plan')),
                                const SizedBox(
                                  height: 20,
                                ),
                                !isload
                                    ? Center(
                                        child: GestureDetector(
                                            onTap: () async {
                                              stateSetter(() {
                                                isload = !isload;
                                              });
                                              GeneralResponse
                                                  sucessinformation =
                                                  await Api.deleteplan(id);

                                              print(sucessinformation.code);
                                              if (sucessinformation.code ==
                                                  "1") {
                                                snackbar2(AppLocalizations.of(
                                                        context)
                                                    .translate(
                                                        'Delete plan successfuly'));
                                                Navigator.pop(context);
                                                planbloc.getplanuseritems(
                                                    1, 100);
                                              } else {
                                                snackbar(AppLocalizations.of(
                                                        context)
                                                    .translate(
                                                        'Something went wrong, please try again later'));
                                              }
                                              stateSetter(() {
                                                isload = !isload;
                                              });
                                            },
                                            child: Container(
                                              height: 50,
                                              width: MediaQuery.of(context)
                                                  .size
                                                  .width,
                                              decoration: BoxDecoration(
                                                  color:
                                                      const Color(0xffE04E4D),
                                                  borderRadius:
                                                      BorderRadius.circular(
                                                          10)),
                                              child: Container(
                                                  padding:
                                                      const EdgeInsets.all(10),
                                                  child: Center(
                                                      child: Text(
                                                    AppLocalizations.of(context)
                                                        .translate(
                                                            'delete plan'),
                                                    style: const TextStyle(
                                                        color: Colors.white),
                                                  ))),
                                            )))
                                    : buildLoadingWidget(),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ],
                  ))),
            );
          }));
}
