import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:page/src/core/localization/app_language.dart';
import 'package:page/src/core/localization/app_localizations.dart';
import 'package:page/src/core/response/plan_response.dart';
import 'package:page/src/core/utils/main_cached_image.dart';
import 'package:page/src/features/views/plan_details/my_plans_details.dart';
import 'package:page/src/features/views/plans/widgets/delete_plan.dart';
import 'package:provider/provider.dart';

Widget noPlanDataFound(context) {
  final Widget svg2 = SizedBox(
      width: 88,
      height: 88.0,
      child: SvgPicture.asset(
        'assets/Group 6222.svg',
        semanticsLabel: 'Acme Logo',
        fit: BoxFit.cover,
      ));
  return Center(
    child: Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        const SizedBox(
          height: 20,
        ),
        svg2,
        const SizedBox(
          height: 20,
        ),
        Text(
          AppLocalizations.of(context).translate('No plans to show'),
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        const SizedBox(
          height: 10,
        ),
        Text(
          AppLocalizations.of(context).translate('No Plans are created yet'),
          style: const TextStyle(color: Color(0xff51565B), fontSize: 12),
        ),
        const SizedBox(
          height: 20,
        ),
      ],
    ),
  );
}

class PlanList extends StatelessWidget {
  final PlanResponse data;

  const PlanList({super.key, required this.data});

  @override
  Widget build(BuildContext context) {
    return data.plans.isNotEmpty
        ? ListView.builder(
            shrinkWrap: true,
            itemBuilder: (BuildContext ctxt, int index) {
              var lang = Provider.of<AppLanguage>(context, listen: false)
                  .appLocal
                  .languageCode;
              final isEnglish = lang == 'en';

              var days = (DateTime.tryParse(data.plans[index].enddate!) ??
                          DateTime.now())
                      .difference(
                          DateTime.tryParse(data.plans[index].startdate!) ??
                              DateTime.now())
                      .inDays +
                  1;

              return InkWell(
                onLongPress: () {
                  log('longdsdgss ${data.plans[index].id}');
                  deleteplan(
                    data.plans[index].id!,
                    context: context,
                  );
                },
                onTap: () {
                  print(
                      'asdassdasdasdasf ${data.plans[index].id} Nname ${data.plans[index].name}');

                  Navigator.of(context).push(
                    MaterialPageRoute(
                      builder: (BuildContext context) => MyplansDetails(
                        data.plans[index].planName!,
                        data.plans[index].id!,
                      ),
                    ),
                  );
                },
                child: Container(
                    padding: const EdgeInsets.only(
                        top: 20, bottom: 5, right: 20, left: 20),
                    child: Container(
                      decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(10),
                          border: Border.all(
                              color: const Color(0xffF1F1F2), width: 1)),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisAlignment: MainAxisAlignment.start,
                        children: [
                          Container(
                            padding: const EdgeInsets.all(20),
                            decoration: const BoxDecoration(
                              borderRadius: BorderRadius.only(
                                  topLeft: Radius.circular(10),
                                  topRight: Radius.circular(10)),
                              color: Color(0xffF1F1F1),
                            ),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              mainAxisAlignment: MainAxisAlignment.start,
                              children: [
                                Row(
                                  children: [
                                    Text(
                                      data.plans[index].planName ?? '',
                                      style: const TextStyle(
                                          fontWeight: FontWeight.bold,
                                          fontSize: 12),
                                    ),
                                    const Spacer(),
                                    Transform.rotate(
                                      angle: isEnglish ? 0 : 180 * 3.14 / 180,
                                      child: const Icon(
                                        Icons.keyboard_arrow_right,
                                        color: Colors.black,
                                      ),
                                    )
                                  ],
                                ),
                                Text(
                                    "${AppLocalizations.of(context).translate('from')} ${data.plans[index].startdate} ${AppLocalizations.of(context).translate('to')} ${data.plans[index].enddate} ($days${AppLocalizations.of(context).translate('days')}) ",
                                    style: const TextStyle(
                                        color: Color(0xff51565B),
                                        fontSize: 12)),
                              ],
                            ),
                          ),
                          data.plans[index].items?.length == 1 &&
                                  data.plans[index].items![0].image == null
                              ? SizedBox(
                                  height: 116,
                                  child: Center(
                                    child: Text(
                                      AppLocalizations.of(context)
                                          .translate('No places are added yet'),
                                      style: const TextStyle(
                                          color: Color(0xffB7B7B7),
                                          fontSize: 12),
                                    ),
                                  ))
                              : SizedBox(
                                  height: 116,
                                  width: MediaQuery.of(context).size.width,
                                  child: ListView.builder(
                                    shrinkWrap: true,
                                    physics: const ClampingScrollPhysics(),
                                    scrollDirection: Axis.horizontal,
                                    itemBuilder:
                                        (BuildContext ctxt, int index2) {
                                      return data.plans[index].items![index2]
                                                  .image !=
                                              null
                                          ? GestureDetector(
                                              onTap: () {
                                                Navigator.of(context).push(
                                                    MaterialPageRoute(
                                                        builder: (BuildContext
                                                                context) =>
                                                            MyplansDetails(
                                                                data
                                                                        .plans[
                                                                            index]
                                                                        .planName ??
                                                                    '',
                                                                data
                                                                    .plans[
                                                                        index]
                                                                    .id!)));
                                              },
                                              child: Container(
                                                  padding:
                                                      const EdgeInsets.only(
                                                          right: 5,
                                                          top: 10,
                                                          left: 5),
                                                  child: Row(children: [
                                                    Column(children: [
                                                      const SizedBox(
                                                        width: 5,
                                                      ),
                                                      Container(
                                                          height: 96,
                                                          width:
                                                              MediaQuery.of(context)
                                                                      .size
                                                                      .width *
                                                                  0.15,
                                                          decoration:
                                                              const BoxDecoration(),
                                                          child: Center(
                                                              child: ClipRRect(
                                                                  borderRadius:
                                                                      BorderRadius.circular(
                                                                          5),
                                                                  child: MainCachedImage(
                                                                      data
                                                                          .plans[
                                                                              index]
                                                                          .items![
                                                                              index2]
                                                                          .image!,
                                                                      height:
                                                                          96,
                                                                      width: MediaQuery.of(context).size.width *
                                                                          0.15,
                                                                      fit: BoxFit
                                                                          .fill)))),
                                                      const SizedBox(
                                                        height: 10,
                                                      )
                                                    ])
                                                  ])))
                                          : Container();
                                    },
                                    itemCount: data.plans[index].items!.length,
                                  ))
                        ],
                      ),
                    )),
              );
            },
            itemCount: data.plans.length,
          )
        : noPlanDataFound(context);
  }
}
