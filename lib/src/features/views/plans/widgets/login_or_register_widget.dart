import 'package:flutter/material.dart';
import 'package:page/src/core/localization/app_localizations.dart';
import 'package:page/src/features/views/login/login.dart';
import 'package:page/src/features/views/register/register.dart';

class LoginOrRegisterPlanWidget extends StatelessWidget {
  const LoginOrRegisterPlanWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
        decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(5), color: Colors.white),
        child: Container(
          padding: const EdgeInsets.only(
            top: 20,
            left: 20,
            right: 20,
          ),
          child: SingleChildScrollView(
              child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                tr(context, "LoginRegister"),
                style:
                    const TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
              ),
              const SizedBox(
                height: 10,
              ),
              Text(
                tr(context, "To create your own plan."),
                style: TextStyle(fontSize: 16),
              ),
              const SizedBox(
                height: 20,
              ),
              Center(
                  child: GestureDetector(
                      onTap: () async {
                        Navigator.of(context).push(MaterialPageRoute(
                            builder: (BuildContext context) => const Login()));
                      },
                      child: Container(
                        height: 50,
                        width: MediaQuery.of(context).size.width,
                        decoration: BoxDecoration(
                            color: const Color(0xFF27b4a8),
                            borderRadius: BorderRadius.circular(5)),
                        child: Container(
                            padding: const EdgeInsets.all(5),
                            child: Center(
                                child: Text(
                              AppLocalizations.of(context).translate('Login'),
                              style: const TextStyle(color: Colors.white),
                            ))),
                      ))),
              const SizedBox(
                height: 20,
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Center(
                    child: Text(
                      AppLocalizations.of(context)
                          .translate('DontHaveAnAccountYet'),
                      style: const TextStyle(fontSize: 16),
                    ),
                  ),
                  const SizedBox(
                    width: 5,
                  ),
                  GestureDetector(
                    onTap: () {
                      Navigator.push(
                        context,
                        MaterialPageRoute(builder: (context) {
                          return FirstRegister();
                        }),
                      );
                    },
                    child: Center(
                        child: Text(
                      AppLocalizations.of(context).translate('Register'),
                      style: const TextStyle(
                          fontSize: 16,
                          color: Color(0xff233549),
                          fontWeight: FontWeight.bold),
                    )),
                  )
                ],
              ),
            ],
          )),
        ));
  }
}
