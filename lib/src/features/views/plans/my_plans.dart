import 'package:flutter/material.dart';
import 'package:page/src/core/response/offers_response.dart';
import 'package:page/src/features/bloc/promo_coded_bloc.dart';
import 'package:page/src/features/controllers/auth_controller.dart';
import 'package:page/src/features/views/offers/widgets/list_offers.dart';
import 'package:page/src/features/views/plans/widgets/login_or_register_widget.dart';
import 'package:page/src/features/views/plans/widgets/plan_widget.dart';

import '../../../core/localization/app_localizations.dart';
import '../../../core/response/plan_response.dart';
import '../../../core/shared_widgets/bottom_navgation_bar.dart';
import '../../../core/shared_widgets/shared_widgets.dart';
import '../../bloc/plan_bloc.dart';
import '../home/<USER>';
import '../home/<USER>/plans/create_plan_plan_bottom_sheet.dart';

class MyPlans extends StatefulWidget {
  const MyPlans({super.key});

  @override
  _Myplans createState() => _Myplans();
}

class _Myplans extends State<MyPlans> {
  int page = 1;
  AuthController authController = AuthController();

  @override
  void initState() {
    super.initState();
    authController.isloggedin();

    if (authController.isLogged == true) {
      planbloc.getplanuseritems(page, 100);
    } else {
      offersBloc.getoffers(page, 20);
    }
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
        onWillPop: () {
          Navigator.of(context).pushAndRemoveUntil(
              MaterialPageRoute(builder: (context) => const Home()),
              (Route<dynamic> route) => false);

          return Future.value(false);
        },
        child: SafeArea(
            child: Scaffold(
                appBar: AppBar(
                  backgroundColor: const Color(0xFF27b4a8),
                  centerTitle: true,
                  title:
                      Text(AppLocalizations.of(context).translate('Myplans')),
                ),
                body: SingleChildScrollView(child: Builder(builder: (context) {
                  if (authController.isLogged) {
                    return Column(
                      children: [
                        SizedBox(
                            height: MediaQuery.of(context).size.height * 0.7,
                            child: StreamBuilder<PlanResponse>(
                              stream: planbloc.subject3.stream,
                              builder: (context,
                                  AsyncSnapshot<PlanResponse> snapshot) {
                                if (snapshot.hasData) {
                                  return PlanList(
                                    data: snapshot.data!,
                                  );
                                } else if (snapshot.hasError) {
                                  return buildErrorWidget(
                                      snapshot.error!.toString());
                                } else {
                                  return Container(child: buildLoadingWidget());
                                }
                              },
                            )),
                        const SizedBox(
                          height: 20,
                        ),
                        // Spacer(),
                        if (authController.isLogged == true)
                          Container(
                              padding: const EdgeInsets.only(
                                  left: 20, right: 20, bottom: 20),
                              child: Center(
                                  child: GestureDetector(
                                      onTap: () async {
                                        createPlan(context, fromPlan: true);
                                      },
                                      child: Container(
                                        height: 50,
                                        width:
                                            MediaQuery.of(context).size.width,
                                        decoration: BoxDecoration(
                                            color: const Color(0xFF27b4a8),
                                            borderRadius:
                                                BorderRadius.circular(10)),
                                        child: Container(
                                            padding: const EdgeInsets.all(10),
                                            child: Center(
                                                child: Text(
                                              AppLocalizations.of(context)
                                                  .translate('Create New Plan'),
                                              style: const TextStyle(
                                                  color: Colors.white),
                                            ))),
                                      ))))
                      ],
                    );
                  }

                  //! If user is not logged in & Show Offers ==============================
                  return Column(
                    children: [
                      const LoginOrRegisterPlanWidget(),
                      const SizedBox(
                        height: 20,
                      ),
                      const Divider(),
                      const SizedBox(
                        height: 20,
                      ),
                      StreamBuilder<OffersResponse>(
                        stream: offersBloc.subject.stream,
                        builder:
                            (context, AsyncSnapshot<OffersResponse> snapshot) {
                          if (snapshot.hasData) {
                            if (snapshot.data!.error.isNotEmpty) {
                              return buildErrorWidget(snapshot.data!.error);
                            }
                            return Column(
                              children: [
                                Padding(
                                  padding: const EdgeInsets.symmetric(
                                      horizontal: 24),
                                  child: Row(
                                    children: [
                                      const Icon(
                                        Icons.local_offer_outlined,
                                        color: Color(0xff233549),
                                      ),
                                      const SizedBox(
                                        width: 10,
                                      ),
                                      Text(
                                        tr(context, "Trending Offers"),
                                        style: const TextStyle(
                                            fontSize: 20,
                                            fontWeight: FontWeight.bold),
                                      ),
                                    ],
                                  ),
                                ),
                                ListOffers(offers: snapshot.data!.offers),
                              ],
                            );
                          } else if (snapshot.hasError) {
                            return buildErrorWidget(snapshot.error.toString());
                          } else {
                            return buildLoadingWidget();
                          }
                        },
                      )
                    ],
                  );
                })),
                bottomNavigationBar: CustomBottomNavgationBar(3))));
  }
}
