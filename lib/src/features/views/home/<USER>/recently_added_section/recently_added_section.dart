import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:page/src/core/config/constants.dart';
import 'package:page/src/core/localization/app_localizations.dart';
import 'package:page/src/core/utils/main_cached_image.dart';
import 'package:provider/provider.dart';

import '../../../../../core/localization/app_language.dart';
import '../../../../../core/shared_widgets/shared_widgets.dart';
import '../../../../controllers/auth_controller.dart';
import '../../../../models/video_model.dart';
import '../../../ad_details/video_widget.dart';
import '../../../holiday_home_details/holiday_home_details.dart';

class HomeRecentlyAdded extends StatelessWidget {
  final List<VideoModel> categorylist;

  const HomeRecentlyAdded({Key? key, required this.categorylist})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    AuthController authController = AuthController();

    var lang =
        Provider.of<AppLanguage>(context, listen: false).appLocal.languageCode;

    return categorylist.isNotEmpty
        ? SizedBox(
            height: 230,
            width: MediaQuery.of(context).size.width,
            child: ListView.builder(
              shrinkWrap: true,
              padding: const EdgeInsets.symmetric(horizontal: 20),
              physics: const ClampingScrollPhysics(),
              scrollDirection: Axis.horizontal,
              itemBuilder: (BuildContext ctxt, int index) {
                return Stack(
                  children: [
                    Padding(
                      padding: lang == 'en'
                          ? const EdgeInsets.only(right: 10)
                          : const EdgeInsets.only(left: 10),
                      child: Stack(children: [
                        ClipRRect(
                          borderRadius: BorderRadius.circular(5),
                          child: categorylist[index].images != null
                              ? MainCachedImage(
                                  categorylist[index].images!,
                                  height: 230,
                                  width: kIsWeb
                                      ? baseWebWidth
                                      : MediaQuery.of(context).size.width *
                                          0.35,
                                  fit: BoxFit.fitHeight,
                                )
                              : const SizedBox(),
                        ),
                        GestureDetector(
                            onTap: () {
                              Navigator.of(context).push(MaterialPageRoute(
                                  builder: (BuildContext context) =>
                                      VideoViewWidget(
                                        video: categorylist[index],
                                      )));
                            },
                            child: Container(
                              height: 230,
                              width: kIsWeb
                                  ? baseWebWidth
                                  : MediaQuery.of(context).size.width * 0.35,
                              decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(5),
                                  gradient: LinearGradient(
                                    end: Alignment.bottomCenter,
                                    begin: Alignment.center,
                                    colors: <Color>[
                                      Colors.transparent,
                                      //  Colors.white.withOpacity(0.5),
                                      Colors.black.withOpacity(0.7)
                                    ],
                                  )),
                            )),
                        GestureDetector(
                            onTap: () {
                              categorylist[index].category?.id ==
                                      AppConstants.holidayHomesId
                                  ? Navigator.of(context).push(
                                      MaterialPageRoute(
                                          builder: (BuildContext context) =>
                                              HoldayHomeDetails(
                                                video: categorylist[index],
                                              )))
                                  : Navigator.of(context).push(
                                      MaterialPageRoute(
                                          builder: (BuildContext context) =>
                                              VideoViewWidget(
                                                video: categorylist[index],
                                              )));
                              // Navigator.of(context).push(MaterialPageRoute(
                              //     builder: (BuildContext context) =>
                              //         VideoViewWidget(
                              //           video: categorylist[index],
                              //         )));
                            },
                            child: Container(
                              height: 230,
                              width: kIsWeb
                                  ? baseWebWidth
                                  : MediaQuery.of(context).size.width * 0.35,
                              decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(5),
                                  gradient: LinearGradient(
                                    end: Alignment.topCenter,
                                    begin: Alignment.center,
                                    colors: <Color>[
                                      Colors.transparent,
                                      //  Colors.white.withOpacity(0.5),
                                      Colors.black.withOpacity(0.4)
                                    ],
                                  )),
                            )),
                        lang == 'ar'
                            ? Positioned(
                                bottom: 12,
                                right: 10,
                                child: SizedBox(
                                    width: MediaQuery.of(context).size.width *
                                        0.25,
                                    child: Text(
                                      categorylist[index].name!,
                                      style: const TextStyle(
                                          color: Colors.white, fontSize: 15),
                                      maxLines: 1,
                                      overflow: TextOverflow.ellipsis,
                                    )),
                              )
                            : Positioned(
                                bottom: 12,
                                left: 10,
                                child: SizedBox(
                                    width: MediaQuery.of(context).size.width *
                                        0.25,
                                    child: Text(
                                      categorylist[index].name!,
                                      style: const TextStyle(
                                          color: Colors.white, fontSize: 15),
                                      maxLines: 1,
                                      overflow: TextOverflow.ellipsis,
                                    )),
                              ),
                        Positioned(
                            top: 17,
                            left: lang == 'ar' ? null : 10,
                            right: lang == 'ar' ? 10 : null,
                            child: SizedBox(
                              width: MediaQuery.of(context).size.width * 0.25,
                              child: Text(
                                categorylist[index].locationName ?? '',
                                style: const TextStyle(
                                    color: Colors.white, fontSize: 13),
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ))
                      ]),
                    )
                  ],
                );
              },
              itemCount: categorylist.length,
            ))
        : nodatafound(
            AppLocalizations.of(context).translate('No data to show'));
  }
}
