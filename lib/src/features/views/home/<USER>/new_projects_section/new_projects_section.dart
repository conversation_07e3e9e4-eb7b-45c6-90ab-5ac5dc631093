import 'package:flutter/material.dart';
import 'package:page/src/core/config/constants.dart';
import 'package:page/src/core/localization/app_localizations.dart';
import 'package:page/src/features/views/home/<USER>/new_projects_section/horizontal_project_card.dart';
import 'package:page/src/features/views/projects/projects_screen.dart';

import '../../../../../core/shared_widgets/shared_widgets.dart';
import '../../../splash_screen/services/splash_services.dart';
import '../../../story/widgets/reel_widgets/reel_details.dart';

class NewProjectsSection extends StatefulWidget {
  const NewProjectsSection({
    super.key,
  });

  @override
  State<NewProjectsSection> createState() => _NewProjectsSectionState();
}

class _NewProjectsSectionState extends State<NewProjectsSection> {
  @override
  void initState() {
    super.initState();
    currencyController.getcuurentcurrency(context).then(
      (value) {
        setState(() {});
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Section header with title and "See More" button
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 20),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                AppLocalizations.of(context).translate('New Projects'),
                style: const TextStyle(
                  color: Color(0xff51565B),
                  fontSize: 17,
                  fontWeight: FontWeight.bold,
                ),
              ),
              GestureDetector(
                onTap: () {
                  Navigator.of(context).push(
                    MaterialPageRoute(
                      builder: (context) => const NewProjectScreen(
                        fromBottomNav: false,
                      ),
                    ),
                  );
                },
                child: Text(
                  AppLocalizations.of(context).translate('See More'),
                  style: TextStyle(
                    color: primaryColor,
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
        ),
        const SizedBox(height: 16),
        // Horizontal scrolling projects with 2 rows
        newProjects.isNotEmpty
            ? SizedBox(
                height: newProjects.length == 1
                    ? 220
                    : 460, // Fixed height for 2 rows
                child: ListView.builder(
                  padding: const EdgeInsets.symmetric(horizontal: 20),
                  scrollDirection: Axis.horizontal,
                  itemCount: (newProjects.length / 2).ceil(),
                  itemBuilder: (context, columnIndex) {
                    final hasFirstItem = columnIndex * 2 < newProjects.length;
                    final hasSecondItem =
                        columnIndex * 2 + 1 < newProjects.length;

                    return SizedBox(
                      width: 300, // Fixed width for each column
                      child: Column(
                        children: [
                          // First row item
                          if (hasFirstItem)
                            SizedBox(
                              height: 220, // Fixed height for each card
                              child: HorizontalProjectCard(
                                project: newProjects[columnIndex * 2],
                              ),
                            ),
                          if (hasFirstItem && hasSecondItem)
                            const SizedBox(height: 16),
                          // Second row item
                          if (hasSecondItem)
                            SizedBox(
                              height: 220, // Fixed height for each card
                              child: HorizontalProjectCard(
                                project: newProjects[columnIndex * 2 + 1],
                              ),
                            ),
                        ],
                      ),
                    );
                  },
                ),
              )
            : Padding(
                padding: const EdgeInsets.symmetric(horizontal: 20),
                child: nodatafound(
                  AppLocalizations.of(context).translate('No Projects to show'),
                ),
              ),
      ],
    );
  }
}
