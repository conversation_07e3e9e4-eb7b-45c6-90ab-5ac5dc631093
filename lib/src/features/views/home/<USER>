import 'package:flutter/material.dart';
import 'package:page/src/core/shared_widgets/main_featured_container.dart';
import 'package:page/src/features/controllers/auth_controller.dart';
import 'package:page/src/features/views/holiday/holiday.dart';
import 'package:page/src/features/views/home/<USER>/home_categories_section.dart';
import 'package:page/src/features/views/home/<USER>/new_projects_section/new_home_projects_section.dart';
import 'package:page/src/features/views/splash_screen/services/splash_services.dart';
import 'package:video_player/video_player.dart';

import '../../../core/localization/app_localizations.dart';
import '../../../core/shared_widgets/bottom_navgation_bar.dart';
import '../../../core/utils/dynamic_links.dart';
import 'widgets/recently_added_section/recently_added_section.dart';

Map<int?, VideoPlayerController?> videoPlayerController = {};
// ValueNotifier<Map<int?, VideoPlayerController?>> videoPlayerControllerN =
//     ValueNotifier({});
Map<int?, VideoPlayerController?> reelsVideoController = {};

class Home extends StatefulWidget {
  const Home({super.key});

  @override
  _HomeState createState() => _HomeState();
}

class _HomeState extends State<Home> {
  int code = 0;
  String msg = 'loading';

  AuthController authController = AuthController();

  final splashServices = SplashServices();

  @override
  void initState() {
    super.initState();
    // splashServices.initControllersForHomeData();
    DynamicLinkHandler.initDynamicLink();
  }

  // @override
  // void dispose() {
  //   SplashServices().disposeHomeVideoControllers();
  //   super.dispose();
  // }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
        top: true,
        child: Scaffold(
          body: SingleChildScrollView(
            child:
                Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
              //! Top Section
              const HomeCategoriesTopSection(),

              // code == 0 && msg == 'loading'
              //     ? buildLoadingWidget()
              //     : code == 1
              //         ?
              Column(children: [
                //! Featured Videos Section
                MainFeaturedContainer(
                  label: 'Ready Property',
                  child: _MainHomeTile(
                      label: 'Ready Property',
                      section: HomeRecentlyAdded(
                        categorylist: categoryList,
                      )
                      // HomeFeaturedVideos(
                      //     featuredvideos: featuredVideos,
                      //     categorylist: categoryList)
                      ),
                ),

                const SizedBox(
                  height: 20,
                ),

                //! New Projects Section
                const NewHomeProjectsSection(),

                const SizedBox(
                  height: 20,
                ),

                HolidayHomeWidgetSection(
                  fromHome: true,
                  propertiesVideos: properties,
                ),

                const SizedBox(
                  height: 20,
                ),
              ])
              // : buildErrorWidget(msg)
            ]),
          ),
          bottomNavigationBar: CustomBottomNavgationBar(0),
        ));
  }
}

class _MainHomeTile extends StatelessWidget {
  const _MainHomeTile({
    Key? key,
    required this.label,
    required this.section,
    this.icon,
  }) : super(key: key);

  final String label;
  final Widget section;
  final Widget? icon;

  @override
  Widget build(BuildContext context) {
    final isFeatured = label == 'Ready Property';
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.only(left: 20, right: 20),
          child: Row(
            children: [
              if (icon != null) ...[
                icon!,
                const SizedBox(
                  width: 10,
                ),
              ],
              if (!isFeatured)
                Text(
                  AppLocalizations.of(context).translate(label),
                  style: const TextStyle(
                      color: Color(0xff51565B),
                      fontSize: 17,
                      fontWeight: FontWeight.bold),
                ),
            ],
          ),
        ),
        if (!isFeatured)
          const SizedBox(
            height: 20,
          ),
        section,
      ],
    );
  }
}
