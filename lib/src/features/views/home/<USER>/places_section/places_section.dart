import 'package:flutter/material.dart';
import 'package:page/src/core/utils/main_cached_image.dart';
import 'package:provider/provider.dart';

import '../../../../../core/localization/app_language.dart';
import '../../../../../core/localization/app_localizations.dart';
import '../../../../../core/shared_widgets/shared_widgets.dart';
import '../../../../controllers/auth_controller.dart';
import '../../../../models/video_model.dart';
import '../../../ad_details/video_widget.dart';

class HomePlacesSection extends StatelessWidget {
  final List<VideoModel> places;

  const HomePlacesSection({
    Key? key,
    required this.places,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    AuthController authController = AuthController();

    var lang =
        Provider.of<AppLanguage>(context, listen: false).appLocal.languageCode;

    return places.isNotEmpty
        ? SizedBox(
            height: 230,
            width: MediaQuery.of(context).size.width,
            child: ListView.builder(
              padding: const EdgeInsets.symmetric(horizontal: 20),
              shrinkWrap: true,
              physics: const ClampingScrollPhysics(),
              scrollDirection: Axis.horizontal,
              itemBuilder: (BuildContext ctxt, int index) {
                return Stack(
                  children: [
                    Padding(
                      padding: lang == 'en'
                          ? const EdgeInsets.only(right: 10)
                          : const EdgeInsets.only(left: 10),
                      child: Stack(children: [
                        ClipRRect(
                          borderRadius: BorderRadius.circular(5),
                          child: places[index].images != null
                              ? MainCachedImage(
                                  places[index].images!,
                                  height: 230,
                                  width:
                                      MediaQuery.of(context).size.width * 0.35,
                                  fit: BoxFit.fitHeight,
                                )
                              : const SizedBox(),
                        ),
                        GestureDetector(
                            onTap: () {
                              Navigator.of(context).push(
                                MaterialPageRoute(
                                  builder: (BuildContext context) =>
                                      VideoViewWidget(
                                    video: places[index],
                                  ),
                                ),
                              );
                            },
                            child: Container(
                              height: 230,
                              width: MediaQuery.of(context).size.width * 0.35,
                              decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(5),
                                  gradient: LinearGradient(
                                    end: Alignment.bottomCenter,
                                    begin: Alignment.center,
                                    colors: <Color>[
                                      Colors.transparent,
                                      //  Colors.white.withOpacity(0.5),
                                      Colors.black.withOpacity(0.7)
                                    ],
                                  )),
                            )),
                        // Positioned(
                        //     top: 10,
                        //     right: 10,
                        //     child: InkWell(
                        //       onTap: () {
                        //         authController.isLogged == true
                        //             ? listmyplan(context, "maincategory",
                        //                 places[index].id!)
                        //             : snackbar(AppLocalizations.of(context)
                        //                 .translate('Please login first'));
                        //       },
                        //       child: Container(
                        //           height: 25,
                        //           width: 25,
                        //           decoration: const BoxDecoration(
                        //               color: Color(0xff4d5e72),
                        //               borderRadius: BorderRadius.all(
                        //                   Radius.circular(30))),
                        //           child: const Center(
                        //               child: Icon(Icons.add,
                        //                   color: Colors.white, size: 18))),
                        //     )),
                        lang == 'ar'
                            ? Positioned(
                                bottom: 12,
                                right: 10,
                                child: SizedBox(
                                    width: MediaQuery.of(context).size.width *
                                        0.25,
                                    child: Text(
                                      places[index].name!,
                                      style: const TextStyle(
                                          color: Colors.white, fontSize: 15),
                                      maxLines: 1,
                                      overflow: TextOverflow.ellipsis,
                                    )),
                              )
                            : Positioned(
                                bottom: 12,
                                left: 10,
                                child: SizedBox(
                                    width: MediaQuery.of(context).size.width *
                                        0.25,
                                    child: Text(
                                      places[index].name!,
                                      style: const TextStyle(
                                          color: Colors.white, fontSize: 15),
                                      maxLines: 1,
                                      overflow: TextOverflow.ellipsis,
                                    )),
                              )
                      ]),
                    )
                  ],
                );
              },
              itemCount: places.length,
            ))
        : nodatafound(
            AppLocalizations.of(context).translate('No data to show'));
  }
}
