import 'package:flutter/material.dart';
import 'package:page/src/core/config/constants.dart';
import 'package:page/src/core/utils/main_cached_image.dart';
import 'package:page/src/features/views/splash_screen/services/splash_services.dart';
import 'package:provider/provider.dart';

import '../../../../core/localization/app_language.dart';
import '../../../../core/localization/app_localizations.dart';
import '../../holiday/holiday.dart';

class HomeCategoriesTopSection extends StatefulWidget {
  const HomeCategoriesTopSection({super.key});

  @override
  State<HomeCategoriesTopSection> createState() =>
      _HomeCategoriesTopSectionState();
}

class _HomeCategoriesTopSectionState extends State<HomeCategoriesTopSection> {
  @override
  Widget build(BuildContext context) {
    final Widget svg1 = SizedBox(
        width: 60,
        height: 65,
        child: Image.asset(
          'assets/images/home_icon.png',
          // 'assets/image00002.png',
          fit: BoxFit.cover,
        ));

    return Stack(
      children: [
        Container(
            width: MediaQuery.of(context).size.width,
            height: 210,
            decoration: BoxDecoration(
              color: primaryColor.withOpacity(0.25),
              border: Border(
                bottom: BorderSide(color: primaryColor, width: .6),
              ),
            ),
            // const Color(0xFF27b4a8)
            padding: const EdgeInsets.only(
              left: 20,
              right: 20,
              top: 10,
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          AppLocalizations.of(context).translate('hellothere'),
                          style: const TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        const SizedBox(height: 5),
                        Text(
                          AppLocalizations.of(context)
                              .translate('ExploreNewAwesomePlaces'),
                          style: const TextStyle(
                            fontSize: 12,
                          ),
                        ),
                      ],
                    ),
                    const Spacer(),
                    svg1
                  ],
                )
              ],
            )),

        //! Categories Section
        Positioned(
          top: 90,
          left: 0,
          right: 0,
          child: _CategoriesSection(),
        ),
      ],
    );
  }
}

class _CategoriesSection extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    var lang =
        Provider.of<AppLanguage>(context, listen: false).appLocal.languageCode;

    Widget categoryCard(BuildContext context,
        {required String icon,
        required String name,
        required String nameAr,
        required Function onTap}) {
      return GestureDetector(
          onTap: () {
            onTap();
          },
          child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 5),
              child: Container(
                  decoration: BoxDecoration(
                      color: Colors.white,
                      boxShadow: [
                        BoxShadow(
                            color: Colors.grey.withOpacity(0.2),
                            spreadRadius: 1,
                            blurRadius: 5,
                            offset: const Offset(0, 3))
                      ],
                      borderRadius: BorderRadius.circular(12)),
                  height: 100,
                  width: MediaQuery.of(context).size.width * 0.28,
                  // width: MediaQuery.of(context).size.width * 0.32,
                  padding: const EdgeInsets.only(top: 10, left: 8, right: 8),
                  child: Column(children: [
                    Center(
                        child: Center(
                            child: Center(
                                child: ClipRRect(
                                    borderRadius: BorderRadius.circular(100),
                                    child: MainCachedImage(
                                      icon,
                                      height: 50,
                                      width: 50,
                                      fit: BoxFit.cover,
                                    ))))),
                    const SizedBox(
                      height: 5,
                    ),
                    FittedBox(
                      fit: BoxFit.scaleDown,
                      child: Center(
                          child: lang == 'en'
                              ? Text(
                                  name,
                                  style: const TextStyle(
                                    fontSize: 15,
                                  ),
                                )
                              : Text(
                                  nameAr,
                                  style: const TextStyle(
                                    fontSize: 15,
                                  ),
                                )),
                    )
                  ]))));
    }

    return SizedBox(
      height: 100,
      child: ListView(
          padding: const EdgeInsets.symmetric(horizontal: 10),
          scrollDirection: Axis.horizontal,
          children: allTypes
              .where((element) =>
                  element.categoryIds.contains(AppConstants.propertiesId))
              .toList()
              .map(
                (e) => categoryCard(
                  context,
                  icon: e.image ?? '',
                  name: e.name ?? '',
                  nameAr: e.name ?? '',
                  onTap: () {
                    Navigator.push(context,
                        MaterialPageRoute(builder: (context) {
                      return HolidayHomePage(
                        type: e,
                      );
                    }));
                  },
                ),
              )
              .toList()
          // [
          //   categoryCard(
          //     context,
          //     icon: 'assets/home2.png',
          //     name: "Holiday Homes",
          //     nameAr: "بيوت العطلات",
          //     onTap: () {
          //       Navigator.push(context, MaterialPageRoute(builder: (context) {
          //         return HolidayHomePage();
          //       }));
          //     },
          //   ),
          //   categoryCard(
          //     context,
          //     icon: 'assets/icons8-surf.png',
          //     name: "Activities",
          //     nameAr: "نشاطات",
          //     onTap: () {
          //       Navigator.push(context, MaterialPageRoute(builder: (context) {
          //         return const Activites();
          //       }));
          //     },
          //   ),
          //   categoryCard(
          //     context,
          //     icon: 'assets/Chalets.png',
          //     name: "Chalets",
          //     nameAr: "شاليهات",
          //     onTap: () {
          //       Navigator.push(context, MaterialPageRoute(builder: (context) {
          //         return const Chalets();
          //       }));
          //     },
          //   ),
          //   if (kIsWeb && MediaQuery.sizeOf(context).width > 500)
          //     const Spacer(
          //       flex: 2,
          //     ),
          // ],
          ),
    );
  }
}

// return SizedBox(
// height: 130,
// width: MediaQuery.of(context).size.width,
// child: ListView.builder(
// shrinkWrap: true,
// physics: const ClampingScrollPhysics(),
// scrollDirection: Axis.horizontal,
// itemBuilder: (BuildContext ctxt, int index) {
// return GestureDetector(
// onTap: () {
// if (_categories[index]['name'] == 'Hotels') {
// Navigator.of(context).push(
// PageRouteBuilder(pageBuilder: (_, __, ___) => Hotels()));
// }
// if (_categories[index]['name'] == 'Restaurants') {
// Navigator.push(context, MaterialPageRoute(builder: (context) {
// return const Restaurants();
// }));
// }
// if (_categories[index]['name'] == 'Destinations') {
// Navigator.push(context, MaterialPageRoute(builder: (context) {
// return Places();
// }));
// }
// if (_categories[index]['name'] == 'Activities') {
// Navigator.push(context, MaterialPageRoute(builder: (context) {
// return const Activites();
// }));
// }
// if (_categories[index]['name'] == 'Coffee Shops') {
// Navigator.push(context, MaterialPageRoute(builder: (context) {
// return Shops();
// }));
// }
// if (_categories[index]['name'] == 'Holiday Home') {
// Navigator.push(context, MaterialPageRoute(builder: (context) {
// return HolidayName();
// }));
// }
// if (_categories[index]['name'] == 'Properties') {
// Navigator.push(context, MaterialPageRoute(builder: (context) {
// return Properties();
// }));
// }
// if (_categories[index]['name'] == 'Chalets') {
// Navigator.push(context, MaterialPageRoute(builder: (context) {
// return Chalets();
// }));
// }
// // if (_categories[index]['name'] == 'Car Rental') {
// //   Navigator.push(context,
// //       MaterialPageRoute(builder: (context) {
// //     return const CarRental();
// //   }));
// // }
// },
// child: Padding(
// padding: lang == 'en'
// ? const EdgeInsets.only(right: 10)
//     : const EdgeInsets.only(left: 10),
// child: Row(children: [
// Column(children: [
// Container(
// decoration: BoxDecoration(
// color: const Color(0xff1C2127),
// borderRadius: BorderRadius.circular(5)),
// height: 100,
// width: MediaQuery.of(context).size.width * 0.32,
// padding: const EdgeInsets.only(top: 10),
// child: Column(children: [
// Center(
// child: Center(
// child: Container(
// height: 50,
// width: 50,
// decoration: BoxDecoration(
// color: const Color(0xff343c45),
// borderRadius:
// BorderRadius.circular(120)),
// child: Center(
// child: ClipRRect(
// borderRadius:
// BorderRadius.circular(5),
// child: Image.asset(
// _categories[index]['icon']!,
// color: Colors.white,
// height: 30,
// width: 20,
// )))))),
// const SizedBox(
// height: 10,
// ),
// FittedBox(
// fit: BoxFit.scaleDown,
// child: Center(
// child: lang == 'en'
// ? Text(
// _categories[index]['name']!,
// style: const TextStyle(
// color: Colors.white,
// fontSize: 15),
// )
//     : Text(
// _categories[index]['name_ar']!,
// style: const TextStyle(
// color: Colors.white,
// fontSize: 15),
// )),
// )
// ])),
// const SizedBox(
// height: 20,
// ),
// ])
// ])));
// },
// itemCount: _categories.length,
// ),
// );
//
// List<Map<String, String>> _categories = [
// {
// 'icon': 'assets/home2.png',
// 'name': "Holiday Homes",
// "name_ar": "بيوت العطلات"
// },
// {'icon': 'assets/icons8-surf.png', 'name': "Activities", "name_ar": "نشاطات"},
// {'icon': 'assets/Chalets.png', 'name': "Chalets", "name_ar": "شاليهات"},
// // {
// //   'icon': 'assets/noun_Hotel_3321668.png',
// //   'name': "Hotels",
// //   "name_ar": "فنادق"
// // },
// // {
// //   'icon': 'assets/restaurant_icon.png',
// //   'name': "Restaurants",
// //   "name_ar": "مطاعم"
// // },
// // {
// //   'icon': 'assets/icons/coffeeshop.png',
// //   'name': "Coffee Shops",
// //   "name_ar": "كوفي شوب"
// // },
// //
// // // {
// // //   'icon': 'assets/icons8-car.png',
// // //   'name': "Car Rental",
// // //   "name_ar": "تاجير سيارات"
// // // },
// // {
// //   'icon': 'assets/icons/destination.png',
// //   'name': "Destinations",
// //   "name_ar": "الوجهات السياحية"
// // },
// // {
// //   'icon': 'assets/icons/home.png',
// //   'name': "Properties",
// //   "name_ar": "العقارات"
// // },
// ];
//
//
