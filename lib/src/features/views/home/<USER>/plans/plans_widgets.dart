import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:page/src/features/views/home/<USER>/plans/create_plan_plan_bottom_sheet.dart';

import '../../../../../core/localization/app_localizations.dart';
import '../../../../../core/response/plan_response.dart';
import '../../../../../core/shared_widgets/shared_widgets.dart';
import '../../../../bloc/plan_bloc.dart';
import '../../../../models/plans.dart';
import 'plan_details_bottom_sheet.dart';

void listmyplan(BuildContext context, String type, int itemid) {
  showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) {
        planbloc.getcategory(0, 20);

        return StreamBuilder<PlanResponse>(
          stream: planbloc.subject.stream,
          builder: (context, AsyncSnapshot<PlanResponse> snapshot) {
            if (snapshot.hasData) {
              if (snapshot.hasError) {
                return buildErrorWidget(snapshot.data!.error);
              }
              return Container(
                  child: Padding(
                      padding: EdgeInsets.only(
                          bottom: MediaQuery.of(context).viewInsets.bottom),
                      child: Container(
                        height: MediaQuery.of(context).size.height * 0.50,
                        decoration: BoxDecoration(
                            color: const Color(0xffF5F6F7),
                            borderRadius: const BorderRadius.only(
                                topLeft: Radius.circular(25.0),
                                topRight: Radius.circular(25.0)),
                            border:
                                Border.all(color: Colors.black, width: 1.0)),
                        child: SingleChildScrollView(
                            child: Column(
                          children: [
                            const SizedBox(
                              height: 10,
                            ),
                            Container(
                                height: 5,
                                width: 50,
                                color: const Color(0xffD2D4D6)),
                            const SizedBox(
                              height: 20,
                            ),
                            Center(
                                child: Text(
                              AppLocalizations.of(context)
                                  .translate('addtoplan'),
                              style:
                                  const TextStyle(fontWeight: FontWeight.bold),
                            )),
                            Container(
                              padding: const EdgeInsets.all(15),
                              child: Container(
                                decoration: BoxDecoration(
                                    color: Colors.white,
                                    borderRadius: BorderRadius.circular(10)),
                                child: Container(
                                  padding: const EdgeInsets.all(15),
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      const SizedBox(
                                        height: 20,
                                      ),
                                      snapshot.data!.plans.isNotEmpty
                                          ? _PlansList(
                                              plans: snapshot.data!.plans,
                                              type: type,
                                              itemid: itemid)
                                          : nodatafound(AppLocalizations.of(
                                                  context)
                                              .translate('No plans to show')),

                                      20.verticalSpace,

                                      //! Create Plan Button
                                      _CreatePlanButton(
                                        type: type,
                                        itemid: itemid,
                                      ),

                                      20.verticalSpace,
                                    ],
                                  ),
                                ),
                              ),
                            ),
                          ],
                        )),
                      )));
            } else if (snapshot.hasError) {
              return buildErrorWidget(snapshot.error!.toString());
            } else {
              return const SizedBox();
            }
          },
        );
      });
}

class _PlansList extends StatelessWidget {
  const _PlansList({
    Key? key,
    required this.plans,
    required this.type,
    required this.itemid,
  }) : super(key: key);

  final List<Plans> plans;
  final String type;
  final int itemid;

  @override
  Widget build(BuildContext context) {
    log('Idddsdsdds33232222 ${plans}');

    return ListView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      // scrollDirection: Axis.vertical,
      itemBuilder: (BuildContext ctxt, int index) {
        var plan = plans[index];

        var days = (DateTime.tryParse(plan.enddate!) ?? DateTime.now())
            .difference(DateTime.tryParse(plan.startdate!) ?? DateTime.now())
            .inDays;
        return GestureDetector(
          onTap: () {
            listmyplandetails(context, plan.id!, type, itemid, "no");
          },
          child: Container(
            margin: const EdgeInsets.only(top: 10),
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(5),
                color: const Color(0xffF1F1F1)),
            child: Row(
              children: [
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      plan.planName ?? '',
                      style: const TextStyle(
                          fontWeight: FontWeight.bold, fontSize: 12),
                    ),
                    Text(
                        AppLocalizations.of(context).translate('from') +
                            " " +
                            plan.startdate.toString() +
                            " " +
                            AppLocalizations.of(context).translate('to') +
                            " " +
                            plan.enddate.toString() +
                            " " +
                            '(' +
                            days.toString() +
                            ')' +
                            AppLocalizations.of(context).translate('days'),
                        style: const TextStyle(fontSize: 12))
                  ],
                ),
                const Spacer(),
                const Icon(Icons.add)
              ],
            ),
          ),
        );
      },
      itemCount: plans.length,
    );
  }
}

class _CreatePlanButton extends StatelessWidget {
  final String? type;
  final int? itemid;

  const _CreatePlanButton({Key? key, this.type, this.itemid}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Center(
        child: GestureDetector(
            onTap: () => createPlan(context, itemId: itemid, type: type),
            child: Container(
              height: 50,
              width: MediaQuery.of(context).size.width,
              decoration: BoxDecoration(
                  color: const Color(0xFF27b4a8),
                  borderRadius: BorderRadius.circular(10)),
              child: Container(
                  padding: const EdgeInsets.all(10),
                  child: Center(
                      child: Text(
                    AppLocalizations.of(context).translate('Create New Plan'),
                    style: const TextStyle(color: Colors.white),
                  ))),
            )));
  }
}
