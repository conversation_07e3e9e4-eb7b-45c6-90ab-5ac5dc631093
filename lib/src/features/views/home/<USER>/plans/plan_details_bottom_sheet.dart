import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import '../../../../../core/localization/app_localizations.dart';
import '../../../../../core/response/plan_response.dart';
import '../../../../../core/shared_widgets/shared_widgets.dart';
import '../../../../bloc/plan_bloc.dart';

void listmyplandetails(
    BuildContext context, int id, String type, int itemid, String isdetails,
    [String? planName]) {
  log('asdsaddfdfdffasdsd');
  showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) {
        // planbloc.getplandetails(id);
        planbloc.getplanuserdates(id);

        return StreamBuilder<PlanItemsResponse>(
          stream: planbloc.subject6.stream,
          builder: (context, AsyncSnapshot<PlanItemsResponse> snapshot) {
            if (snapshot.hasData) {
              if (snapshot.data!.error.isNotEmpty) {
                return buildErrorWidget(snapshot.data!.error);
              }

              return Padding(
                  padding: EdgeInsets.only(
                      bottom: MediaQuery.of(context).viewInsets.bottom),
                  child: Container(
                    height: MediaQuery.of(context).size.height * 0.60,
                    decoration: BoxDecoration(
                        color: const Color(0xffF5F6F7),
                        borderRadius: const BorderRadius.only(
                            topLeft: Radius.circular(25.0),
                            topRight: Radius.circular(25.0)),
                        border: Border.all(color: Colors.black, width: 1.0)),
                    child: SingleChildScrollView(
                        child: Column(
                      children: [
                        const SizedBox(
                          height: 10,
                        ),
                        Container(
                            height: 5,
                            width: 50,
                            color: const Color(0xffD2D4D6)),
                        const SizedBox(
                          height: 20,
                        ),
                        Center(
                            child: Text(
                          planName ?? '',
                          // snapshot.data!.plans?.first?.planName ?? '',
                          style: const TextStyle(fontWeight: FontWeight.bold),
                        )),
                        Container(
                          padding: const EdgeInsets.all(15),
                          child: Container(
                              decoration: BoxDecoration(
                                  color: Colors.white,
                                  borderRadius: BorderRadius.circular(10)),
                              child: Container(
                                padding: const EdgeInsets.all(15),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    20.verticalSpace,

                                    //! Plan Details
                                    _PlainDetails(
                                      snapshot: snapshot,
                                      type: type,
                                      itemid: itemid,
                                      id: id,
                                    ),

                                    20.verticalSpace,

                                    //! Plan Actions
                                    _PlanActions(
                                      snapshot: snapshot,
                                      isDetails: isdetails,
                                    ),
                                    const SizedBox(height: 20),
                                  ],
                                ),
                              )),
                        ),
                      ],
                    )),
                  ));
            } else if (snapshot.hasError) {
              return buildErrorWidget(snapshot.error.toString());
            } else {
              return Container(
                // height: MediaQuery.of(context)
                //     .size
                //     .height,
                child: buildLoadingWidget(),
              );
            }
          },
        );

        // return StreamBuilder<PlanDetailsResponse>(
        //   stream: planbloc.subject2.stream,
        //   builder: (context, AsyncSnapshot<PlanDetailsResponse> snapshot) {
        //     if (snapshot.hasData) {
        //       if (snapshot.hasError) {
        //         return buildErrorWidget(snapshot.data!.error);
        //       }
        //       return Padding(
        //           padding: EdgeInsets.only(
        //               bottom: MediaQuery.of(context).viewInsets.bottom),
        //           child: Container(
        //             height: MediaQuery.of(context).size.height * 0.60,
        //             decoration: BoxDecoration(
        //                 color: const Color(0xffF5F6F7),
        //                 borderRadius: const BorderRadius.only(
        //                     topLeft: Radius.circular(25.0),
        //                     topRight: Radius.circular(25.0)),
        //                 border: Border.all(color: Colors.black, width: 1.0)),
        //             child: SingleChildScrollView(
        //                 child: Column(
        //               children: [
        //                 const SizedBox(
        //                   height: 10,
        //                 ),
        //                 Container(
        //                     height: 5,
        //                     width: 50,
        //                     color: const Color(0xffD2D4D6)),
        //                 const SizedBox(
        //                   height: 20,
        //                 ),
        //                 Center(
        //                     child: Text(
        //                   snapshot.data!.planname,
        //                   style: const TextStyle(fontWeight: FontWeight.bold),
        //                 )),
        //                 Container(
        //                   padding: const EdgeInsets.all(15),
        //                   child: Container(
        //                       decoration: BoxDecoration(
        //                           color: Colors.white,
        //                           borderRadius: BorderRadius.circular(10)),
        //                       child: Container(
        //                         padding: const EdgeInsets.all(15),
        //                         child: Column(
        //                           crossAxisAlignment: CrossAxisAlignment.start,
        //                           children: [
        //                             20.verticalSpace,
        //
        //                             //! Plan Details
        //                             _PlainDetails(
        //                               snapshot: snapshot,
        //                               type: type,
        //                               itemid: itemid,
        //                               id: id,
        //                             ),
        //
        //                             20.verticalSpace,
        //
        //                             //! Plan Actions
        //                             _PlanActions(
        //                               snapshot: snapshot,
        //                               isDetails: isdetails,
        //                             ),
        //                             const SizedBox(height: 20),
        //                           ],
        //                         ),
        //                       )),
        //                 ),
        //               ],
        //             )),
        //           ));
        //     } else if (snapshot.hasError) {
        //       return buildErrorWidget(snapshot.error!.toString());
        //     } else {
        //       return Container(
        //           // height: MediaQuery.of(context)
        //           //     .size
        //           //     .height,
        //           child: buildLoadingWidget());
        //     }
        //   },
        // );
      });
}

class _PlainDetails extends StatelessWidget {
  final AsyncSnapshot<PlanItemsResponse> snapshot;
  final String type;
  final int itemid;
  final int id;

  const _PlainDetails(
      {Key? key,
      required this.snapshot,
      required this.type,
      required this.itemid,
      required this.id})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return ListView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      scrollDirection: Axis.vertical,
      itemBuilder: (BuildContext ctxt, int index) {
        return GestureDetector(
          onTap: () {},
          child: Container(
            margin: const EdgeInsets.only(top: 10),
            padding: const EdgeInsets.all(10),
            decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(5),
                color: const Color(0xffF1F1F1)),
            child: Row(
              children: [
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '${AppLocalizations.of(context).translate('day')}${snapshot.data!.dates[index].day}(${snapshot.data!.dates[index].date!})',
                      style: const TextStyle(fontSize: 14),
                    ),
                  ],
                ),
                const Spacer(),
                // InkWell(
                //     onTap: () async {
                //       progrsss(context);
                //       pr!.show();
                //       GeneralResponse sucessinformation =
                //           await Api.additemtoplan(
                //               snapshot.data!.dates[index].date,
                //               type,
                //               itemid,
                //               id);
                //
                //       print(sucessinformation.code);
                //       if (sucessinformation.code == "1") {
                //         await planbloc.getplanudetailsseritems(id, "");
                //
                //         pr!.hide();
                //
                //         Navigator.pop(context);
                //
                //         Navigator.of(context).push(
                //           MaterialPageRoute(
                //             builder: (BuildContext context) => MyplansDetails(
                //               // data.plans[index],
                //               // data.plans[index].name!,
                //               '', id,
                //             ),
                //           ),
                //         );
                //         snackbar2(AppLocalizations.of(context)
                //             .translate('Item added successfuly'));
                //       } else {
                //         pr!.hide();
                //
                //         snackbar(AppLocalizations.of(context).translate(
                //             'Something went wrong, please try again later'));
                //       }
                //     },
                //     child: const Icon(Icons.add, size: 20))
              ],
            ),
          ),
        );
      },
      itemCount: snapshot.data!.dates.length,
    );
  }
}

class _PlanActions extends StatelessWidget {
  final AsyncSnapshot<PlanItemsResponse> snapshot;
  final String isDetails;

  const _PlanActions(
      {Key? key, required this.snapshot, required this.isDetails})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Center(
        child: GestureDetector(
            onTap: () async {
              Get.showSnackbar(GetSnackBar(
                duration: 3.seconds,
                margin: const EdgeInsets.all(8.0),
                backgroundColor: const Color(0xff2AC294),
                borderColor: const Color(0xff2AC294),
                snackStyle: SnackStyle.FLOATING,
                messageText: Row(
                  children: [
                    Container(
                      height: 25,
                      width: 25,
                      decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(25),
                          color: Colors.green[100]),
                      child: const Center(
                        child: Icon(
                          Icons.check,
                          color: Colors.white,
                        ),
                      ),
                    ),
                    const SizedBox(
                      width: 10,
                    ),
                    Text(
                      AppLocalizations.of(context).translate('Added to Plan'),
                      style: const TextStyle(color: Colors.white),
                    )
                  ],
                ),
                borderRadius: 5.0,
                isDismissible: true,
                snackPosition: SnackPosition.TOP,
              ));
              // _submit(rate.toString(), _comment.text);
            },
            child: isDetails == "no"
                ? Container(
                    height: 50,
                    width: MediaQuery.of(context).size.width,
                    decoration: BoxDecoration(
                        color: const Color(0xFF27b4a8),
                        borderRadius: BorderRadius.circular(10)),
                    child: Container(
                        padding: const EdgeInsets.all(10),
                        child: Center(
                            child: Text(
                          AppLocalizations.of(context)
                              .translate('Check Plan Details'),
                          style: const TextStyle(color: Colors.white),
                        ))),
                  )
                : Container()));
  }
}
