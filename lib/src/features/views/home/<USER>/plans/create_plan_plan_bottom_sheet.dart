import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:intl/intl.dart';
import 'package:page/src/features/views/home/<USER>/plans/plan_details_bottom_sheet.dart';

import '../../../../../core/localization/app_localizations.dart';
import '../../../../../core/services/api.dart';
import '../../../../../core/shared_widgets/date_picker.dart';
import '../../../../../core/shared_widgets/shared_widgets.dart';
import '../../../../controllers/plan_controller.dart';
import '../../../plans/my_plans.dart';

TextEditingController planname = TextEditingController();
TextEditingController fromdate = TextEditingController();
TextEditingController todate = TextEditingController();
PlanController planController = PlanController();
final format2 = DateFormat("yyyy-MM-dd");

void createPlan(BuildContext context,
    {bool fromPlan = false, int? itemId, String? type}) {
  showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Padding(
          padding:
              EdgeInsets.only(bottom: MediaQuery.of(context).viewInsets.bottom),
          child: Container(
            height: MediaQuery.of(context).size.height * 0.50,
            decoration: BoxDecoration(
                color: const Color(0xffF5F6F7),
                borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(25.0),
                    topRight: Radius.circular(25.0)),
                border: Border.all(color: Colors.black, width: 1.0)),
            child: SingleChildScrollView(
                child: Column(
              children: [
                10.verticalSpace,
                Container(height: 5, width: 50, color: const Color(0xffD2D4D6)),
                20.verticalSpace,
                Center(
                    child: Text(
                  AppLocalizations.of(context).translate('Createnewplan'),
                  style: const TextStyle(fontWeight: FontWeight.bold),
                )),
                Container(
                  padding: const EdgeInsets.all(15),
                  child: Container(
                    decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(10)),
                    child: Container(
                      padding: const EdgeInsets.all(15),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Center(
                              child: Text(AppLocalizations.of(context).translate(
                                  'We just need your registered email to send you password reset instructions'))),

                          20.verticalSpace,

                          Text(
                            AppLocalizations.of(context).translate('Planname'),
                            style: const TextStyle(
                                fontSize: 16, color: Color(0xff1C2127)),
                          ),
                          10.verticalSpace,

                          Container(
                              height: 40,
                              decoration: BoxDecoration(
                                  color: Colors.white,
                                  borderRadius: BorderRadius.circular(3)),
                              child: Container(
                                  decoration: BoxDecoration(
                                      borderRadius: const BorderRadius.all(
                                          Radius.circular(5)),
                                      border: Border.all(
                                          color: Colors.black12, width: 1.0)),
                                  child: TextFormField(
                                    controller: planname,
                                    decoration: InputDecoration(
                                        contentPadding: const EdgeInsets.only(
                                            left: 20, right: 20, bottom: 10),
                                        hintText: AppLocalizations.of(context)
                                            .translate('Planname'),
                                        hintStyle: const TextStyle(
                                            color: Color(0xffB7B7B7),
                                            fontSize: 16),
                                        border: InputBorder.none),
                                  ))),

                          20.verticalSpace,

                          //! From Date & To Date
                          const _Date(),

                          20.verticalSpace,

                          //! Create Plan
                          _CreatePlan(
                            fromPlan: fromPlan,
                            type: type,
                            itemid: itemId,
                          ),
                          20.verticalSpace,
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            )),
          )));
}

class _Date extends StatelessWidget {
  const _Date({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Expanded(
              child: Text(
                AppLocalizations.of(context).translate('Fromdate'),
                style: const TextStyle(fontSize: 13, color: Color(0xff1C2127)),
              ),
            ),
            Expanded(
              child: Text(
                AppLocalizations.of(context).translate('Todate'),
                style: const TextStyle(fontSize: 13, color: Color(0xff1C2127)),
              ),
            ),
          ],
        ),

        10.verticalSpace,

        //! From Date & To Date
        Row(
          children: [
            Expanded(
              child: BuildDateFieldWidget(
                fromdate,
                format: format2,
              ),
            ),
            const SizedBox(
              width: 10,
            ),
            Expanded(
              child: BuildDateFieldWidget(
                todate,
                format: format2,
              ),
            ),
          ],
        ),
      ],
    );
  }
}

class _CreatePlan extends StatelessWidget {
  final bool fromPlan;
  final String? type;
  final int? itemid;

  const _CreatePlan(
      {Key? key,
      required this.fromPlan,
      required this.type,
      required this.itemid})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Center(
        child: GestureDetector(
            onTap: () async {
              if (planname.text.isEmpty) {
                snackbar(AppLocalizations.of(context)
                    .translate('Please enter the plan name field'));

                return;
              }
              if (fromdate.text.isEmpty) {
                snackbar(AppLocalizations.of(context)
                    .translate('Please enter the start date field'));

                return;
              }
              if (todate.text.isEmpty) {
                snackbar(AppLocalizations.of(context)
                    .translate('Please enter the end date'));

                return;
              }
              int? sucessinformation = await Api.createplan(
                  planname.text, fromdate.text, todate.text);

              if (type != null && sucessinformation != null) {
                //? Add Item to Plan
                listmyplandetails(
                  context,
                  sucessinformation!,
                  type!,
                  itemid!,
                  "no",
                  planname.text,
                );
              } else {
                Navigator.of(context).pushReplacement(MaterialPageRoute(
                    builder: (BuildContext context) => MyPlans()));
              }

              print(sucessinformation);
              if (sucessinformation != null) {
                snackbar2(AppLocalizations.of(context)
                    .translate('Plan creatrd successfuly'));

                _clear();
              } else {
                snackbar(AppLocalizations.of(context)
                    .translate('Something went wrong, please try again later'));
              }
            },
            child: Container(
              height: 50,
              width: MediaQuery.of(context).size.width,
              decoration: BoxDecoration(
                  color: const Color(0xFF27b4a8),
                  borderRadius: BorderRadius.circular(10)),
              child: Container(
                  padding: const EdgeInsets.all(10),
                  child: Center(
                      child: Text(
                    AppLocalizations.of(context).translate('CreatePlan'),
                    style: const TextStyle(color: Colors.white),
                  ))),
            )));
  }
}

void _clear() {
  planname.clear();
  fromdate.clear();
  todate.clear();
}
