// import 'package:flutter/material.dart';
// import 'package:page/src/core/localization/app_localizations.dart';
//
// class HolidayPriceDetailsSection extends StatelessWidget {
//   final Map<String, dynamic> pricerequest;
//   final bool? isPrivateCar;
//   final currencyController;
//
//   const HolidayPriceDetailsSection(
//       {super.key,
//       required this.pricerequest,
//       required this.isPrivateCar,
//       required this.currencyController});
//
//   @override
//   Widget build(BuildContext context) {
//     return Container(
//         padding: const EdgeInsets.all(20),
//         decoration: BoxDecoration(
//             color: Colors.white,
//             borderRadius: const BorderRadius.all(Radius.circular(5)),
//             border: Border.all(color: Colors.black12, width: 1.0)),
//         child: Column(
//           children: [
//             Container(
//                 decoration: BoxDecoration(
//                     color: Colors.grey[200],
//                     borderRadius: const BorderRadius.all(Radius.circular(5)),
//                     border: Border.all(color: Colors.black12, width: 1.0)),
//                 height: 50,
//                 child: Center(
//                     child: Text(
//                   AppLocalizations.of(context).translate('Price Breakdown'),
//                 ))),
//             const SizedBox(
//               height: 20,
//             ),
//             Row(
//               mainAxisAlignment: MainAxisAlignment.spaceBetween,
//               children: [
//                 Text(
//                   AppLocalizations.of(context).translate('No Of days'),
//                   style:
//                       const TextStyle(color: Color(0xff51565B), fontSize: 13),
//                 ),
//                 pricerequest['num_days'] != null
//                     ? Text(
//                         pricerequest['num_days'
//                                     '']
//                                 .toString() +
//                             AppLocalizations.of(context).translate('days'),
//                         style: const TextStyle(
//                             color: Color(0xff51565B), fontSize: 13),
//                       )
//                     : Container(),
//               ],
//             ),
//             const SizedBox(
//               height: 20,
//             ),
//             Row(
//               mainAxisAlignment: MainAxisAlignment.spaceBetween,
//               children: [
//                 Text(
//                   AppLocalizations.of(context).translate('Sub Total'),
//                   style:
//                       const TextStyle(color: Color(0xff51565B), fontSize: 13),
//                 ),
//                 pricerequest['subtotal'] != null
//                     ? Text(
//                         pricerequest['subtotal'].toStringAsFixed(2) +
//                             ' ' +
//                             currencyController.currency,
//                         style: const TextStyle(
//                             color: Color(0xff51565B), fontSize: 13),
//                       )
//                     : Container(),
//               ],
//             ),
//             const SizedBox(
//               height: 20,
//             ),
//             if (isPrivateCar == true) ...[
//               Row(
//                 mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                 children: [
//                   Text(
//                     AppLocalizations.of(context).translate('carprivatedriver'),
//                     style:
//                         const TextStyle(color: Color(0xff51565B), fontSize: 13),
//                   ),
//                   pricerequest['car_private_driver'] != null
//                       ? Text(
//                           pricerequest['car_private_driver']
//                                   .toStringAsFixed(2) +
//                               ' ' +
//                               currencyController.currency,
//                           style: const TextStyle(
//                               color: Color(0xff51565B), fontSize: 13),
//                         )
//                       : Container(),
//                 ],
//               ),
//               const SizedBox(
//                 height: 20,
//               ),
//             ],
//             Row(
//               mainAxisAlignment: MainAxisAlignment.spaceBetween,
//               children: [
//                 Text(
//                   AppLocalizations.of(context).translate('Promo Subtotal'),
//                   style:
//                       const TextStyle(color: Color(0xff51565B), fontSize: 13),
//                 ),
//                 pricerequest['discount'] != null
//                     ? Text(
//                         '${pricerequest['discount']} ${currencyController.currency}',
//                         style: const TextStyle(
//                             color: Color(0xff51565B), fontSize: 13),
//                       )
//                     : Container(),
//               ],
//             ),
//             const SizedBox(
//               height: 20,
//             ),
//             Row(
//               mainAxisAlignment: MainAxisAlignment.spaceBetween,
//               children: [
//                 Text(
//                   AppLocalizations.of(context).translate('VAT'),
//                   style:
//                       const TextStyle(color: Color(0xff51565B), fontSize: 13),
//                 ),
//                 pricerequest['vat'] != null
//                     ? Text(
//                         pricerequest['vat'].toStringAsFixed(2) +
//                             ' ' +
//                             currencyController.currency,
//                         style: const TextStyle(
//                             color: Color(0xff51565B), fontSize: 13),
//                       )
//                     : Container(),
//               ],
//             ),
//             const SizedBox(
//               height: 20,
//             ),
//             Text(
//               AppLocalizations.of(context).translate('Price Details'),
//               style: const TextStyle(
//                   color: Colors.black,
//                   fontSize: 14,
//                   fontWeight: FontWeight.bold),
//             ),
//             const SizedBox(
//               height: 20,
//             ),
//             _PriceDetails(
//                 periods: pricerequest['periods'],
//                 currency: currencyController.currency),
//             const SizedBox(
//               height: 20,
//             ),
//             _NormalDaysPriceDetails(
//               priceRequest: pricerequest,
//               currency: currencyController.currency,
//             ),
//             const SizedBox(
//               height: 20,
//             ),
//             Row(
//               mainAxisAlignment: MainAxisAlignment.spaceBetween,
//               children: [
//                 Text(
//                   AppLocalizations.of(context).translate('total'),
//                   style: const TextStyle(
//                       color: Colors.black,
//                       fontSize: 13,
//                       fontWeight: FontWeight.bold),
//                 ),
//                 pricerequest['total_amount'] != null
//                     ? Text(
//                         '${pricerequest['total_amount']} ${currencyController.currency}',
//                         style: const TextStyle(
//                             color: Color(0xff51565B),
//                             fontSize: 13,
//                             fontWeight: FontWeight.bold),
//                       )
//                     : Container(),
//               ],
//             ),
//           ],
//         ));
//   }
// }
//
// class _PriceDetails extends StatelessWidget {
//   final List periods;
//   final String currency;
//
//   const _PriceDetails({required this.periods, required this.currency});
//
//   @override
//   Widget build(BuildContext context) {
//     return ListView.separated(
//         shrinkWrap: true,
//         physics: const NeverScrollableScrollPhysics(),
//         itemCount: periods.length,
//         itemBuilder: (context, index) {
//           final period = periods[index];
//
//           return Column(
//             children: [
//               Container(
//                 padding: const EdgeInsets.all(10),
//                 decoration: BoxDecoration(
//                     color: Colors.grey[200],
//                     borderRadius: BorderRadius.circular(8),
//                     border: Border.all(color: Colors.black12, width: 1.0)),
//                 child: Text(
//                   period['start_date'] +
//                       ' - ' +
//                       period['end_date'] +
//                       " (${period['num_days']} ${AppLocalizations.of(context).translate('days')})",
//                   style: const TextStyle(
//                       color: Colors.black,
//                       fontSize: 13,
//                       fontWeight: FontWeight.bold),
//                 ),
//               ),
//               const SizedBox(
//                 height: 20,
//               ),
//               Row(
//                 mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                 children: [
//                   Text(
//                     AppLocalizations.of(context).translate('Day Price'),
//                     style: const TextStyle(),
//                   ),
//                   Text(
//                     '${period['price_day']?.toString() ?? '0'} $currency',
//                     style: const TextStyle(
//                         color: Color(0xff51565B),
//                         fontSize: 13,
//                         fontWeight: FontWeight.bold),
//                   ),
//                 ],
//               ),
//               const SizedBox(
//                 height: 5,
//               ),
//               Row(
//                 mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                 children: [
//                   Text(
//                     AppLocalizations.of(context).translate('Private Driver'),
//                     style: const TextStyle(),
//                   ),
//                   Text(
//                     '${period['price_driver']?.toString() ?? '0'} $currency',
//                     style: const TextStyle(
//                         color: Color(0xff51565B),
//                         fontSize: 13,
//                         fontWeight: FontWeight.bold),
//                   ),
//                 ],
//               ),
//             ],
//           );
//         },
//         separatorBuilder: (context, index) => const SizedBox(
//               height: 20,
//             ));
//   }
// }
//
// class _NormalDaysPriceDetails extends StatelessWidget {
//   final Map<String, dynamic> priceRequest;
//   final String currency;
//
//   const _NormalDaysPriceDetails(
//       {required this.priceRequest, required this.currency});
//
//   @override
//   Widget build(BuildContext context) {
//     return Column(
//       children: [
//         Container(
//           padding: const EdgeInsets.all(10),
//           decoration: BoxDecoration(
//               color: Colors.grey[200],
//               borderRadius: BorderRadius.circular(8),
//               border: Border.all(color: Colors.black12, width: 1.0)),
//           child: Text(
//             "${AppLocalizations.of(context).translate('Normal Days')} (${priceRequest['num_not_common_days']} ${AppLocalizations.of(context).translate('days')})",
//             style: const TextStyle(
//                 color: Colors.black, fontSize: 13, fontWeight: FontWeight.bold),
//           ),
//         ),
//         const SizedBox(
//           height: 20,
//         ),
//         Row(
//           mainAxisAlignment: MainAxisAlignment.spaceBetween,
//           children: [
//             Text(
//               AppLocalizations.of(context).translate('Day Price'),
//               style: const TextStyle(),
//             ),
//             Text(
//               '${priceRequest['normal_price'].toString()} $currency',
//               style: const TextStyle(
//                   color: Color(0xff51565B),
//                   fontSize: 13,
//                   fontWeight: FontWeight.bold),
//             ),
//           ],
//         ),
//         const SizedBox(
//           height: 5,
//         ),
//         Row(
//           mainAxisAlignment: MainAxisAlignment.spaceBetween,
//           children: [
//             Text(
//               AppLocalizations.of(context).translate('Private Driver'),
//             ),
//             Text(
//               '${priceRequest['normal_driver_price']?.toString() ?? '0'} $currency',
//               style: const TextStyle(
//                   color: Color(0xff51565B),
//                   fontSize: 13,
//                   fontWeight: FontWeight.bold),
//             ),
//           ],
//         ),
//       ],
//     );
//   }
// }
