import 'package:flutter/material.dart';
import 'package:page/src/core/localization/app_localizations.dart';

class ViewHolidayPriceDaysDetails extends StatelessWidget {
  final List<(String date, num amount)> priceByDateDetails;
  final currencyController;

  const ViewHolidayPriceDaysDetails({
    super.key,
    required this.currencyController,
    required this.priceByDateDetails,
  });

  @override
  Widget build(BuildContext context) {
    return ListView.separated(
      padding: const EdgeInsets.all(12),
      itemCount: priceByDateDetails.length,
      itemBuilder: (context, index) {
        return Padding(
          padding: const EdgeInsets.symmetric(vertical: 8.0),
          child: ListTile(
            title: Container(
              padding: const EdgeInsets.all(10),
              decoration: BoxDecoration(
                  color: Colors.grey[200],
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.black12, width: 1.0)),
              alignment: Alignment.center,
              child: Text(
                priceByDateDetails[index].$1.split(' ').first,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
            subtitle: Padding(
              padding: const EdgeInsets.symmetric(vertical: 12.0),
              child: _Prices(
                label: priceByDateDetails[index].$2.toString(),
                currency: currencyController.currency,
              ),
            ),
          ),
        );
      },
      separatorBuilder: (context, index) {
        return const Divider(
          color: Colors.black12,
          height: 1,
        );
      },
    );
  }
}

class _Prices extends StatelessWidget {
  final String label;
  final String? currency;

  const _Prices({super.key, required this.label, required this.currency});

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Text(
        '${AppLocalizations.of(context).translate('Night Price')}: $label $currency',
        style: const TextStyle(
          fontSize: 16,
        ),
      ),
    );
  }
}
