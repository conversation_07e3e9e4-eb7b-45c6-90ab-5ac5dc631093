import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:page/src/core/utils/print_services.dart';

import '../../../../core/localization/app_localizations.dart';
import 'view_holiday_schedule_days.dart';

class PaymentDetailsWidget extends StatelessWidget {
  final currencyController;
  final List<(String date, num amount)> priceByDateDetails;
  final pricerequest;

  const PaymentDetailsWidget(
      {super.key,
      required this.currencyController,
      required this.pricerequest,
      required this.priceByDateDetails});

  @override
  Widget build(BuildContext context) {
    return Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: const BorderRadius.all(Radius.circular(5)),
            border: Border.all(color: Colors.black12, width: 1.0)),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
                decoration: BoxDecoration(
                    color: Colors.grey[200],
                    borderRadius: const BorderRadius.all(Radius.circular(5)),
                    border: Border.all(color: Colors.black12, width: 1.0)),
                height: 50,
                child: Center(
                    child: Text(
                  AppLocalizations.of(context).translate('Price Breakdown'),
                ))),
            const SizedBox(
              height: 20,
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  AppLocalizations.of(context).translate('Start Date'),
                  style:
                      const TextStyle(color: Color(0xff51565B), fontSize: 13),
                ),
                pricerequest?['start_date'] != null
                    ? Text(
                        '${pricerequest['start_date']}',
                        style: const TextStyle(
                            color: Color(0xff51565B), fontSize: 13),
                      )
                    : Container(),
              ],
            ),

            const SizedBox(
              height: 20,
            ),

            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  AppLocalizations.of(context).translate('End Date'),
                  style:
                      const TextStyle(color: Color(0xff51565B), fontSize: 13),
                ),
                pricerequest?['end_date'] != null
                    ? Text(
                        '${pricerequest['end_date']}',
                        style: const TextStyle(
                            color: Color(0xff51565B), fontSize: 13),
                      )
                    : Container(),
              ],
            ),

            const SizedBox(
              height: 20,
            ),

            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  AppLocalizations.of(context).translate('No Of Nights'),
                  style:
                      const TextStyle(color: Color(0xff51565B), fontSize: 13),
                ),
                pricerequest?['num_days'] != null
                    ? Text(
                        '${pricerequest['num_days']?.toString() == '0' ? '1' : pricerequest['num_days']?.toString()} ${AppLocalizations.of(context).translate('nights')}',
                        style: const TextStyle(
                            color: Color(0xff51565B), fontSize: 13),
                      )
                    : Container(),
              ],
            ),

            const SizedBox(
              height: 20,
            ),

            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  AppLocalizations.of(context).translate('Sub Total'),
                  style:
                      const TextStyle(color: Color(0xff51565B), fontSize: 13),
                ),
                Row(
                  children: [
                    pricerequest?['subtotal'] != null
                        ? Text(
                            (pricerequest?['subtotal'].toStringAsFixed(2) ??
                                    '') +
                                ' ' +
                                '${currencyController.currency}',
                            style: const TextStyle(
                                color: Color(0xff51565B), fontSize: 13),
                          )
                        : Container(),
                  ],
                )
              ],
            ),
            const SizedBox(
              height: 20,
            ),

            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  AppLocalizations.of(context).translate('VAT'),
                  // AppLocalizations.of(context).translate('VAT'),
                  style:
                      const TextStyle(color: Color(0xff51565B), fontSize: 13),
                ),
                pricerequest?['baseRateTax'] != null
                    ? Text(
                        pricerequest?['baseRateTax'].toStringAsFixed(2) +
                            ' ' +
                            '${currencyController.currency}',
                        style: const TextStyle(
                            color: Color(0xff51565B), fontSize: 13),
                      )
                    : Container(),
              ],
            ),
            const SizedBox(
              height: 20,
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  AppLocalizations.of(context).translate('Promo Subtotal'),
                  style:
                      const TextStyle(color: Color(0xff51565B), fontSize: 13),
                ),
                pricerequest?['discount'] != null
                    ? Text(
                        '${pricerequest?['discount']} ${currencyController.currency}',
                        style: const TextStyle(
                            color: Color(0xff51565B), fontSize: 13),
                      )
                    : Container(),
              ],
            ),
            const SizedBox(
              height: 20,
            ),
            // Row(
            //   mainAxisAlignment: MainAxisAlignment.spaceBetween,
            //   children: [
            //     Text(
            //       AppLocalizations.of(context).translate('Tourism Fee'),
            //       // ( ${pricerequest?['days'] ?? ''} ${AppLocalizations.of(context).translate('nights')} )
            //       style:
            //           const TextStyle(color: Color(0xff51565B), fontSize: 13),
            //     ),
            //     pricerequest?['fee'] != null
            //         ? Text(
            //             pricerequest?['fee'].toStringAsFixed(2) +
            //                 ' ' +
            //                 '${currencyController.currency}',
            //             style: const TextStyle(
            //                 color: Color(0xff51565B), fontSize: 13),
            //           )
            //         : Container(),
            //   ],
            // ),
            // const SizedBox(
            //   height: 20,
            // ),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  AppLocalizations.of(context).translate('total'),
                  style: const TextStyle(
                      color: Colors.black,
                      fontSize: 13,
                      fontWeight: FontWeight.bold),
                ),
                pricerequest?['total_amount'] != null
                    ? Text(
                        pricerequest?['total_amount'].toStringAsFixed(2) +
                            ' ' +
                            '${currencyController.currency}',
                        style: const TextStyle(
                            color: Color(0xff51565B),
                            fontSize: 13,
                            fontWeight: FontWeight.bold),
                      )
                    : Container(),
              ],
            ),
            const SizedBox(
              height: 20,
            ),
            InkWell(
              onTap: () => showModalBottomSheet(
                  context: context,
                  backgroundColor: Colors.white,
                  builder: (_) {
                    return ViewHolidayPriceDaysDetails(
                      priceByDateDetails: priceByDateDetails,
                      currencyController: currencyController,
                    );
                  }),
              child: Container(
                width: double.infinity,
                padding: const EdgeInsets.all(10),
                decoration: BoxDecoration(
                    color: Colors.grey[200],
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.black12, width: 1.0)),
                child: Center(
                  child: Text(
                    AppLocalizations.of(context).translate('Price Details'),
                    style: TextStyle(
                        fontSize: 13,
                        fontFamily: isEng(context)
                            ? 'Roboto'
                            : GoogleFonts.tajawal().fontFamily,
                        fontWeight: FontWeight.bold,
                        decoration: TextDecoration.underline,
                        color: const Color(0xff233549)),
                  ),
                ),
              ),
            ),
            const SizedBox(
              height: 20,
            ),
            // Text(
            //   AppLocalizations.of(context).translate('Price Details'),
            //   style: const TextStyle(
            //       color: Colors.black,
            //       fontSize: 14,
            //       fontWeight: FontWeight.bold),
            // ),
            // if (pricerequest?['periods'].isNotEmpty)
            //   const SizedBox(
            //     height: 20,
            //   ),
            // _PriceDetails(
            //   periods: pricerequest?['periods'] ?? [],
            //   currency: currencyController.currency,
            // ),
            // const SizedBox(
            //   height: 20,
            // ),
            // _NormalDaysPriceDetails(
            //   priceRequest: pricerequest,
            //   currency: currencyController.currency,
            // ),
          ],
        ));
  }
}

class _PriceDetails extends StatelessWidget {
  final List periods;
  final String currency;

  const _PriceDetails({required this.periods, required this.currency});

  @override
  Widget build(BuildContext context) {
    return ListView.separated(
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        itemCount: periods.length,
        itemBuilder: (context, index) {
          final period = periods[index];

          final commonDays = List<String>.from(period['commonDays'] ?? []);

          final startDate = commonDays.first;
          final endDate = commonDays.last;

          return Column(
            children: [
              Container(
                padding: const EdgeInsets.all(10),
                decoration: BoxDecoration(
                    color: Colors.grey[200],
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.black12, width: 1.0)),
                child: Text(
                  startDate +
                      ' - ' +
                      endDate +
                      " (${period['num_days']} ${AppLocalizations.of(context).translate('days')})",
                  style: const TextStyle(
                      color: Colors.black,
                      fontSize: 13,
                      fontWeight: FontWeight.bold),
                ),
              ),
              const SizedBox(
                height: 20,
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    AppLocalizations.of(context).translate('Night Price'),
                    style: const TextStyle(),
                  ),
                  Text(
                    '${period['price_day']?.toString() ?? '0'} $currency',
                    style: const TextStyle(
                        color: Color(0xff51565B),
                        fontSize: 13,
                        fontWeight: FontWeight.bold),
                  ),
                ],
              ),
            ],
          );
        },
        separatorBuilder: (context, index) => const SizedBox(
              height: 20,
            ));
  }
}

// class _NormalDaysPriceDetails extends StatelessWidget {
//   final Map<String, dynamic> priceRequest;
//   final String currency;
//
//   const _NormalDaysPriceDetails(
//       {required this.priceRequest, required this.currency});
//
//   @override
//   Widget build(BuildContext context) {
//     if (priceRequest['num_not_common_days'] == 0) {
//       return const SizedBox.shrink();
//     }
//
//     return Column(
//       children: [
//         Container(
//           padding: const EdgeInsets.all(10),
//           decoration: BoxDecoration(
//               color: Colors.grey[200],
//               borderRadius: BorderRadius.circular(8),
//               border: Border.all(color: Colors.black12, width: 1.0)),
//           child: Text(
//             "${AppLocalizations.of(context).translate('Normal Days')} (${priceRequest['num_not_common_days']} ${AppLocalizations.of(context).translate('days')})",
//             style: const TextStyle(
//                 color: Colors.black, fontSize: 13, fontWeight: FontWeight.bold),
//           ),
//         ),
//         const SizedBox(
//           height: 20,
//         ),
//         Row(
//           mainAxisAlignment: MainAxisAlignment.spaceBetween,
//           children: [
//             Text(
//               AppLocalizations.of(context).translate('Night Price'),
//               style: const TextStyle(),
//             ),
//             Text(
//               '${priceRequest['normal_price'].toString()} $currency',
//               style: const TextStyle(
//                   color: Color(0xff51565B),
//                   fontSize: 13,
//                   fontWeight: FontWeight.bold),
//             ),
//           ],
//         ),
//       ],
//     );
//   }
// }
