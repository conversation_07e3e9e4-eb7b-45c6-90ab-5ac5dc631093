import 'package:flutter/material.dart';
import 'package:page/src/core/localization/app_localizations.dart';
import 'package:page/src/features/controllers/language_controller.dart';
import 'package:page/src/features/views/home/<USER>';

class SuccessRequestScreen extends StatelessWidget {
  final bool isFromSendRequest;
  // final Requests holidayHome;

  const SuccessRequestScreen({
    super.key,
    this.isFromSendRequest = false,
    // required this.holidayHome,
  });

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: () async {
        if (isFromSendRequest) {
          Navigator.pushReplacement(context,
              MaterialPageRoute(builder: (context) {
            return const Home();
          }));

          return false;
        }
        return true;
      },
      child: Scaffold(
        appBar: AppBar(
          leading: IconButton(
            icon: const Icon(Icons.arrow_back),
            onPressed: () {
              if (isFromSendRequest) {
                Navigator.pushReplacement(context,
                    MaterialPageRoute(builder: (context) {
                  return const Home();
                }));
              } else {
                Navigator.pop(context);
              }
            },
          ),
          centerTitle: true,
          backgroundColor: const Color(0xFF27b4a8),
          title: Text(
            AppLocalizations.of(context).translate('Success'),
            style: TextStyle(
              fontFamily: isEnglish(context) ? 'Roboto' : 'Tajawal',
            ),
          ),
        ),
        body: Padding(
          padding: const EdgeInsets.all(32.0),
          child: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(
                  Icons.check_circle,
                  color: Color(0xff25d366),
                  size: 150,
                ),

                const SizedBox(height: 20),

                Text(
                  AppLocalizations.of(context)
                      .translate('Success Request Details'),
                  textAlign: TextAlign.center,
                  style: const TextStyle(
                    fontSize: 16,
                  ),
                ),

                // button for whatsApp
                // const SizedBox(height: 40),
                //
                // GestureDetector(
                //   onTap: () async {
                //     final text =
                //         // ignore: prefer_interpolation_to_compose_strings
                //         "Hello, I need to pay this request\n\n" +
                //             "Request Details:\n" +
                //             "Place: ${holidayHome.name ?? '-'}\n" +
                //             "Name: ${holidayHome.fullname == null || holidayHome.fullname == '' ? '-' : holidayHome.fullname}\n" +
                //             "Phone: ${holidayHome.phone == null || holidayHome.phone == '' ? '-' : holidayHome.phone}\n" +
                //             "Email: ${holidayHome.email == null || holidayHome.email == '' ? '-' : holidayHome.email}\n" +
                //             "Start Date: ${holidayHome.startDate ?? '-'}\n" +
                //             "End Date: ${holidayHome.endDate ?? '-'}\n" +
                //             "Number of Adults: ${holidayHome.numberOfPeople ?? '-'}\n" +
                //             "Number of Children: ${holidayHome.numberOfChildren ?? '-'}\n" +
                //             "Number of Rooms: ${holidayHome.numOfRooms ?? '-'}\n" +
                //             "Note: ${holidayHome.userNote ?? '-'}\n" +
                //             // "Agent Note: ${holidayHome?.agentNote ?? '-'}\n" +
                //             "Days: ${holidayHome.days ?? '-'}\n" +
                //             "Stay Price: ${holidayHome.subTotal ?? '-'}\n" +
                //             "VAT - Tourism - Cleaning: ${holidayHome.baseRateTax?.round().toStringAsFixed(2) ?? '-'}\n" +
                //             "Promo Discount: ${holidayHome.discount?.round().toStringAsFixed(2) ?? '-'}\n" +
                //             "Total: ${holidayHome.finalAmount?.round().toStringAsFixed(2) ?? '-'}\n" +
                //             "Request Date: ${holidayHome.requestedat ?? '-'}";
                //
                //     log('text: $text');
                //
                //     const phone = '+971526177589';
                //
                //     final link = WhatsAppUnilink(
                //       phoneNumber: phone,
                //       text: text,
                //     );
                //     // Convert the WhatsAppUnilink instance to a string.
                //     // Use either Dart's string interpolation or the toString() method.
                //     // The "launchUrlString" method is part of "url_launcher_string".
                //     await launchUrlString('$link');
                //   },
                //   child: Container(
                //     height: 50,
                //     width: MediaQuery.of(context).size.width,
                //     decoration: BoxDecoration(
                //         color: const Color(0xff25d366),
                //         borderRadius: BorderRadius.circular(10)),
                //     child: Container(
                //         padding: const EdgeInsets.all(10),
                //         child: Center(
                //             child: Text(
                //           AppLocalizations.of(context).translate('WhatsApp'),
                //           style: const TextStyle(
                //             color: Colors.white,
                //             fontSize: 16,
                //             fontWeight: FontWeight.bold,
                //           ),
                //         ))),
                //   ),
                // )
              ],
            ),
          ),
        ),
      ),
    );
  }
}
