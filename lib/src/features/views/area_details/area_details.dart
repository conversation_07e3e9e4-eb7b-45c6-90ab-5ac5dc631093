import 'package:flutter/material.dart';
import 'package:video_player/video_player.dart';

import '../../../core/shared_widgets/back_button.dart';

class AreaDetails extends StatefulWidget {
  final String video;
  final String name;
  const AreaDetails(this.video, this.name, {super.key});
  @override
  _AreaDetails createState() => _AreaDetails();
}

VideoPlayerController? _controller;

class _AreaDetails extends State<AreaDetails> {
  bool ismute = false;

  @override
  void initState() {
    super.initState();
    _controller = VideoPlayerController.networkUrl(Uri.parse(widget.video!))
      ..initialize().then((_) {
        setState(() {});
      });
    _controller!.play();
  }

  @override
  void dispose() {
    _controller?.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
        onWillPop: () {
          if (_controller!.value.isPlaying) {
            _controller!.pause();
            _controller = null;
            _controller?.dispose();
          }
          return Future.value(true);
        },
        child: Safe<PERSON><PERSON>(
            child: Scaffold(
          body: Stack(
            alignment: Alignment.bottomCenter,
            children: <Widget>[
              SizedBox(
                  height: MediaQuery.of(context).size.height,
                  width: MediaQuery.of(context).size.width,
                  child: _controller!.value.isInitialized
                      ? FittedBox(
                          fit: BoxFit.cover,
                          child: SizedBox(
                              height: _controller!.value.size.height,
                              width: _controller!.value.size.width,
                              child: VideoPlayer(_controller!)))
                      : Container()),

              Positioned(
                  left: 10,
                  right: 10,
                  top: 20,
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      const BackButtonWidget(),
                      InkWell(
                          onTap: () {
                            setState(() {
                              ismute = !ismute;
                              ismute
                                  ? _controller!.setVolume(0.0)
                                  : _controller!.setVolume(30.0);
                            });
                            // _controller.setVolume(0.0);
                          },
                          child: !ismute
                              ? const Icon(
                                  Icons.volume_down,
                                  color: Colors.white,
                                )
                              : const Icon(
                                  Icons.volume_off,
                                  color: Colors.white,
                                ))
                    ],
                  )),
              Positioned(
                  bottom: 131,
                  left: 16,
                  child: Column(
                    children: [
                      Text(
                        widget.name,
                        style: const TextStyle(color: Colors.white),
                      ),
                    ],
                  )),
              Padding(
                padding: const EdgeInsetsDirectional.only(
                    bottom: 44, top: 19, end: 16),
                child: Row(
                  children: [
                    Expanded(
                      child: IconButton(
                          onPressed: () {
                            _controller!.value.isPlaying
                                ? _controller!.pause()
                                : _controller!.play();
                            setState(() {});
                          },
                          icon: Icon(
                            _controller!.value.isPlaying
                                ? Icons.pause
                                : Icons.play_arrow,
                            color: Colors.white,
                          )),
                    ),
                    Expanded(
                      flex: 8,
                      child: VideoProgressIndicator(
                        _controller!,
                        allowScrubbing: true,
                        colors: VideoProgressColors(
                            playedColor: _controller!.value.isInitialized
                                ? Colors.white
                                : const Color(0xFF27b4a8),
                            backgroundColor: Colors.white.withOpacity(0.44)),
                      ),
                    ),
                    const SizedBox(width: 5),
                    Expanded(
                      child: FittedBox(
                        fit: BoxFit.scaleDown,
                        child: Text(
                          _controller!.value.duration.toString().substring(
                              _controller!.value.duration
                                      .toString()
                                      .indexOf(":") +
                                  1,
                              _controller!.value.duration
                                  .toString()
                                  .indexOf(".")),
                          style: const TextStyle(color: Colors.white),
                          maxLines: 1,
                        ),
                      ),
                    ),
                  ],
                ),
              )
              //FURTHER IMPLEMENTATION
            ],
          ),
        )));
  }
}
