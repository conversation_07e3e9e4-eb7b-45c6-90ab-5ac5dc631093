import 'dart:async';

import 'package:page/src/core/response/offers_response.dart';

import '../repository/offers_repository.dart';

class OffersBloc {
  final OffersRepository _repository = OffersRepository();

  final StreamController<OffersResponse> _subject =
      StreamController<OffersResponse>.broadcast();

  getoffers(int page, int size) async {
    OffersResponse response = await _repository.getoffers(page, size);
    _subject.sink.add(response);
  }

  StreamController<OffersResponse> get subject => _subject;
  dispose() {
    _subject.close();
  }
}

final offersBloc = OffersBloc();
