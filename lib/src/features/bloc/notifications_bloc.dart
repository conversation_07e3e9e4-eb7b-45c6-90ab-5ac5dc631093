import 'dart:async';

import '../../core/response/notification_response.dart';
import '../repository/notifications_repository.dart';

class NotificationsBloc {
  final NotificationsRepository _repository = NotificationsRepository();

  final StreamController<NotificationsResponse> _subject =
      StreamController<NotificationsResponse>.broadcast();

  getnotifications(int page, int size) async {
    NotificationsResponse response =
        await _repository.getnotifications(page, size);
    _subject.sink.add(response);
  }

  StreamController<NotificationsResponse> get subject => _subject;
  dispose() {
    _subject.close();
  }
}

final notificationsBloc = NotificationsBloc();
