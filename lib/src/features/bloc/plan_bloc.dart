import 'dart:async';

import '../../core/response/plan_response.dart';
import '../repository/plan_repository.dart';

class PlanBloc {
  final PlanRepository _repository = PlanRepository();

  final StreamController<PlanResponse> _subject =
      StreamController<PlanResponse>.broadcast();
  final StreamController<PlanDetailsResponse> _subject2 =
      StreamController<PlanDetailsResponse>.broadcast();
  final StreamController<PlanResponse> _subject3 =
      StreamController<PlanResponse>.broadcast();
  final StreamController<PlanItemsResponse> _subject4 =
      StreamController<PlanItemsResponse>.broadcast();
  final StreamController<PlanItemsResponse> _subject6 =
      StreamController<PlanItemsResponse>.broadcast();
  final StreamController<PlanItemsResponse> _subject5 =
      StreamController<PlanItemsResponse>.broadcast();

  getcategory(int page, int size) async {
    PlanResponse? response = await _repository.getplan(page, size);
    _subject.sink.add(response!);
  }

  getplandetails(int id) async {
    PlanDetailsResponse response = await _repository.getplandetails(id);
    _subject2.sink.add(response);
  }

  getplanuseritems(int page, int size) async {
    PlanResponse response = await _repository.getplanuseritems(page, size);
    _subject3.sink.add(response);
  }

  getplanudetailsseritems(int id, var date) async {
    PlanItemsResponse response =
        await _repository.getplanudetailsseritems(id, date);
    _subject4.sink.add(response);
  }

  Future<PlanItemsResponse> getplanuserdates(int id) async {
    PlanItemsResponse response = await _repository.getplanuserdates(id);
    _subject6.sink.add(response);

    return response;
  }

  // getplandetailsschedules(int id, var date) async {
  //   PlanItemsResponse response =
  //       await _repository.getplandetailsschedules(id, date);
  //   _subject5.sink.add(response);
  // }

  StreamController<PlanResponse> get subject => _subject;

  StreamController<PlanDetailsResponse> get subject2 => _subject2;

  StreamController<PlanResponse> get subject3 => _subject3;

  StreamController<PlanItemsResponse> get subject4 => _subject4;

  StreamController<PlanItemsResponse> get subject6 => _subject6;

  // StreamController<PlanItemsResponse> get subject5 => _subject5;
  dispose() {
    _subject.close();
    _subject2.close();
    _subject3.close();
    _subject4.close();
    _subject5.close();
    _subject6.close();
  }
}

final planbloc = PlanBloc();
