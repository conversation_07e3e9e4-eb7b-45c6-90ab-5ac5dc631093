import 'dart:async';

import 'package:rxdart/rxdart.dart';

import '../../core/response/category_response.dart';
import '../repository/category_repository.dart';

class CategoryBloc {
  final CategoryRepository _repository = CategoryRepository();

  final StreamController<CategoryResponse> _subject =
      StreamController<CategoryResponse>.broadcast();
  final BehaviorSubject<CategoryetailsResponse> _subject2 =
      BehaviorSubject<CategoryetailsResponse>();
  final BehaviorSubject<CategoryImagesResponse> _subject3 =
      BehaviorSubject<CategoryImagesResponse>();
  final StreamController<CategoryResponse> _subject4 =
      StreamController<CategoryResponse>.broadcast();
  final StreamController<HolidayHomeResponse> _subject5 =
      StreamController<HolidayHomeResponse>.broadcast();
  getcategory(int page, int size, String key, String category) async {
    CategoryResponse response =
        await _repository.getmaincategory(page, size, key, category);
    _subject.sink.add(response);
  }

  // getholidayhome(int page, int size, String key) async {
  //   CategoryResponse? response =
  //       await _repository.getmaincategory(page, size, key, AppConstants.holidayHomesId.toString());
  //   _subject5.sink.add(response!);
  // }

  getfeaturedvideo(int page, int size, String key, String category) async {
    CategoryResponse response =
        await _repository.getfeaturedvideo(page, size, key, category);
    _subject4.sink.add(response);
  }

  getcategorydetials(int id) async {
    CategoryetailsResponse? response =
        await _repository.getmaincategorydetails(id);
    _subject2.sink.add(response!);
  }

  getmainCategoryimages(int id) async {
    CategoryImagesResponse response =
        await _repository.getmainCategoryimages(id);
    _subject3.sink.add(response);
  }

  StreamController<CategoryResponse?> get subject => _subject;
  StreamController<CategoryResponse?> get subject4 => _subject4;
  StreamController<HolidayHomeResponse?> get subject5 => _subject5;
  BehaviorSubject<CategoryetailsResponse?> get subject2 => _subject2;
  BehaviorSubject<CategoryImagesResponse?> get subject3 => _subject3;
  dispose() {
    _subject.close();
    _subject2.close();
    _subject3.close();
    _subject4.close();
    _subject5.close();
  }
}

final categorybloc = CategoryBloc();
