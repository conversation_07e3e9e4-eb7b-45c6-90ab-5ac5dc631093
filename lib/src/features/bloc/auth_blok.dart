import 'dart:developer';

import '../../core/response/auth_response.dart';
import '../repository/auth_repository.dart';

class AuthBloc {
  final AuthRepository _repository = AuthRepository();

  register(String phone, String password, String fullname, String email,
      String token, String phonecode) async {
    int code = 0;
    String message = 'Something went wrong.';

    AuthResponse response = await _repository.register(
        phone, password, fullname, email, token, phonecode);

    if (response.code == 1) {
      code = response.code;
      message = response.msg;

      return {
        'code': code,
        'msg': message,
        "data": response.results,
        'token': response.token,
      };
    } else {
      if (response.emailerror != null) {
        return {
          'code': response.code,
          'msg': response.msg,
          'email_msg': response.emailerror![0],
        };
      } else if (response.phoneError != null) {
        log('afasfsaf ${response.phoneError}}');
        return {
          'code': response.code,
          'msg': response.msg,
          'phone_msg': response.phoneError![0],
        };
      } else {
        return {
          'code': response.code,
          'msg': response.msg,
        };
      }
    }
  }

  login(String email, String password, String? token) async {
    int code = 0;
    String message = 'Something went wrong.';
    AuthResponse response = await _repository.login(email, password, token);

    if (response.code == 1) {
      code = response.code;
      message = response.msg;

      return {
        'code': code,
        'msg': message,
        'token': response.token,
        'data': response.results
      };
    } else {
      return {'code': response.code, 'msg': response.msg};
    }
  }

  updateFcmToken(String? token) async {
    int code = 0;
    String message = 'Something went wrong.';
    dynamic response = await _repository.updateFcmToken(token);

    log('TokenUpdated $response');
    if (response['code'] == 1) {
      code = response['code'];
      message = response['msg'];

      return {'code': code, 'msg': message, 'data': response['data']};
    } else {
      return {'code': response['code'], 'msg': response['msg']};
    }
  }

  sendrequestpassword(String email) async {
    int code = 0;
    String message = 'Something went wrong.';
    AuthResponse response = await _repository.sendrequestpassword(email);

    log('Reqqwewedas ${response.code}');

    if (response.code == 1) {
      code = response.code;
      message = response.msg;

      return {'code': code, 'msg': message};
    } else {
      return {'code': response.code, 'msg': response.msg};
    }
  }

  verfiycode(String email, String codes) async {
    int code = 0;
    String message = 'Something went wrong.';
    AuthResponse response = await _repository.verfiycode(email, codes);

    if (response.code == 1) {
      code = response.code;
      message = response.msg;

      return {'code': code, 'msg': message};
    } else {
      return {'code': response.code, 'msg': response.msg};
    }
  }

  resetpassword(String email, String password) async {
    int code = 0;
    String message = 'Something went wrong.';
    AuthResponse response = await _repository.resetpassword(email, password);

    if (response.code == 1) {
      code = response.code;
      message = response.msg;

      return {'code': code, 'msg': message};
    } else {
      return {'code': response.code, 'msg': response.msg};
    }
  }
}

final bloc = AuthBloc();
