import 'dart:async';
import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:progress_dialog_null_safe/progress_dialog_null_safe.dart';

import '../../core/localization/app_localizations.dart';
import '../../core/response/generalResponse.dart';
import '../../core/response/plan_response.dart';
import '../../core/services/api.dart';
import '../../core/shared_widgets/shared_widgets.dart';
import '../models/plans.dart';
import '../views/plan_details/my_plans_details.dart';

class PlanController {
  List<Plans> results = [];

  getplanuser(int page, int size, BuildContext context) async {
    await Api.getplanuser(page, size).then((value) {
      value != null ? results.addAll(value.plans) : '';
    });
  }

  // addtimetoitem(var time, int itemid, BuildContext context, planId) async {
  //   GeneralResponse sucessinformation = await Api.addtimetoitem(time, itemid, planId);
  //
  //   print(sucessinformation.code);
  //   if (sucessinformation.code == "1") {
  //     snackbar2(
  //         AppLocalizations.of(context).translate('time added successfuly'));
  //   } else {
  //     snackbar(AppLocalizations.of(context)
  //         .translate('Something went wrong, please try again later'));
  //   }
  // }

  ProgressDialog? pr;
  void progrsss(BuildContext context) {
    pr = ProgressDialog(context);
    // print(widget.delId);
    pr!.update(
      progress: 50.0,
      message: AppLocalizations.of(context).translate('Please Wait'),
      progressWidget: Container(
          padding: const EdgeInsets.all(8.0),
          child: const CircularProgressIndicator(
              valueColor: AlwaysStoppedAnimation<Color>(Color(
            0xffD8B77F,
          )))),
      maxProgress: 100.0,
      progressTextStyle: const TextStyle(
          color: Colors.black, fontSize: 13.0, fontWeight: FontWeight.w400),
      messageTextStyle: const TextStyle(
          color: Colors.black, fontSize: 19.0, fontWeight: FontWeight.w600),
    );
  }

  additemtoplan(
      var date, String type, int itemid, int planid, BuildContext context,
      {time}) async {
    log('fff3332232');

    progrsss(context);
    pr!.show();
    GeneralResponse sucessinformation =
        await Api.additemtoplan(date, type, itemid, planid);
    print(sucessinformation.code);
    log('asdaaaasdsdsdsd');

    if (sucessinformation.code == "1") {
      if (time != null && time != '') {
        await Api.addtimetoitem(time, sucessinformation.id!, planid, date);
      }
      PlannameResponse planname = await Api.getplannamebyid(planid);
      if (planname.code == 1) {
        snackbar2(
            AppLocalizations.of(context).translate('Item added successfuly'));

        Future.delayed(const Duration(seconds: 1), () {
          Timer(
              const Duration(seconds: 1),
              () => Navigator.of(context).pushReplacement(MaterialPageRoute(
                  builder: (BuildContext context) =>
                      MyplansDetails(planname.data['planname'], planid))));
        });
      }
    } else {
      snackbar(AppLocalizations.of(context)
          .translate('Something went wrong, please try again later'));
    }
    pr!.hide();
  }
}
