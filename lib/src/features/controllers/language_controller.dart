import 'package:flutter/material.dart';
import 'package:page/src/core/utils/print_services.dart';
import 'package:shared_preferences/shared_preferences.dart';

isEnglish(BuildContext context) => isEng(context);

// AppLocalizations.of(context).locale.languageCode == 'en';

class LanguageController {
  LanguageController._();

  static LanguageController languageController = LanguageController._();

  LanguageController() {
    getcuurentlanguage();
  }

  var languagecode = 'en';

  Future<String?> getcuurentlanguage() async {
    SharedPreferences _prefs = await SharedPreferences.getInstance();
    var currentlanguagecode = _prefs.getString('language_code');
    if (currentlanguagecode == null) {
      languagecode = 'en';
    } else {
      languagecode = currentlanguagecode;
    }

    return currentlanguagecode;
  }

  String getCurrentLanguageSync() {
    return languagecode;
  }
}
