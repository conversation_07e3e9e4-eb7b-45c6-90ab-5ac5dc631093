import 'package:page/src/features/repository/auth_phone_repo.dart';
import 'package:shared_preferences/shared_preferences.dart';

class AuthController {
  static final AuthController _instance = AuthController._internal();

  AuthController._internal() {
    isloggedin();
  }

  factory AuthController() => _instance;

  final AuthPhoneRepo _phoneAuthRepo = AuthPhoneRepo();

  bool isLogged = false;

  Future<void> isloggedin() async {
    SharedPreferences _prefs = await SharedPreferences.getInstance();

    var islogged = _prefs.getBool('is_logged');

    print("DFfehehetet");

    if (islogged == true) {
      isLogged = true;
    } else {
      isLogged = false;
    }
  }

  //? Sign In With Phone Number ------------------------
  Future sendOtp({
    required String? phoneNumber,
  }) async {
    try {
      await _phoneAuthRepo.sendOtp(phoneNumber!);
    } catch (e) {
      rethrow;
    }
  }

  Future<void> verifyOTP({
    required String otp,
  }) async {
    try {
      await _phoneAuthRepo.verifyOTP(otp);
    } catch (e) {
      rethrow;
    }
  }

  //? Resend OTP ------------------------
  Future resendCode({
    required String phoneNumber,
  }) async {
    try {
      await _phoneAuthRepo.sendOtp(phoneNumber);
    } catch (e) {
      rethrow;
    }
  }
}
