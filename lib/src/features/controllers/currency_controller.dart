import 'package:flutter/cupertino.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../core/localization/app_language.dart';

class CurrencyController {
  var currency = 'AED';

  Future<void>getcuurentcurrency(BuildContext context) async {
    var lang =
        Provider.of<AppLanguage>(context, listen: false).appLocal.languageCode;
    final isEnglish = lang == 'en';

    SharedPreferences _prefs = await SharedPreferences.getInstance();
    var currentcurrency = _prefs.getString('currency');
    if (currentcurrency == null) {
      currency = isEnglish ? 'AED' : 'د.إ';
    } else {
      currency = getCurrencyLang(context, currentcurrency);
    }
  }

  String getCurrencyLang(BuildContext context, String currency) {
    var lang =
        Provider.of<AppLanguage>(context, listen: false).appLocal.languageCode;

    final isEnglish = lang == 'en';

    switch (currency) {
      case 'AED':
        return isEnglish ? 'AED' : 'د.إ';
      case 'USD':
        return isEnglish ? 'USD' : 'دولار';
      case 'EUR':
        return isEnglish ? 'EUR' : 'يورو';

      default:
        return isEnglish ? 'AED' : 'د.إ';
    }
  }
}
