class NotificationModel {
  List<ItemsModel>? items;
  String? date;

  NotificationModel.fromJson(Map<String, dynamic> json) {
    date = json['date'];
    items = json['items'] != null
        ? (json['items'] as List).map((p) => ItemsModel.fromJson(p)).toList()
        : [];
  }
}

class ItemsModel {
  int? id;

  String? title;
  String? message;

  ItemsModel({
    this.id,
  });

  ItemsModel.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    title = json['title'];
    message = json['message'];
  }
}
