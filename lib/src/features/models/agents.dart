import 'package:page/src/core/config/constants.dart';

class AgentModel {
  int? id;
  String? name;
  String? image;
  String? email;
  String? phone;
  bool? isHolidayHomeAgent;

  AgentModel({
    this.id,
  });

  AgentModel.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['full_name'] == null || json['full_name'] == ''
        ? json['name']
        : json['full_name'];
    email = json['email'];
    phone = json['phone'];
    image = json['image'];
    isHolidayHomeAgent = json['category'] != null &&
            json['category']['id'] == AppConstants.holidayHomesId
        ? true
        : false;
  }
}
