import 'package:page/src/features/models/agents.dart';

class OfferModel {
  int? id;
  String? title;
  String? description;
  String? code;
  String? discount;
  String? expirationDate;
  bool? isPublished;
  String? createdAt;
  AgentModel? agent;

  OfferModel({
    this.id,
    this.title,
    this.description,
    this.code,
    this.discount,
    this.expirationDate,
    this.isPublished,
    this.createdAt,
    this.agent,
  });

  OfferModel.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    title = json['title'];
    description = json['description'];
    code = json['code'];
    discount = json['discount'];
    expirationDate = json['expiration_date'];
    isPublished = json['is_published'] == 1;
    createdAt = json['created_at'];
    agent = json['agent'] != null ? AgentModel.fromJson(json['agent']) : null;
  }
}
