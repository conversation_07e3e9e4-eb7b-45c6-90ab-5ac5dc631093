import 'package:page/src/features/models/content.dart';

class StaticProjectTypes {
  // Static type models for the 3 project categories
  static final Types apartmentsType = Types(
    id: 21,
    name: "Apartments",
    nameEn: "Apartments",
    nameAr: "شقق",
    categoryIds: [10],
  );

  static final Types villaType = Types(
    id: 22,
    name: "Villa",
    nameEn: "Villa",
    nameAr: "فلل",
    categoryIds: [10],
  );

  static final Types luxuryType = Types(
    id: 28,
    name: "Luxury",
    nameEn: "Luxury",
    nameAr: "فاخر",
    categoryIds: [10],
  );

  static List<Types> getAllTypes() {
    return [apartmentsType, villaType, luxuryType];
  }

  static Types? getTypeById(int id) {
    switch (id) {
      case 21:
        return apartmentsType;
      case 22:
        return villaType;
      case 28:
        return luxuryType;
      default:
        return null;
    }
  }
}
