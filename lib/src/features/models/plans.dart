import 'dart:developer';

class Plans {
  int? id, videoId;
  String? name;
  String? planName;
  String? startdate, enddate, image;
  num? startprice, endprie;

  int? startPrice, endPrice;
  var time;

  // List<Items>? items;
  // List<PlanDetails>? dates;
  List<Plans>? items;

  Plans({
    this.id,
  });

  Plans.fromJson(Map<String?, dynamic> json) {
    log('sadkasjsdadasn ${json}');

    final video = json['video'] ?? json['video_id'];
    id = json['id'];
    videoId = video != null ? video['id'] : 0;
    planName = json['name'];
    name = video != null ? video['name'] : '';

    image = video != null && video['images'] != null
        ? video['images'][0]['url']
        : '';
    startdate = json['start_date'];
    enddate = json['end_date'];
    startprice = json['startprice'];
    endprie = json['endprice'];
    time = json['time'];
    items = json['item'] != null
        ? (json['item'] as List).map((p) => Plans.fromJson(p)).toList()
        : [];
    // items = json['days'] != null
    //     ? (json['days'] as List).map((p) => Items.fromJson(p)).toList()
    //     : [];
  }
}

class PlanDetails {
  int? day;
  String? date;
  String? datedisplay;
  bool isselected = false;

  PlanDetails({this.day});

  PlanDetails.fromJson(Map<String?, dynamic> json) {
    log('Daayss ${json}');
    day = json['day'];
    date = json['date'];
    datedisplay = json['date_display'];
  }
}

class Items {
  String? imagename;
  String? name;
  var startprice, endprie, time;
  int? id;

  Items({this.imagename});

  Items.fromJson(Map<String?, dynamic> json) {
    id = json['item_id'];
    imagename = json['image_name'];
    name = json['name'];
    startprice = json['startprice'];
    endprie = json['endprice'];
    time = json['time'];
  }
}
