import 'package:page/src/core/response/main_category_response.dart';

class Reels {
  int? id;
  int? rmsCategoryId;
  String? name;
  int? reelid;
  String? video;
  String? type = '2';
  MainCategoryModel? category;
  String? categorytype;
  num? carPrice;
  num? privateDriverPrice;
  num? startPrice;
  String? numOfRooms;
  int? agentId;
  String? description;
  String? label;
  String? greviewlink;
  String? greviewName;
  int? isfavourite;
  var latitude, longitude;
  var year;

  Reels(this.id, this.name, this.video, this.type);

  Reels.fromJson(Map<String?, dynamic> json) {
    id = json['video_id'];
    rmsCategoryId = json['rms_category_id'];
    name = json['name'];
    description = json['description'];
    video = json['url'];
    categorytype = category?.id.toString();
    carPrice = json['price'];
    startPrice = json['start_price'];
    privateDriverPrice = json['private_driver_price'];
    // json['type'] == "places" ? "activities" : json['type'];
    label = category?.name ?? '';
    latitude = json['latitude'];
    latitude = json['latitude'];
    longitude = json['longitude'];
    greviewlink = json['review_link'];
    greviewName = json['review_name'];
    reelid = json['video_id'];
    isfavourite = json['is_favorite'] == true ? 1 : 0;
    numOfRooms = json['rooms'];
    agentId = json['agent'] != null ? json['agent']['id'] : null;
  }
}
