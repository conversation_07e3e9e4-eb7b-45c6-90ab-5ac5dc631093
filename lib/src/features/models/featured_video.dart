// import 'package:page/src/core/response/main_category_response.dart';
// import 'package:page/src/core/response/main_response.dart';
// import 'package:page/src/features/models/video_model.dart';
//
// class VideoDetailsModel extends VideoDetailsModel {
//   int? id;
//   String? title;
//   String? name;
//   String? photo;
//   String? video;
//   MainCategoryModel? category;
//   String? label;
//   MainModel? location;
//   int? numberofstarts;
//   double? latitude;
//   double? longitude;
//   int? media;
//   // int? parentId;
//   String? type;
//   num? startprice;
//   num? endprice;
//   String? startpricecurrency;
//   String? endpricecurrency;
//   String? greviewlink;
//   String? phone;
//   String? facebook;
//   String? instagram;
//   String? createdAt;
//   String? updatedAt;
//   String? year;
//   String? locationName;
//   int? rating;
//
//   VideoDetailsModel(
//       {this.id,
//       this.title,
//       this.photo,
//       this.video,
//       this.category,
//       this.label,
//       this.location,
//       this.numberofstarts,
//       this.latitude,
//       this.longitude,
//       this.media,
//       this.type,
//       this.startprice,
//       this.endprice,
//       this.startpricecurrency,
//       this.endpricecurrency,
//       this.greviewlink,
//       this.phone,
//       this.facebook,
//       this.instagram,
//       // this.parentId,
//       this.createdAt,
//       this.updatedAt,
//       this.rating});
//
//   VideoDetailsModel.fromJson(Map<String?, dynamic> json) {
//     id = json['id'];
//     category = json['category'] == null
//         ? null
//         : MainCategoryModel.fromJson(json['category']);
//     label = category?.name;
//
//     title = json['name'];
//     // parentId = json['parent'] != null ? json['parent']['id'] : null;
//
//     year = json['year_car'] != null ? json['year_car']['year'] : '';
//     name = title;
//     locationName = json['location'] != null ? json['location']['name'] : '';
//     // photo = json['photo'];
//     photo = json['images'] != null ? json['images'][0]['url'] : '';
//
//     video = json['video'];
//     location =
//         json['location'] == null ? null : MainModel.fromJson(json['location']);
//     numberofstarts = json['numberofstarts'];
//     latitude = double.tryParse(json['latitude'].toString());
//     longitude = double.tryParse(json['longitude'].toString());
//     media = json['media'];
//     type = json['type'] == null ? '' : json['type']['name']['en'];
//     startprice = json['startprice'] == 0
//         ? json['price'].toString()
//         : json['startprice'].toString();
//     endprice = json['endprice'].toString();
//     startpricecurrency = json['startpricecurrency'];
//     endpricecurrency = json['endpricecurrency'];
//     greviewlink = json['review_link'];
//     phone = json['phone'];
//     facebook = json['website'];
//     instagram = json['instagram'];
//     createdAt = json['created_at'];
//     updatedAt = json['updated_at'];
//     rating = json['rating'];
//   }
// }
