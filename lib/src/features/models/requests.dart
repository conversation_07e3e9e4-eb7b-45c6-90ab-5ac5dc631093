import 'dart:developer';

import 'package:page/src/core/response/main_category_response.dart';

import 'agents.dart';

// class PriceRequest {
//   var days, subtotal, endsubtotal, discount, vat, tourismfees, total;
//
//   PriceRequest({
//     this.days,
//   });
//
//   PriceRequest.fromJson(Map<String?, dynamic> json) {
//     days = json['days'];
//     subtotal = json['subtotal'];
//     endsubtotal = json['endsubtotal'];
//     discount = json['discount'];
//     vat = json['vat'];
//     tourismfees = json['tourismfees'];
//     total = json['total'];
//   }
// }

class Requests {
  int? id;
  String? name, status, fullname, requestedat, image;
  String? phone;
  String? email;
  MainCategoryModel? category;

  num? total;
  num? dropOff;
  num? finalAmount;
  String? agentNote;
  String? userNote;
  String? promoCode;
  String? startDate;
  String? endDate;
  String? location;
  dynamic request;
  int? privateDrop;
  String? days;
  num? fee;
  num? baseRateTax;
  num? discount;
  num? subTotal;
  num? numberOfPeople;
  num? numberOfChildren;
  String? numOfRooms;
  AgentModel? agent;

  int? numNormalDays;
  num? normalPrice;
  num? normalDriverPrice;

  List? periods;

  Requests({
    this.id,
  });

  Requests.fromJson(Map<String?, dynamic> json) {
    // log('Datawsasfasf ${ json['video_id'] != null ?json['video_id']['category'].runtimeType: ''}');

    final amount = json['amount'];
    log('asffsfasa ${json['baseRateTax']}');

    final userData = json['user_id'];

    fullname = userData == null ? '' : userData['name'];
    phone = userData == null ? '' : userData['phone'];
    email = userData == null ? '' : userData['email'];

    id = json['id'];
    name = json['video_id'] != null ? json['video_id']['name'] : '';
    status = json['status'];
    // fullname = json['fullname'];
    requestedat = json['created_at']?.split('T')[0] ?? '';
    image = json['video_id'] != null && json['video_id']['images'] != null
        ? json['video_id']['images'][0]['url']
        : '';
    category = json['video_id'] != null && json['video_id']['category'] != null
        ? MainCategoryModel.fromJson(json['video_id']['category'])
        : null;
    dropOff = json['drop_off'];
    agentNote = json['agent_note'];
    userNote = json['user_note'];
    promoCode = json['promo_code'];
    startDate = json['start_date'];
    endDate = json['end_date'];
    request = json['request'];
    privateDrop = json['private_drop'];

    agent = json['video_id'] != null
        ? AgentModel.fromJson(json['video_id']['agent'])
        : null;

    baseRateTax = json['baseRateTax'] ?? 0;
    discount = amount['discount'] ?? 0;
    fee = amount['fee'] ?? 0;
    total = amount['total_amount'] ?? 0;
    days = amount['num_days']?.toString() ?? '0';
    subTotal = amount['subtotal'] ?? 0;
    finalAmount = amount['final_amount'];

    normalPrice = amount['normal_price'] ?? 0;
    normalDriverPrice = amount['normal_driver_price'] ?? 0;
    numNormalDays = amount['num_not_common_days'] ?? 0;

    periods = json['periods'];

    numberOfPeople = json['number_people'];
    numberOfChildren = json['number_child'];

    numOfRooms = json['video_id'] != null
        ? (json['video_id']['rooms'] as String?)
            ?.replaceAll('[', '')
            .replaceAll(']', '')
        : '';
    location = json['video_id'] != null && json['video_id']['location'] != null
        ? json['video_id']['location']['name']
        : null;

    // fee = json['fee'];
    // days = json['days']?.toString();
    // vat = num.parse(json['Vat']?.toString() ?? '0');
    // discount = json['discount'];
    // subTotal = json['subTotal'];
    // total = json['total'];
  }
}
