import 'package:page/src/features/models/video_model.dart';

class PlanDetailsModel {
  int? code;
  String? msg;
  PlanDetailsDataModel? data;

  PlanDetailsModel({this.code, this.msg, this.data});

  PlanDetailsModel.fromJson(Map<String, dynamic> json) {
    code = json['code'];
    msg = json['msg'];

    // if (json['data'].runtimeType == List) {
    //   json['data'].forEach((v) {
    //     log('ddalkdmsklvvvv $v');
    //     data?.add(Data.fromJson(v));
    //   });
    // } else {
    //   log('dataaaa ${json['data']}');

    data = json['data'] != null
        ? PlanDetailsDataModel.fromJson(json['data'])
        : null;
    // }
  }

  PlanDetailsModel.withError(String errorValue)
      : data = null,
        code = 0,
        msg = "fail";
}

class PlanDetailsDataModel {
  int? id;
  String? planname;
  String? startdate;
  String? enddate;
  int? userId;
  String? createat;
  String? date;
  String? types;
  int? itemId;
  int? planId;
  String? createdAt;
  String? time;
  String? label;
  int? location;
  int? numberofstarts;
  double? latitude;
  double? longitude;
  int? media;
  int? startprice;
  int? endprice;
  String? startpricecurrency;
  String? endpricecurrency;
  String? greviewlink;
  String? phone;
  String? facebook;
  String? instagram;
  String? updatedAt;
  int? rating;
  String? name;
  String? description;
  String? language;
  int? mainCategoryId;
  dynamic albumId;
  String? image;
  String? createdat;
  List<PlanDetailsVideoDataModel>? items;

  PlanDetailsDataModel(
      {this.id,
      this.planname,
      this.startdate,
      this.enddate,
      this.userId,
      this.createat,
      this.date,
      this.types,
      this.itemId,
      this.planId,
      this.createdAt,
      this.time,
      this.label,
      this.location,
      this.numberofstarts,
      this.latitude,
      this.longitude,
      this.media,
      this.startprice,
      this.endprice,
      this.startpricecurrency,
      this.endpricecurrency,
      this.greviewlink,
      this.phone,
      this.facebook,
      this.instagram,
      this.updatedAt,
      this.rating,
      this.name,
      this.description,
      this.language,
      this.mainCategoryId,
      this.albumId,
      this.image,
      this.createdat,
      this.items = const []});

  PlanDetailsDataModel.fromJson(Map<String?, dynamic> json) {
    // json['items'] ;

    types = json['types'];
    id = json['id'];
    planname = json['planname'] ?? json['name'];
    startdate = json['start_date'];
    enddate = json['end_date'];
    userId = json['user_id'];
    createat = json['createat'];
    date = json['date'];
    // json['item'][0]['date'];
    // json['date'] ?? json['item'] != null
    //     ? json['item'][0]['date']
    //     : '';
    time = json['time'];
    // ??
    // json['item'] != null ? json['item'][0]['time'] : '';

    // date = json['date'];
    itemId = json['item_id'];
    planId = json['plan_id'];
    createdAt = json['created_at'];
    label = json['label'];
    location = json['location'];
    numberofstarts = json['numberofstarts'];
    latitude = json['latitude'];
    longitude = json['longitude'];
    media = json['media'];
    startprice = json['startprice'];
    endprice = json['endprice'];
    startpricecurrency = json['startpricecurrency'];
    endpricecurrency = json['endpricecurrency'];
    greviewlink = json['greviewlink'];
    phone = json['phone'];
    facebook = json['website'];
    instagram = json['instagram'];
    updatedAt = json['updated_at'];
    rating = json['rating'];
    name = json['name'];
    description = json['description'];
    language = json['language'];
    mainCategoryId = json['main_category_id'];
    albumId = json['album_id'] ?? 0;
    image = json['image'];
    createdat = json['createdat'];
    items = json['schedule'] != null
        ? (json['schedule'] as List)
            .map((i) => PlanDetailsVideoDataModel.fromJson(i))
            .toList()
        : [];
  }

  Map<String?, dynamic> toJson() {
    final Map<String?, dynamic> data = <String?, dynamic>{};
    data['id'] = id;
    data['planname'] = planname;
    data['start_date'] = startdate;
    data['end_date'] = enddate;
    data['user_id'] = userId;
    data['createat'] = createat;
    data['date'] = date;
    data['type'] = types;
    data['item_id'] = itemId;
    data['plan_id'] = planId;
    data['created_at'] = createdAt;
    data['time'] = time;
    data['label'] = label;
    data['location'] = location;
    data['numberofstarts'] = numberofstarts;
    data['latitude'] = latitude;
    data['longitude'] = longitude;
    data['media'] = media;
    data['startprice'] = startprice;
    data['endprice'] = endprice;
    data['startpricecurrency'] = startpricecurrency;
    data['endpricecurrency'] = endpricecurrency;
    data['greviewlink'] = greviewlink;
    data['phone'] = phone;
    data['website'] = facebook;
    data['instagram'] = instagram;
    data['updated_at'] = updatedAt;
    data['rating'] = rating;
    data['name'] = name;
    data['description'] = description;
    data['language'] = language;
    data['main_category_id'] = mainCategoryId;
    data['album_id'] = albumId;
    data['image'] = image;
    data['createdat'] = createdat;
    return data;
  }
}

class PlanDetailsVideoDataModel {
  final VideoModel? videoDetails;
  final String? date;
  final String? time;

  PlanDetailsVideoDataModel({this.videoDetails, this.date, this.time});

  PlanDetailsVideoDataModel.fromJson(Map<String?, dynamic> json)
      : videoDetails = json['video_id'] != null
            ? VideoModel.fromJson(json['video_id'])
            : null,
        date = json['date'],
        time = json['time'];
}
