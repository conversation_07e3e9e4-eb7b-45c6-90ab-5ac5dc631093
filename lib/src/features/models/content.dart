class Types {
  int? id;
  String? name;
  String? nameEn;
  String? nameAr;
  String? image;
  List<int> categoryIds = [];

  Types({
    this.id,
    this.name,
    this.nameEn,
    this.nameAr,
    this.image,
    this.categoryIds = const [],
  });
  Types.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    image = json['image'];
    //category is field name and it's [
    //                     "8",
    //                     "10"
    //                 ],
    categoryIds = json['category'] != null
        ? List<int>.from(
            json['category'].map((x) => int.parse(x?.toString() ?? '0')))
        : [];
  }
}

class Agents {
  int? id;
  String? name;

  Agents({
    this.id,
  });
  Agents.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['full_name'] ?? json['name'];
  }
}

class Sizes {
  int? id;
  double? size;

  Sizes({
    this.id,
  });
  Sizes.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    size = json['size'].toDouble();
  }
}

class Brands {
  int? id;
  String? name;

  Brands({
    this.id,
  });
  Brands.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
  }
}

class Years {
  int? id;
  var year;

  Years({
    this.id,
  });
  Years.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    year = json['year'];
  }
}
