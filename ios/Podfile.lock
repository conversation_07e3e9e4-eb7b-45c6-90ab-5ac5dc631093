PODS:
  - app_tracking_transparency (0.0.1):
    - Flutter
  - connectivity_plus (0.0.1):
    - Flutter
    - FlutterMacOS
  - Firebase/Auth (11.6.0):
    - Firebase/CoreOnly
    - FirebaseAuth (~> 11.6.0)
  - Firebase/CoreOnly (11.6.0):
    - FirebaseCore (~> 11.6.0)
  - Firebase/DynamicLinks (11.6.0):
    - Firebase/CoreOnly
    - FirebaseDynamicLinks (~> 11.6.0)
  - Firebase/Messaging (11.6.0):
    - Firebase/CoreOnly
    - FirebaseMessaging (~> 11.6.0)
  - firebase_auth (5.3.1):
    - Firebase/Auth (= 11.6.0)
    - firebase_core
    - Flutter
  - firebase_core (3.10.1):
    - Firebase/CoreOnly (= 11.6.0)
    - Flutter
  - firebase_dynamic_links (6.0.8):
    - Firebase/DynamicLinks (= 11.6.0)
    - firebase_core
    - Flutter
  - firebase_messaging (15.2.1):
    - Firebase/Messaging (= 11.6.0)
    - firebase_core
    - Flutter
  - FirebaseAppCheckInterop (11.15.0)
  - FirebaseAuth (11.6.0):
    - FirebaseAppCheckInterop (~> 11.0)
    - FirebaseAuthInterop (~> 11.0)
    - FirebaseCore (~> 11.6.0)
    - FirebaseCoreExtension (~> 11.6.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.0)
    - GoogleUtilities/Environment (~> 8.0)
    - GTMSessionFetcher/Core (< 5.0, >= 3.4)
    - RecaptchaInterop (~> 100.0)
  - FirebaseAuthInterop (11.15.0)
  - FirebaseCore (11.6.0):
    - FirebaseCoreInternal (~> 11.6.0)
    - GoogleUtilities/Environment (~> 8.0)
    - GoogleUtilities/Logger (~> 8.0)
  - FirebaseCoreExtension (11.6.0):
    - FirebaseCore (~> 11.6.0)
  - FirebaseCoreInternal (11.6.0):
    - "GoogleUtilities/NSData+zlib (~> 8.0)"
  - FirebaseDynamicLinks (11.6.0):
    - FirebaseCore (~> 11.6.0)
  - FirebaseInstallations (11.6.0):
    - FirebaseCore (~> 11.6.0)
    - GoogleUtilities/Environment (~> 8.0)
    - GoogleUtilities/UserDefaults (~> 8.0)
    - PromisesObjC (~> 2.4)
  - FirebaseMessaging (11.6.0):
    - FirebaseCore (~> 11.6.0)
    - FirebaseInstallations (~> 11.0)
    - GoogleDataTransport (~> 10.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.0)
    - GoogleUtilities/Environment (~> 8.0)
    - GoogleUtilities/Reachability (~> 8.0)
    - GoogleUtilities/UserDefaults (~> 8.0)
    - nanopb (~> 3.30910.0)
  - Flutter (1.0.0)
  - flutter_keyboard_visibility (0.0.1):
    - Flutter
  - fluttertoast (0.0.2):
    - Flutter
  - geolocator_apple (1.2.0):
    - Flutter
  - google_maps_flutter_ios (0.0.1):
    - Flutter
    - GoogleMaps (< 10.0, >= 8.4)
  - GoogleDataTransport (10.1.0):
    - nanopb (~> 3.30910.0)
    - PromisesObjC (~> 2.4)
  - GoogleMaps (8.4.0):
    - GoogleMaps/Maps (= 8.4.0)
  - GoogleMaps/Base (8.4.0)
  - GoogleMaps/Maps (8.4.0):
    - GoogleMaps/Base
  - GoogleUtilities/AppDelegateSwizzler (8.1.0):
    - GoogleUtilities/Environment
    - GoogleUtilities/Logger
    - GoogleUtilities/Network
    - GoogleUtilities/Privacy
  - GoogleUtilities/Environment (8.1.0):
    - GoogleUtilities/Privacy
  - GoogleUtilities/Logger (8.1.0):
    - GoogleUtilities/Environment
    - GoogleUtilities/Privacy
  - GoogleUtilities/Network (8.1.0):
    - GoogleUtilities/Logger
    - "GoogleUtilities/NSData+zlib"
    - GoogleUtilities/Privacy
    - GoogleUtilities/Reachability
  - "GoogleUtilities/NSData+zlib (8.1.0)":
    - GoogleUtilities/Privacy
  - GoogleUtilities/Privacy (8.1.0)
  - GoogleUtilities/Reachability (8.1.0):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GoogleUtilities/UserDefaults (8.1.0):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GTMSessionFetcher/Core (4.5.0)
  - map_launcher (0.0.1):
    - Flutter
  - nanopb (3.30910.0):
    - nanopb/decode (= 3.30910.0)
    - nanopb/encode (= 3.30910.0)
  - nanopb/decode (3.30910.0)
  - nanopb/encode (3.30910.0)
  - open_file_ios (0.0.1):
    - Flutter
  - package_info_plus (0.4.5):
    - Flutter
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - permission_handler_apple (9.3.0):
    - Flutter
  - printing (1.0.0):
    - Flutter
  - PromisesObjC (2.4.0)
  - RecaptchaInterop (100.0.0)
  - share_plus (0.0.1):
    - Flutter
  - shared_preferences_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - sqflite (0.0.3):
    - Flutter
    - FlutterMacOS
  - url_launcher_ios (0.0.1):
    - Flutter
  - video_player_avfoundation (0.0.1):
    - Flutter
    - FlutterMacOS

DEPENDENCIES:
  - app_tracking_transparency (from `.symlinks/plugins/app_tracking_transparency/ios`)
  - connectivity_plus (from `.symlinks/plugins/connectivity_plus/darwin`)
  - firebase_auth (from `.symlinks/plugins/firebase_auth/ios`)
  - firebase_core (from `.symlinks/plugins/firebase_core/ios`)
  - firebase_dynamic_links (from `.symlinks/plugins/firebase_dynamic_links/ios`)
  - firebase_messaging (from `.symlinks/plugins/firebase_messaging/ios`)
  - Flutter (from `Flutter`)
  - flutter_keyboard_visibility (from `.symlinks/plugins/flutter_keyboard_visibility/ios`)
  - fluttertoast (from `.symlinks/plugins/fluttertoast/ios`)
  - geolocator_apple (from `.symlinks/plugins/geolocator_apple/ios`)
  - google_maps_flutter_ios (from `.symlinks/plugins/google_maps_flutter_ios/ios`)
  - map_launcher (from `.symlinks/plugins/map_launcher/ios`)
  - open_file_ios (from `.symlinks/plugins/open_file_ios/ios`)
  - package_info_plus (from `.symlinks/plugins/package_info_plus/ios`)
  - path_provider_foundation (from `.symlinks/plugins/path_provider_foundation/darwin`)
  - permission_handler_apple (from `.symlinks/plugins/permission_handler_apple/ios`)
  - printing (from `.symlinks/plugins/printing/ios`)
  - share_plus (from `.symlinks/plugins/share_plus/ios`)
  - shared_preferences_foundation (from `.symlinks/plugins/shared_preferences_foundation/darwin`)
  - sqflite (from `.symlinks/plugins/sqflite/darwin`)
  - url_launcher_ios (from `.symlinks/plugins/url_launcher_ios/ios`)
  - video_player_avfoundation (from `.symlinks/plugins/video_player_avfoundation/darwin`)

SPEC REPOS:
  trunk:
    - Firebase
    - FirebaseAppCheckInterop
    - FirebaseAuth
    - FirebaseAuthInterop
    - FirebaseCore
    - FirebaseCoreExtension
    - FirebaseCoreInternal
    - FirebaseDynamicLinks
    - FirebaseInstallations
    - FirebaseMessaging
    - GoogleDataTransport
    - GoogleMaps
    - GoogleUtilities
    - GTMSessionFetcher
    - nanopb
    - PromisesObjC
    - RecaptchaInterop

EXTERNAL SOURCES:
  app_tracking_transparency:
    :path: ".symlinks/plugins/app_tracking_transparency/ios"
  connectivity_plus:
    :path: ".symlinks/plugins/connectivity_plus/darwin"
  firebase_auth:
    :path: ".symlinks/plugins/firebase_auth/ios"
  firebase_core:
    :path: ".symlinks/plugins/firebase_core/ios"
  firebase_dynamic_links:
    :path: ".symlinks/plugins/firebase_dynamic_links/ios"
  firebase_messaging:
    :path: ".symlinks/plugins/firebase_messaging/ios"
  Flutter:
    :path: Flutter
  flutter_keyboard_visibility:
    :path: ".symlinks/plugins/flutter_keyboard_visibility/ios"
  fluttertoast:
    :path: ".symlinks/plugins/fluttertoast/ios"
  geolocator_apple:
    :path: ".symlinks/plugins/geolocator_apple/ios"
  google_maps_flutter_ios:
    :path: ".symlinks/plugins/google_maps_flutter_ios/ios"
  map_launcher:
    :path: ".symlinks/plugins/map_launcher/ios"
  open_file_ios:
    :path: ".symlinks/plugins/open_file_ios/ios"
  package_info_plus:
    :path: ".symlinks/plugins/package_info_plus/ios"
  path_provider_foundation:
    :path: ".symlinks/plugins/path_provider_foundation/darwin"
  permission_handler_apple:
    :path: ".symlinks/plugins/permission_handler_apple/ios"
  printing:
    :path: ".symlinks/plugins/printing/ios"
  share_plus:
    :path: ".symlinks/plugins/share_plus/ios"
  shared_preferences_foundation:
    :path: ".symlinks/plugins/shared_preferences_foundation/darwin"
  sqflite:
    :path: ".symlinks/plugins/sqflite/darwin"
  url_launcher_ios:
    :path: ".symlinks/plugins/url_launcher_ios/ios"
  video_player_avfoundation:
    :path: ".symlinks/plugins/video_player_avfoundation/darwin"

SPEC CHECKSUMS:
  app_tracking_transparency: e169b653478da7bb15a6c61209015378ca73e375
  connectivity_plus: ddd7f30999e1faaef5967c23d5b6d503d10434db
  Firebase: 374a441a91ead896215703a674d58cdb3e9d772b
  firebase_auth: b773a7217ba010031bc0f673578dd6246cc07bf8
  firebase_core: e2aa06dbd854d961f8ce46c2e20933bee1bf2d2b
  firebase_dynamic_links: a9fa1674082c770152fd7db12177c358767e34be
  firebase_messaging: 96cf6d67121b3f39746b2a4f29a26c0eee4af70e
  FirebaseAppCheckInterop: 06fe5a3799278ae4667e6c432edd86b1030fa3df
  FirebaseAuth: 0304982cfe00df8d49bf533bc4becd3de36c7122
  FirebaseAuthInterop: 7087d7a4ee4bc4de019b2d0c240974ed5d89e2fd
  FirebaseCore: 48b0dd707581cf9c1a1220da68223fb0a562afaa
  FirebaseCoreExtension: 2d77d6430c16cf43ca2b04608302ed02b3598361
  FirebaseCoreInternal: d98ab91e2d80a56d7b246856a8885443b302c0c2
  FirebaseDynamicLinks: bc4e79f608ce4ee066125695f20f2d9fc137d29e
  FirebaseInstallations: efc0946fc756e4d22d8113f7c761948120322e8c
  FirebaseMessaging: e1aca1fcc23e8b9eddb0e33f375ff90944623021
  Flutter: cabc95a1d2626b1b06e7179b784ebcf0c0cde467
  flutter_keyboard_visibility: 0339d06371254c3eb25eeb90ba8d17dca8f9c069
  fluttertoast: 21eecd6935e7064cc1fcb733a4c5a428f3f24f0f
  geolocator_apple: 6cbaf322953988e009e5ecb481f07efece75c450
  google_maps_flutter_ios: 5bc2be60ad012e79b182ce0fb0ef5030a50fb03e
  GoogleDataTransport: aae35b7ea0c09004c3797d53c8c41f66f219d6a7
  GoogleMaps: 8939898920281c649150e0af74aa291c60f2e77d
  GoogleUtilities: 00c88b9a86066ef77f0da2fab05f65d7768ed8e1
  GTMSessionFetcher: fc75fc972958dceedee61cb662ae1da7a83a91cf
  map_launcher: 5fde49ac9a52672bf99da746599f507b4490d7b5
  nanopb: fad817b59e0457d11a5dfbde799381cd727c1275
  open_file_ios: 461db5853723763573e140de3193656f91990d9e
  package_info_plus: c0502532a26c7662a62a356cebe2692ec5fe4ec4
  path_provider_foundation: 2b6b4c569c0fb62ec74538f866245ac84301af46
  permission_handler_apple: 9878588469a2b0d0fc1e048d9f43605f92e6cec2
  printing: 233e1b73bd1f4a05615548e9b5a324c98588640b
  PromisesObjC: f5707f49cb48b9636751c5b2e7d227e43fba9f47
  RecaptchaInterop: 7d1a4a01a6b2cb1610a47ef3f85f0c411434cb21
  share_plus: c3fef564749587fc939ef86ffb283ceac0baf9f5
  shared_preferences_foundation: fcdcbc04712aee1108ac7fda236f363274528f78
  sqflite: 673a0e54cc04b7d6dba8d24fb8095b31c3a99eec
  url_launcher_ios: 5334b05cef931de560670eeae103fd3e431ac3fe
  video_player_avfoundation: 7c6c11d8470e1675df7397027218274b6d2360b3

PODFILE CHECKSUM: 039b4722f48f40a43fc624cea52aad2ed0626334

COCOAPODS: 1.16.2
