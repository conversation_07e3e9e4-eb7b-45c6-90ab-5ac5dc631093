<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CADisableMinimumFrameDurationOnPhone</key>
	<true/>
	<key>CFBundleDevelopmentRegion</key>
	<string>$(DEVELOPMENT_LANGUAGE)</string>
	<key>CFBundleDisplayName</key>
	<string>Aqar Dubai</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleLocalizations</key>
	<array>
		<string>en</string>
		<string>Arabic</string>
	</array>
	<key>CFBundleName</key>
	<string>ae.dubaipage.dubaiUserDeveloper</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>162.0</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleURLTypes</key>
	<array>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLName</key>
			<string>Bundle ID</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>com.googleusercontent.apps.308257888508-1iqvnuq0v5ea8r57k40v26j29miv0j9l</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>ae.dubaipage.dubaiUserDeveloper</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>app-1-308257888508-ios-557dbaea581b78fe405f79</string>
			</array>
		</dict>
	</array>
	<key>CFBundleVersion</key>
	<string>162</string>
	<key>FirebaseAppDelegateProxyEnabled</key>
	<false/>
	<key>FirebaseDynamicLinksCustomDomains</key>
	<array>
		<string>https://dubaiuser.page.link</string>
	</array>
	<key>LSApplicationQueriesSchemes</key>
	<array>
		<string>tel</string>
		<string>http</string>
		<string>https</string>
		<string>comgooglemaps</string>
		<string>baidumap</string>
		<string>iosamap</string>
		<string>waze</string>
		<string>yandexmaps</string>
		<string>yandexnavi</string>
		<string>citymapper</string>
		<string>mapswithme</string>
		<string>osmandmaps</string>
		<string>dgis</string>
		<string>qqmap</string>
		<string>here-location</string>
		<string>tomtomgo</string>
		<string>whatsapp</string>
	</array>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>NSAppTransportSecurity</key>
	<dict>
		<key>NSAllowsArbitraryLoads</key>
		<true/>
		<key>NSExceptionDomains</key>
		<dict>
			<key>aqardxbapp.com</key>
			<dict>
				<key>NSExceptionAllowsInsecureHTTPLoads</key>
				<true/>
				<key>NSExceptionRequiresForwardSecrecy</key>
				<false/>
				<key>NSIncludesSubdomains</key>
				<true/>
				<key>NSTemporaryExceptionAllowsInsecureHTTPLoads</key>
				<true/>
				<key>NSTemporaryExceptionMinimumTLSVersion</key>
				<string>TLSv1.1</string>
			</dict>
		</dict>
		<key>UIBackgroundModes</key>
		<array>
			<string>audio</string>
			<string>fetch</string>
		</array>
	</dict>
	<key>NSLocationAlwaysAndWhenInUseUsageDescription</key>
	<string>app need to access location To show neear places</string>
	<key>NSLocationAlwaysUsageDescription</key>
	<string>app need to access location To show neear places</string>
	<key>NSLocationUsageDescription</key>
	<string>app need to access location To show neear places</string>
	<key>NSLocationWhenInUseUsageDescription</key>
	<string>app need to access location To show neear places</string>
	<key>NSUserTrackingUsageDescription</key>
	<string>This identifier will be used to deliver personalized data to you.</string>
	<key>UIApplicationSupportsIndirectInputEvents</key>
	<true/>
	<key>UIBackgroundModes</key>
	<array>
		<string>fetch</string>
		<string>remote-notification</string>
	</array>
	<key>UILaunchStoryboardName</key>
	<string>LaunchScreen</string>
	<key>UIMainStoryboardFile</key>
	<string>Main</string>
	<key>UIStatusBarHidden</key>
	<false/>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>UISupportedInterfaceOrientations~ipad</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationPortraitUpsideDown</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>UIViewControllerBasedStatusBarAppearance</key>
	<false/>
</dict>
</plist>
