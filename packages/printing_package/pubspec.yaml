name: printing
description: >
  Plugin that allows Flutter apps to generate and print documents to
  compatible printers on Android, iOS, macOS, Windows, and Linux,
  as well as web print.
homepage: https://github.com/DavBfr/dart_pdf/tree/master/printing
repository: https://github.com/DavBfr/dart_pdf
issue_tracker: https://github.com/DavBfr/dart_pdf/issues
version: 5.6.5

environment:
  sdk: ">=2.12.0 <3.0.0"
  flutter: ">=1.16.0"

dependencies:
  ffi: ^2.0.1
  flutter:
    sdk: flutter
  flutter_web_plugins:
    sdk: flutter
  http:
  image: ^3.0.1
  js: ^0.6.3
  meta: ^1.3.0
  pdf: ^3.4.2
#  pdf: ^3.4.2
#  pdf: ^3.2.0
  plugin_platform_interface: ^2.0.0

dev_dependencies:
  flutter_lints: ^1.0.4
  flutter_test:
    sdk: flutter
  mockito: ^5.0.0

_dependency_overrides:
  pdf:
    path: ../pdf

flutter:
  plugin:
    platforms:
      android:
        package: net.nfet.flutter.printing
        pluginClass: PrintingPlugin
      ios:
        pluginClass: PrintingPlugin
      linux:
        pluginClass: PrintingPlugin
      macos:
        pluginClass: PrintingPlugin
      web:
        fileName: printing_web.dart
        pluginClass: PrintingPlugin
      windows:
        pluginClass: PrintingPlugin
