import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:video_player/video_player.dart';

import 'multi_video_model.dart';

/// Stateful widget to fetch and then display video content.
/// ignore: must_be_immutable
class MultiVideoItem extends StatefulWidget {
  dynamic videoSource;
  int index;
  Function(VideoPlayerController controller) onInit;
  Function(int index) onDispose;
  VideoPlayerOptions? videoPlayerOptions;
  VideoSource sourceType;
  Future<ClosedCaptionFile>? closedCaptionFile;
  Map<String, String>? httpHeaders;
  VideoFormat? formatHint;
  String? package;
  bool showControlsOverlay;
  bool showVideoProgressIndicator;
  bool show = true;

  MultiVideoItem({
    super.key,
    required this.videoSource,
    required this.index,
    required this.onInit,
    required this.onDispose,
    this.videoPlayerOptions,
    this.closedCaptionFile,
    this.httpHeaders,
    this.formatHint,
    this.package,
    this.showControlsOverlay = true,
    this.showVideoProgressIndicator = true,
    required this.sourceType,
  });

  @override
  State<MultiVideoItem> createState() => _MultiVideoItemState();
}

class _MultiVideoItemState extends State<MultiVideoItem> {
  late VideoPlayerController _controller;
  bool isLoading = true;

  @override
  void initState() {
    super.initState();

    _initializeVideo();
  }

  /// initializes videos
  void _initializeVideo() {
    if (widget.sourceType == VideoSource.network) {
      _controller = VideoPlayerController.networkUrl(
        Uri.parse(widget.videoSource!.toString()),
        videoPlayerOptions: widget.videoPlayerOptions,
        closedCaptionFile: widget.closedCaptionFile,
        httpHeaders: widget.httpHeaders ?? {},
        formatHint: widget.formatHint,
      )..setLooping(true);
    } else if (widget.sourceType == VideoSource.asset) {
      _controller = VideoPlayerController.asset(
        widget.videoSource,
        videoPlayerOptions: widget.videoPlayerOptions,
        closedCaptionFile: widget.closedCaptionFile,
        package: widget.package,
      )..setLooping(true);
    } else if (widget.sourceType == VideoSource.file) {
      _controller = VideoPlayerController.file(
        widget.videoSource,
        videoPlayerOptions: widget.videoPlayerOptions,
        closedCaptionFile: widget.closedCaptionFile,
        httpHeaders: widget.httpHeaders ?? {},
      )..setLooping(true);
    }

    _controller.initialize().then((_) {
      widget.onInit.call(_controller);
      if (widget.index == MultiVideo.currentIndex) {
        log('PPPPPSADASDSAD ${widget.index} afasf ${MultiVideo.currentIndex}');
        _controller.play();
      }

      if (mounted) {
        _controller.addListener(() => _videoListener());
        setState(() => isLoading = false);
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: isLoading
          ? const Center(child: CircularProgressIndicator())
          : _controller.value.isInitialized
              ? Center(
                  child: SizedBox(
                    height: MediaQuery.sizeOf(context).height,
                    width: MediaQuery.sizeOf(context).width,
                    // aspectRatio: _controller.value.aspectRatio,
                    child: Stack(
                      alignment: Alignment.bottomCenter,
                      children: <Widget>[
                        VideoPlayer(_controller),
                        widget.showControlsOverlay
                            ? _ControlsOverlay(controller: _controller)
                            : const SizedBox.shrink(),
                        widget.showVideoProgressIndicator
                            ? VideoProgressIndicator(_controller,
                                allowScrubbing: true)
                            : const SizedBox.shrink(),
                      ],
                    ),
                  ),
                )
              : const SizedBox.shrink(),
    );
  }

  @override
  void dispose() {
    _controller.dispose();
    widget.onDispose.call(widget.index);
    super.dispose();
  }

  _videoListener() {
    if (widget.index != MultiVideo.currentIndex) {
      if (_controller.value.isInitialized) {
        if (_controller.value.isPlaying) {
          _controller.pause();
        }
      }
    }
    setState(() {});
  }
}

class _ControlsOverlay extends StatefulWidget {
  const _ControlsOverlay({required this.controller});

  final VideoPlayerController controller;

  @override
  State<_ControlsOverlay> createState() => _ControlsOverlayState();
}

class _ControlsOverlayState extends State<_ControlsOverlay> {
  @override
  Widget build(BuildContext context) {
    return Stack(
      children: <Widget>[
        AnimatedSwitcher(
          duration: const Duration(milliseconds: 50),
          reverseDuration: const Duration(milliseconds: 200),
          child: widget.controller.value.isPlaying
              ? const SizedBox.shrink()
              : Center(
                  child: CircleAvatar(
                    backgroundColor: Colors.black.withOpacity(0.5),
                    maxRadius: 30,
                    child: const Icon(
                      Icons.play_arrow,
                      color: Colors.white,
                      size: 50,
                      semanticLabel: 'Play',
                    ),
                  ),
                ),
        ),
        GestureDetector(
          onTap: () {
            setState(() {
              widget.controller.value.isPlaying
                  ? widget.controller.pause()
                  : widget.controller.play();
            });
          },
        ),
      ],
    );
  }
}
