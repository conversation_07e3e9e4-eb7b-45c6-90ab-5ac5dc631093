<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="88" height="88" viewBox="0 0 88 88">
  <defs>
    <clipPath id="clip-path">
      <circle id="Ellipse_22" data-name="Ellipse 22" cx="44" cy="44" r="44" transform="translate(144 278)" fill="#f0f0f0" opacity="0.548"/>
    </clipPath>
    <filter id="Ellipse_23" x="16" y="16" width="57" height="57" filterUnits="userSpaceOnUse">
      <feOffset input="SourceAlpha"/>
      <feGaussianBlur stdDeviation="3" result="blur"/>
      <feFlood flood-opacity="0.161"/>
      <feComposite operator="in" in2="blur"/>
      <feComposite in="SourceGraphic"/>
    </filter>
  </defs>
  <g id="Group_7271" data-name="Group 7271" transform="translate(-144 -292)">
    <g id="Group_6222" data-name="Group 6222" transform="translate(0 14)">
      <circle id="Ellipse_21" data-name="Ellipse 21" cx="44" cy="44" r="44" transform="translate(144 278)" fill="#f0f0f0" opacity="0.548"/>
    </g>
    <g id="Group_6389" data-name="Group 6389" transform="translate(-1751.647 -2484.469)">
      <g transform="matrix(1, 0, 0, 1, 1895.65, 2776.47)" filter="url(#Ellipse_23)">
        <circle id="Ellipse_23-2" data-name="Ellipse 23" cx="19.5" cy="19.5" r="19.5" transform="translate(25 25)" fill="#fff"/>
      </g>
      <g id="heart" transform="translate(1931.54 2814.352)" opacity="0.39">
        <path id="Path_14" data-name="Path 14" d="M13.075,35.525a4.909,4.909,0,0,1,4.885,4.885c0,4.933-8.98,9.818-8.98,9.818S0,45.272,0,40.41a4.885,4.885,0,0,1,4.885-4.885h0a4.837,4.837,0,0,1,4.1,2.2A4.885,4.885,0,0,1,13.075,35.525Z" transform="translate(0 -35.525)" fill="#b7b7b7"/>
      </g>
    </g>
  </g>
</svg>
